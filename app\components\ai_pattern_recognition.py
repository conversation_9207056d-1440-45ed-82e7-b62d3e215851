"""
AI Pattern Recognition for SMC Analysis
Machine learning-based pattern detection and success probability analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@dataclass
class PatternMatch:
    """Represents a detected pattern match"""
    pattern_name: str
    confidence: float  # 0-1
    success_probability: float  # Historical success rate
    pattern_type: str  # 'bullish', 'bearish', 'reversal', 'continuation'
    start_index: int
    end_index: int
    key_levels: List[float]
    expected_move: float  # Expected price move percentage
    time_horizon: int  # Expected time to target in bars
    risk_reward_ratio: float
    similar_patterns_count: int  # Number of similar historical patterns

@dataclass
class PatternDatabase:
    """Database of historical patterns and their outcomes"""
    patterns: List[Dict]
    success_rates: Dict[str, float]
    average_moves: Dict[str, float]
    time_horizons: Dict[str, int]

class AIPatternRecognizer:
    """AI-powered pattern recognition system"""
    
    def __init__(self):
        self.pattern_database = self._initialize_pattern_database()
        self.feature_extractors = self._initialize_feature_extractors()
        
    def detect_patterns(self, df: pd.DataFrame, smc_results: Dict) -> List[PatternMatch]:
        """
        Detect patterns using AI analysis
        
        Args:
            df: OHLCV DataFrame
            smc_results: SMC analysis results
            
        Returns:
            List of detected patterns
        """
        patterns = []
        
        try:
            # Extract features for pattern matching
            features = self._extract_features(df, smc_results)
            
            # Detect different pattern types
            patterns.extend(self._detect_smc_patterns(df, smc_results, features))
            patterns.extend(self._detect_price_action_patterns(df, features))
            patterns.extend(self._detect_volume_patterns(df, features))
            patterns.extend(self._detect_structure_patterns(df, smc_results, features))
            
            # Score and rank patterns
            patterns = self._score_patterns(patterns, features)
            
            # Filter by confidence threshold
            patterns = [p for p in patterns if p.confidence >= 0.6]
            
            # Sort by confidence
            patterns.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            logger.error(f"Error in pattern detection: {str(e)}")
        
        return patterns[:10]  # Return top 10 patterns
    
    def _initialize_pattern_database(self) -> PatternDatabase:
        """Initialize pattern database with historical success rates"""
        
        # SMC Pattern success rates (based on typical institutional trading)
        smc_patterns = {
            'order_block_retest': {'success_rate': 0.75, 'avg_move': 0.025, 'time_horizon': 15},
            'fvg_fill_reversal': {'success_rate': 0.68, 'avg_move': 0.020, 'time_horizon': 10},
            'liquidity_sweep_reversal': {'success_rate': 0.82, 'avg_move': 0.035, 'time_horizon': 8},
            'bos_continuation': {'success_rate': 0.71, 'avg_move': 0.040, 'time_horizon': 20},
            'choch_reversal': {'success_rate': 0.79, 'avg_move': 0.055, 'time_horizon': 25},
            'premium_discount_reversal': {'success_rate': 0.65, 'avg_move': 0.030, 'time_horizon': 18}
        }
        
        # Price action patterns
        price_action_patterns = {
            'double_top': {'success_rate': 0.62, 'avg_move': 0.045, 'time_horizon': 30},
            'double_bottom': {'success_rate': 0.64, 'avg_move': 0.042, 'time_horizon': 28},
            'head_shoulders': {'success_rate': 0.58, 'avg_move': 0.065, 'time_horizon': 35},
            'ascending_triangle': {'success_rate': 0.69, 'avg_move': 0.038, 'time_horizon': 22},
            'descending_triangle': {'success_rate': 0.67, 'avg_move': 0.036, 'time_horizon': 24}
        }
        
        # Combine all patterns
        all_patterns = {**smc_patterns, **price_action_patterns}
        
        return PatternDatabase(
            patterns=[],
            success_rates={k: v['success_rate'] for k, v in all_patterns.items()},
            average_moves={k: v['avg_move'] for k, v in all_patterns.items()},
            time_horizons={k: v['time_horizon'] for k, v in all_patterns.items()}
        )
    
    def _initialize_feature_extractors(self) -> Dict:
        """Initialize feature extraction functions"""
        
        return {
            'price_features': self._extract_price_features,
            'volume_features': self._extract_volume_features,
            'smc_features': self._extract_smc_features,
            'technical_features': self._extract_technical_features
        }
    
    def _extract_features(self, df: pd.DataFrame, smc_results: Dict) -> Dict:
        """Extract comprehensive features for pattern matching"""
        
        features = {}
        
        # Price-based features
        features.update(self._extract_price_features(df))
        
        # Volume-based features
        features.update(self._extract_volume_features(df))
        
        # SMC-based features
        features.update(self._extract_smc_features(smc_results))
        
        # Technical indicator features
        features.update(self._extract_technical_features(df))
        
        return features
    
    def _extract_price_features(self, df: pd.DataFrame) -> Dict:
        """Extract price-based features"""
        
        if len(df) < 20:
            return {}
        
        recent_data = df.tail(20)
        
        return {
            'price_volatility': recent_data['close'].std() / recent_data['close'].mean(),
            'price_trend': (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0],
            'high_low_ratio': recent_data['high'].max() / recent_data['low'].min(),
            'body_to_range_ratio': ((recent_data['close'] - recent_data['open']).abs() / 
                                   (recent_data['high'] - recent_data['low'])).mean(),
            'consecutive_closes': self._count_consecutive_closes(recent_data),
            'gap_frequency': self._calculate_gap_frequency(recent_data)
        }
    
    def _extract_volume_features(self, df: pd.DataFrame) -> Dict:
        """Extract volume-based features"""
        
        if len(df) < 20 or 'volume' not in df.columns:
            return {}
        
        recent_data = df.tail(20)
        
        return {
            'volume_trend': (recent_data['volume'].iloc[-5:].mean() - 
                           recent_data['volume'].iloc[:5].mean()) / recent_data['volume'].mean(),
            'volume_volatility': recent_data['volume'].std() / recent_data['volume'].mean(),
            'price_volume_correlation': recent_data['close'].corr(recent_data['volume']),
            'volume_breakout_strength': self._calculate_volume_breakout_strength(recent_data),
            'accumulation_distribution': self._calculate_accumulation_distribution(recent_data)
        }
    
    def _extract_smc_features(self, smc_results: Dict) -> Dict:
        """Extract SMC-based features"""
        
        return {
            'order_blocks_count': len(smc_results.get('order_blocks', [])),
            'fvgs_count': len(smc_results.get('fvgs', [])),
            'liquidity_zones_count': len(smc_results.get('liquidity_zones', [])),
            'bos_events_count': len(smc_results.get('bos_events', [])),
            'liquidity_sweeps_count': len(smc_results.get('liquidity_sweeps', [])),
            'confluence_score': smc_results.get('confluence', {}).get('total_score', 0),
            'market_structure_trend': smc_results.get('market_structure', {}).get('trend', 'sideways'),
            'premium_discount_zone': smc_results.get('premium_discount', {}).get('current_zone', 'equilibrium') if smc_results.get('premium_discount') else 'equilibrium'
        }
    
    def _extract_technical_features(self, df: pd.DataFrame) -> Dict:
        """Extract technical indicator features"""
        
        if len(df) < 20:
            return {}
        
        # Calculate basic technical indicators
        df_temp = df.copy()
        df_temp['sma_20'] = df_temp['close'].rolling(20).mean()
        df_temp['ema_12'] = df_temp['close'].ewm(span=12).mean()
        df_temp['rsi'] = self._calculate_rsi(df_temp['close'], 14)
        
        return {
            'sma_position': (df_temp['close'].iloc[-1] - df_temp['sma_20'].iloc[-1]) / df_temp['sma_20'].iloc[-1],
            'ema_slope': (df_temp['ema_12'].iloc[-1] - df_temp['ema_12'].iloc[-5]) / df_temp['ema_12'].iloc[-5],
            'rsi_level': df_temp['rsi'].iloc[-1],
            'rsi_divergence': self._detect_rsi_divergence(df_temp),
            'momentum': (df_temp['close'].iloc[-1] - df_temp['close'].iloc[-5]) / df_temp['close'].iloc[-5]
        }
    
    def _detect_smc_patterns(self, df: pd.DataFrame, smc_results: Dict, features: Dict) -> List[PatternMatch]:
        """Detect SMC-specific patterns"""
        
        patterns = []
        current_price = df['close'].iloc[-1]
        
        # Order Block Retest Pattern
        if features.get('order_blocks_count', 0) > 0:
            ob_pattern = self._detect_order_block_retest(df, smc_results, current_price)
            if ob_pattern:
                patterns.append(ob_pattern)
        
        # FVG Fill Reversal Pattern
        if features.get('fvgs_count', 0) > 0:
            fvg_pattern = self._detect_fvg_fill_reversal(df, smc_results, current_price)
            if fvg_pattern:
                patterns.append(fvg_pattern)
        
        # Liquidity Sweep Reversal Pattern
        if features.get('liquidity_sweeps_count', 0) > 0:
            sweep_pattern = self._detect_liquidity_sweep_reversal(df, smc_results, current_price)
            if sweep_pattern:
                patterns.append(sweep_pattern)
        
        # BOS Continuation Pattern
        if features.get('bos_events_count', 0) > 0:
            bos_pattern = self._detect_bos_continuation(df, smc_results, current_price)
            if bos_pattern:
                patterns.append(bos_pattern)
        
        # Premium/Discount Reversal Pattern
        if features.get('premium_discount_zone') in ['premium', 'discount']:
            pd_pattern = self._detect_premium_discount_reversal(df, smc_results, current_price)
            if pd_pattern:
                patterns.append(pd_pattern)
        
        return patterns
    
    def _detect_price_action_patterns(self, df: pd.DataFrame, features: Dict) -> List[PatternMatch]:
        """Detect classic price action patterns"""
        
        patterns = []
        
        # Double Top/Bottom
        double_pattern = self._detect_double_top_bottom(df)
        if double_pattern:
            patterns.append(double_pattern)
        
        # Head and Shoulders
        hs_pattern = self._detect_head_shoulders(df)
        if hs_pattern:
            patterns.append(hs_pattern)
        
        # Triangle Patterns
        triangle_pattern = self._detect_triangle_patterns(df)
        if triangle_pattern:
            patterns.append(triangle_pattern)
        
        return patterns
    
    def _detect_volume_patterns(self, df: pd.DataFrame, features: Dict) -> List[PatternMatch]:
        """Detect volume-based patterns"""
        
        patterns = []
        
        # Volume Breakout Pattern
        if features.get('volume_breakout_strength', 0) > 1.5:
            volume_pattern = PatternMatch(
                pattern_name='volume_breakout',
                confidence=min(features['volume_breakout_strength'] / 3, 1.0),
                success_probability=0.72,
                pattern_type='continuation',
                start_index=len(df) - 10,
                end_index=len(df) - 1,
                key_levels=[df['close'].iloc[-1]],
                expected_move=0.025,
                time_horizon=12,
                risk_reward_ratio=2.0,
                similar_patterns_count=15
            )
            patterns.append(volume_pattern)
        
        return patterns
    
    def _detect_structure_patterns(self, df: pd.DataFrame, smc_results: Dict, features: Dict) -> List[PatternMatch]:
        """Detect market structure patterns"""
        
        patterns = []
        
        # Strong Confluence Pattern
        if features.get('confluence_score', 0) > 0.75:
            confluence_pattern = PatternMatch(
                pattern_name='high_confluence_setup',
                confidence=features['confluence_score'],
                success_probability=0.78,
                pattern_type='bullish' if features.get('market_structure_trend') == 'bullish' else 'bearish',
                start_index=len(df) - 15,
                end_index=len(df) - 1,
                key_levels=[df['close'].iloc[-1]],
                expected_move=0.035,
                time_horizon=18,
                risk_reward_ratio=2.5,
                similar_patterns_count=22
            )
            patterns.append(confluence_pattern)
        
        return patterns
    
    def _score_patterns(self, patterns: List[PatternMatch], features: Dict) -> List[PatternMatch]:
        """Score and enhance pattern confidence based on market context"""
        
        for pattern in patterns:
            # Adjust confidence based on market conditions
            market_trend = features.get('market_structure_trend', 'sideways')
            
            # Boost confidence if pattern aligns with market trend
            if pattern.pattern_type == market_trend:
                pattern.confidence = min(pattern.confidence * 1.2, 1.0)
            
            # Boost confidence for high confluence
            confluence_boost = features.get('confluence_score', 0) * 0.1
            pattern.confidence = min(pattern.confidence + confluence_boost, 1.0)
            
            # Adjust based on volume confirmation
            volume_strength = features.get('volume_breakout_strength', 1.0)
            if volume_strength > 1.5:
                pattern.confidence = min(pattern.confidence * 1.1, 1.0)
        
        return patterns
    
    # Helper methods for pattern detection
    def _detect_order_block_retest(self, df: pd.DataFrame, smc_results: Dict, current_price: float) -> Optional[PatternMatch]:
        """Detect order block retest pattern"""
        
        order_blocks = smc_results.get('order_blocks', [])
        if not order_blocks:
            return None
        
        # Check if price is near an order block
        for ob in order_blocks[:3]:
            distance_to_ob = min(abs(current_price - ob.high), abs(current_price - ob.low))
            distance_pct = distance_to_ob / current_price
            
            if distance_pct < 0.01:  # Within 1% of order block
                confidence = 0.7 + (ob.strength * 0.3)
                
                return PatternMatch(
                    pattern_name='order_block_retest',
                    confidence=confidence,
                    success_probability=self.pattern_database.success_rates['order_block_retest'],
                    pattern_type=ob.block_type,
                    start_index=ob.timestamp,
                    end_index=len(df) - 1,
                    key_levels=[ob.high, ob.low],
                    expected_move=self.pattern_database.average_moves['order_block_retest'],
                    time_horizon=self.pattern_database.time_horizons['order_block_retest'],
                    risk_reward_ratio=2.0,
                    similar_patterns_count=18
                )
        
        return None
    
    def _detect_fvg_fill_reversal(self, df: pd.DataFrame, smc_results: Dict, current_price: float) -> Optional[PatternMatch]:
        """Detect FVG fill reversal pattern"""
        
        fvgs = smc_results.get('fvgs', [])
        if not fvgs:
            return None
        
        # Check if price is filling an FVG
        for fvg in fvgs[:3]:
            if fvg.low <= current_price <= fvg.high and not fvg.filled:
                confidence = 0.65 + (fvg.strength * 0.35)
                
                return PatternMatch(
                    pattern_name='fvg_fill_reversal',
                    confidence=confidence,
                    success_probability=self.pattern_database.success_rates['fvg_fill_reversal'],
                    pattern_type=fvg.gap_type,
                    start_index=fvg.timestamp,
                    end_index=len(df) - 1,
                    key_levels=[fvg.high, fvg.low],
                    expected_move=self.pattern_database.average_moves['fvg_fill_reversal'],
                    time_horizon=self.pattern_database.time_horizons['fvg_fill_reversal'],
                    risk_reward_ratio=1.8,
                    similar_patterns_count=14
                )
        
        return None
    
    def _detect_liquidity_sweep_reversal(self, df: pd.DataFrame, smc_results: Dict, current_price: float) -> Optional[PatternMatch]:
        """Detect liquidity sweep reversal pattern"""
        
        sweeps = smc_results.get('liquidity_sweeps', [])
        if not sweeps:
            return None
        
        # Check for recent confirmed sweeps
        for sweep in sweeps[:2]:
            if sweep.reversal_confirmed:
                confidence = 0.75 + (sweep.strength * 0.25)
                
                return PatternMatch(
                    pattern_name='liquidity_sweep_reversal',
                    confidence=confidence,
                    success_probability=self.pattern_database.success_rates['liquidity_sweep_reversal'],
                    pattern_type='bullish' if sweep.sweep_type == 'sell_side' else 'bearish',
                    start_index=sweep.timestamp,
                    end_index=len(df) - 1,
                    key_levels=[sweep.sweep_price, sweep.liquidity_level],
                    expected_move=self.pattern_database.average_moves['liquidity_sweep_reversal'],
                    time_horizon=self.pattern_database.time_horizons['liquidity_sweep_reversal'],
                    risk_reward_ratio=2.8,
                    similar_patterns_count=12
                )
        
        return None
    
    def _detect_bos_continuation(self, df: pd.DataFrame, smc_results: Dict, current_price: float) -> Optional[PatternMatch]:
        """Detect BOS continuation pattern"""
        
        bos_events = smc_results.get('bos_events', [])
        if not bos_events:
            return None
        
        # Check for recent confirmed BOS
        for bos in bos_events[:2]:
            if bos.confirmed:
                confidence = 0.68 + (bos.strength * 0.32)
                
                return PatternMatch(
                    pattern_name='bos_continuation',
                    confidence=confidence,
                    success_probability=self.pattern_database.success_rates['bos_continuation'],
                    pattern_type=bos.direction,
                    start_index=bos.timestamp,
                    end_index=len(df) - 1,
                    key_levels=[bos.price, bos.previous_level],
                    expected_move=self.pattern_database.average_moves['bos_continuation'],
                    time_horizon=self.pattern_database.time_horizons['bos_continuation'],
                    risk_reward_ratio=2.2,
                    similar_patterns_count=20
                )
        
        return None
    
    def _detect_premium_discount_reversal(self, df: pd.DataFrame, smc_results: Dict, current_price: float) -> Optional[PatternMatch]:
        """Detect premium/discount reversal pattern"""
        
        pd_zone = smc_results.get('premium_discount')
        if not pd_zone:
            return None
        
        if pd_zone.current_zone in ['premium', 'discount']:
            confidence = 0.6 + (pd_zone.zone_strength * 0.4)
            
            return PatternMatch(
                pattern_name='premium_discount_reversal',
                confidence=confidence,
                success_probability=self.pattern_database.success_rates['premium_discount_reversal'],
                pattern_type='bearish' if pd_zone.current_zone == 'premium' else 'bullish',
                start_index=len(df) - 20,
                end_index=len(df) - 1,
                key_levels=[pd_zone.high, pd_zone.low, pd_zone.equilibrium],
                expected_move=self.pattern_database.average_moves['premium_discount_reversal'],
                time_horizon=self.pattern_database.time_horizons['premium_discount_reversal'],
                risk_reward_ratio=2.0,
                similar_patterns_count=16
            )
        
        return None
    
    # Additional helper methods would be implemented here for:
    # - _detect_double_top_bottom
    # - _detect_head_shoulders  
    # - _detect_triangle_patterns
    # - _count_consecutive_closes
    # - _calculate_gap_frequency
    # - _calculate_volume_breakout_strength
    # - _calculate_accumulation_distribution
    # - _calculate_rsi
    # - _detect_rsi_divergence
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _count_consecutive_closes(self, df: pd.DataFrame) -> int:
        """Count consecutive up/down closes"""
        closes = df['close'].diff()
        consecutive = 0
        current_direction = None

        for change in closes.dropna():
            if change > 0:
                if current_direction == 'up':
                    consecutive += 1
                else:
                    consecutive = 1
                    current_direction = 'up'
            elif change < 0:
                if current_direction == 'down':
                    consecutive += 1
                else:
                    consecutive = 1
                    current_direction = 'down'

        return consecutive

    def _calculate_gap_frequency(self, df: pd.DataFrame) -> float:
        """Calculate frequency of price gaps"""
        gaps = 0
        for i in range(1, len(df)):
            prev_close = df.iloc[i-1]['close']
            curr_open = df.iloc[i]['open']
            gap_pct = abs(curr_open - prev_close) / prev_close
            if gap_pct > 0.005:  # 0.5% gap threshold
                gaps += 1

        return gaps / len(df) if len(df) > 0 else 0

    def _calculate_volume_breakout_strength(self, df: pd.DataFrame) -> float:
        """Calculate volume breakout strength"""
        if 'volume' not in df.columns:
            return 1.0

        recent_volume = df['volume'].iloc[-1]
        avg_volume = df['volume'].mean()

        return recent_volume / avg_volume if avg_volume > 0 else 1.0

    def _calculate_accumulation_distribution(self, df: pd.DataFrame) -> float:
        """Calculate accumulation/distribution indicator"""
        if 'volume' not in df.columns:
            return 0.0

        ad_line = 0
        for _, row in df.iterrows():
            if row['high'] != row['low']:
                clv = ((row['close'] - row['low']) - (row['high'] - row['close'])) / (row['high'] - row['low'])
                ad_line += clv * row['volume']

        return ad_line

    def _detect_rsi_divergence(self, df: pd.DataFrame) -> float:
        """Detect RSI divergence"""
        if len(df) < 20 or 'rsi' not in df.columns:
            return 0.0

        # Simple divergence detection
        recent_prices = df['close'].tail(10)
        recent_rsi = df['rsi'].tail(10)

        price_trend = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
        rsi_trend = recent_rsi.iloc[-1] - recent_rsi.iloc[0]

        # Divergence occurs when price and RSI move in opposite directions
        if (price_trend > 0 and rsi_trend < 0) or (price_trend < 0 and rsi_trend > 0):
            return abs(price_trend) + abs(rsi_trend) / 100

        return 0.0
