2025-05-30 07:53:15,131 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-30 07:53:17,691 - app - INFO - Memory management utilities loaded
2025-05-30 07:53:17,693 - app - INFO - Error handling utilities loaded
2025-05-30 07:53:17,693 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 07:53:17,694 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 07:53:17,694 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 07:53:17,694 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 07:53:17,698 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 07:53:17,699 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 07:53:17,699 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 07:53:17,699 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 07:53:17,699 - app - INFO - Applied NumPy fix
2025-05-30 07:53:17,700 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 07:53:17,700 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 07:53:17,700 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 07:53:17,701 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 07:53:27,519 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 07:53:27,519 - app - INFO - Applied TensorFlow fix
2025-05-30 07:53:27,521 - app.config - INFO - Configuration initialized
2025-05-30 07:53:27,523 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 07:53:27,770 - models.train - INFO - TensorFlow test successful
2025-05-30 07:53:29,755 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 07:53:29,755 - models.train - INFO - Transformer model is available
2025-05-30 07:53:29,756 - models.train - INFO - Using TensorFlow-based models
2025-05-30 07:53:29,757 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 07:53:29,757 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 07:53:29,758 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 07:53:30,137 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 07:53:30,137 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 07:53:30,201 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 07:53:30,213 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:53:30,465 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 07:53:31,177 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 07:53:31,189 - app.utils.session_state - INFO - Initializing session state
2025-05-30 07:53:31,190 - app.utils.session_state - INFO - Session state initialized
2025-05-30 07:53:32,259 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 07:53:32,268 - app.utils.memory_management - INFO - Memory before cleanup: 430.06 MB
2025-05-30 07:53:32,382 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 07:53:32,383 - app.utils.memory_management - INFO - Memory after cleanup: 430.06 MB (freed -0.00 MB)
2025-05-30 07:54:01,894 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:01,946 - app.utils.memory_management - INFO - Memory before cleanup: 434.05 MB
2025-05-30 07:54:02,142 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 07:54:02,143 - app.utils.memory_management - INFO - Memory after cleanup: 434.05 MB (freed 0.00 MB)
2025-05-30 07:54:08,799 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:08,954 - app - INFO - File COMI contains 2025 data
2025-05-30 07:54:08,976 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:54:08,977 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:54:08,980 - app.utils.memory_management - INFO - Memory before cleanup: 437.45 MB
2025-05-30 07:54:09,144 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-05-30 07:54:09,145 - app.utils.memory_management - INFO - Memory after cleanup: 437.49 MB (freed -0.04 MB)
2025-05-30 07:54:42,238 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:42,251 - app.utils.session_state - INFO - Initializing session state
2025-05-30 07:54:42,261 - app.utils.session_state - INFO - Session state initialized
2025-05-30 07:54:42,281 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 07:54:42,289 - app.utils.memory_management - INFO - Memory before cleanup: 437.86 MB
2025-05-30 07:54:42,479 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-30 07:54:42,480 - app.utils.memory_management - INFO - Memory after cleanup: 437.86 MB (freed 0.00 MB)
2025-05-30 07:54:49,644 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:49,663 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 07:54:49,690 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-30 07:54:49,691 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-30 07:54:49,691 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-30 07:54:49,691 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-30 07:54:50,172 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.51 seconds
2025-05-30 07:54:50,173 - app.utils.memory_management - INFO - Memory before cleanup: 444.59 MB
2025-05-30 07:54:50,281 - app.utils.memory_management - INFO - Garbage collection: collected 2395 objects
2025-05-30 07:54:50,282 - app.utils.memory_management - INFO - Memory after cleanup: 444.59 MB (freed 0.00 MB)
2025-05-30 07:54:53,083 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:53,111 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 07:54:53,122 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-30 07:54:53,123 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-30 07:54:53,123 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-30 07:54:53,124 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-30 07:54:53,320 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.21 seconds
2025-05-30 07:54:53,322 - app.utils.memory_management - INFO - Memory before cleanup: 447.00 MB
2025-05-30 07:54:53,430 - app.utils.memory_management - INFO - Garbage collection: collected 1450 objects
2025-05-30 07:54:53,431 - app.utils.memory_management - INFO - Memory after cleanup: 447.00 MB (freed 0.00 MB)
2025-05-30 07:54:54,294 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:54,313 - app.utils.memory_management - INFO - Memory before cleanup: 446.98 MB
2025-05-30 07:54:54,433 - app.utils.memory_management - INFO - Garbage collection: collected 215 objects
2025-05-30 07:54:54,434 - app.utils.memory_management - INFO - Memory after cleanup: 446.98 MB (freed 0.00 MB)
2025-05-30 07:55:01,388 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:01,434 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:01,438 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:01,439 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:01,441 - app.utils.memory_management - INFO - Memory before cleanup: 447.02 MB
2025-05-30 07:55:01,621 - app.utils.memory_management - INFO - Garbage collection: collected 207 objects
2025-05-30 07:55:01,621 - app.utils.memory_management - INFO - Memory after cleanup: 447.02 MB (freed 0.00 MB)
2025-05-30 07:55:06,036 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:06,076 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:06,080 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:06,081 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:06,083 - app.utils.memory_management - INFO - Memory before cleanup: 447.03 MB
2025-05-30 07:55:06,216 - app.utils.memory_management - INFO - Garbage collection: collected 222 objects
2025-05-30 07:55:06,216 - app.utils.memory_management - INFO - Memory after cleanup: 447.03 MB (freed 0.00 MB)
2025-05-30 07:55:10,835 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:10,893 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:10,897 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:10,898 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:10,899 - app.utils.memory_management - INFO - Memory before cleanup: 447.07 MB
2025-05-30 07:55:11,036 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-30 07:55:11,037 - app.utils.memory_management - INFO - Memory after cleanup: 447.07 MB (freed 0.00 MB)
2025-05-30 07:55:12,638 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:12,709 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:12,716 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:12,717 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:12,719 - app.utils.memory_management - INFO - Memory before cleanup: 447.07 MB
2025-05-30 07:55:12,850 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-05-30 07:55:12,851 - app.utils.memory_management - INFO - Memory after cleanup: 447.07 MB (freed 0.00 MB)
2025-05-30 07:55:15,851 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:15,943 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:15,980 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:15,981 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:15,982 - app.utils.memory_management - INFO - Memory before cleanup: 447.16 MB
2025-05-30 07:55:16,111 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-05-30 07:55:16,119 - app.utils.memory_management - INFO - Memory after cleanup: 447.16 MB (freed 0.00 MB)
2025-05-30 07:55:23,262 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:23,395 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:23,427 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:23,428 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:23,430 - app.utils.memory_management - INFO - Memory before cleanup: 447.37 MB
2025-05-30 07:55:23,598 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-05-30 07:55:23,598 - app.utils.memory_management - INFO - Memory after cleanup: 447.37 MB (freed 0.00 MB)
2025-05-30 07:55:40,621 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:40,657 - app.utils.memory_management - INFO - Memory before cleanup: 447.36 MB
2025-05-30 07:55:40,823 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-05-30 07:55:40,824 - app.utils.memory_management - INFO - Memory after cleanup: 447.36 MB (freed 0.00 MB)
2025-05-30 07:56:01,265 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:56:01,316 - app.utils.memory_management - INFO - Memory before cleanup: 447.36 MB
2025-05-30 07:56:01,447 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-05-30 07:56:01,447 - app.utils.memory_management - INFO - Memory after cleanup: 447.36 MB (freed 0.00 MB)
2025-05-30 07:56:57,384 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:56:57,440 - app.utils.memory_management - INFO - Memory before cleanup: 447.36 MB
2025-05-30 07:56:57,667 - app.utils.memory_management - INFO - Garbage collection: collected 228 objects
2025-05-30 07:56:57,668 - app.utils.memory_management - INFO - Memory after cleanup: 447.36 MB (freed 0.00 MB)
2025-05-30 07:56:57,806 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:56:57,823 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:57,824 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:57,825 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:57,831 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,832 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,832 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,832 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-30 07:56:57,837 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:57,837 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-30 07:56:57,842 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,850 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-30 07:56:57,855 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-30 07:56:57,855 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-30 07:56:57,855 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-30 07:56:57,856 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-30 07:56:57,861 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,861 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,861 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,862 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-30 07:56:57,862 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-30 07:56:57,862 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-30 07:56:57,866 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,867 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,867 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,868 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:57,868 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:57,868 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:57,872 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,874 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,874 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,893 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-30 07:56:57,913 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:57,914 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-30 07:56:57,921 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,928 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-30 07:56:57,930 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-30 07:56:57,932 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-30 07:56:57,933 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-30 07:56:57,933 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-30 07:56:57,935 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-30 07:56:57,936 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-30 07:56:57,937 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-30 07:56:57,938 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-30 07:56:57,938 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-30 07:56:57,938 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-30 07:56:58,061 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-30 07:56:58,080 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:58,080 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,080 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:58,089 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,091 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,093 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-30 07:56:58,102 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:58,102 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-30 07:56:58,106 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,112 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-30 07:56:58,112 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-30 07:56:58,112 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-30 07:56:58,114 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-30 07:56:58,117 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,118 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,118 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-30 07:56:58,118 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-30 07:56:58,119 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-30 07:56:58,124 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,126 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,126 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:58,126 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,128 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:58,132 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,133 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,137 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,143 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:58,145 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-30 07:56:58,145 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-30 07:56:58,145 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,152 - app.utils.memory_management - INFO - Memory before cleanup: 447.40 MB
2025-05-30 07:56:58,276 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 07:56:58,276 - app.utils.memory_management - INFO - Memory after cleanup: 447.40 MB (freed 0.00 MB)
2025-05-30 07:57:17,652 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:57:17,683 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 07:57:17,701 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 07:57:17,701 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:57:17,702 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-30 07:57:17,702 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-30 07:57:17,729 - app.utils.memory_management - INFO - Memory before cleanup: 449.32 MB
2025-05-30 07:57:17,913 - app.utils.memory_management - INFO - Garbage collection: collected 326 objects
2025-05-30 07:57:17,913 - app.utils.memory_management - INFO - Memory after cleanup: 449.32 MB (freed 0.00 MB)
2025-05-30 08:22:30,348 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 08:22:30,393 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 08:22:30,437 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 08:22:30,478 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 08:22:30,480 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 08:22:30,481 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-30 08:22:30,481 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-30 08:22:30,526 - app.utils.memory_management - INFO - Memory before cleanup: 445.26 MB
2025-05-30 08:22:30,710 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-05-30 08:22:30,711 - app.utils.memory_management - INFO - Memory after cleanup: 445.26 MB (freed -0.00 MB)
2025-05-30 09:04:05,220 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:04:06,376 - app - INFO - Memory management utilities loaded
2025-05-30 09:04:06,378 - app - INFO - Error handling utilities loaded
2025-05-30 09:04:06,379 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:04:06,380 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:04:06,380 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:04:06,380 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:04:06,381 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 09:04:06,381 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 09:04:06,382 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 09:04:06,385 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:04:06,386 - app - INFO - Applied NumPy fix
2025-05-30 09:04:06,387 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:04:06,389 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:04:06,389 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:04:06,389 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:04:10,085 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:04:10,085 - app - INFO - Applied TensorFlow fix
2025-05-30 09:04:10,088 - app.config - INFO - Configuration initialized
2025-05-30 09:04:10,092 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:04:10,099 - models.train - INFO - TensorFlow test successful
2025-05-30 09:04:10,543 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:04:10,543 - models.train - INFO - Transformer model is available
2025-05-30 09:04:10,544 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:04:10,545 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:04:10,545 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:04:10,547 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:04:10,816 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:04:10,816 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:04:10,816 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:04:10,818 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:04:10,818 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:04:10,894 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:04:10,896 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:11,214 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:04:11,796 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:04:11,825 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:04:11,826 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:04:13,030 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:04:13,041 - app.utils.memory_management - INFO - Memory before cleanup: 430.63 MB
2025-05-30 09:04:13,158 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-05-30 09:04:13,158 - app.utils.memory_management - INFO - Memory after cleanup: 430.64 MB (freed -0.01 MB)
2025-05-30 09:04:19,836 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:19,861 - app.utils.memory_management - INFO - Memory before cleanup: 433.97 MB
2025-05-30 09:04:20,011 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:04:20,011 - app.utils.memory_management - INFO - Memory after cleanup: 433.98 MB (freed -0.02 MB)
2025-05-30 09:04:24,460 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:24,480 - app.utils.memory_management - INFO - Memory before cleanup: 434.20 MB
2025-05-30 09:04:24,704 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-05-30 09:04:24,704 - app.utils.memory_management - INFO - Memory after cleanup: 434.25 MB (freed -0.04 MB)
2025-05-30 09:04:26,489 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:26,517 - app.utils.memory_management - INFO - Memory before cleanup: 434.25 MB
2025-05-30 09:04:26,657 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-05-30 09:04:26,657 - app.utils.memory_management - INFO - Memory after cleanup: 434.25 MB (freed 0.00 MB)
2025-05-30 09:04:31,412 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:31,435 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:04:31,438 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:04:31,467 - app.utils.memory_management - INFO - Memory before cleanup: 434.25 MB
2025-05-30 09:04:31,624 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-30 09:04:31,626 - app.utils.memory_management - INFO - Memory after cleanup: 434.25 MB (freed 0.00 MB)
2025-05-30 09:04:37,450 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:37,478 - app.utils.memory_management - INFO - Memory before cleanup: 435.30 MB
2025-05-30 09:04:37,640 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-30 09:04:37,640 - app.utils.memory_management - INFO - Memory after cleanup: 435.30 MB (freed 0.00 MB)
2025-05-30 09:04:38,493 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:38,568 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-30 09:04:38,569 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:04:38,569 - app - INFO - Data shape: (581, 36)
2025-05-30 09:04:38,569 - app - INFO - File COMI contains 2025 data
2025-05-30 09:04:38,623 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-30 09:04:38,623 - app - INFO - Features shape: (581, 36)
2025-05-30 09:04:38,640 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 09:04:38,641 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:04:38,641 - app - INFO - Data shape: (581, 36)
2025-05-30 09:04:38,641 - app - INFO - File COMI contains 2025 data
2025-05-30 09:04:38,645 - app.utils.memory_management - INFO - Memory before cleanup: 438.93 MB
2025-05-30 09:04:38,796 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 09:04:38,796 - app.utils.memory_management - INFO - Memory after cleanup: 438.93 MB (freed 0.00 MB)
2025-05-30 09:04:38,953 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:39,010 - app.utils.memory_management - INFO - Memory before cleanup: 439.89 MB
2025-05-30 09:04:39,163 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:04:39,164 - app.utils.memory_management - INFO - Memory after cleanup: 439.83 MB (freed 0.06 MB)
2025-05-30 09:04:42,170 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:43,652 - app.utils.memory_management - INFO - Memory before cleanup: 453.00 MB
2025-05-30 09:04:43,787 - app.utils.memory_management - INFO - Garbage collection: collected 1672 objects
2025-05-30 09:04:43,788 - app.utils.memory_management - INFO - Memory after cleanup: 453.00 MB (freed 0.00 MB)
2025-05-30 09:06:01,383 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:06:01,390 - app - INFO - Memory management utilities loaded
2025-05-30 09:06:01,392 - app - INFO - Error handling utilities loaded
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:06:01,394 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'CompatibleMT19937' object has no attribute 'capsule'
2025-05-30 09:06:01,395 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-30 09:06:01,396 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-30 09:06:01,396 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:06:01,396 - app - INFO - Applied NumPy fix
2025-05-30 09:06:01,397 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:06:01,397 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:06:01,397 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:06:01,398 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:06:01,400 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:06:01,400 - app - INFO - Applied TensorFlow fix
2025-05-30 09:06:01,402 - app.config - INFO - Configuration initialized
2025-05-30 09:06:01,407 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:06:01,414 - models.train - INFO - TensorFlow test successful
2025-05-30 09:06:01,417 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:06:01,418 - models.train - INFO - Transformer model is available
2025-05-30 09:06:01,419 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:06:01,421 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:06:01,422 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:06:01,424 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:06:01,433 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:06:01,435 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:06:01,436 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:06:01,436 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:06:01,437 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:06:01,437 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:06:01,437 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'CompatibleBitGenerator' has no attribute 'register'
2025-05-30 09:06:01,438 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:06:01,438 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:06:01,438 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:06:01,440 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:06:01,443 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:06:01,469 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:06:01,488 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:06:01,510 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:06:01,511 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:06:01,528 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:06:01,538 - app.utils.memory_management - INFO - Memory before cleanup: 199.43 MB
2025-05-30 09:06:01,932 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-05-30 09:06:01,933 - app.utils.memory_management - INFO - Memory after cleanup: 385.62 MB (freed -186.19 MB)
2025-05-30 09:06:24,395 - app - INFO - Cleaning up resources...
2025-05-30 09:06:24,395 - app.utils.memory_management - INFO - Memory before cleanup: 387.89 MB
2025-05-30 09:06:24,503 - app.utils.memory_management - INFO - Garbage collection: collected 333 objects
2025-05-30 09:06:24,504 - app.utils.memory_management - INFO - Memory after cleanup: 387.93 MB (freed -0.04 MB)
2025-05-30 09:06:24,504 - app - INFO - Application shutdown complete
2025-05-30 09:06:24,505 - app - INFO - Cleaning up resources...
2025-05-30 09:06:24,505 - app.utils.memory_management - INFO - Memory before cleanup: 387.93 MB
2025-05-30 09:06:24,614 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-05-30 09:06:24,614 - app.utils.memory_management - INFO - Memory after cleanup: 387.93 MB (freed 0.00 MB)
2025-05-30 09:06:24,614 - app - INFO - Application shutdown complete
