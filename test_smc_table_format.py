"""
Test script for SMC table format display
Verify the new clean table format matches user preferences
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_smc_table_format():
    """Test the new SMC table format"""
    print("🧪 Testing SMC Table Format")
    print("=" * 50)
    
    try:
        from app.components.smc_indicators import (
            detect_order_blocks, detect_fvg, detect_liquidity_zones
        )
        from app.pages.smc_analysis import generate_ohlcv_data
        
        # Generate sample data
        df = generate_ohlcv_data(current_price=82.5, bars=100)
        print(f"✅ Generated sample data: {len(df)} bars")
        
        # Test order blocks detection
        order_blocks = detect_order_blocks(df, lookback=10, min_strength=0.2)
        print(f"✅ Order blocks detected: {len(order_blocks)}")
        
        # Test FVG detection
        fvgs = detect_fvg(df, min_gap_size=0.001)
        print(f"✅ Fair Value Gaps detected: {len(fvgs)}")
        
        # Test liquidity zones
        liquidity_zones = detect_liquidity_zones(df, lookback=10)
        print(f"✅ Liquidity zones detected: {len(liquidity_zones)}")
        
        # Test table format creation
        print("\n📊 Testing Table Format Creation")
        print("-" * 40)
        
        # Order Blocks Table
        if order_blocks:
            print("\n🔲 Order Blocks Table Format:")
            table_data = []
            for i, ob in enumerate(order_blocks[:3]):
                status_emoji = "🟢" if not ob.broken else "🔴" if ob.broken else "🟡" if ob.tested else "⚪"
                status_text = "BROKEN" if ob.broken else "TESTED" if ob.tested else "ACTIVE"
                
                table_data.append({
                    "Level": f"OB-{i+1}",
                    "Type": ob.block_type.upper(),
                    "High": f"{ob.high:,.2f}",
                    "Low": f"{ob.low:,.2f}",
                    "Strength": f"{ob.strength:.1%}",
                    "Status": f"{status_emoji} {status_text}"
                })
            
            df_display = pd.DataFrame(table_data)
            print(df_display.to_string(index=False))
            print("✅ Order Blocks table format working")
        
        # FVG Table
        if fvgs:
            print("\n⚡ Fair Value Gaps Table Format:")
            table_data = []
            for i, fvg in enumerate(fvgs[:3]):
                status_emoji = "🟢" if not fvg.filled else "🟡" if fvg.partially_filled else "🔴"
                status_text = "FILLED" if fvg.filled else "PARTIAL" if fvg.partially_filled else "OPEN"
                
                table_data.append({
                    "Level": f"FVG-{i+1}",
                    "Type": fvg.gap_type.upper(),
                    "High": f"{fvg.high:,.2f}",
                    "Low": f"{fvg.low:,.2f}",
                    "Strength": f"{fvg.strength:.1%}",
                    "Status": f"{status_emoji} {status_text}"
                })
            
            df_display = pd.DataFrame(table_data)
            print(df_display.to_string(index=False))
            print("✅ FVG table format working")
        
        # Liquidity Zones Table
        if liquidity_zones:
            print("\n💧 Liquidity Zones Table Format:")
            table_data = []
            for i, lz in enumerate(liquidity_zones[:3]):
                status_emoji = "🟢" if not lz.swept else "🔴"
                status_text = "SWEPT" if lz.swept else "ACTIVE"
                level_price = (lz.high + lz.low) / 2
                
                table_data.append({
                    "Level": f"LZ-{i+1}",
                    "Type": lz.zone_type.upper().replace('_', ' '),
                    "Price": f"{level_price:,.2f}",
                    "Range": f"{lz.low:,.2f} - {lz.high:,.2f}",
                    "Strength": f"{lz.strength:.1%}",
                    "Status": f"{status_emoji} {status_text}"
                })
            
            df_display = pd.DataFrame(table_data)
            print(df_display.to_string(index=False))
            print("✅ Liquidity Zones table format working")
        
        print("\n🎯 Expected Table Format in Streamlit:")
        print("=" * 50)
        print("✅ Clean table layout with proper columns")
        print("✅ Formatted numbers with commas (e.g., 89,933)")
        print("✅ Consistent column widths")
        print("✅ Status indicators with emojis")
        print("✅ Professional appearance matching Advanced Technical Analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing table format: {str(e)}")
        return False

def main():
    """Run SMC table format test"""
    print("🧠 SMC Table Format Test")
    print("=" * 60)
    
    success = test_smc_table_format()
    
    if success:
        print("\n🎉 SMC Table Format Test PASSED!")
        print("\n📊 Your SMC Analysis will now display:")
        print("- 🔲 Order Blocks in clean table format")
        print("- ⚡ Fair Value Gaps in clean table format") 
        print("- 💧 Liquidity Zones in clean table format")
        print("- 📈 Professional appearance matching your preferences")
        
        print("\n🚀 Ready to test in Streamlit!")
        print("The SMC Analysis page will now show data in the clean table format you prefer.")
        
    else:
        print("\n❌ SMC Table Format Test FAILED!")
        print("Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
