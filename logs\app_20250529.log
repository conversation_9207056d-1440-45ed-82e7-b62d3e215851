2025-05-29 12:07:32,246 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-29 12:07:38,306 - app - INFO - Memory management utilities loaded
2025-05-29 12:07:38,319 - app - INFO - Error handling utilities loaded
2025-05-29 12:07:38,325 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-29 12:07:38,326 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-29 12:07:38,326 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-29 12:07:38,326 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-29 12:07:38,339 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-29 12:07:38,353 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-29 12:07:38,356 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-29 12:07:38,359 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-29 12:07:38,359 - app - INFO - Applied NumPy fix
2025-05-29 12:07:38,364 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-29 12:07:38,369 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-29 12:07:38,370 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-29 12:07:38,372 - app - INFO - Applied NumPy BitGenerator fix
2025-05-29 12:07:57,402 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-29 12:07:57,410 - app - INFO - Applied TensorFlow fix
2025-05-29 12:07:57,424 - app.config - INFO - Configuration initialized
2025-05-29 12:07:57,440 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-29 12:07:57,736 - models.train - INFO - TensorFlow test successful
2025-05-29 12:08:02,019 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-29 12:08:02,019 - models.train - INFO - Transformer model is available
2025-05-29 12:08:02,019 - models.train - INFO - Using TensorFlow-based models
2025-05-29 12:08:02,031 - models.predict - INFO - Transformer model is available for predictions
2025-05-29 12:08:02,031 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-29 12:08:02,059 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-29 12:08:03,677 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-29 12:08:03,677 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-29 12:08:04,038 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-29 12:08:04,084 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:05,005 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-29 12:08:08,330 - app.services.llm_service - INFO - llama_cpp is available
2025-05-29 12:08:08,389 - app.utils.session_state - INFO - Initializing session state
2025-05-29 12:08:08,389 - app.utils.session_state - INFO - Session state initialized
2025-05-29 12:08:09,557 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:08:09,569 - app.utils.memory_management - INFO - Memory before cleanup: 430.14 MB
2025-05-29 12:08:09,726 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:08:09,727 - app.utils.memory_management - INFO - Memory after cleanup: 430.54 MB (freed -0.40 MB)
2025-05-29 12:08:20,194 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:20,230 - app.utils.memory_management - INFO - Memory before cleanup: 434.28 MB
2025-05-29 12:08:20,396 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:08:20,397 - app.utils.memory_management - INFO - Memory after cleanup: 434.28 MB (freed 0.00 MB)
2025-05-29 12:08:21,427 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:21,570 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.12 seconds
2025-05-29 12:08:21,585 - app - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:08:21,585 - app - INFO - Data shape: (579, 36)
2025-05-29 12:08:21,585 - app - INFO - File COMI contains 2025 data
2025-05-29 12:08:21,633 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-29 12:08:21,633 - app - INFO - Features shape: (579, 36)
2025-05-29 12:08:21,647 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:08:21,648 - app - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:08:21,648 - app - INFO - Data shape: (579, 36)
2025-05-29 12:08:21,648 - app - INFO - File COMI contains 2025 data
2025-05-29 12:08:21,650 - app.utils.memory_management - INFO - Memory before cleanup: 438.27 MB
2025-05-29 12:08:21,760 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-29 12:08:21,760 - app.utils.memory_management - INFO - Memory after cleanup: 438.31 MB (freed -0.04 MB)
2025-05-29 12:08:21,899 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:22,004 - app.utils.memory_management - INFO - Memory before cleanup: 439.38 MB
2025-05-29 12:08:22,146 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-29 12:08:22,147 - app.utils.memory_management - INFO - Memory after cleanup: 439.36 MB (freed 0.02 MB)
2025-05-29 12:08:44,819 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:44,863 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:44,867 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:44,868 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:44,876 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,877 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,877 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:08:44,878 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:08:44,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:44,885 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:44,889 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,893 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:08:44,953 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:08:44,954 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:08:44,954 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:44,954 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:08:44,975 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,976 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,976 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:08:44,976 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:44,977 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:44,977 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:44,982 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,983 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,984 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:08:45,002 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:08:45,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:45,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:45,013 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,031 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:08:45,034 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:45,037 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:08:45,037 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:08:45,038 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:08:45,038 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:08:45,286 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:08:45,327 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:45,327 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,330 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:45,338 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,338 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,338 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:08:45,342 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:45,342 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:45,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,361 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:08:45,363 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:08:45,364 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:45,369 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:08:45,378 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,379 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,379 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:08:45,380 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:45,380 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:08:45,390 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,391 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,392 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:45,392 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,393 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:45,406 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,421 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,467 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,474 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:45,476 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:45,476 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:45,476 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,487 - app.utils.memory_management - INFO - Memory before cleanup: 440.03 MB
2025-05-29 12:08:45,611 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-29 12:08:45,612 - app.utils.memory_management - INFO - Memory after cleanup: 440.03 MB (freed 0.00 MB)
2025-05-29 12:08:55,651 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:55,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:08:55,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:55,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:55,714 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,721 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:08:55,721 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:08:55,722 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:08:55,722 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:08:55,723 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:08:55,723 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:55,726 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:08:55,727 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:08:55,728 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:08:55,728 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:08:55,862 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:08:55,888 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:55,888 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:55,889 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:55,894 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,894 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:55,895 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:08:55,906 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:55,939 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:55,957 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,966 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:08:55,968 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:08:55,968 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:55,968 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:08:55,972 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,983 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:08:55,984 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:08:55,988 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:55,989 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:08:55,998 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,998 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:08:55,999 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:56,000 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:56,002 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:56,015 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:56,015 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:56,022 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:56,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:56,036 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:56,037 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:56,037 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:56,051 - app.utils.memory_management - INFO - Memory before cleanup: 440.09 MB
2025-05-29 12:08:56,177 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:08:56,178 - app.utils.memory_management - INFO - Memory after cleanup: 440.09 MB (freed 0.00 MB)
2025-05-29 12:08:57,616 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:57,648 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:57,662 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:08:58,910 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:08:58,910 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:08:58,918 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.73
2025-05-29 12:09:01,532 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:09:01,532 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:09:01,532 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:09:32,324 - app.utils.retry - WARNING - Attempt 1 failed, retrying in 1.82s: Message: timeout: Timed out receiving message from renderer: 30.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7249F5335+78597]
	GetHandleVerifier [0x00007FF7249F5390+78688]
	(No symbol) [0x00007FF7247A91AA]
	(No symbol) [0x00007FF7247965AC]
	(No symbol) [0x00007FF72479629A]
	(No symbol) [0x00007FF724793F4A]
	(No symbol) [0x00007FF7247948FF]
	(No symbol) [0x00007FF7247A34FE]
	(No symbol) [0x00007FF7247B9931]
	(No symbol) [0x00007FF7247C08DA]
	(No symbol) [0x00007FF72479506D]
	(No symbol) [0x00007FF7247B9665]
	(No symbol) [0x00007FF72484F194]
	(No symbol) [0x00007FF724826EC3]
	(No symbol) [0x00007FF7247F03F8]
	(No symbol) [0x00007FF7247F1163]
	GetHandleVerifier [0x00007FF724C9EEED+2870973]
	GetHandleVerifier [0x00007FF724C99698+2848360]
	GetHandleVerifier [0x00007FF724CB6973+2967875]
	GetHandleVerifier [0x00007FF724A1017A+188746]
	GetHandleVerifier [0x00007FF724A1845F+222255]
	GetHandleVerifier [0x00007FF7249FD2B4+111236]
	GetHandleVerifier [0x00007FF7249FD462+111666]
	GetHandleVerifier [0x00007FF7249E3589+5465]
	BaseThreadInitThunk [0x00007FFF0932E8D7+23]
	RtlUserThreadStart [0x00007FFF0AC9C5DC+44]

2025-05-29 12:09:34,165 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:09:36,550 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:09:38,738 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:09:38,746 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.3
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.3
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:09:38,747 - app.utils.error_handling - INFO - fetch_price executed in 37.21 seconds
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:09:40,979 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-29 12:09:41,103 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-29 12:09:41,220 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:09:41,225 - models.predict - INFO - Using RobustEnsembleModel for 4 minutes horizon
2025-05-29 12:09:41,334 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_4min.joblib
2025-05-29 12:09:41,334 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 4
2025-05-29 12:09:41,334 - models.predict - INFO - Loading ensemble model for COMI with horizon 4
2025-05-29 12:09:41,336 - models.predict - INFO - Ensemble model already loaded
2025-05-29 12:09:41,353 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:09:41,353 - models.predict - INFO - Current price: 81.3, Predicted scaled value: 0.750632905440788
2025-05-29 12:09:41,353 - models.predict - INFO - Prediction for 4 minutes horizon: 82.83468137890404
2025-05-29 12:09:41,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:09:41,699 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:09:41,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:09:41,709 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,711 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:09:41,715 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:09:41,839 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:09:41,864 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:09:41,864 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,864 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:09:41,871 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,871 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,872 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:09:41,876 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:09:41,877 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:09:41,882 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:09:41,889 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:09:41,890 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:09:41,891 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:09:41,896 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,896 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,896 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:09:41,897 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:09:41,898 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:09:41,902 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,903 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,904 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:09:41,905 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,906 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:09:41,911 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,913 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,917 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,925 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:09:41,925 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:09:41,925 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:09:41,929 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,933 - app.utils.memory_management - INFO - Memory before cleanup: 444.67 MB
2025-05-29 12:09:42,041 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:09:42,041 - app.utils.memory_management - INFO - Memory after cleanup: 444.67 MB (freed 0.00 MB)
2025-05-29 12:10:42,981 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:43,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:43,058 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:43,066 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,073 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:43,077 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:43,079 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:43,080 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:43,081 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:43,081 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:43,352 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:43,399 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:43,400 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,400 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:43,410 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,413 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,413 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:43,421 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,421 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:43,426 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,433 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:43,434 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:43,435 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:43,436 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:43,442 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,443 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,445 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:43,447 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:43,448 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:43,459 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,459 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,459 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:43,461 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,461 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:43,470 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,470 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,476 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,484 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,484 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:43,484 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:43,486 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,494 - app.utils.memory_management - INFO - Memory before cleanup: 446.01 MB
2025-05-29 12:10:43,636 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:43,636 - app.utils.memory_management - INFO - Memory after cleanup: 446.01 MB (freed 0.00 MB)
2025-05-29 12:10:43,829 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:43,872 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:43,883 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:43,895 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,905 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:43,905 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:43,907 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:43,907 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:43,907 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:43,908 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:43,909 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:43,909 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:43,910 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:43,910 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:44,080 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:44,116 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:44,117 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,117 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:44,123 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,124 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:44,130 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:44,131 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:44,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,147 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:44,147 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:44,148 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:44,148 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:44,168 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,168 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,172 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,191 - app.utils.memory_management - INFO - Memory before cleanup: 446.02 MB
2025-05-29 12:10:44,319 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:44,319 - app.utils.memory_management - INFO - Memory after cleanup: 446.02 MB (freed 0.00 MB)
2025-05-29 12:10:44,944 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:45,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:45,039 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:45,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:45,054 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,062 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:45,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:45,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:45,067 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:45,069 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:45,074 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:45,082 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:45,083 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:45,083 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:45,083 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:45,255 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:45,316 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:45,316 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,318 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:45,323 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,323 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,323 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:45,334 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:45,334 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:45,340 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,359 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:45,361 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:45,362 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:45,368 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:45,374 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,376 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,377 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:45,377 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:45,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:45,390 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,391 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,391 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:45,391 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,392 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:45,400 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,401 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,406 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,413 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:45,415 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:45,417 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:45,418 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,432 - app.utils.memory_management - INFO - Memory before cleanup: 446.03 MB
2025-05-29 12:10:45,599 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:45,599 - app.utils.memory_management - INFO - Memory after cleanup: 446.03 MB (freed 0.00 MB)
2025-05-29 12:10:46,185 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:46,274 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:46,284 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:46,286 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:46,292 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,296 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:46,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:46,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:46,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:46,299 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:46,300 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:46,302 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:46,303 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:46,303 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:46,303 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:46,455 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:46,484 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:46,485 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,486 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:46,491 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,492 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:46,496 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:46,497 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:46,502 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,507 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:46,507 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:46,507 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:46,508 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:46,518 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,519 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,519 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:46,520 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:46,520 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:46,525 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,526 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,526 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:46,526 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,527 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:46,531 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,531 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,538 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,544 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:46,545 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:46,546 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:46,546 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,556 - app.utils.memory_management - INFO - Memory before cleanup: 446.04 MB
2025-05-29 12:10:46,691 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:46,691 - app.utils.memory_management - INFO - Memory after cleanup: 446.02 MB (freed 0.02 MB)
2025-05-29 12:10:54,290 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:54,354 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:54,362 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:54,363 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:54,367 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,374 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:54,374 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:54,375 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:54,376 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:54,376 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:54,377 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:54,379 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:54,379 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:54,379 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:54,380 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:54,518 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:54,544 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:54,544 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,545 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:54,552 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,553 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,553 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:54,561 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:54,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:54,567 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:54,594 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:54,602 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,602 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,603 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:54,603 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,603 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:54,612 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,612 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,617 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,623 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:54,623 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:54,624 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:54,624 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,629 - app.utils.memory_management - INFO - Memory before cleanup: 446.03 MB
2025-05-29 12:10:54,758 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:54,759 - app.utils.memory_management - INFO - Memory after cleanup: 446.03 MB (freed 0.00 MB)
2025-05-29 12:10:55,111 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:55,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:55,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:55,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:55,156 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,161 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:55,162 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:55,162 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:55,163 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:55,163 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:55,164 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:55,165 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:55,167 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:55,169 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:55,170 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:55,303 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:55,336 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:55,336 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,336 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:55,344 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,344 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,345 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:55,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:55,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:55,355 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:55,366 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:55,366 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:55,367 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:55,383 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,385 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,385 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:55,387 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:55,387 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:55,394 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:55,401 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,401 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,411 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,417 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:55,417 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:55,418 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:55,418 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,427 - app.utils.memory_management - INFO - Memory before cleanup: 446.03 MB
2025-05-29 12:10:55,565 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:55,566 - app.utils.memory_management - INFO - Memory after cleanup: 446.03 MB (freed 0.00 MB)
2025-05-29 12:10:57,094 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:57,131 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:57,140 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:57,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:57,150 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,156 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:57,159 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:57,160 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:57,160 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:57,161 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:57,162 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:57,164 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:57,164 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:57,164 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:57,164 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:57,302 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:57,335 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:57,336 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,336 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:57,344 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,345 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,345 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:57,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:57,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:57,358 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:57,365 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:57,366 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:57,366 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:57,371 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,371 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,372 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:57,372 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:57,372 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:57,382 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,382 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,383 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:57,383 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,384 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:57,391 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,392 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,395 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,417 - app.utils.memory_management - INFO - Memory before cleanup: 446.05 MB
2025-05-29 12:10:57,551 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:57,551 - app.utils.memory_management - INFO - Memory after cleanup: 446.05 MB (freed 0.00 MB)
2025-05-29 12:10:58,967 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:59,010 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:59,015 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:59,015 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:59,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:59,034 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:59,170 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:59,199 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:59,200 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,200 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:59,208 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,209 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,209 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:59,213 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:59,213 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:59,213 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:59,235 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,235 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,236 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:59,236 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:59,236 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:59,240 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,241 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,241 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:59,242 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,242 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:59,248 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,248 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,253 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,269 - app.utils.memory_management - INFO - Memory before cleanup: 446.11 MB
2025-05-29 12:10:59,391 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:59,392 - app.utils.memory_management - INFO - Memory after cleanup: 446.11 MB (freed 0.00 MB)
2025-05-29 12:11:06,768 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:11:06,805 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:11:06,814 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:11:06,815 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:11:06,822 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:11:06,827 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:11:06,982 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:11:06,995 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:11:08,174 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:11:08,174 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.65
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:11:33,851 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:11:35,973 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:11:35,984 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.31
2025-05-29 12:11:35,984 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.31
2025-05-29 12:11:35,985 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:11:35,985 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:11:35,985 - app.utils.error_handling - INFO - fetch_price executed in 27.79 seconds
2025-05-29 12:11:35,987 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:11:35,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:11:35,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:11:35,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:11:35,989 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:11:35,990 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:11:35,991 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:11:35,991 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:11:35,992 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:11:36,143 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:36,317 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:36,431 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:36,433 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-29 12:11:36,433 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-29 12:11:38,006 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:40,573 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-29 12:11:40,575 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7009795904159546
2025-05-29 12:11:40,575 - models.predict - INFO - Prediction for 30 minutes horizon: 80.11367986759097
2025-05-29 12:11:40,599 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:40,763 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:40,875 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.18 MB, VMS: 0.21 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:40,877 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-05-29 12:11:41,100 - models.hybrid_model - INFO - XGBoost is available
2025-05-29 12:11:41,100 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-29 12:11:41,113 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-05-29 12:11:41,114 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-05-29 12:11:41,114 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-05-29 12:11:41,196 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:41,197 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:41,197 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:41,203 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:41,204 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7304014849662781
2025-05-29 12:11:41,204 - models.predict - INFO - Prediction for 30 minutes horizon: 81.72599959885332
2025-05-29 12:11:41,225 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:41,346 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:41,498 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:41,500 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-05-29 12:11:41,501 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-05-29 12:11:41,503 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-05-29 12:11:41,503 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-05-29 12:11:41,512 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:41,531 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:41,532 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:41,533 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:41,533 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7324837511963868
2025-05-29 12:11:41,534 - models.predict - INFO - Prediction for 30 minutes horizon: 81.84010778188697
2025-05-29 12:11:41,571 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:41,828 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:42,009 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:42,012 - models.predict - INFO - Using scikit-learn svr model for 30 minutes horizon
2025-05-29 12:11:42,013 - models.predict - INFO - Loading svr model for COMI with horizon 30
2025-05-29 12:11:42,013 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_30min.joblib
2025-05-29 12:11:42,013 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_30min.joblib
2025-05-29 12:11:42,015 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:42,016 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:11:42,016 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:42,026 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:42,027 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.49390464032770726
2025-05-29 12:11:42,030 - models.predict - INFO - Prediction for 30 minutes horizon: 68.76597323685753
2025-05-29 12:11:42,063 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:42,200 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:42,347 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:42,350 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-05-29 12:11:42,350 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-05-29 12:11:42,351 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_30min.joblib
2025-05-29 12:11:42,352 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_30min.joblib
2025-05-29 12:11:42,356 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:42,357 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:11:42,357 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:42,359 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:42,359 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.734099911823467
2025-05-29 12:11:42,361 - models.predict - INFO - Prediction for 30 minutes horizon: 81.92867337930198
2025-05-29 12:11:42,364 - models.predict - INFO - Current price for COMI: 81.31
2025-05-29 12:11:42,365 - models.predict - INFO - Prophet prediction for 30 minutes: 81.28613431803268
2025-05-29 12:11:42,409 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:42,669 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:42,917 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-29 12:11:42,919 - models.predict - INFO - Using scikit-learn hybrid model for 30 minutes horizon
2025-05-29 12:11:42,922 - models.predict - INFO - Loading hybrid model for COMI with horizon 30
2025-05-29 12:11:42,925 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_30min.joblib
2025-05-29 12:11:42,927 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-29 12:11:42,927 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-29 12:11:43,040 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:43,042 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:11:43,044 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:43,052 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:43,059 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7304014849662781
2025-05-29 12:11:43,064 - models.predict - INFO - Prediction for 30 minutes horizon: 81.72599959885332
2025-05-29 12:11:43,108 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:43,277 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:43,417 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:43,418 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:11:43,419 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:11:43,419 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:11:43,420 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:11:43,447 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:43,600 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:43,743 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:43,745 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:11:43,746 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:11:43,746 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:11:43,747 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:11:43,748 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:43,750 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:11:43,751 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:11:43,842 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:11:43,877 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:43,878 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:43,878 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:43,884 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:43,884 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:11:43,884 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:11:43,913 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:44,093 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:44,290 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-29 12:11:44,291 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:11:44,292 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:11:44,292 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:11:44,293 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:11:44,295 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:44,296 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:11:44,296 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:11:44,302 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:11:44,312 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:44,331 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:44,331 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:44,332 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:44,333 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7194341839453234
2025-05-29 12:11:44,334 - models.predict - INFO - Prediction for 60 minutes horizon: 81.12499328020373
2025-05-29 12:11:44,367 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:44,504 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:44,630 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:44,632 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:11:44,632 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:11:44,633 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:11:44,633 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:11:44,635 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:44,635 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:11:44,636 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:11:44,637 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:11:44,640 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:44,640 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:11:44,640 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:44,641 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:44,641 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.49872770271068445
2025-05-29 12:11:44,642 - models.predict - INFO - Prediction for 60 minutes horizon: 69.0302781085455
2025-05-29 12:11:44,667 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:44,802 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:44,945 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:44,947 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:11:44,948 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:11:44,948 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:11:44,949 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:11:44,951 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:44,951 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:11:44,951 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:11:44,952 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:11:44,952 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:44,953 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:11:44,953 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:44,953 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:44,953 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7427804284288911
2025-05-29 12:11:44,954 - models.predict - INFO - Prediction for 60 minutes horizon: 82.40436747790324
2025-05-29 12:11:44,958 - models.predict - INFO - Current price for COMI: 81.31
2025-05-29 12:11:44,958 - models.predict - INFO - Prophet prediction for 60 minutes: 81.44004793329107
2025-05-29 12:11:44,983 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:45,116 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:45,264 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:45,266 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:11:45,266 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:11:45,266 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:11:45,267 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:11:45,269 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:45,269 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:11:45,270 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:11:45,344 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:11:45,373 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:45,374 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:11:45,379 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:45,380 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:11:45,381 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:11:45,441 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-29 12:11:45,480 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-29 12:11:45,576 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:11:48,006 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:11:48,006 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,006 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:11:48,024 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,024 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,024 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:11:48,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:11:48,032 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:11:48,035 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:11:48,043 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:11:48,043 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:11:48,045 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:11:48,065 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,067 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,067 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,077 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:11:48,078 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:11:48,078 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:11:48,078 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,089 - app.utils.memory_management - INFO - Memory before cleanup: 479.90 MB
2025-05-29 12:11:48,268 - app.utils.memory_management - INFO - Garbage collection: collected 604 objects
2025-05-29 12:11:48,268 - app.utils.memory_management - INFO - Memory after cleanup: 479.90 MB (freed 0.00 MB)
2025-05-29 12:12:30,603 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:12:30,726 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:12:30,740 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:30,742 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:30,762 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:30,771 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:12:30,772 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:12:30,773 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:12:30,773 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:12:30,773 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:12:30,775 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:12:30,777 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:12:30,777 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:12:30,777 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:12:30,777 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:12:30,952 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:12:30,992 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:30,992 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:30,992 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:31,001 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,001 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,001 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:12:31,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:31,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:31,013 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,026 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:12:31,026 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:12:31,027 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:31,027 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:12:31,039 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,040 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,040 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:12:31,040 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:31,041 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:12:31,046 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,047 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,047 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:31,048 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:31,048 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:31,057 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,057 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,063 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:31,072 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:31,073 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:31,073 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:31,073 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:31,081 - app.utils.memory_management - INFO - Memory before cleanup: 480.34 MB
2025-05-29 12:12:31,231 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:12:31,232 - app.utils.memory_management - INFO - Memory after cleanup: 480.34 MB (freed 0.00 MB)
2025-05-29 12:12:31,984 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:12:32,041 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:12:32,060 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:32,067 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:32,078 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:32,097 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:12:32,097 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:12:32,104 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:12:32,106 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:12:32,106 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:12:32,107 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:12:32,108 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:12:32,109 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:12:32,109 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:12:32,109 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:12:32,253 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:12:32,297 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:32,442 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:32,573 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:32,575 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:12:32,575 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:12:32,575 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:12:32,576 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:12:32,599 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:32,716 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:32,830 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:32,830 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:12:32,830 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:12:32,833 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:12:32,833 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:12:32,835 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:32,835 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:12:32,835 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:12:32,865 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:12:32,898 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:32,898 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:12:32,898 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:32,902 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:32,902 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:12:32,902 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:12:32,927 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,044 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,152 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,152 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:12:33,152 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:12:33,152 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:12:33,152 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,163 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,163 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:12:33,163 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:12:33,171 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:12:33,171 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:33,186 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:12:33,186 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:33,186 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:33,186 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7194341839453234
2025-05-29 12:12:33,186 - models.predict - INFO - Prediction for 60 minutes horizon: 81.12499328020373
2025-05-29 12:12:33,211 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,321 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,436 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,436 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:12:33,436 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:12:33,436 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:12:33,436 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:12:33,453 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:33,453 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:12:33,453 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:33,453 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:33,453 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.49872770271068445
2025-05-29 12:12:33,453 - models.predict - INFO - Prediction for 60 minutes horizon: 69.0302781085455
2025-05-29 12:12:33,469 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,600 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,714 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,714 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:12:33,714 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:12:33,714 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:12:33,714 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:12:33,719 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:33,719 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:33,719 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:33,719 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7427804284288911
2025-05-29 12:12:33,719 - models.predict - INFO - Prediction for 60 minutes horizon: 82.40436747790324
2025-05-29 12:12:33,725 - models.predict - INFO - Current price for COMI: 81.31
2025-05-29 12:12:33,725 - models.predict - INFO - Prophet prediction for 60 minutes: 81.39339142882824
2025-05-29 12:12:33,741 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,863 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,975 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,975 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:12:33,975 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:12:33,975 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:12:33,975 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,980 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,980 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:12:33,980 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:12:34,010 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:12:34,036 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:34,036 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:12:34,044 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:34,044 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:12:34,044 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:12:34,216 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:34,216 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,216 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:34,240 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,240 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,241 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:12:34,245 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:34,246 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:34,256 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,261 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:12:34,261 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:12:34,261 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:34,262 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:12:34,266 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,266 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,267 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:12:34,267 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:34,268 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:12:34,274 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,274 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,275 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:34,275 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,275 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:34,280 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,280 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,284 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,293 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:34,293 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:34,294 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:34,294 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,304 - app.utils.memory_management - INFO - Memory before cleanup: 481.02 MB
2025-05-29 12:12:34,434 - app.utils.memory_management - INFO - Garbage collection: collected 1067 objects
2025-05-29 12:12:34,434 - app.utils.memory_management - INFO - Memory after cleanup: 481.02 MB (freed 0.00 MB)
