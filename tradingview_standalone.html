<!DOCTYPE HTML>
<html>
<head>
    <title>EGX TradingView Professional Charts</title>
    
    <!-- Fix for iOS Safari zooming bug -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1e1e1e;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .controls select, .controls button {
            padding: 8px 12px;
            border: 1px solid #333;
            background: #2a2a2a;
            color: white;
            border-radius: 4px;
        }
        
        .controls button {
            background: #0066cc;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #0052a3;
        }
        
        #tv_chart_container {
            width: 100%;
            height: 600px;
            border: 1px solid #333;
            border-radius: 8px;
        }
        
        .status {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            opacity: 0.7;
        }
    </style>
    
    <!-- TradingView Charting Library -->
    <script type="text/javascript" src="trading-view-charting-library/tradingview-master_0x1af2aec8f957/charting_library/charting_library.min.js"></script>
    <script type="text/javascript" src="trading-view-charting-library/tradingview-master_0x1af2aec8f957/datafeeds/udf/dist/polyfills.js"></script>
    <script type="text/javascript" src="trading-view-charting-library/tradingview-master_0x1af2aec8f957/datafeeds/udf/dist/bundle.js"></script>
</head>

<body>
    <div class="header">
        <h1>🚀 EGX Professional TradingView Charts</h1>
        <p>Professional-grade charting for Egyptian Exchange stocks</p>
    </div>
    
    <div class="controls">
        <select id="symbolSelect">
            <option value="COMI">COMI - Commercial International Bank</option>
            <option value="ETEL">ETEL - Egyptian Company for Mobile Services</option>
            <option value="AAPL">AAPL - Apple Inc. (Demo)</option>
        </select>
        
        <select id="intervalSelect">
            <option value="1D">1 Day</option>
            <option value="1W">1 Week</option>
            <option value="1M">1 Month</option>
            <option value="4H">4 Hours</option>
            <option value="1H">1 Hour</option>
        </select>
        
        <select id="themeSelect">
            <option value="dark">Dark Theme</option>
            <option value="light">Light Theme</option>
        </select>
        
        <button onclick="loadChart()">📊 Load Chart</button>
        <button onclick="addStudy('RSI')">📈 Add RSI</button>
        <button onclick="addStudy('MACD')">📊 Add MACD</button>
    </div>
    
    <div id="tv_chart_container"></div>
    
    <div class="status" id="status">
        Ready to load professional TradingView chart...
    </div>

    <script type="text/javascript">
        let widget = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function createDatafeed() {
            return {
                onReady: function(callback) {
                    console.log('[onReady]: Method call');
                    updateStatus('Datafeed ready...');
                    setTimeout(() => callback({
                        supported_resolutions: ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'],
                        supports_group_request: false,
                        supports_marks: false,
                        supports_search: true,
                        supports_timescale_marks: false
                    }), 0);
                },
                
                searchSymbols: function(userInput, exchange, symbolType, onResultReadyCallback) {
                    console.log('[searchSymbols]: Method call');
                    const symbols = [
                        {
                            symbol: 'COMI',
                            full_name: 'EGX:COMI',
                            description: 'Commercial International Bank',
                            exchange: 'EGX',
                            ticker: 'COMI',
                            type: 'stock'
                        },
                        {
                            symbol: 'ETEL',
                            full_name: 'EGX:ETEL',
                            description: 'Egyptian Company for Mobile Services',
                            exchange: 'EGX',
                            ticker: 'ETEL',
                            type: 'stock'
                        }
                    ];
                    onResultReadyCallback(symbols);
                },
                
                resolveSymbol: function(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
                    console.log('[resolveSymbol]: Method call', symbolName);
                    updateStatus(`Resolving symbol: ${symbolName}...`);
                    
                    const symbolInfo = {
                        name: symbolName,
                        description: `${symbolName} Stock`,
                        type: 'stock',
                        session: '0930-1430',
                        timezone: 'Africa/Cairo',
                        ticker: symbolName,
                        exchange: 'EGX',
                        minmov: 1,
                        pricescale: 100,
                        has_intraday: true,
                        has_no_volume: false,
                        has_weekly_and_monthly: true,
                        supported_resolutions: ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'],
                        volume_precision: 0,
                        data_status: 'streaming'
                    };
                    
                    setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
                },
                
                getBars: function(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) {
                    console.log('[getBars]: Method call', symbolInfo, resolution, periodParams);
                    updateStatus(`Loading ${symbolInfo.name} data...`);
                    
                    // Generate sample OHLCV data
                    const bars = [];
                    const now = Math.floor(Date.now() / 1000);
                    const oneDay = 24 * 60 * 60;
                    
                    for (let i = 100; i >= 0; i--) {
                        const time = now - (i * oneDay);
                        const basePrice = 45 + Math.sin(i * 0.1) * 10;
                        const open = basePrice + (Math.random() - 0.5) * 2;
                        const close = open + (Math.random() - 0.5) * 4;
                        const high = Math.max(open, close) + Math.random() * 2;
                        const low = Math.min(open, close) - Math.random() * 2;
                        const volume = Math.floor(Math.random() * 1000000) + 100000;
                        
                        bars.push({
                            time: time * 1000,
                            open: parseFloat(open.toFixed(2)),
                            high: parseFloat(high.toFixed(2)),
                            low: parseFloat(low.toFixed(2)),
                            close: parseFloat(close.toFixed(2)),
                            volume: volume
                        });
                    }
                    
                    console.log('[getBars]: Returning', bars.length, 'bars');
                    setTimeout(() => onHistoryCallback(bars, { noData: false }), 100);
                },
                
                subscribeBars: function(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
                    console.log('[subscribeBars]: Method call with subscriberUID:', subscriberUID);
                },
                
                unsubscribeBars: function(subscriberUID) {
                    console.log('[unsubscribeBars]: Method call with subscriberUID:', subscriberUID);
                }
            };
        }
        
        function loadChart() {
            const symbol = document.getElementById('symbolSelect').value;
            const interval = document.getElementById('intervalSelect').value;
            const theme = document.getElementById('themeSelect').value;
            
            updateStatus('Initializing TradingView chart...');
            
            try {
                // Clear existing chart
                document.getElementById('tv_chart_container').innerHTML = '';
                
                TradingView.onready(function () {
                    widget = new TradingView.widget({
                        debug: true,
                        fullscreen: false,
                        symbol: symbol,
                        interval: interval,
                        container_id: "tv_chart_container",
                        datafeed: createDatafeed(),
                        library_path: "trading-view-charting-library/tradingview-master_0x1af2aec8f957/charting_library/",
                        locale: "en",
                        disabled_features: ["use_localstorage_for_settings"],
                        enabled_features: ["study_templates"],
                        charts_storage_url: 'https://saveload.tradingview.com',
                        charts_storage_api_version: "1.1",
                        client_id: 'tradingview.com',
                        user_id: 'public_user_id',
                        theme: theme,
                        autosize: true
                    });
                    
                    widget.onChartReady(() => {
                        console.log('✅ TradingView chart is ready!');
                        updateStatus(`✅ ${symbol} chart loaded successfully!`);
                    });
                });
                
            } catch (error) {
                console.error('Error loading chart:', error);
                updateStatus(`❌ Error: ${error.message}`);
            }
        }
        
        function addStudy(studyName) {
            if (widget) {
                try {
                    widget.chart().createStudy(studyName);
                    updateStatus(`✅ Added ${studyName} indicator`);
                } catch (error) {
                    updateStatus(`❌ Failed to add ${studyName}: ${error.message}`);
                }
            } else {
                updateStatus('❌ Please load a chart first');
            }
        }
        
        // Auto-load chart on page load
        window.addEventListener('load', function() {
            setTimeout(loadChart, 1000);
        });
    </script>
</body>
</html>
