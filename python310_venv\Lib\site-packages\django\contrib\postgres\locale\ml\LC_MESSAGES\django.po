# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>h <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-13 21:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>h <<EMAIL>>\n"
"Language-Team: Malayalam (http://www.transifex.com/django/django/language/"
"ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL എക്സ്റ്റൻഷനുക"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr ""

msgid "Nested arrays must have the same length."
msgstr "നെസ്റ്റഡ് അറേകൾക്ക് ഒരേ ലെങ്ത്തായിരിക്കണം."

msgid "Map of strings to strings/nulls"
msgstr "സ്ട്രിങ്ങുകളിൽ നിന്ന് സ്ട്രിങ്ങുകളിലേക്ക്/nulls ലേയ്ക്ക് ഉള്ള Map."

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr "JSON ഡാറ്റ ലോഡുചെയ്യാനായില്ല."

msgid "Input must be a JSON dictionary."
msgstr "ഇൻപുട്ട് നിർബന്ധമായു ഒരു JSON ഡിക്ഷ്ണറി ആയിരിക്കണം."

msgid "Enter two valid values."
msgstr "ശരിയായ രണ്ട് വാല്യൂകൾ നൽകു"

msgid "The start of the range must not exceed the end of the range."
msgstr ""

msgid "Enter two whole numbers."
msgstr "രണ്ട് പൂർണ്ണസംഖ്യകൾ നൽകുക."

msgid "Enter two numbers."
msgstr ""

msgid "Enter two valid date/times."
msgstr ""

msgid "Enter two valid dates."
msgstr "ശരിയായ രണ്ട് തീയതികൾ നകുക."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr ""

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr ""

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr ""

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr ""
