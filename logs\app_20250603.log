2025-06-03 11:58:56,880 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-03 11:59:05,305 - app - INFO - Memory management utilities loaded
2025-06-03 11:59:05,325 - app - INFO - Error handling utilities loaded
2025-06-03 11:59:05,343 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 11:59:05,347 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 11:59:05,349 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 11:59:05,350 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 11:59:05,367 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 11:59:05,368 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 11:59:05,372 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 11:59:05,373 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 11:59:05,375 - app - INFO - Applied NumPy fix
2025-06-03 11:59:05,399 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 11:59:05,400 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 11:59:05,402 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 11:59:05,412 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 11:59:05,415 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 11:59:05,416 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 11:59:05,425 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 11:59:05,428 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 11:59:31,059 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 11:59:31,059 - app - INFO - Applied TensorFlow fix
2025-06-03 11:59:31,086 - app.config - INFO - Configuration initialized
2025-06-03 11:59:31,110 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 11:59:31,130 - models.train - INFO - TensorFlow test successful
2025-06-03 11:59:36,643 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 11:59:36,643 - models.train - INFO - Transformer model is available
2025-06-03 11:59:36,645 - models.train - INFO - Using TensorFlow-based models
2025-06-03 11:59:36,657 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 11:59:36,658 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 11:59:36,682 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 11:59:38,739 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 11:59:38,740 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 11:59:38,740 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 11:59:38,742 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 11:59:38,743 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 11:59:38,744 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 11:59:38,744 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 11:59:38,746 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 11:59:38,747 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 11:59:38,750 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 11:59:39,220 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 11:59:39,237 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 11:59:40,248 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 11:59:44,041 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 11:59:44,116 - app.utils.session_state - INFO - Initializing session state
2025-06-03 11:59:44,118 - app.utils.session_state - INFO - Session state initialized
2025-06-03 11:59:45,646 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 11:59:45,703 - app.utils.memory_management - INFO - Memory before cleanup: 428.73 MB
2025-06-03 11:59:46,006 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 11:59:46,010 - app.utils.memory_management - INFO - Memory after cleanup: 429.20 MB (freed -0.46 MB)
2025-06-03 12:01:44,153 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:44,228 - app.utils.memory_management - INFO - Memory before cleanup: 432.46 MB
2025-06-03 12:01:44,456 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:01:44,461 - app.utils.memory_management - INFO - Memory after cleanup: 432.46 MB (freed 0.00 MB)
2025-06-03 12:01:48,977 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:49,154 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-06-03 12:01:49,158 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:01:49,160 - app - INFO - Data shape: (581, 36)
2025-06-03 12:01:49,171 - app - INFO - File COMI contains 2025 data
2025-06-03 12:01:49,226 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-03 12:01:49,228 - app - INFO - Features shape: (581, 36)
2025-06-03 12:01:49,261 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 12:01:49,267 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:01:49,268 - app - INFO - Data shape: (581, 36)
2025-06-03 12:01:49,272 - app - INFO - File COMI contains 2025 data
2025-06-03 12:01:49,288 - app.utils.memory_management - INFO - Memory before cleanup: 436.91 MB
2025-06-03 12:01:49,531 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 12:01:49,533 - app.utils.memory_management - INFO - Memory after cleanup: 436.91 MB (freed 0.00 MB)
2025-06-03 12:01:49,758 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:49,858 - app.utils.memory_management - INFO - Memory before cleanup: 438.00 MB
2025-06-03 12:01:50,125 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:01:50,130 - app.utils.memory_management - INFO - Memory after cleanup: 438.00 MB (freed 0.00 MB)
2025-06-03 12:02:08,058 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:10,102 - app.utils.memory_management - INFO - Memory before cleanup: 438.50 MB
2025-06-03 12:02:10,284 - app.utils.memory_management - INFO - Garbage collection: collected 313 objects
2025-06-03 12:02:10,286 - app.utils.memory_management - INFO - Memory after cleanup: 438.50 MB (freed 0.00 MB)
2025-06-03 12:02:51,072 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:51,079 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:02:51,081 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:02:51,111 - app.utils.memory_management - INFO - Memory before cleanup: 438.37 MB
2025-06-03 12:02:51,334 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-06-03 12:02:51,336 - app.utils.memory_management - INFO - Memory after cleanup: 438.37 MB (freed 0.00 MB)
2025-06-03 12:02:56,379 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:56,416 - app.utils.memory_management - INFO - Memory before cleanup: 438.57 MB
2025-06-03 12:02:56,647 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 12:02:56,647 - app.utils.memory_management - INFO - Memory after cleanup: 438.57 MB (freed 0.00 MB)
2025-06-03 12:02:57,524 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:57,556 - app.utils.memory_management - INFO - Memory before cleanup: 438.59 MB
2025-06-03 12:02:57,811 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-03 12:02:57,813 - app.utils.memory_management - INFO - Memory after cleanup: 438.59 MB (freed 0.00 MB)
2025-06-03 12:02:58,029 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:58,096 - app.utils.memory_management - INFO - Memory before cleanup: 438.59 MB
2025-06-03 12:02:58,306 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:02:58,307 - app.utils.memory_management - INFO - Memory after cleanup: 438.59 MB (freed 0.00 MB)
2025-06-03 12:03:00,343 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:00,439 - app.utils.memory_management - INFO - Memory before cleanup: 438.60 MB
2025-06-03 12:03:00,673 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 12:03:00,673 - app.utils.memory_management - INFO - Memory after cleanup: 438.60 MB (freed 0.00 MB)
2025-06-03 12:03:05,176 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:05,231 - app.utils.memory_management - INFO - Memory before cleanup: 438.61 MB
2025-06-03 12:03:05,458 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 12:03:05,458 - app.utils.memory_management - INFO - Memory after cleanup: 438.61 MB (freed 0.00 MB)
2025-06-03 12:03:06,801 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:24,118 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 12:03:24,120 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 12:03:24,121 - app.utils.memory_management - INFO - Memory before cleanup: 438.81 MB
2025-06-03 12:03:24,319 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 12:03:24,322 - app.utils.memory_management - INFO - Memory after cleanup: 438.81 MB (freed 0.00 MB)
2025-06-03 12:03:24,327 - app.utils.memory_management - INFO - Memory before cleanup: 438.82 MB
2025-06-03 12:03:24,626 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 12:03:24,628 - app.utils.memory_management - INFO - Memory after cleanup: 438.82 MB (freed 0.00 MB)
2025-06-03 12:26:17,672 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:26:17,677 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:26:17,680 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:26:17,695 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:26:17,710 - app.utils.memory_management - INFO - Memory before cleanup: 433.84 MB
2025-06-03 12:26:17,909 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-03 12:26:17,910 - app.utils.memory_management - INFO - Memory after cleanup: 433.84 MB (freed 0.00 MB)
2025-06-03 12:27:29,337 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:27:29,347 - app - INFO - Memory management utilities loaded
2025-06-03 12:27:29,351 - app - INFO - Error handling utilities loaded
2025-06-03 12:27:29,355 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:27:29,358 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:27:29,359 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:27:29,360 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:28:20,797 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:28:27,693 - app - INFO - Memory management utilities loaded
2025-06-03 12:28:27,696 - app - INFO - Error handling utilities loaded
2025-06-03 12:28:27,701 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:28:27,705 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:28:27,706 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:28:27,708 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:28:27,714 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:28:27,724 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:28:27,731 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:28:27,739 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:28:27,742 - app - INFO - Applied NumPy fix
2025-06-03 12:28:27,749 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:28:27,753 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:28:27,754 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:28:27,757 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:28:27,759 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:28:27,767 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:28:27,776 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:28:27,779 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:28:43,957 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:28:43,958 - app - INFO - Applied TensorFlow fix
2025-06-03 12:28:43,959 - app.config - INFO - Configuration initialized
2025-06-03 12:28:43,965 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:28:43,975 - models.train - INFO - TensorFlow test successful
2025-06-03 12:28:48,635 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:28:48,635 - models.train - INFO - Transformer model is available
2025-06-03 12:28:48,636 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:28:48,637 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:28:48,637 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:28:48,640 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:28:50,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:28:50,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:28:50,349 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:28:50,351 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:28:50,352 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:28:50,714 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:28:52,909 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:28:52,949 - app.utils.memory_management - INFO - Memory before cleanup: 426.55 MB
2025-06-03 12:28:53,115 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:28:53,116 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:28:53,117 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:28:53,118 - app.utils.memory_management - INFO - Memory after cleanup: 426.91 MB (freed -0.36 MB)
2025-06-03 12:28:54,664 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:28:54,691 - app.utils.memory_management - INFO - Memory before cleanup: 431.00 MB
2025-06-03 12:28:54,919 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-03 12:28:54,922 - app.utils.memory_management - INFO - Memory after cleanup: 431.00 MB (freed 0.00 MB)
2025-06-03 12:29:00,198 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:29:00,253 - app.utils.memory_management - INFO - Memory before cleanup: 433.99 MB
2025-06-03 12:29:00,450 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:29:00,451 - app.utils.memory_management - INFO - Memory after cleanup: 434.03 MB (freed -0.04 MB)
2025-06-03 12:29:08,961 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:29:24,569 - app.utils.memory_management - INFO - Memory before cleanup: 449.51 MB
2025-06-03 12:29:24,774 - app.utils.memory_management - INFO - Garbage collection: collected 980 objects
2025-06-03 12:29:24,774 - app.utils.memory_management - INFO - Memory after cleanup: 449.51 MB (freed 0.00 MB)
2025-06-03 12:36:43,358 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:36:44,065 - app - INFO - Memory management utilities loaded
2025-06-03 12:36:44,065 - app - INFO - Error handling utilities loaded
2025-06-03 12:36:44,065 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:36:44,072 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:36:44,072 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:36:44,072 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:36:44,072 - app - INFO - Applied NumPy fix
2025-06-03 12:36:44,074 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:36:44,078 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:36:44,081 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:36:44,081 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:36:44,081 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:36:48,996 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:36:48,996 - app - INFO - Applied TensorFlow fix
2025-06-03 12:36:49,761 - app - INFO - Cleaning up resources...
2025-06-03 12:36:49,764 - app.utils.memory_management - INFO - Memory before cleanup: 320.98 MB
2025-06-03 12:36:49,881 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-03 12:36:49,881 - app.utils.memory_management - INFO - Memory after cleanup: 321.32 MB (freed -0.34 MB)
2025-06-03 12:36:49,881 - app - INFO - Application shutdown complete
2025-06-03 12:40:55,936 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:40:55,971 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:40:55,996 - app.utils.memory_management - INFO - Memory before cleanup: 447.53 MB
2025-06-03 12:40:56,224 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-06-03 12:40:56,225 - app.utils.memory_management - INFO - Memory after cleanup: 447.56 MB (freed -0.03 MB)
2025-06-03 12:40:59,884 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:41:09,709 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 12:41:09,712 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 12:41:09,714 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-03 12:41:09,924 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-03 12:41:09,926 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed 0.00 MB)
2025-06-03 12:41:09,926 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-03 12:41:10,148 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 12:41:10,149 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed 0.00 MB)
2025-06-03 12:43:40,200 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:43:40,878 - app - INFO - Memory management utilities loaded
2025-06-03 12:43:40,878 - app - INFO - Error handling utilities loaded
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:43:40,888 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:43:40,888 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:43:40,888 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:43:40,889 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:43:40,889 - app - INFO - Applied NumPy fix
2025-06-03 12:43:40,890 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:43:40,890 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:43:40,892 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:43:45,367 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:43:45,367 - app - INFO - Applied TensorFlow fix
2025-06-03 12:43:46,155 - app - INFO - Cleaning up resources...
2025-06-03 12:43:46,161 - app.utils.memory_management - INFO - Memory before cleanup: 321.38 MB
2025-06-03 12:43:46,277 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-03 12:43:46,279 - app.utils.memory_management - INFO - Memory after cleanup: 321.73 MB (freed -0.35 MB)
2025-06-03 12:43:46,279 - app - INFO - Application shutdown complete
2025-06-03 12:44:02,992 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:44:03,002 - app - INFO - Memory management utilities loaded
2025-06-03 12:44:03,007 - app - INFO - Error handling utilities loaded
2025-06-03 12:44:03,009 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:44:03,011 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:44:03,013 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:44:03,015 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:44:32,676 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:44:34,480 - app - INFO - Memory management utilities loaded
2025-06-03 12:44:34,483 - app - INFO - Error handling utilities loaded
2025-06-03 12:44:34,484 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:44:34,486 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:44:34,486 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:44:34,489 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:44:34,491 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:44:34,493 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:44:34,494 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:44:34,497 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:44:34,497 - app - INFO - Applied NumPy fix
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:44:34,501 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:44:34,503 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:44:39,833 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:44:39,833 - app - INFO - Applied TensorFlow fix
2025-06-03 12:44:39,835 - app.config - INFO - Configuration initialized
2025-06-03 12:44:39,839 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:44:39,852 - models.train - INFO - TensorFlow test successful
2025-06-03 12:44:40,475 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:44:40,475 - models.train - INFO - Transformer model is available
2025-06-03 12:44:40,477 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:44:40,478 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:44:40,478 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:44:40,478 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:44:40,835 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:44:40,835 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:44:40,942 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:44:40,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:40,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:41,290 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:44:41,962 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:44:41,991 - app.utils.memory_management - INFO - Memory before cleanup: 426.16 MB
2025-06-03 12:44:41,992 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:44:42,139 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:44:42,140 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:44:42,141 - app.utils.memory_management - INFO - Memory after cleanup: 426.54 MB (freed -0.38 MB)
2025-06-03 12:44:43,422 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:44:43,443 - app.utils.memory_management - INFO - Memory before cleanup: 430.34 MB
2025-06-03 12:44:43,651 - app.utils.memory_management - INFO - Garbage collection: collected 70 objects
2025-06-03 12:44:43,651 - app.utils.memory_management - INFO - Memory after cleanup: 430.34 MB (freed 0.00 MB)
2025-06-03 12:44:48,865 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:48,903 - app.utils.memory_management - INFO - Memory before cleanup: 433.75 MB
2025-06-03 12:44:49,104 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:44:49,125 - app.utils.memory_management - INFO - Memory after cleanup: 433.79 MB (freed -0.04 MB)
2025-06-03 12:44:53,578 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:45:00,951 - app.utils.memory_management - INFO - Memory before cleanup: 450.56 MB
2025-06-03 12:45:01,163 - app.utils.memory_management - INFO - Garbage collection: collected 998 objects
2025-06-03 12:45:01,164 - app.utils.memory_management - INFO - Memory after cleanup: 450.56 MB (freed 0.00 MB)
2025-06-03 12:49:04,520 - app - INFO - Cleaning up resources...
2025-06-03 12:49:04,521 - app.utils.memory_management - INFO - Memory before cleanup: 452.26 MB
2025-06-03 12:49:04,856 - app.utils.memory_management - INFO - Garbage collection: collected 294 objects
2025-06-03 12:49:04,860 - app.utils.memory_management - INFO - Memory after cleanup: 452.26 MB (freed 0.00 MB)
2025-06-03 12:49:04,865 - app - INFO - Application shutdown complete
