2025-06-03 11:58:56,880 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-03 11:59:05,305 - app - INFO - Memory management utilities loaded
2025-06-03 11:59:05,325 - app - INFO - Error handling utilities loaded
2025-06-03 11:59:05,343 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 11:59:05,347 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 11:59:05,349 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 11:59:05,350 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 11:59:05,367 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 11:59:05,368 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 11:59:05,372 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 11:59:05,373 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 11:59:05,375 - app - INFO - Applied NumPy fix
2025-06-03 11:59:05,399 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 11:59:05,400 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 11:59:05,402 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 11:59:05,412 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 11:59:05,415 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 11:59:05,416 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 11:59:05,425 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 11:59:05,428 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 11:59:31,059 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 11:59:31,059 - app - INFO - Applied TensorFlow fix
2025-06-03 11:59:31,086 - app.config - INFO - Configuration initialized
2025-06-03 11:59:31,110 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 11:59:31,130 - models.train - INFO - TensorFlow test successful
2025-06-03 11:59:36,643 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 11:59:36,643 - models.train - INFO - Transformer model is available
2025-06-03 11:59:36,645 - models.train - INFO - Using TensorFlow-based models
2025-06-03 11:59:36,657 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 11:59:36,658 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 11:59:36,682 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 11:59:38,739 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 11:59:38,740 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 11:59:38,740 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 11:59:38,742 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 11:59:38,743 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 11:59:38,744 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 11:59:38,744 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 11:59:38,746 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 11:59:38,747 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 11:59:38,750 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 11:59:39,220 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 11:59:39,237 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 11:59:40,248 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 11:59:44,041 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 11:59:44,116 - app.utils.session_state - INFO - Initializing session state
2025-06-03 11:59:44,118 - app.utils.session_state - INFO - Session state initialized
2025-06-03 11:59:45,646 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 11:59:45,703 - app.utils.memory_management - INFO - Memory before cleanup: 428.73 MB
2025-06-03 11:59:46,006 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 11:59:46,010 - app.utils.memory_management - INFO - Memory after cleanup: 429.20 MB (freed -0.46 MB)
2025-06-03 12:01:44,153 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:44,228 - app.utils.memory_management - INFO - Memory before cleanup: 432.46 MB
2025-06-03 12:01:44,456 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:01:44,461 - app.utils.memory_management - INFO - Memory after cleanup: 432.46 MB (freed 0.00 MB)
2025-06-03 12:01:48,977 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:49,154 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-06-03 12:01:49,158 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:01:49,160 - app - INFO - Data shape: (581, 36)
2025-06-03 12:01:49,171 - app - INFO - File COMI contains 2025 data
2025-06-03 12:01:49,226 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-03 12:01:49,228 - app - INFO - Features shape: (581, 36)
2025-06-03 12:01:49,261 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 12:01:49,267 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:01:49,268 - app - INFO - Data shape: (581, 36)
2025-06-03 12:01:49,272 - app - INFO - File COMI contains 2025 data
2025-06-03 12:01:49,288 - app.utils.memory_management - INFO - Memory before cleanup: 436.91 MB
2025-06-03 12:01:49,531 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 12:01:49,533 - app.utils.memory_management - INFO - Memory after cleanup: 436.91 MB (freed 0.00 MB)
2025-06-03 12:01:49,758 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:49,858 - app.utils.memory_management - INFO - Memory before cleanup: 438.00 MB
2025-06-03 12:01:50,125 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:01:50,130 - app.utils.memory_management - INFO - Memory after cleanup: 438.00 MB (freed 0.00 MB)
2025-06-03 12:02:08,058 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:10,102 - app.utils.memory_management - INFO - Memory before cleanup: 438.50 MB
2025-06-03 12:02:10,284 - app.utils.memory_management - INFO - Garbage collection: collected 313 objects
2025-06-03 12:02:10,286 - app.utils.memory_management - INFO - Memory after cleanup: 438.50 MB (freed 0.00 MB)
2025-06-03 12:02:51,072 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:51,079 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:02:51,081 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:02:51,111 - app.utils.memory_management - INFO - Memory before cleanup: 438.37 MB
2025-06-03 12:02:51,334 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-06-03 12:02:51,336 - app.utils.memory_management - INFO - Memory after cleanup: 438.37 MB (freed 0.00 MB)
2025-06-03 12:02:56,379 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:56,416 - app.utils.memory_management - INFO - Memory before cleanup: 438.57 MB
2025-06-03 12:02:56,647 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 12:02:56,647 - app.utils.memory_management - INFO - Memory after cleanup: 438.57 MB (freed 0.00 MB)
2025-06-03 12:02:57,524 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:57,556 - app.utils.memory_management - INFO - Memory before cleanup: 438.59 MB
2025-06-03 12:02:57,811 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-03 12:02:57,813 - app.utils.memory_management - INFO - Memory after cleanup: 438.59 MB (freed 0.00 MB)
2025-06-03 12:02:58,029 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:58,096 - app.utils.memory_management - INFO - Memory before cleanup: 438.59 MB
2025-06-03 12:02:58,306 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:02:58,307 - app.utils.memory_management - INFO - Memory after cleanup: 438.59 MB (freed 0.00 MB)
2025-06-03 12:03:00,343 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:00,439 - app.utils.memory_management - INFO - Memory before cleanup: 438.60 MB
2025-06-03 12:03:00,673 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 12:03:00,673 - app.utils.memory_management - INFO - Memory after cleanup: 438.60 MB (freed 0.00 MB)
2025-06-03 12:03:05,176 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:05,231 - app.utils.memory_management - INFO - Memory before cleanup: 438.61 MB
2025-06-03 12:03:05,458 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 12:03:05,458 - app.utils.memory_management - INFO - Memory after cleanup: 438.61 MB (freed 0.00 MB)
2025-06-03 12:03:06,801 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:24,118 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 12:03:24,120 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 12:03:24,121 - app.utils.memory_management - INFO - Memory before cleanup: 438.81 MB
2025-06-03 12:03:24,319 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 12:03:24,322 - app.utils.memory_management - INFO - Memory after cleanup: 438.81 MB (freed 0.00 MB)
2025-06-03 12:03:24,327 - app.utils.memory_management - INFO - Memory before cleanup: 438.82 MB
2025-06-03 12:03:24,626 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 12:03:24,628 - app.utils.memory_management - INFO - Memory after cleanup: 438.82 MB (freed 0.00 MB)
2025-06-03 12:26:17,672 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:26:17,677 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:26:17,680 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:26:17,695 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:26:17,710 - app.utils.memory_management - INFO - Memory before cleanup: 433.84 MB
2025-06-03 12:26:17,909 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-03 12:26:17,910 - app.utils.memory_management - INFO - Memory after cleanup: 433.84 MB (freed 0.00 MB)
2025-06-03 12:27:29,337 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:27:29,347 - app - INFO - Memory management utilities loaded
2025-06-03 12:27:29,351 - app - INFO - Error handling utilities loaded
2025-06-03 12:27:29,355 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:27:29,358 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:27:29,359 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:27:29,360 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:28:20,797 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:28:27,693 - app - INFO - Memory management utilities loaded
2025-06-03 12:28:27,696 - app - INFO - Error handling utilities loaded
2025-06-03 12:28:27,701 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:28:27,705 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:28:27,706 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:28:27,708 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:28:27,714 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:28:27,724 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:28:27,731 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:28:27,739 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:28:27,742 - app - INFO - Applied NumPy fix
2025-06-03 12:28:27,749 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:28:27,753 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:28:27,754 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:28:27,757 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:28:27,759 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:28:27,767 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:28:27,776 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:28:27,779 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:28:43,957 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:28:43,958 - app - INFO - Applied TensorFlow fix
2025-06-03 12:28:43,959 - app.config - INFO - Configuration initialized
2025-06-03 12:28:43,965 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:28:43,975 - models.train - INFO - TensorFlow test successful
2025-06-03 12:28:48,635 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:28:48,635 - models.train - INFO - Transformer model is available
2025-06-03 12:28:48,636 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:28:48,637 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:28:48,637 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:28:48,640 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:28:50,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:28:50,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:28:50,349 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:28:50,351 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:28:50,352 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:28:50,714 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:28:52,909 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:28:52,949 - app.utils.memory_management - INFO - Memory before cleanup: 426.55 MB
2025-06-03 12:28:53,115 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:28:53,116 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:28:53,117 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:28:53,118 - app.utils.memory_management - INFO - Memory after cleanup: 426.91 MB (freed -0.36 MB)
2025-06-03 12:28:54,664 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:28:54,691 - app.utils.memory_management - INFO - Memory before cleanup: 431.00 MB
2025-06-03 12:28:54,919 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-03 12:28:54,922 - app.utils.memory_management - INFO - Memory after cleanup: 431.00 MB (freed 0.00 MB)
2025-06-03 12:29:00,198 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:29:00,253 - app.utils.memory_management - INFO - Memory before cleanup: 433.99 MB
2025-06-03 12:29:00,450 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:29:00,451 - app.utils.memory_management - INFO - Memory after cleanup: 434.03 MB (freed -0.04 MB)
2025-06-03 12:29:08,961 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:29:24,569 - app.utils.memory_management - INFO - Memory before cleanup: 449.51 MB
2025-06-03 12:29:24,774 - app.utils.memory_management - INFO - Garbage collection: collected 980 objects
2025-06-03 12:29:24,774 - app.utils.memory_management - INFO - Memory after cleanup: 449.51 MB (freed 0.00 MB)
2025-06-03 12:36:43,358 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:36:44,065 - app - INFO - Memory management utilities loaded
2025-06-03 12:36:44,065 - app - INFO - Error handling utilities loaded
2025-06-03 12:36:44,065 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:36:44,072 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:36:44,072 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:36:44,072 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:36:44,072 - app - INFO - Applied NumPy fix
2025-06-03 12:36:44,074 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:36:44,078 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:36:44,081 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:36:44,081 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:36:44,081 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:36:48,996 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:36:48,996 - app - INFO - Applied TensorFlow fix
2025-06-03 12:36:49,761 - app - INFO - Cleaning up resources...
2025-06-03 12:36:49,764 - app.utils.memory_management - INFO - Memory before cleanup: 320.98 MB
2025-06-03 12:36:49,881 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-03 12:36:49,881 - app.utils.memory_management - INFO - Memory after cleanup: 321.32 MB (freed -0.34 MB)
2025-06-03 12:36:49,881 - app - INFO - Application shutdown complete
2025-06-03 12:40:55,936 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:40:55,971 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:40:55,996 - app.utils.memory_management - INFO - Memory before cleanup: 447.53 MB
2025-06-03 12:40:56,224 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-06-03 12:40:56,225 - app.utils.memory_management - INFO - Memory after cleanup: 447.56 MB (freed -0.03 MB)
2025-06-03 12:40:59,884 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:41:09,709 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 12:41:09,712 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 12:41:09,714 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-03 12:41:09,924 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-03 12:41:09,926 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed 0.00 MB)
2025-06-03 12:41:09,926 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-03 12:41:10,148 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 12:41:10,149 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed 0.00 MB)
2025-06-03 12:43:40,200 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:43:40,878 - app - INFO - Memory management utilities loaded
2025-06-03 12:43:40,878 - app - INFO - Error handling utilities loaded
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:43:40,888 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:43:40,888 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:43:40,888 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:43:40,889 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:43:40,889 - app - INFO - Applied NumPy fix
2025-06-03 12:43:40,890 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:43:40,890 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:43:40,892 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:43:45,367 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:43:45,367 - app - INFO - Applied TensorFlow fix
2025-06-03 12:43:46,155 - app - INFO - Cleaning up resources...
2025-06-03 12:43:46,161 - app.utils.memory_management - INFO - Memory before cleanup: 321.38 MB
2025-06-03 12:43:46,277 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-03 12:43:46,279 - app.utils.memory_management - INFO - Memory after cleanup: 321.73 MB (freed -0.35 MB)
2025-06-03 12:43:46,279 - app - INFO - Application shutdown complete
2025-06-03 12:44:02,992 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:44:03,002 - app - INFO - Memory management utilities loaded
2025-06-03 12:44:03,007 - app - INFO - Error handling utilities loaded
2025-06-03 12:44:03,009 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:44:03,011 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:44:03,013 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:44:03,015 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:44:32,676 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:44:34,480 - app - INFO - Memory management utilities loaded
2025-06-03 12:44:34,483 - app - INFO - Error handling utilities loaded
2025-06-03 12:44:34,484 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:44:34,486 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:44:34,486 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:44:34,489 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:44:34,491 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:44:34,493 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:44:34,494 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:44:34,497 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:44:34,497 - app - INFO - Applied NumPy fix
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:44:34,501 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:44:34,503 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:44:39,833 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:44:39,833 - app - INFO - Applied TensorFlow fix
2025-06-03 12:44:39,835 - app.config - INFO - Configuration initialized
2025-06-03 12:44:39,839 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:44:39,852 - models.train - INFO - TensorFlow test successful
2025-06-03 12:44:40,475 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:44:40,475 - models.train - INFO - Transformer model is available
2025-06-03 12:44:40,477 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:44:40,478 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:44:40,478 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:44:40,478 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:44:40,835 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:44:40,835 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:44:40,942 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:44:40,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:40,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:41,290 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:44:41,962 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:44:41,991 - app.utils.memory_management - INFO - Memory before cleanup: 426.16 MB
2025-06-03 12:44:41,992 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:44:42,139 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:44:42,140 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:44:42,141 - app.utils.memory_management - INFO - Memory after cleanup: 426.54 MB (freed -0.38 MB)
2025-06-03 12:44:43,422 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:44:43,443 - app.utils.memory_management - INFO - Memory before cleanup: 430.34 MB
2025-06-03 12:44:43,651 - app.utils.memory_management - INFO - Garbage collection: collected 70 objects
2025-06-03 12:44:43,651 - app.utils.memory_management - INFO - Memory after cleanup: 430.34 MB (freed 0.00 MB)
2025-06-03 12:44:48,865 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:48,903 - app.utils.memory_management - INFO - Memory before cleanup: 433.75 MB
2025-06-03 12:44:49,104 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:44:49,125 - app.utils.memory_management - INFO - Memory after cleanup: 433.79 MB (freed -0.04 MB)
2025-06-03 12:44:53,578 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:45:00,951 - app.utils.memory_management - INFO - Memory before cleanup: 450.56 MB
2025-06-03 12:45:01,163 - app.utils.memory_management - INFO - Garbage collection: collected 998 objects
2025-06-03 12:45:01,164 - app.utils.memory_management - INFO - Memory after cleanup: 450.56 MB (freed 0.00 MB)
2025-06-03 12:49:04,520 - app - INFO - Cleaning up resources...
2025-06-03 12:49:04,521 - app.utils.memory_management - INFO - Memory before cleanup: 452.26 MB
2025-06-03 12:49:04,856 - app.utils.memory_management - INFO - Garbage collection: collected 294 objects
2025-06-03 12:49:04,860 - app.utils.memory_management - INFO - Memory after cleanup: 452.26 MB (freed 0.00 MB)
2025-06-03 12:49:04,865 - app - INFO - Application shutdown complete
2025-06-03 12:50:16,779 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:50:18,404 - app - INFO - Memory management utilities loaded
2025-06-03 12:50:18,406 - app - INFO - Error handling utilities loaded
2025-06-03 12:50:18,406 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:50:18,408 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:50:18,408 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:50:18,410 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:50:18,411 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:50:18,412 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:50:18,412 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:50:18,413 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:50:18,413 - app - INFO - Applied NumPy fix
2025-06-03 12:50:18,418 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:50:18,418 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:50:18,420 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:50:18,420 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:50:18,420 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:50:18,421 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:50:18,421 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:50:18,423 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:50:23,636 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:50:23,636 - app - INFO - Applied TensorFlow fix
2025-06-03 12:50:23,638 - app.config - INFO - Configuration initialized
2025-06-03 12:50:23,642 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:50:23,652 - models.train - INFO - TensorFlow test successful
2025-06-03 12:50:24,246 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:50:24,246 - models.train - INFO - Transformer model is available
2025-06-03 12:50:24,246 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:50:24,246 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:50:24,246 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:50:24,246 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:50:24,600 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:50:24,601 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:50:24,601 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:50:24,601 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:50:24,601 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:50:24,716 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:50:24,718 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:25,099 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:50:25,768 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:50:25,796 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:50:25,797 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:50:27,089 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:50:27,104 - app.utils.memory_management - INFO - Memory before cleanup: 431.12 MB
2025-06-03 12:50:27,313 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:50:27,314 - app.utils.memory_management - INFO - Memory after cleanup: 431.12 MB (freed -0.00 MB)
2025-06-03 12:50:33,075 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:33,111 - app.utils.memory_management - INFO - Memory before cleanup: 434.16 MB
2025-06-03 12:50:33,314 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:50:33,347 - app.utils.memory_management - INFO - Memory after cleanup: 434.16 MB (freed 0.00 MB)
2025-06-03 12:50:34,242 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:34,310 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.05 seconds
2025-06-03 12:50:34,312 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:50:34,314 - app - INFO - Data shape: (581, 36)
2025-06-03 12:50:34,318 - app - INFO - File COMI contains 2025 data
2025-06-03 12:50:34,372 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-03 12:50:34,373 - app - INFO - Features shape: (581, 36)
2025-06-03 12:50:34,406 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-03 12:50:34,408 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:50:34,408 - app - INFO - Data shape: (581, 36)
2025-06-03 12:50:34,410 - app - INFO - File COMI contains 2025 data
2025-06-03 12:50:34,412 - app.utils.memory_management - INFO - Memory before cleanup: 438.49 MB
2025-06-03 12:50:34,618 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 12:50:34,619 - app.utils.memory_management - INFO - Memory after cleanup: 438.53 MB (freed -0.04 MB)
2025-06-03 12:50:34,798 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:34,861 - app.utils.memory_management - INFO - Memory before cleanup: 439.58 MB
2025-06-03 12:50:35,054 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:50:35,055 - app.utils.memory_management - INFO - Memory after cleanup: 439.56 MB (freed 0.02 MB)
2025-06-03 12:50:36,677 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:36,713 - app.utils.memory_management - INFO - Memory before cleanup: 440.11 MB
2025-06-03 12:50:36,911 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 12:50:36,912 - app.utils.memory_management - INFO - Memory after cleanup: 440.11 MB (freed 0.00 MB)
2025-06-03 12:50:39,532 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:46,452 - app.utils.memory_management - INFO - Memory before cleanup: 452.37 MB
2025-06-03 12:50:46,666 - app.utils.memory_management - INFO - Garbage collection: collected 1082 objects
2025-06-03 12:50:46,668 - app.utils.memory_management - INFO - Memory after cleanup: 452.37 MB (freed 0.00 MB)
2025-06-03 13:02:44,064 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:02:44,072 - app - INFO - Memory management utilities loaded
2025-06-03 13:02:44,078 - app - INFO - Error handling utilities loaded
2025-06-03 13:02:44,080 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:02:44,081 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:02:44,081 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:02:44,082 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:03:10,942 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:03:12,702 - app - INFO - Memory management utilities loaded
2025-06-03 13:03:12,706 - app - INFO - Error handling utilities loaded
2025-06-03 13:03:12,708 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:03:12,708 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:03:12,710 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:03:12,710 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:03:12,710 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 13:03:12,711 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 13:03:12,711 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 13:03:12,711 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 13:03:12,712 - app - INFO - Applied NumPy fix
2025-06-03 13:03:12,713 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:03:12,714 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:03:12,714 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:03:12,716 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 13:03:17,970 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 13:03:17,970 - app - INFO - Applied TensorFlow fix
2025-06-03 13:03:17,973 - app.config - INFO - Configuration initialized
2025-06-03 13:03:17,978 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 13:03:17,987 - models.train - INFO - TensorFlow test successful
2025-06-03 13:03:18,740 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 13:03:18,740 - models.train - INFO - Transformer model is available
2025-06-03 13:03:18,743 - models.train - INFO - Using TensorFlow-based models
2025-06-03 13:03:18,747 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 13:03:18,747 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 13:03:18,750 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 13:03:19,154 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:03:19,155 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:03:19,280 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 13:03:19,282 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:19,283 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:19,754 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 13:03:20,646 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 13:03:20,681 - app.utils.session_state - INFO - Initializing session state
2025-06-03 13:03:20,686 - app.utils.memory_management - INFO - Memory before cleanup: 428.08 MB
2025-06-03 13:03:20,688 - app.utils.session_state - INFO - Session state initialized
2025-06-03 13:03:20,869 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-03 13:03:20,871 - app.utils.memory_management - INFO - Memory after cleanup: 428.13 MB (freed -0.05 MB)
2025-06-03 13:03:22,430 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 13:03:22,439 - app.utils.memory_management - INFO - Memory before cleanup: 431.63 MB
2025-06-03 13:03:22,612 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-03 13:03:22,613 - app.utils.memory_management - INFO - Memory after cleanup: 431.63 MB (freed 0.00 MB)
2025-06-03 13:03:29,627 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:29,672 - app.utils.memory_management - INFO - Memory before cleanup: 434.52 MB
2025-06-03 13:03:29,922 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 13:03:29,925 - app.utils.memory_management - INFO - Memory after cleanup: 434.56 MB (freed -0.04 MB)
2025-06-03 13:03:31,048 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:31,095 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 13:03:31,098 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 13:03:31,100 - app - INFO - Data shape: (581, 36)
2025-06-03 13:03:31,102 - app - INFO - File COMI contains 2025 data
2025-06-03 13:03:31,132 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-03 13:03:31,132 - app - INFO - Features shape: (581, 36)
2025-06-03 13:03:31,160 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-03 13:03:31,162 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 13:03:31,163 - app - INFO - Data shape: (581, 36)
2025-06-03 13:03:31,163 - app - INFO - File COMI contains 2025 data
2025-06-03 13:03:31,166 - app.utils.memory_management - INFO - Memory before cleanup: 438.82 MB
2025-06-03 13:03:31,358 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 13:03:31,359 - app.utils.memory_management - INFO - Memory after cleanup: 438.82 MB (freed 0.00 MB)
2025-06-03 13:03:31,548 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:31,612 - app.utils.memory_management - INFO - Memory before cleanup: 439.88 MB
2025-06-03 13:03:31,803 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 13:03:31,803 - app.utils.memory_management - INFO - Memory after cleanup: 439.86 MB (freed 0.02 MB)
2025-06-03 13:03:34,441 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:34,485 - app.utils.memory_management - INFO - Memory before cleanup: 440.55 MB
2025-06-03 13:03:34,685 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 13:03:34,686 - app.utils.memory_management - INFO - Memory after cleanup: 440.55 MB (freed 0.00 MB)
2025-06-03 13:03:37,753 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:48,110 - app.utils.memory_management - INFO - Memory before cleanup: 444.76 MB
2025-06-03 13:03:48,281 - app.utils.memory_management - INFO - Garbage collection: collected 721 objects
2025-06-03 13:03:48,281 - app.utils.memory_management - INFO - Memory after cleanup: 444.76 MB (freed 0.00 MB)
2025-06-03 13:03:48,281 - app.utils.memory_management - INFO - Memory before cleanup: 444.76 MB
2025-06-03 13:03:48,444 - app.utils.memory_management - INFO - Garbage collection: collected 1454 objects
2025-06-03 13:03:48,445 - app.utils.memory_management - INFO - Memory after cleanup: 444.77 MB (freed -0.01 MB)
2025-06-03 13:10:27,767 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:10:27,773 - app - INFO - Memory management utilities loaded
2025-06-03 13:10:27,775 - app - INFO - Error handling utilities loaded
2025-06-03 13:10:27,780 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:10:27,781 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:10:27,781 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:10:27,781 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:11:00,498 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:11:03,201 - app - INFO - Memory management utilities loaded
2025-06-03 13:11:03,204 - app - INFO - Error handling utilities loaded
2025-06-03 13:11:03,206 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:11:03,211 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:11:03,213 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:11:03,216 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:11:03,217 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 13:11:03,220 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 13:11:03,222 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 13:11:03,223 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 13:11:03,226 - app - INFO - Applied NumPy fix
2025-06-03 13:11:03,230 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:11:03,232 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:11:03,235 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:11:03,237 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 13:11:03,239 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:11:03,241 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:11:03,242 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:11:03,245 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 13:11:09,878 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 13:11:09,878 - app - INFO - Applied TensorFlow fix
2025-06-03 13:11:09,880 - app.config - INFO - Configuration initialized
2025-06-03 13:11:09,884 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 13:11:09,893 - models.train - INFO - TensorFlow test successful
2025-06-03 13:11:10,423 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 13:11:10,423 - models.train - INFO - Transformer model is available
2025-06-03 13:11:10,423 - models.train - INFO - Using TensorFlow-based models
2025-06-03 13:11:10,425 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 13:11:10,425 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 13:11:10,427 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 13:11:10,767 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:11:10,767 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:11:10,767 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:11:10,769 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:11:10,769 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:11:10,867 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 13:11:10,869 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:10,869 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:11,205 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 13:11:11,844 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 13:11:11,881 - app.utils.memory_management - INFO - Memory before cleanup: 427.60 MB
2025-06-03 13:11:11,885 - app.utils.session_state - INFO - Initializing session state
2025-06-03 13:11:12,045 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 13:11:12,047 - app.utils.session_state - INFO - Session state initialized
2025-06-03 13:11:12,048 - app.utils.memory_management - INFO - Memory after cleanup: 427.70 MB (freed -0.10 MB)
2025-06-03 13:11:13,395 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 13:11:13,406 - app.utils.memory_management - INFO - Memory before cleanup: 431.56 MB
2025-06-03 13:11:13,584 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 13:11:13,585 - app.utils.memory_management - INFO - Memory after cleanup: 431.56 MB (freed 0.00 MB)
2025-06-03 13:11:16,091 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:16,128 - app.utils.memory_management - INFO - Memory before cleanup: 434.82 MB
2025-06-03 13:11:16,300 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-03 13:11:16,301 - app.utils.memory_management - INFO - Memory after cleanup: 434.86 MB (freed -0.04 MB)
2025-06-03 13:11:19,633 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:26,990 - app.utils.session_state - ERROR - Error tracked: app_crash - Invalid property specified for object of type plotly.graph_objs.Candlestick: 'hovertemplate'

Did you mean "hovertext"?

    Valid properties:
        close
            Sets the close values.
        closesrc
            Sets the source reference on Chart Studio Cloud for
            `close`.
        customdata
            Assigns extra data each datum. This may be useful when
            listening to hover, click and selection events. Note
            that, "scatter" traces also appends customdata items in
            the markers DOM elements
        customdatasrc
            Sets the source reference on Chart Studio Cloud for
            `customdata`.
        decreasing
            :class:`plotly.graph_objects.candlestick.Decreasing`
            instance or dict with compatible properties
        high
            Sets the high values.
        highsrc
            Sets the source reference on Chart Studio Cloud for
            `high`.
        hoverinfo
            Determines which trace information appear on hover. If
            `none` or `skip` are set, no information is displayed
            upon hovering. But, if `none` is set, click and hover
            events are still fired.
        hoverinfosrc
            Sets the source reference on Chart Studio Cloud for
            `hoverinfo`.
        hoverlabel
            :class:`plotly.graph_objects.candlestick.Hoverlabel`
            instance or dict with compatible properties
        hovertext
            Same as `text`.
        hovertextsrc
            Sets the source reference on Chart Studio Cloud for
            `hovertext`.
        ids
            Assigns id labels to each datum. These ids for object
            constancy of data points during animation. Should be an
            array of strings, not numbers or any other type.
        idssrc
            Sets the source reference on Chart Studio Cloud for
            `ids`.
        increasing
            :class:`plotly.graph_objects.candlestick.Increasing`
            instance or dict with compatible properties
        legend
            Sets the reference to a legend to show this trace in.
            References to these legends are "legend", "legend2",
            "legend3", etc. Settings for these legends are set in
            the layout, under `layout.legend`, `layout.legend2`,
            etc.
        legendgroup
            Sets the legend group for this trace. Traces and shapes
            part of the same legend group hide/show at the same
            time when toggling legend items.
        legendgrouptitle
            :class:`plotly.graph_objects.candlestick.Legendgrouptit
            le` instance or dict with compatible properties
        legendrank
            Sets the legend rank for this trace. Items and groups
            with smaller ranks are presented on top/left side while
            with "reversed" `legend.traceorder` they are on
            bottom/right side. The default legendrank is 1000, so
            that you can use ranks less than 1000 to place certain
            items before all unranked items, and ranks greater than
            1000 to go after all unranked items. When having
            unranked or equal rank items shapes would be displayed
            after traces i.e. according to their order in data and
            layout.
        legendwidth
            Sets the width (in px or fraction) of the legend for
            this trace.
        line
            :class:`plotly.graph_objects.candlestick.Line` instance
            or dict with compatible properties
        low
            Sets the low values.
        lowsrc
            Sets the source reference on Chart Studio Cloud for
            `low`.
        meta
            Assigns extra meta information associated with this
            trace that can be used in various text attributes.
            Attributes such as trace `name`, graph, axis and
            colorbar `title.text`, annotation `text`
            `rangeselector`, `updatemenues` and `sliders` `label`
            text all support `meta`. To access the trace `meta`
            values in an attribute in the same trace, simply use
            `%{meta[i]}` where `i` is the index or key of the
            `meta` item in question. To access trace `meta` in
            layout attributes, use `%{data[n[.meta[i]}` where `i`
            is the index or key of the `meta` and `n` is the trace
            index.
        metasrc
            Sets the source reference on Chart Studio Cloud for
            `meta`.
        name
            Sets the trace name. The trace name appears as the
            legend item and on hover.
        opacity
            Sets the opacity of the trace.
        open
            Sets the open values.
        opensrc
            Sets the source reference on Chart Studio Cloud for
            `open`.
        selectedpoints
            Array containing integer indices of selected points.
            Has an effect only for traces that support selections.
            Note that an empty array means an empty selection where
            the `unselected` are turned on for all points, whereas,
            any other non-array values means no selection all where
            the `selected` and `unselected` styles have no effect.
        showlegend
            Determines whether or not an item corresponding to this
            trace is shown in the legend.
        stream
            :class:`plotly.graph_objects.candlestick.Stream`
            instance or dict with compatible properties
        text
            Sets hover text elements associated with each sample
            point. If a single string, the same string appears over
            all the data points. If an array of string, the items
            are mapped in order to this trace's sample points.
        textsrc
            Sets the source reference on Chart Studio Cloud for
            `text`.
        uid
            Assign an id to this trace, Use this to provide object
            constancy between traces during animations and
            transitions.
        uirevision
            Controls persistence of some user-driven changes to the
            trace: `constraintrange` in `parcoords` traces, as well
            as some `editable: true` modifications such as `name`
            and `colorbar.title`. Defaults to `layout.uirevision`.
            Note that other user-driven trace attribute changes are
            controlled by `layout` attributes: `trace.visible` is
            controlled by `layout.legend.uirevision`,
            `selectedpoints` is controlled by
            `layout.selectionrevision`, and `colorbar.(x|y)`
            (accessible with `config: {editable: true}`) is
            controlled by `layout.editrevision`. Trace changes are
            tracked by `uid`, which only falls back on trace index
            if no `uid` is provided. So if your app can add/remove
            traces before the end of the `data` array, such that
            the same trace has a different index, you can still
            preserve user-driven changes if you give each trace a
            `uid` that stays with it as it moves.
        visible
            Determines whether or not this trace is visible. If
            "legendonly", the trace is not drawn, but can appear as
            a legend item (provided that the legend itself is
            visible).
        whiskerwidth
            Sets the width of the whiskers relative to the box'
            width. For example, with 1, the whiskers are as wide as
            the box(es).
        x
            Sets the x coordinates. If absent, linear coordinate
            will be generated.
        xaxis
            Sets a reference between this trace's x coordinates and
            a 2D cartesian x axis. If "x" (the default value), the
            x coordinates refer to `layout.xaxis`. If "x2", the x
            coordinates refer to `layout.xaxis2`, and so on.
        xcalendar
            Sets the calendar system to use with `x` date data.
        xhoverformat
            Sets the hover text formatting rulefor `x`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `xaxis.hoverformat`.
        xperiod
            Only relevant when the axis `type` is "date". Sets the
            period positioning in milliseconds or "M<n>" on the x
            axis. Special values in the form of "M<n>" could be
            used to declare the number of months. In this case `n`
            must be a positive integer.
        xperiod0
            Only relevant when the axis `type` is "date". Sets the
            base for period positioning in milliseconds or date
            string on the x0 axis. When `x0period` is round number
            of weeks, the `x0period0` by default would be on a
            Sunday i.e. 2000-01-02, otherwise it would be at
            2000-01-01.
        xperiodalignment
            Only relevant when the axis `type` is "date". Sets the
            alignment of data points on the x axis.
        xsrc
            Sets the source reference on Chart Studio Cloud for
            `x`.
        yaxis
            Sets a reference between this trace's y coordinates and
            a 2D cartesian y axis. If "y" (the default value), the
            y coordinates refer to `layout.yaxis`. If "y2", the y
            coordinates refer to `layout.yaxis2`, and so on.
        yhoverformat
            Sets the hover text formatting rulefor `y`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `yaxis.hoverformat`.
        zorder
            Sets the layer on which this trace is displayed,
            relative to other SVG traces on the same subplot. SVG
            traces with higher `zorder` appear in front of those
            with lower `zorder`.
        
Did you mean "hovertext"?

Bad property path:
hovertemplate
^^^^^^^^^^^^^
2025-06-03 13:11:26,992 - app - ERROR - Application crashed: Invalid property specified for object of type plotly.graph_objs.Candlestick: 'hovertemplate'

Did you mean "hovertext"?

    Valid properties:
        close
            Sets the close values.
        closesrc
            Sets the source reference on Chart Studio Cloud for
            `close`.
        customdata
            Assigns extra data each datum. This may be useful when
            listening to hover, click and selection events. Note
            that, "scatter" traces also appends customdata items in
            the markers DOM elements
        customdatasrc
            Sets the source reference on Chart Studio Cloud for
            `customdata`.
        decreasing
            :class:`plotly.graph_objects.candlestick.Decreasing`
            instance or dict with compatible properties
        high
            Sets the high values.
        highsrc
            Sets the source reference on Chart Studio Cloud for
            `high`.
        hoverinfo
            Determines which trace information appear on hover. If
            `none` or `skip` are set, no information is displayed
            upon hovering. But, if `none` is set, click and hover
            events are still fired.
        hoverinfosrc
            Sets the source reference on Chart Studio Cloud for
            `hoverinfo`.
        hoverlabel
            :class:`plotly.graph_objects.candlestick.Hoverlabel`
            instance or dict with compatible properties
        hovertext
            Same as `text`.
        hovertextsrc
            Sets the source reference on Chart Studio Cloud for
            `hovertext`.
        ids
            Assigns id labels to each datum. These ids for object
            constancy of data points during animation. Should be an
            array of strings, not numbers or any other type.
        idssrc
            Sets the source reference on Chart Studio Cloud for
            `ids`.
        increasing
            :class:`plotly.graph_objects.candlestick.Increasing`
            instance or dict with compatible properties
        legend
            Sets the reference to a legend to show this trace in.
            References to these legends are "legend", "legend2",
            "legend3", etc. Settings for these legends are set in
            the layout, under `layout.legend`, `layout.legend2`,
            etc.
        legendgroup
            Sets the legend group for this trace. Traces and shapes
            part of the same legend group hide/show at the same
            time when toggling legend items.
        legendgrouptitle
            :class:`plotly.graph_objects.candlestick.Legendgrouptit
            le` instance or dict with compatible properties
        legendrank
            Sets the legend rank for this trace. Items and groups
            with smaller ranks are presented on top/left side while
            with "reversed" `legend.traceorder` they are on
            bottom/right side. The default legendrank is 1000, so
            that you can use ranks less than 1000 to place certain
            items before all unranked items, and ranks greater than
            1000 to go after all unranked items. When having
            unranked or equal rank items shapes would be displayed
            after traces i.e. according to their order in data and
            layout.
        legendwidth
            Sets the width (in px or fraction) of the legend for
            this trace.
        line
            :class:`plotly.graph_objects.candlestick.Line` instance
            or dict with compatible properties
        low
            Sets the low values.
        lowsrc
            Sets the source reference on Chart Studio Cloud for
            `low`.
        meta
            Assigns extra meta information associated with this
            trace that can be used in various text attributes.
            Attributes such as trace `name`, graph, axis and
            colorbar `title.text`, annotation `text`
            `rangeselector`, `updatemenues` and `sliders` `label`
            text all support `meta`. To access the trace `meta`
            values in an attribute in the same trace, simply use
            `%{meta[i]}` where `i` is the index or key of the
            `meta` item in question. To access trace `meta` in
            layout attributes, use `%{data[n[.meta[i]}` where `i`
            is the index or key of the `meta` and `n` is the trace
            index.
        metasrc
            Sets the source reference on Chart Studio Cloud for
            `meta`.
        name
            Sets the trace name. The trace name appears as the
            legend item and on hover.
        opacity
            Sets the opacity of the trace.
        open
            Sets the open values.
        opensrc
            Sets the source reference on Chart Studio Cloud for
            `open`.
        selectedpoints
            Array containing integer indices of selected points.
            Has an effect only for traces that support selections.
            Note that an empty array means an empty selection where
            the `unselected` are turned on for all points, whereas,
            any other non-array values means no selection all where
            the `selected` and `unselected` styles have no effect.
        showlegend
            Determines whether or not an item corresponding to this
            trace is shown in the legend.
        stream
            :class:`plotly.graph_objects.candlestick.Stream`
            instance or dict with compatible properties
        text
            Sets hover text elements associated with each sample
            point. If a single string, the same string appears over
            all the data points. If an array of string, the items
            are mapped in order to this trace's sample points.
        textsrc
            Sets the source reference on Chart Studio Cloud for
            `text`.
        uid
            Assign an id to this trace, Use this to provide object
            constancy between traces during animations and
            transitions.
        uirevision
            Controls persistence of some user-driven changes to the
            trace: `constraintrange` in `parcoords` traces, as well
            as some `editable: true` modifications such as `name`
            and `colorbar.title`. Defaults to `layout.uirevision`.
            Note that other user-driven trace attribute changes are
            controlled by `layout` attributes: `trace.visible` is
            controlled by `layout.legend.uirevision`,
            `selectedpoints` is controlled by
            `layout.selectionrevision`, and `colorbar.(x|y)`
            (accessible with `config: {editable: true}`) is
            controlled by `layout.editrevision`. Trace changes are
            tracked by `uid`, which only falls back on trace index
            if no `uid` is provided. So if your app can add/remove
            traces before the end of the `data` array, such that
            the same trace has a different index, you can still
            preserve user-driven changes if you give each trace a
            `uid` that stays with it as it moves.
        visible
            Determines whether or not this trace is visible. If
            "legendonly", the trace is not drawn, but can appear as
            a legend item (provided that the legend itself is
            visible).
        whiskerwidth
            Sets the width of the whiskers relative to the box'
            width. For example, with 1, the whiskers are as wide as
            the box(es).
        x
            Sets the x coordinates. If absent, linear coordinate
            will be generated.
        xaxis
            Sets a reference between this trace's x coordinates and
            a 2D cartesian x axis. If "x" (the default value), the
            x coordinates refer to `layout.xaxis`. If "x2", the x
            coordinates refer to `layout.xaxis2`, and so on.
        xcalendar
            Sets the calendar system to use with `x` date data.
        xhoverformat
            Sets the hover text formatting rulefor `x`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `xaxis.hoverformat`.
        xperiod
            Only relevant when the axis `type` is "date". Sets the
            period positioning in milliseconds or "M<n>" on the x
            axis. Special values in the form of "M<n>" could be
            used to declare the number of months. In this case `n`
            must be a positive integer.
        xperiod0
            Only relevant when the axis `type` is "date". Sets the
            base for period positioning in milliseconds or date
            string on the x0 axis. When `x0period` is round number
            of weeks, the `x0period0` by default would be on a
            Sunday i.e. 2000-01-02, otherwise it would be at
            2000-01-01.
        xperiodalignment
            Only relevant when the axis `type` is "date". Sets the
            alignment of data points on the x axis.
        xsrc
            Sets the source reference on Chart Studio Cloud for
            `x`.
        yaxis
            Sets a reference between this trace's y coordinates and
            a 2D cartesian y axis. If "y" (the default value), the
            y coordinates refer to `layout.yaxis`. If "y2", the y
            coordinates refer to `layout.yaxis2`, and so on.
        yhoverformat
            Sets the hover text formatting rulefor `y`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `yaxis.hoverformat`.
        zorder
            Sets the layer on which this trace is displayed,
            relative to other SVG traces on the same subplot. SVG
            traces with higher `zorder` appear in front of those
            with lower `zorder`.
        
Did you mean "hovertext"?

Bad property path:
hovertemplate
^^^^^^^^^^^^^
2025-06-03 13:11:26,996 - app.utils.memory_management - INFO - Memory before cleanup: 453.59 MB
2025-06-03 13:11:27,180 - app.utils.memory_management - INFO - Garbage collection: collected 759 objects
2025-06-03 13:11:27,181 - app.utils.memory_management - INFO - Memory after cleanup: 453.59 MB (freed 0.00 MB)
2025-06-03 13:11:27,182 - app.utils.memory_management - INFO - Memory before cleanup: 453.59 MB
2025-06-03 13:11:27,348 - app.utils.memory_management - INFO - Garbage collection: collected 1919 objects
2025-06-03 13:11:27,349 - app.utils.memory_management - INFO - Memory after cleanup: 453.59 MB (freed -0.01 MB)
2025-06-03 13:13:17,826 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:13:17,832 - app - INFO - Memory management utilities loaded
2025-06-03 13:13:17,834 - app - INFO - Error handling utilities loaded
2025-06-03 13:13:17,835 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:13:17,836 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:13:17,836 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:13:17,837 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
