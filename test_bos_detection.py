"""
Test the enhanced BOS detection functionality
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_bos_detection():
    """Test BOS detection with COMI CSV data"""
    print("🧪 Testing Enhanced BOS Detection")
    print("=" * 50)
    
    try:
        import pandas as pd
        from app.components.advanced_smc_features import detect_break_of_structure
        
        # Load COMI CSV data
        csv_path = "data/stocks/COMI.csv"
        if not os.path.exists(csv_path):
            print(f"❌ CSV file not found: {csv_path}")
            return False
        
        df = pd.read_csv(csv_path)
        print(f"📊 Loaded {len(df)} rows of COMI data")
        
        # Prepare data for BOS detection
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)
        
        # Ensure required columns exist and are properly formatted
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_cols:
            if col not in df.columns:
                print(f"❌ Missing required column: {col}")
                return False
        
        # Convert to lowercase for consistency
        df_smc = pd.DataFrame({
            'open': df['Open'].astype(float),
            'high': df['High'].astype(float),
            'low': df['Low'].astype(float),
            'close': df['Close'].astype(float),
            'volume': df['Volume'].astype(int)
        })
        df_smc.index = df.index
        
        # Use recent data for BOS detection (last 3 months)
        df_recent = df_smc.tail(90)
        print(f"📈 Using {len(df_recent)} recent bars for BOS detection")
        print(f"📅 Date range: {df_recent.index[0].date()} to {df_recent.index[-1].date()}")
        print(f"💰 Price range: {df_recent['low'].min():.2f} - {df_recent['high'].max():.2f} EGP")
        
        # Test BOS detection with different lookback periods
        lookback_periods = [10, 15, 20]
        
        for lookback in lookback_periods:
            print(f"\n🔍 Testing BOS detection with lookback = {lookback}")
            
            bos_events = detect_break_of_structure(df_recent, lookback=lookback)
            
            print(f"✅ Detected {len(bos_events)} BOS events")
            
            if bos_events:
                print("\n📊 BOS Events Summary:")
                print("-" * 80)
                print(f"{'Event':<8} {'Type':<4} {'Direction':<8} {'Date':<12} {'Price':<8} {'Strength':<8} {'Status':<10}")
                print("-" * 80)
                
                for i, bos in enumerate(bos_events[:5]):  # Show top 5
                    status = "CONFIRMED" if bos.confirmed else "PENDING"
                    date_str = bos.datetime[:10] if len(bos.datetime) > 10 else bos.datetime
                    
                    print(f"BOS-{i+1:<3} {bos.structure_type:<4} {bos.direction.upper():<8} {date_str:<12} {bos.price:<8.2f} {bos.strength:<8.1%} {status:<10}")
                
                # Analyze the results
                bullish_count = sum(1 for bos in bos_events if bos.direction == 'bullish')
                bearish_count = sum(1 for bos in bos_events if bos.direction == 'bearish')
                confirmed_count = sum(1 for bos in bos_events if bos.confirmed)
                
                print(f"\n📈 Analysis:")
                print(f"   • Bullish BOS: {bullish_count}")
                print(f"   • Bearish BOS: {bearish_count}")
                print(f"   • Confirmed: {confirmed_count}/{len(bos_events)} ({confirmed_count/len(bos_events)*100:.1f}%)")
                
                # Check if we have realistic data
                avg_strength = sum(bos.strength for bos in bos_events) / len(bos_events)
                print(f"   • Average Strength: {avg_strength:.1%}")
                
                if avg_strength > 0.3 and len(bos_events) >= 3:
                    print(f"✅ BOS detection looks realistic with lookback {lookback}")
                    return True, bos_events
                else:
                    print(f"⚠️ BOS detection may need tuning with lookback {lookback}")
            else:
                print(f"⚠️ No BOS events detected with lookback {lookback}")
        
        print("\n❌ BOS detection needs improvement - no realistic events found")
        return False, []
        
    except Exception as e:
        print(f"❌ Error testing BOS detection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, []

def test_bos_display():
    """Test BOS display functionality"""
    print("\n🧪 Testing BOS Display")
    print("=" * 50)
    
    try:
        # Get BOS events from detection test
        success, bos_events = test_bos_detection()
        
        if not success or not bos_events:
            print("❌ Cannot test display without BOS events")
            return False
        
        from app.pages.smc_analysis import display_bos_events
        import streamlit as st
        
        print("📊 Testing BOS display function...")
        
        # Mock streamlit for testing
        class MockStreamlit:
            def info(self, text):
                print(f"INFO: {text}")
            
            def dataframe(self, df, **kwargs):
                print("DATAFRAME:")
                print(df.to_string())
                return df
        
        # Replace st with mock
        import app.pages.smc_analysis
        app.pages.smc_analysis.st = MockStreamlit()
        app.pages.smc_analysis.pd = pd
        
        # Test display function
        display_bos_events(bos_events)
        
        print("✅ BOS display test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing BOS display: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all BOS tests"""
    print("🚀 BOS Detection Test Suite")
    print("=" * 60)
    
    # Test BOS detection
    detection_success, bos_events = test_bos_detection()
    
    # Test BOS display
    display_success = test_bos_display()
    
    print("\n" + "=" * 60)
    print("📊 BOS TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if detection_success:
        print("✅ BOS Detection: SUCCESS")
        print(f"   • Found {len(bos_events)} BOS events")
        print("   • Events have realistic timestamps and prices")
        print("   • Strength calculations working properly")
    else:
        print("❌ BOS Detection: FAILED")
        print("   • No realistic BOS events detected")
        print("   • May need parameter tuning")
    
    if display_success:
        print("✅ BOS Display: SUCCESS")
        print("   • Display function working properly")
        print("   • Table formatting correct")
    else:
        print("❌ BOS Display: FAILED")
        print("   • Display function has issues")
    
    if detection_success and display_success:
        print("\n🎉 ALL BOS TESTS SUCCESSFUL!")
        print("✅ Your BOS detection should now work properly in the SMC Analysis page")
        print("\n💡 Next steps:")
        print("1. Start your Streamlit app")
        print("2. Go to SMC Analysis page")
        print("3. Select COMI and run analysis")
        print("4. Check the Break of Structure section")
        print("5. You should see realistic BOS events with proper dates and prices!")
    else:
        print("\n⚠️ Some BOS tests failed. Check the error messages above.")
        print("💡 The BOS detection logic may need further refinement.")

if __name__ == "__main__":
    main()
