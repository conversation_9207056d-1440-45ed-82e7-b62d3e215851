(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9727],{66783:e=>{"use strict";var t=Object.prototype.hasOwnProperty;function n(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}e.exports=function(e,r){if(n(e,r))return!0;if("object"!=typeof e||null===e||"object"!=typeof r||null===r)return!1;var o=Object.keys(e),a=Object.keys(r);if(o.length!==a.length)return!1;for(var l=0;l<o.length;l++)if(!t.call(r,o[l])||!n(e[o[l]],r[o[l]]))return!1;return!0}},58222:e=>{e.exports={"light-button":"light-button-bYDQcOkp",link:"link-bYDQcOkp",content:"content-bYDQcOkp","visually-hidden":"visually-hidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp","ellipsis-container":"ellipsis-container-bYDQcOkp","text-wrap-container":"text-wrap-container-bYDQcOkp","text-wrap-with-ellipsis":"text-wrap-with-ellipsis-bYDQcOkp",icon:"icon-bYDQcOkp","force-direction-ltr":"force-direction-ltr-bYDQcOkp","force-direction-rtl":"force-direction-rtl-bYDQcOkp","with-grouped":"with-grouped-bYDQcOkp","variant-quiet-primary":"variant-quiet-primary-bYDQcOkp",selected:"selected-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp","size-xsmall":"size-xsmall-bYDQcOkp","with-start-icon":"with-start-icon-bYDQcOkp","with-end-icon":"with-end-icon-bYDQcOkp","no-content":"no-content-bYDQcOkp",wrap:"wrap-bYDQcOkp","size-small":"size-small-bYDQcOkp","size-medium":"size-medium-bYDQcOkp","variant-primary":"variant-primary-bYDQcOkp","color-gray":"color-gray-bYDQcOkp",caret:"caret-bYDQcOkp",grouped:"grouped-bYDQcOkp",pills:"pills-bYDQcOkp","disable-active-on-touch":"disable-active-on-touch-bYDQcOkp","disable-active-state-styles":"disable-active-state-styles-bYDQcOkp","color-green":"color-green-bYDQcOkp","color-red":"color-red-bYDQcOkp","variant-secondary":"variant-secondary-bYDQcOkp","variant-ghost":"variant-ghost-bYDQcOkp"}},45350:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},88803:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},55596:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},57240:e=>{e.exports={container:"container-M1mz4quA",pairContainer:"pairContainer-M1mz4quA",logo:"logo-M1mz4quA",hidden:"hidden-M1mz4quA"}},69827:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",
container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},40281:e=>{e.exports={container:"container-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},52597:e=>{e.exports={actions:"actions-rarsm4ka",actionButton:"actionButton-rarsm4ka"}},54257:e=>{e.exports={logo:"logo-d0vVmGvT"}},39339:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",itemRow:"itemRow-oRSs8UQo",multiLine:"multiLine-oRSs8UQo",cell:"cell-oRSs8UQo",itemInfoCell:"itemInfoCell-oRSs8UQo",description:"description-oRSs8UQo",symbolDescription:"symbolDescription-oRSs8UQo",flag:"flag-oRSs8UQo",exchangeDescription:"exchangeDescription-oRSs8UQo",marketType:"marketType-oRSs8UQo",exchangeName:"exchangeName-oRSs8UQo",actionHandleWrap:"actionHandleWrap-oRSs8UQo",source:"source-oRSs8UQo",hover:"hover-oRSs8UQo",selected:"selected-oRSs8UQo",active:"active-oRSs8UQo",highlighted:"highlighted-oRSs8UQo",light:"light-oRSs8UQo","highlight-animation-theme-light":"highlight-animation-theme-light-oRSs8UQo",dark:"dark-oRSs8UQo","highlight-animation-theme-dark":"highlight-animation-theme-dark-oRSs8UQo",markedFlag:"markedFlag-oRSs8UQo",offset:"offset-oRSs8UQo",descriptionCell:"descriptionCell-oRSs8UQo",addition:"addition-oRSs8UQo",exchangeCell:"exchangeCell-oRSs8UQo",fixedWidth:"fixedWidth-oRSs8UQo",expandHandle:"expandHandle-oRSs8UQo",expanded:"expanded-oRSs8UQo",symbolTitle:"symbolTitle-oRSs8UQo",invalid:"invalid-oRSs8UQo",noDescription:"noDescription-oRSs8UQo",highlightedText:"highlightedText-oRSs8UQo",icon:"icon-oRSs8UQo",narrow:"narrow-oRSs8UQo",wide:"wide-oRSs8UQo",dataMode:"dataMode-oRSs8UQo",actionsCell:"actionsCell-oRSs8UQo",action:"action-oRSs8UQo",targetAction:"targetAction-oRSs8UQo",removeAction:"removeAction-oRSs8UQo",addAction:"addAction-oRSs8UQo",markedFlagWrap:"markedFlagWrap-oRSs8UQo",markedFlagMobile:"markedFlagMobile-oRSs8UQo",logo:"logo-oRSs8UQo",isExpandable:"isExpandable-oRSs8UQo",primaryIcon:"primaryIcon-oRSs8UQo"}},33172:e=>{e.exports={icon:"icon-OJpk_CAQ"}},50674:e=>{e.exports={wrap:"wrap-IxKZEhmO",libAllSelected:"libAllSelected-IxKZEhmO",container:"container-IxKZEhmO",iconWrap:"iconWrap-IxKZEhmO",icon:"icon-IxKZEhmO",title:"title-IxKZEhmO",highlighted:"highlighted-IxKZEhmO",description:"description-IxKZEhmO",mobile:"mobile-IxKZEhmO",allSelected:"allSelected-IxKZEhmO",desktop:"desktop-IxKZEhmO",allSelectedIcon:"allSelectedIcon-IxKZEhmO",selected:"selected-IxKZEhmO",titleWithoutDesc:"titleWithoutDesc-IxKZEhmO",textBlock:"textBlock-IxKZEhmO",bordered:"bordered-IxKZEhmO"}},70699:e=>{e.exports={container:"container-dfKL9A7t",contentList:"contentList-dfKL9A7t",contentListDesktop:"contentListDesktop-dfKL9A7t",searchSourceItemsContainer:"searchSourceItemsContainer-dfKL9A7t",searchSourceItemsContainerDesktop:"searchSourceItemsContainerDesktop-dfKL9A7t",
groupTitleDesktop:"groupTitleDesktop-dfKL9A7t",emptyText:"emptyText-dfKL9A7t",emptyIcon:"emptyIcon-dfKL9A7t",noResultsDesktop:"noResultsDesktop-dfKL9A7t"}},37796:e=>{e.exports={wrap:"wrap-gjrLBBL3",item:"item-gjrLBBL3",small:"small-gjrLBBL3",newStyles:"newStyles-gjrLBBL3",mobile:"mobile-gjrLBBL3",text:"text-gjrLBBL3",exchange:"exchange-gjrLBBL3",filterItem:"filterItem-gjrLBBL3",brokerWrap:"brokerWrap-gjrLBBL3"}},52662:e=>{e.exports={wrap:"wrap-dlewR1s1",watchlist:"watchlist-dlewR1s1",noFeed:"noFeed-dlewR1s1",newStyles:"newStyles-dlewR1s1",scrollContainer:"scrollContainer-dlewR1s1",listContainer:"listContainer-dlewR1s1",multiLineItemsContainer:"multiLineItemsContainer-dlewR1s1",withSpinner:"withSpinner-dlewR1s1",spinnerContainer:"spinnerContainer-dlewR1s1",largeSpinner:"largeSpinner-dlewR1s1"}},85544:e=>{e.exports={search:"search-ZXzPWcCf",upperCase:"upperCase-ZXzPWcCf",withFilters:"withFilters-ZXzPWcCf",withButton:"withButton-ZXzPWcCf",symbolType:"symbolType-ZXzPWcCf",spinnerWrap:"spinnerWrap-ZXzPWcCf",emptyText:"emptyText-ZXzPWcCf",emptyIcon:"emptyIcon-ZXzPWcCf",noResultsDesktop:"noResultsDesktop-ZXzPWcCf",brokerButtonWrap:"brokerButtonWrap-ZXzPWcCf",brokerButton:"brokerButton-ZXzPWcCf"}},14444:e=>{e.exports={flagWrap:"flagWrap-QKnxaZOG",icon:"icon-QKnxaZOG",caret:"caret-QKnxaZOG",title:"title-QKnxaZOG",button:"button-QKnxaZOG",withFlag:"withFlag-QKnxaZOG",buttonContent:"buttonContent-QKnxaZOG"}},62393:e=>{e.exports={dialog:"dialog-u2dP3kv1",tabletDialog:"tabletDialog-u2dP3kv1",desktopDialog:"desktopDialog-u2dP3kv1",backButton:"backButton-u2dP3kv1"}},54638:e=>{e.exports={childrenWrapper:"childrenWrapper-_RhDhmVQ",container:"container-_RhDhmVQ"}},88389:e=>{e.exports={bubbles:"bubbles-Ie7o2cas",multiLine:"multiLine-Ie7o2cas",bubble:"bubble-Ie7o2cas"}},61371:e=>{e.exports={bubble:"bubble-zcjhaZ_y",animated:"animated-zcjhaZ_y",content:"content-zcjhaZ_y","appearance-default":"appearance-default-zcjhaZ_y",active:"active-zcjhaZ_y",gray:"gray-zcjhaZ_y",red:"red-zcjhaZ_y",blue:"blue-zcjhaZ_y",green:"green-zcjhaZ_y",orange:"orange-zcjhaZ_y",purple:"purple-zcjhaZ_y",cyan:"cyan-zcjhaZ_y",pink:"pink-zcjhaZ_y","appearance-text":"appearance-text-zcjhaZ_y","fontSize-s":"fontSize-s-zcjhaZ_y","fontSize-m":"fontSize-m-zcjhaZ_y","size-m":"size-m-zcjhaZ_y","size-l":"size-l-zcjhaZ_y"}},82112:e=>{e.exports={}},45300:e=>{e.exports={}},75623:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},45719:e=>{e.exports={separator:"separator-Pf4rIzEt"}},34587:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},18429:(e,t,n)=>{"use strict";n.d(t,{SEPARATOR_PREFIX:()=>r,isSeparatorItem:()=>o});const r="###";function o(e){return e.startsWith(r)}},48199:(e,t,n)=>{"use strict";n.d(t,{BackButton:()=>v});var r=n(50959),o=n(64388),a=n(95694),l=n(49498),s=n(60176),i=n(35369),c=n(58478),u=n(73063),d=n(14127),m=n(18073),p=n(99243),h=n(42576);function g(e="large",t="1.2"){switch(e){case"large":return"1.2"===t?a:u;case"medium":return"1.2"===t?l:d;case"small":return"1.2"===t?s:m;case"xsmall":return"1.2"===t?i:p;case"xxsmall":return"1.2"===t?c:h
;default:return l}}const v=r.forwardRef(((e,t)=>{const{"aria-label":n,...a}=e;return r.createElement(o.NavButton,{...a,"aria-label":n,ref:t,icon:g(e.size,e.iconStrokeWidth),flipIconOnRtl:!0})}))},27011:(e,t,n)=>{"use strict";function r(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}n.d(t,{isIconOnly:()=>r})},90744:(e,t,n)=>{"use strict";n.d(t,{LightButton:()=>g});var r=n(50959),o=n(97754),a=n(9745),l=n(17946),s=n(27011),i=n(86332);const c=r.createContext({isInButtonGroup:!1,isGroupPrimary:!1});var u=n(2948),d=n(58222),m=n.n(d);const p=(e,t)=>{const n=(0,r.useContext)(l.CustomBehaviourContext),a=(0,r.useContext)(i.ControlGroupContext),{isInButtonGroup:u,isGroupPrimary:d}=(0,r.useContext)(c),{className:p,isSelected:h,children:g,startIcon:v,showCaret:f,endIcon:y,forceDirection:b,iconOnly:S,color:x="gray",variant:w="primary",size:k="medium",enableActiveStateStyles:C=n.enableActiveStateStyles,typography:E,isLink:L=!1,textWrap:N,isPills:I}=e,R=E?m()[`typography-${E}`]:m()[`typography-${((e,t)=>"xsmall"===e?t?"semibold14px":"regular14px":"small"===e||"medium"===e?t?"semibold16px":"regular16px":"")(k,h||I)}`];return o(p,m()["light-button"],L&&m().link,h&&m().selected,(0,s.isIconOnly)(g,S)&&m()["no-content"],v&&m()["with-start-icon"],(f||y)&&m()["with-end-icon"],t&&m()["with-grouped"],b&&m()[`force-direction-${b}`],m()[`variant-${d?"primary":w}`],m()[`color-${d?"gray":x}`],m()[`size-${k}`],R,!C&&m()["disable-active-state-styles"],a.isGrouped&&m().grouped,N&&m().wrap,u&&m()["disable-active-on-touch"],I&&m().pills)};function h(e){const{startIcon:t,endIcon:n,showCaret:l,iconOnly:i,ellipsis:c=!0,textWrap:d,children:p}=e;return r.createElement(r.Fragment,null,t&&r.createElement(a.Icon,{className:m().icon,icon:t}),!(0,s.isIconOnly)(p,i)&&r.createElement("span",{className:o(m().content,!d&&m().nowrap)},d||c?r.createElement(r.Fragment,null,r.createElement("span",{className:o(!d&&c&&m()["ellipsis-container"],d&&m()["text-wrap-container"],d&&c&&m()["text-wrap-with-ellipsis"])},p),r.createElement("span",{className:m()["visually-hidden"],"aria-hidden":!0},p)):r.createElement(r.Fragment,null,p,r.createElement("span",{className:m()["visually-hidden"],"aria-hidden":!0},p))),(n||l)&&(e=>r.createElement(a.Icon,{className:o(m().icon,e.showCaret&&m().caret),icon:e.showCaret?u:e.endIcon}))(e))}function g(e){const{isGrouped:t}=r.useContext(i.ControlGroupContext),{reference:n,className:o,isSelected:a,children:l,startIcon:s,iconOnly:c,ellipsis:u,showCaret:d,forceDirection:m,endIcon:g,color:v,variant:f,size:y,enableActiveStateStyles:b,typography:S,textWrap:x=!1,maxLines:w,style:k={},isPills:C,...E}=e,L=x?null!=w?w:2:1,N=L>0?{...k,"--ui-lib-light-button-content-max-lines":L}:k;return r.createElement("button",{...E,className:p({className:o,isSelected:a,children:l,startIcon:s,iconOnly:c,showCaret:d,forceDirection:m,endIcon:g,color:v,variant:f,size:y,enableActiveStateStyles:b,typography:S,textWrap:x,isPills:C},t),ref:n,style:N},r.createElement(h,{showCaret:d,startIcon:s,endIcon:g,iconOnly:c,ellipsis:u,textWrap:x},l))}
n(21593)},64388:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var r=n(50959),o=n(97754),a=n(9745),l=n(45350);function s(e){const{size:t="large",preservePaddings:n,isLink:r,flipIconOnRtl:a,className:s}=e;return o(l["nav-button"],l[`size-${t}`],n&&l["preserve-paddings"],a&&l["flip-icon"],r&&l.link,s)}function i(e){const{children:t,icon:n}=e;return r.createElement(r.Fragment,null,r.createElement("span",{className:l.background}),r.createElement(a.Icon,{icon:n,className:l.icon,"aria-hidden":!0}),t&&r.createElement("span",{className:l["visually-hidden"]},t))}const c=(0,r.forwardRef)(((e,t)=>{const{icon:n,type:o="button",preservePaddings:a,flipIconOnRtl:l,size:c,"aria-label":u,...d}=e;return r.createElement("button",{...d,className:s({...e,children:u}),ref:t,type:o},r.createElement(i,{icon:n},u))}));c.displayName="NavButton";var u=n(21593),d=n(53017);(0,r.forwardRef)(((e,t)=>{const{icon:n,renderComponent:o,"aria-label":a,...l}=e,c=null!=o?o:u.CustomComponentDefaultLink;return r.createElement(c,{...l,className:s({...e,children:a,isLink:!0}),reference:(0,d.isomorphicRef)(t)},r.createElement(i,{icon:n},a))})).displayName="NavAnchorButton"},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>r});const r=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},38952:(e,t,n)=>{"use strict";function r(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>r})},21593:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>a});var r=n(50959),o=n(38952);function a(e){return r.createElement("a",{...(0,o.renameRef)(e)})}r.PureComponent},17946:(e,t,n)=>{"use strict";n.d(t,{CustomBehaviourContext:()=>r});const r=(0,n(50959).createContext)({enableActiveStateStyles:!0});r.displayName="CustomBehaviourContext"},39416:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>a});var r=n(50959),o=n(43010);function a(e){const t=(0,r.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{s.current(e)}))),[]),n=(0,r.useRef)(null),a=t=>{if(null===t)return l(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,l(n.current,t))},s=(0,r.useRef)(a);return s.current=a,(0,o.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return s.current(t.current),()=>s.current(null)}),[e]),t}function l(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},43010:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>o});var r=n(50959);function o(e,t){("undefined"==typeof window?r.useEffect:r.useLayoutEffect)(e,t)}},27267:(e,t,n)=>{"use strict";function r(e,t,n,r,o){function a(o){if(e>o.timeStamp)return;const a=o.target;void 0!==n&&null!==t&&null!==a&&a.ownerDocument===r&&(t.contains(a)||n(o))}return o.click&&r.addEventListener("click",a,!1),o.mouseDown&&r.addEventListener("mousedown",a,!1),o.touchEnd&&r.addEventListener("touchend",a,!1),o.touchStart&&r.addEventListener("touchstart",a,!1),()=>{r.removeEventListener("click",a,!1),r.removeEventListener("mousedown",a,!1),r.removeEventListener("touchend",a,!1),r.removeEventListener("touchstart",a,!1)}}n.d(t,{
addOutsideEventListener:()=>r})},67842:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>s});var r=n(50959),o=n(59255),a=n(43010),l=n(39416);function s(e,t=[]){const{callback:n,ref:s=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),i=(0,r.useRef)(null),c=(0,r.useRef)(n);c.current=n;const u=(0,l.useFunctionalRefObject)(s),d=(0,r.useCallback)((e=>{u(e),null!==i.current&&(i.current.disconnect(),null!==e&&i.current.observe(e))}),[u,i]);return(0,a.useIsomorphicLayoutEffect)((()=>(i.current=new o.default(((e,t)=>{c.current(e,t)})),u.current&&d(u.current),()=>{var e;null===(e=i.current)||void 0===e||e.disconnect()})),[u,...t]),d}},90186:(e,t,n)=>{"use strict";function r(e){return a(e,l)}function o(e){return a(e,s)}function a(e,t){const n=Object.entries(e).filter(t),r={};for(const[e,t]of n)r[e]=t;return r}function l(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function s(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>o,filterDataProps:()=>r,filterProps:()=>a,isAriaAttribute:()=>s,isDataAttribute:()=>l})},76460:(e,t,n)=>{"use strict";function r(e){return 0===e.detail}n.d(t,{isKeyboardClick:()=>r})},53017:(e,t,n)=>{"use strict";function r(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){return r([e])}n.d(t,{isomorphicRef:()=>o,mergeRefs:()=>r})},3685:(e,t,n)=>{"use strict";function r(){var e,t,n;return null!==(n=null===(t=null===(e=window.configurationData)||void 0===e?void 0:e.exchanges)||void 0===t?void 0:t.map((e=>({...e,country:"",providerId:"",flag:""}))))&&void 0!==n?n:[]}n.d(t,{getExchanges:()=>r})},36279:(e,t,n)=>{"use strict";var r;n.d(t,{LogoSize:()=>r,getLogoUrlResolver:()=>l}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"}(r||(r={}));class o{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}}let a;function l(){return a||(a=new o),a}},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>o});var r=n(88803);const o={SmallHeight:r["small-height-breakpoint"],TabletSmall:r["tablet-small-breakpoint"],TabletNormal:r["tablet-normal-breakpoint"]}},35057:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>N});var r=n(50959),o=n(50151);var a=n(97754),l=n.n(a),s=n(68335),i=n(38223),c=n(35749),u=n(63016),d=n(1109),m=n(24437),p=n(90692),h=n(95711);var g=n(52092),v=n(76422),f=n(9745);const y=r.createContext({setHideClose:()=>{}});var b=n(7720),S=n(69827);function x(e){const{title:t,titleTextWrap:n=!1,subtitle:o,showCloseIcon:a=!0,onClose:s,onCloseButtonKeyDown:i,renderBefore:c,renderAfter:u,draggable:d,className:m,unsetAlign:p,closeAriaLabel:h,closeButtonReference:g}=e,[v,x]=(0,r.useState)(!1);return r.createElement(y.Provider,{value:{setHideClose:x}},r.createElement("div",{className:l()(S.container,m,(o||p)&&S.unsetAlign)},c,r.createElement("div",{"data-dragg-area":d,className:S.title},r.createElement("div",{className:l()(n?S.textWrap:S.ellipsis)},t),o&&r.createElement("div",{className:l()(S.ellipsis,S.subtitle)
},o)),u,a&&!v&&r.createElement("button",{className:S.close,onClick:s,onKeyDown:i,"data-name":"close","aria-label":h,type:"button",ref:g},r.createElement(f.Icon,{className:S.icon,icon:b,"data-name":"close","data-role":"button"}))))}var w=n(53017),k=n(90186),C=n(55596);const E={vertical:20},L={vertical:0};class N extends r.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(m.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document,r=(0,o.ensureNotNull)(this._reference);if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();if((0,c.isTextEditingField)(n))return void r.focus();if(r.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,o.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,i.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){var e,t;this.props.ignoreClosePopupsAndDialog||v.subscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),e=this._orientationMediaQuery,t=this._handleOpen,(null==e?void 0:e.addEventListener)?e.addEventListener("change",t):e.addListener(t)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){var e,t;this.props.ignoreClosePopupsAndDialog||v.unsubscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&(e=this._orientationMediaQuery,t=this._handleOpen,
(null==e?void 0:e.removeEventListener)?e.removeEventListener("change",t):e.removeListener(t)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,o.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,n;return null!==(n=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==n&&n}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:o,title:a,titleTextWrap:s,dataName:i,onClickOutside:c,additionalElementPos:g,additionalHeaderElement:v,backdrop:f,shouldForceFocus:y=!0,shouldReturnFocus:b,onForceFocus:S,showSeparator:N,subtitle:I,draggable:R=!0,fullScreen:_=!1,showCloseIcon:T=!0,rounded:D=!0,isAnimationEnabled:B,growPoint:M,dialogTooltip:O,unsetHeaderAlign:A,onDragStart:z,dataDialogName:F,closeAriaLabel:Q,containerAriaLabel:P,reference:U,containerTabIndex:W,closeButtonReference:V,onCloseButtonKeyDown:Z,shadowed:K,fullScreenViewOffsets:q}=this.props,j="after"!==g?v:void 0,H="after"===g?v:void 0,G="string"==typeof a?a:F||"",$=(0,k.filterDataProps)(this.props),Y=(0,w.mergeRefs)([this._handleReference,U]);return r.createElement(p.MatchMedia,{rule:m.DialogBreakpoints.SmallHeight},(g=>r.createElement(p.MatchMedia,{rule:m.DialogBreakpoints.TabletSmall},(m=>r.createElement(u.PopupDialog,{rounded:!(m||_)&&D,className:l()(C.dialog,_&&q&&C.bounded,e),isOpened:o,reference:Y,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:m||_,guard:g?L:E,boundByScreen:m||_,shouldForceFocus:y,onForceFocus:S,shouldReturnFocus:b,backdrop:f,draggable:R,isAnimationEnabled:B,growPoint:M,name:this.props.dataName,dialogTooltip:O,onDragStart:z,containerAriaLabel:P,containerTabIndex:W,calculateDialogPosition:_&&q?this._calculatePositionWithOffsets:void 0,shadowed:K,...$},r.createElement("div",{className:l()(C.wrapper,t),"data-name":i,"data-dialog-name":G},void 0!==a&&r.createElement(x,{draggable:R&&!(m||_),onClose:this._handleCloseBtnClick,renderAfter:H,renderBefore:j,subtitle:I,title:a,titleTextWrap:s,showCloseIcon:T,className:n,unsetAlign:A,closeAriaLabel:Q,closeButtonReference:V,onCloseButtonKeyDown:Z}),N&&r.createElement(d.Separator,{className:C.separator}),r.createElement(h.PopupContext.Consumer,null,(e=>this._renderChildren(e,m||_)))))))))}}},69654:(e,t,n)=>{"use strict";n.d(t,{DialogSearch:()=>u});var r=n(50959),o=n(97754),a=n.n(o),l=n(44352),s=n(9745),i=n(69859),c=n(40281);function u(e){const{children:t,renderInput:o,onCancel:u,containerClassName:m,inputContainerClassName:p,iconClassName:h,...g}=e;return r.createElement("div",{className:a()(c.container,m)},r.createElement("div",{className:a()(c.inputContainer,p,u&&c.withCancel)},o||r.createElement(d,{...g})),t,r.createElement(s.Icon,{className:a()(c.icon,h),icon:i}),u&&r.createElement("div",{className:c.cancel,onClick:u},l.t(null,void 0,n(20036))))}function d(e){const{className:t,reference:n,value:o,onChange:l,onFocus:s,onBlur:i,onKeyDown:u,onSelect:d,placeholder:m,...p}=e;return r.createElement("input",{...p,ref:n,
type:"text",className:a()(t,c.input),autoComplete:"off","data-role":"search",placeholder:m,value:o,onChange:l,onFocus:s,onBlur:i,onSelect:d,onKeyDown:u})}},40987:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchDialogContentItem:()=>z});var r=n(50959),o=n(97754),a=n.n(o),l=(n(44352),n(50151)),s=n(9745),i=n(14483),c=n(24637),u=n(19785),d=n(84524),m=n(24633),p=n(77975),h=n(45345),g=n(32563),v=n(94474),f=n(93251),y=n(36279),b=n(44747);n(82112);var S=n(76068),x=n(58492),w=n(12767),k=n(43010),C=n(57240);const E="tv-circle-logo--visually-hidden";function L(e){const{className:t,placeholderLetter:n,url1:o,url2:l,size:s="xxxsmall"}=e,i=(0,r.useRef)(null),c=(0,r.useRef)(null),u=(0,r.useRef)(null),d=(0,r.useRef)(null),m=(0,r.useRef)(null),p=(0,r.useRef)(null);return(0,k.useIsomorphicLayoutEffect)((()=>{const e=void 0===o?[]:void 0===l?[o]:[o,l],t=p.current=(n=e,Promise.all(n.map((e=>(0,w.getImage)(`symbol_logo_${e}`,e,I).then((e=>e.cloneNode()))))));var n;t.then((e=>{var n,r,o,a,l,s,h,g,v;if(t===p.current)switch(e.length){case 0:null===(n=u.current)||void 0===n||n.classList.add(C.hidden),null===(r=c.current)||void 0===r||r.classList.add(E),null===(o=i.current)||void 0===o||o.classList.remove(E);break;case 1:N(c.current,e[0]),null===(a=u.current)||void 0===a||a.classList.add(C.hidden),null===(l=c.current)||void 0===l||l.classList.remove(E),null===(s=i.current)||void 0===s||s.classList.add(E);break;case 2:N(d.current,e[0]),N(m.current,e[1]),null===(h=u.current)||void 0===h||h.classList.remove(C.hidden),null===(g=c.current)||void 0===g||g.classList.add(E),null===(v=i.current)||void 0===v||v.classList.add(E)}}))}),[o,l]),r.createElement("span",{className:a()(t,C.container)},r.createElement("span",{ref:u,className:a()(C.pairContainer,C.hidden)},r.createElement("span",{className:(0,b.getBlockStyleClasses)(s)},r.createElement("span",{ref:m,className:a()(C.logo,(0,b.getLogoStyleClasses)(s))}),r.createElement("span",{ref:d,className:a()(C.logo,(0,b.getLogoStyleClasses)(s))}))),r.createElement("span",{ref:c,className:a()(C.logo,E,(0,x.getStyleClasses)(s))}),r.createElement("span",{ref:i,className:a()(C.logo,(0,x.getStyleClasses)(s))},r.createElement(S.CircleLogo,{size:s,placeholderLetter:n})))}function N(e,t){e&&(e.innerHTML="",e.appendChild(t))}function I(e){e.decoding="async"}var R=n(54257);function _(e){const{logoId:t,baseCurrencyLogoId:n,currencyLogoId:o,placeholder:l,className:s,size:i="xsmall"}=e,c=(0,r.useMemo)((()=>{const e={logoid:t,"currency-logoid":o,"base-currency-logoid":n};return(0,f.removeUsdFromCryptoPairLogos)((0,f.resolveLogoUrls)(e,y.LogoSize.Medium))}),[t,o,n]);return r.createElement(L,{key:i,className:a()(R.logo,s),url1:c[0],url2:c[1],placeholderLetter:l,size:i})}function T(e){return e===m.StdTheme.Dark?n(64494):n(2495)}const D=y.LogoSize.Medium;var B=n(33172);function M(e){const{country:t,providerId:n,sourceId:o,className:s}=e,i=(0,p.useWatchedValueReadonly)({watchedValue:h.watchedTheme}),[c,u]=(0,r.useState)(function({country:e,providerId:t,sourceId:n}){const r=(0,y.getLogoUrlResolver)();return o=>{
const a=e=>r.getProviderLogoUrl(e,D),s=[{value:n,resolve:a},{value:e,resolve:e=>r.getCountryFlagUrl(e.toUpperCase(),D)},{value:t,resolve:a}].find((({value:e})=>void 0!==e&&e.length>0));return void 0!==s?s.resolve((0,l.ensureDefined)(s.value)):T(o)}}({country:t,providerId:n,sourceId:o})(i));return r.createElement("img",{className:a()(s,B.icon),src:c,onError:function(){u(T(i))}})}var O=n(69533),A=n(39339);function z(e){var t,n;const{dangerousTitleHTML:o,title:f,dangerousDescriptionHTML:y,description:b,searchToken:S,exchangeName:x,marketType:w,onClick:k,isSelected:C,isEod:E=!1,isActive:L=!1,isOffset:N=!1,invalid:I=!1,isHighlighted:R=!1,hideExchange:T=!1,hideMarkedListFlag:D=!1,onExpandClick:B,isExpanded:z,hoverComponent:F,country:Q,providerId:P,source:U,source2:W,type:V,flag:Z,itemRef:K,onMouseOut:q,onMouseOver:j,className:H,actions:G,reference:$,fullSymbolName:Y,logoId:X,currencyLogoId:J,baseCurrencyLogoId:ee,shortName:te,hideLogo:ne=!1,exchangeTooltip:re,hideMarketType:oe,isPrimary:ae}=e,{isSmallWidth:le,isMobile:se}=(0,l.ensureNotNull)((0,r.useContext)(d.SymbolSearchItemsDialogContext)),ie=Boolean(F),ce=!I&&!T&&(se||!ie),ue=(0,p.useWatchedValueReadonly)({watchedValue:h.watchedTheme})===m.StdTheme.Dark?A.dark:A.light,de=F,me=i.enabled("show_symbol_logos"),pe=i.enabled("show_exchange_logos"),he=me||!1,ge=null!==(t=null==W?void 0:W.description)&&void 0!==t?t:U,ve=null!==(n=null==W?void 0:W.name)&&void 0!==n?n:U;return r.createElement("div",{className:a()(A.itemRow,le&&A.multiLine,R&&A.highlighted,R&&ue,C&&A.selected,L&&A.active,I&&A.invalid,!se&&g.mobiletouch&&ie&&A.hover,H),onClick:function(e){if(!k||e.defaultPrevented)return;e.preventDefault(),k(e)},"data-role":e["data-role"]||"list-item","data-active":L,"data-type":w,"data-name":"symbol-search-dialog-content-item",onMouseOut:q,onMouseOver:j,ref:$},r.createElement("div",{ref:K,className:a()(A.itemInfoCell,A.cell,N&&A.offset)},r.createElement("div",{className:a()(A.actionHandleWrap,!he&&A.fixedWidth)},r.createElement(r.Fragment,null,!1,B&&r.createElement("div",{onClick:function(e){if(!B||e.defaultPrevented)return;e.preventDefault(),B(e)}},r.createElement(s.Icon,{className:a()(A.expandHandle,z&&A.expanded,C&&A.selected),icon:O})),he&&!N&&r.createElement("div",{className:a()(A.logo,Boolean(B)&&A.isExpandable)},r.createElement(_,{key:Y,logoId:X,currencyLogoId:J,baseCurrencyLogoId:ee,placeholder:te?te[0]:void 0})))),r.createElement("div",{className:a()(A.description,he&&N&&A.offset)},f&&r.createElement("div",{className:a()(A.symbolTitle,L&&A.active,I&&A.invalid,!Boolean(y)&&A.noDescription),"data-name":"list-item-title"},"string"==typeof f&&S?r.createElement(c.HighlightedText,{className:A.highlightedText,text:f,queryString:S,rules:(0,u.createRegExpList)(S)}):f,E&&r.createElement("span",{className:A.dataMode},"E")),!f&&o&&r.createElement("div",{className:a()(A.symbolTitle,L&&A.active,I&&A.invalid),"data-name":"list-item-title"},r.createElement("span",{dangerouslySetInnerHTML:{__html:o}}),E&&r.createElement("span",{className:A.dataMode
},"E")),le&&fe())),!le&&r.createElement("div",{className:a()(A.cell,A.descriptionCell,Boolean(de)&&A.addition)},fe(),de?r.createElement(de,{...e,className:A.actions,onMouseOver:void 0,onMouseOut:void 0}):null),le&&de?r.createElement(de,{...e,className:A.cell,onMouseOver:void 0,onMouseOut:void 0}):null,ce&&r.createElement("div",{className:a()(A.exchangeCell,A.cell)},r.createElement("div",{className:a()(A.exchangeDescription)},!oe&&r.createElement("div",{className:a()(A.marketType,L&&A.active)},w),r.createElement("div",{className:A.source},!1,"economic"===V&&ge&&ve?r.createElement("div",{className:a()(A.exchangeName,L&&A.active,"apply-common-tooltip",A.narrow,oe&&A.wide),title:ge},ve):r.createElement("div",{className:a()(A.exchangeName,L&&A.active,re&&"apply-common-tooltip"),title:re},x))),pe&&r.createElement("div",{className:A.flag},r.createElement(M,{key:pe?`${Y}_exchange`:`${Q}_${P}_${null==W?void 0:W.id}_${V}_${Z}`,className:A.icon,country:Q,providerId:P,sourceId:"economic"===V&&W?W.id:void 0}))),r.createElement("div",{className:a()(A.cell,Boolean(G)&&A.actionsCell)},G));function fe(){if(I)return null;const e=a()(A.symbolDescription,L&&A.active,!g.mobiletouch&&"apply-overflow-tooltip apply-overflow-tooltip--allow-text");return b?r.createElement("div",{className:e},S?r.createElement(c.HighlightedText,{className:A.highlightedText,text:b,queryString:S,rules:(0,u.createRegExpList)(S)}):b):y?r.createElement("div",{"data-overflow-tooltip-text":(0,v.removeTags)(y),className:e,dangerouslySetInnerHTML:{__html:y}}):null}}},58442:(e,t,n)=>{"use strict";n.d(t,{QualifiedSources:()=>r,qualifyProName:()=>l});var r,o=n(50151),a=n(14483);n(81319);function l(e){return e}!function(e){function t(e){return e.pro_name}function n(e){{const t=a.enabled("pay_attention_to_ticker_not_symbol")?e.ticker:e.full_name;return(0,o.ensureDefined)(t)}}e.fromQuotesSnapshot=function(e){return"error"===e.status?e.symbolname:e.values.pro_name},e.fromQuotesResponse=function(e){const{values:n,symbolname:r,status:o}=e;return"error"===o&&r?r:t(n)},e.fromQuotes=t,e.fromSymbolSearchResult=function(e,t){{const{ticker:n,full_name:r}=null!=t?t:e;return a.enabled("pay_attention_to_ticker_not_symbol")?(0,o.ensureDefined)(null!=n?n:r):(0,o.ensureDefined)(r)}},e.fromSymbolInfo=n,e.fromSymbolMessage=function(e,t){return"symbol_resolved"===t.method?n(t.params[1]):e}}(r||(r={}))},20882:(e,t,n)=>{"use strict";n.d(t,{createSearchSources:()=>s,filterSearchSources:()=>a,isAllSearchSourcesSelected:()=>o,splitSearchSourcesByGroup:()=>l});const r=[];function o(e){return""===e.value()}function a(e,t){return e.filter((e=>e.includes(t)))}function l(e){const t=new Map;e.forEach((e=>{t.has(e.group())?t.get(e.group()).push(e):t.set(e.group(),[e])}));for(const e of t.values()){e[0].group()!==ExchangeGroup.NorthAmerica&&e.sort(((e,t)=>e.name().toLowerCase()>t.name().toLowerCase()?1:-1))}return new Map([...t.entries()].sort((([e],[t])=>r.indexOf(e)-r.indexOf(t))))}function s(e,t){return t.map((t=>new e(t)))}},70613:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchDialogBodyContext:()=>r})
;const r=n(50959).createContext(null)},84524:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchItemsDialogContext:()=>r});const r=n(50959).createContext(null)},32456:(e,t,n)=>{"use strict";n.d(t,{SymbolSearchItemsDialog:()=>We});var r=n(50959),o=n(97754),a=n.n(o),l=n(44352),s=n(15983);const i=["futures","forex","bond","economic"];var c=n(84877),u=n(14483),d=n(24437),m=n(35057),p=n(9745),h=n(84524),g=n(69654),v=n(20882),f=n(54638);function y(e){const{children:t,className:n}=e;return r.createElement("div",{className:a()(f.container,n)},r.createElement("div",{className:f.childrenWrapper},t))}var b=n(50151),S=n(78036),x=n(24637),w=n(19785),k=n(81319),C=n(91540),E=n(50674);function L(e){const{searchSource:t,onClick:n,queryString:o}=e,{symbolSearchContent:l,isAllSearchSourcesSelected:s,allSearchSourcesTitle:i,isMobile:c}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),u=l.currentSelectedSearchSource,d=(0,b.ensureNotNull)(u).value(),m=s(t),g=t.value()===d,v=(0,r.useMemo)((()=>(0,w.createRegExpList)(o)),[o]),f=t.description(),y=f&&!m,L=k.isSeparateSymbolSearchTabs&&m&&i?i:t.name(),N=a()(E.container,c?E.mobile:E.desktop,g&&E.selected,m&&E.allSelected,m&&E.libAllSelected,!m&&c&&E.bordered);return r.createElement("div",{className:a()(!c&&E.wrap,m&&E.libAllSelected),onClick:n},r.createElement("div",{className:N},r.createElement("div",{className:E.iconWrap},!!m&&r.createElement(p.Icon,{className:a()(E.icon,E.allSelectedIcon),icon:C})),r.createElement("div",{className:E.textBlock},r.createElement("div",{className:a()(E.title,!y&&!c&&E.titleWithoutDesc)},r.createElement(x.HighlightedText,{className:a()(g&&E.highlighted),queryString:o,text:L,rules:v})),y&&r.createElement("div",{className:a()(E.description,"apply-overflow-tooltip")},r.createElement(x.HighlightedText,{className:E.highlighted,queryString:o,rules:v,text:f})))))}var N=n(77975),I=n(45345),R=n(26843),_=n(70613),T=n(66619),D=n(67562),B=n(70699);const M={emptyTextClassName:B.emptyText};function O(e){const{searchSources:t}=e,{setSelectedSearchSource:o,setMode:s,isMobile:i,emptyState:c,autofocus:u}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),d=(0,N.useWatchedValueReadonly)({watchedValue:I.watchedTheme})===R.StdTheme.Dark?T:D,[m,f]=(0,r.useState)(""),b=(0,r.useMemo)((()=>[{group:null,sources:(0,v.filterSearchSources)(t,m)}]),[t,m]),x=(0,r.useRef)(null),w=(0,r.useRef)(null);(0,r.useLayoutEffect)((()=>{var e;u&&(null===(e=null==x?void 0:x.current)||void 0===e||e.focus())}),[]);const k=c?r.createElement(c,null):r.createElement(y,{className:B.noResultsDesktop},r.createElement(p.Icon,{icon:d,className:B.emptyIcon}),r.createElement("div",{className:B.emptyText},l.t(null,void 0,n(29673)))),C=!(b.length&&b.every((e=>0===e.sources.length)));return r.createElement(_.SymbolSearchDialogBodyContext.Provider,{value:M},r.createElement(g.DialogSearch,{placeholder:l.t(null,void 0,n(52298)),onChange:function(e){f(e.target.value),w&&w.current&&(w.current.scrollTop=0)},reference:x}),C?r.createElement("div",{ref:w,className:a()(B.contentList,!i&&B.contentListDesktop),
onTouchStart:function(){var e;null===(e=x.current)||void 0===e||e.blur()}},b.map((e=>{const{group:t,sources:n}=e;return 0===n.length?r.createElement(r.Fragment,{key:t}):r.createElement(r.Fragment,{key:t},!1,r.createElement("div",{className:a()(B.searchSourceItemsContainer,!i&&B.searchSourceItemsContainerDesktop)},n.map((e=>r.createElement(L,{key:e.value(),searchSource:e,queryString:m,onClick:E.bind(null,e)})))))}))):k);function E(e){o(e),s("symbolSearch")}}var A=n(962),z=n(45884);n(76861),n(69798);function F(e){return e.hasOwnProperty("exchange")}async function Q(e){{const t=await async function(e){return new Promise((t=>{window.ChartApiInstance.searchSymbols(e.text||"",e.exchange||"",e.type||"","",!1,!0,"",!0,"",(e=>{t(e)}))}))}(e);return{symbols:t,symbols_remaining:0}}}new Map([].map((({value:e,search_type:t})=>[e,t])));var P=n(78136),U=n(51768),W=n(68335),V=n(31409),Z=n(44254),K=n(486),q=n(81574),j=n(35119),H=n(32617),G=n(69135),$=n(63861),Y=n(52597);function X(e){var t;const{state:n,update:o}=e,{searchRef:a,forceUpdate:l,upperCaseEnabled:i}=(0,b.ensureNotNull)((0,r.useContext)(h.SymbolSearchItemsDialogContext)),c=(0,Z.tokenize)(null===(t=a.current)||void 0===t?void 0:t.value),d=(0,s.validate)(c);let m=[{icon:K,insert:"/",type:"binaryOp",name:"division"},{icon:q,insert:"-",type:"binaryOp",name:"subtraction"},{icon:j,insert:"+",type:"binaryOp",name:"addition"},{icon:H,insert:"*",type:"binaryOp",name:"multiplication"}];return u.enabled("hide_exponentiation_spread_operator")||(m=m.concat([{icon:G,insert:"^",type:"binaryOp",name:"exponentiation"}])),u.enabled("hide_reciprocal_spread_operator")||(m=m.concat([{icon:$,type:"complete",name:"1/x",callback:()=>{!a.current||d.errors.length||d.warnings.length||(a.current.value=(0,s.stringifyTokens)((0,s.flip)(c)),l())}}])),r.createElement("div",{className:Y.actions},m.map((e=>r.createElement(V.ToolWidgetButton,{className:Y.actionButton,icon:e.icon,key:e.name,isDisabled:J(e,d),onClick:()=>function(e){var t;if(!J(e,d)){if(e.insert&&a.current){const t=a.current.value+e.insert;a.current.value=t,a.current.setSelectionRange(t.length,t.length);const[r,,c]=(0,s.getCurrentTokenParamsFromInput)(a.current,i);n.current&&(n.current.selectedIndexValue=-1,n.current.searchSpreadsValue=(0,s.isSpread)(c),n.current.searchTokenValue=r),l(),o()}e.callback&&e.callback(),null===(t=a.current)||void 0===t||t.focus(),(0,U.trackEvent)("GUI","SS",e.name)}}(e)}))))}function J(e,t){let n=!1;if(!t.errors.length)switch(e.type){case"binaryOp":n="var"===t.currentState;break;case"openBrace":n="var"!==t.currentState;break;case"closeBrace":n="var"===t.currentState&&t.braceBalance>0;break;case"complete":n=!t.errors.length&&!t.warnings.length}return!n}var ee=n(90186),te=n(61371);function ne(e){const{title:t,isActive:n,isAnimated:r,activeColor:o,size:l="m",appearance:s="default",fontSize:i="m",grayStyles:c,className:u}=e;return a()(te.bubble,n&&te.active,o&&te[o],t&&"apply-common-tooltip",l&&te[`size-${l}`],i&&te[`fontSize-${i}`],s&&te[`appearance-${s}`],r&&te.animated,c&&te.gray,u)}function re(e){
const{id:t,title:n,tabIndex:o,role:l,contentClassName:s,children:i,onClick:c,onMouseDown:u,reference:d,grayStyles:m,...p}=e;return r.createElement("span",{...(0,ee.filterAriaProps)(p),...(0,ee.filterDataProps)(p),id:t,title:n,tabIndex:o,role:l,className:ne(e),onClick:c,onMouseDown:u,ref:d},r.createElement("span",{className:a()(te.content,s)},i))}var oe=n(88389);function ae(e){const{className:t,itemClassName:n,itemContentClassName:a,items:l,getItemTitle:s,getItemTooltip:i,getItemKey:c,checkItemIsActive:u,getItemColor:d,onBubbleClick:m,multiline:p,children:h,BubbleComponent:g=re,reference:v,fontSize:f,grayStyles:y}=e;return r.createElement("div",{className:o(t,oe.bubbles,p&&oe.multiLine),ref:v},l.map(((e,t)=>r.createElement(g,{key:c?c(e):t,id:c?c(e):t.toString(),className:o(oe.bubble,n),contentClassName:a,onClick:function(){m(e)},onMouseDown:function(e){e.preventDefault()},isActive:!!u&&u(e),activeColor:d?d(e):void 0,fontSize:f,title:i?i(e):void 0,grayStyles:y},s(e)))),h)}var le=n(63932),se=n(20037),ie=n(29006),ce=n(90744),ue=n(10381),de=n(52019),me=n(14444);const pe=(0,k.getDefaultSearchSource)();function he(e){const{mode:t,setMode:o,searchRef:s,cachedInputValue:i,setSelectedSearchSource:c,isAllSearchSourcesSelected:u,allSearchSourcesTitle:d,upperCaseEnabled:m,symbolSearchContent:g}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),v=g.currentSelectedSearchSource,f=(0,b.ensureNotNull)(v),y="symbolSearch"===t,x=u(f),w=k.isSeparateSymbolSearchTabs&&x&&d?d:f.name(),E=(0,r.useCallback)((()=>{k.isSeparateSymbolSearchTabs&&!x&&pe?c(pe):(s.current&&(i.current=m?s.current.value.toUpperCase():s.current.value),o("exchange"))}),[x,s,m,o,c]);return k.isSeparateSymbolSearchTabs?y?r.createElement(ce.LightButton,{onClick:E,isPills:!x,size:"xsmall",variant:x?"ghost":"quiet-primary",showCaret:x,endIcon:x?void 0:de,enableActiveStateStyles:!1,className:a()(me.button,!x&&me.withFlag,"apply-common-tooltip"),title:w,tabIndex:-1,"data-name":"sources-button"},r.createElement("div",{className:me.buttonContent},null,r.createElement("span",null,w))):null:y?r.createElement("div",{className:a()(me.flagWrap,"apply-common-tooltip",!x&&me.withFlag),title:l.t(null,void 0,n(13269)),onClick:E,"data-name":"sources-button"},x&&r.createElement(p.Icon,{className:me.icon,icon:C}),null,r.createElement("div",{className:a()(me.title)},w),r.createElement(ue.ToolWidgetCaret,{className:me.caret,dropped:!1})):null}var ge=n(37796);function ve(e){const{brokerButton:t=null}=e,{isSmallWidth:o,selectedFilterValues:s,setSelectedFilterValues:i,isMobile:c,searchRef:u,symbolSearchContent:d}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),m=d.tabSelectFilters;return k.isSeparateSymbolSearchTabs?r.createElement("div",{className:a()(ge.wrap,ge.small,ge.newStyles,c&&ge.mobile)},t&&r.createElement("div",{className:ge.brokerWrap},t),d.canChangeExchange&&r.createElement("div",{className:ge.filterItem},r.createElement(he,null)),m&&m.map((e=>{const{id:t,options:n,label:o}=e,a=n.find((e=>e.value===FILTER_DEFAULT_VALUE))
;if(!a)throw new Error("There must be default filter value in filter definition");const l=n.find((e=>{var n;return e.value===(null===(n=s[d.currentSymbolType])||void 0===n?void 0:n[t])}))||a;return r.createElement("div",{key:t,className:ge.filterItem},r.createElement(SymbolSearchSelectFilter,{selectedOption:l,defaultOption:a,options:n,onSelect:e=>{var n;i(d.currentSymbolType,{[t]:e.value}),trackEvent("New SS",d.currentSymbolType,null===e.value?e.analyticsLabel:e.value),null===(n=u.current)||void 0===n||n.focus()},label:o,isMobile:c,"data-name":t}))}))):r.createElement("div",{className:a()(ge.wrap,o&&ge.small)},r.createElement("div",{className:ge.item},r.createElement("div",{className:ge.text},o?l.t(null,void 0,n(48490)):l.t(null,void 0,n(89053)))),r.createElement("div",{className:ge.item},!o&&r.createElement("div",{className:ge.text},l.t(null,void 0,n(29601))),d.canChangeExchange&&r.createElement("div",{className:ge.exchange},r.createElement(he,null))))}var fe=n(38223),ye=n(52662);function be(e){const{onTouchMove:t,listRef:n,className:o,listWrapRef:l,virtualListKey:s,items:i,getItemSize:c,hideFeed:u,canLoadMore:d,onLoadMoreSymbols:m}=e,{mode:p,isSmallWidth:g,handleListWidth:v}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),[f,y]=(0,r.useState)(null),x=(0,ie.useResizeObserver)((function([e]){y(e.contentRect.height),v(e.contentRect.width)})),w=(0,r.useCallback)((e=>{const{index:t,style:n}=e;return r.createElement("div",{style:n},i[t])}),[i]),C=(0,r.useCallback)((e=>(0,b.ensure)(i[e].key)),[i]),E="watchlist"===p&&null!==f;return r.createElement("div",{className:a()(ye.wrap,E&&ye.watchlist,u&&ye.noFeed,u&&k.isSeparateSymbolSearchTabs&&ye.newStyles,o),onTouchMove:t,ref:x},r.createElement("div",{ref:l,className:a()(ye.scrollContainer,u&&ye.noFeed)},E?r.createElement(se.VariableSizeList,{key:s,ref:n,className:ye.listContainer,width:"100%",height:(0,b.ensureNotNull)(f),itemCount:i.length,itemSize:c,children:w,itemKey:C,overscanCount:20,direction:(0,fe.isRtl)()?"rtl":"ltr"}):r.createElement(r.Fragment,null,r.createElement("div",{className:a()(ye.listContainer,g&&ye.multiLineItemsContainer)},!k.isSeparateSymbolSearchTabs&&r.createElement(ve,null),...i,!1))))}var Se=n(40987),xe=n(85544);const we=u.enabled("hide_image_invalid_symbol");function ke(e){const{otherSymbolsCount:t,onChangeSymbolTypeFilter:n,onResetFilters:a,onListTouchMove:l,brokerTitle:s,brokerLogoInfo:i,isBrokerActive:c,onBrokerToggle:u,listRef:d,listWrapRef:m,onLoadMoreSymbols:p,canLoadMore:g}=e,{mode:v,isMobile:f,selectedSymbolType:y,symbolTypes:b,feedItems:x,contentItem:w,emptyState:C=Ce,symbolSearchContent:E,symbolSearchState:L}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),N=s?r.createElement(BrokerButton,{brokerTitle:s,isActive:c,onToggle:u,logoInfo:i}):null,I="symbolSearch"===v&&["good","loadingWithPaginated"].includes(L),R=null!=w?w:Se.SymbolSearchDialogContentItem,_=(0,r.useMemo)((()=>x.map((e=>r.createElement(R,{...e,searchToken:E.token})))),[x])
;return r.createElement(r.Fragment,null,"symbolSearch"===v&&r.createElement(r.Fragment,null,b.length>0&&r.createElement(ae,{className:o(k.isSeparateSymbolSearchTabs&&(E.withFilters||f&&N)&&xe.withFilters,!f&&N&&xe.withButton),itemClassName:xe.symbolType,items:b,getItemTitle:e=>e.name,getItemKey:e=>e.value,checkItemIsActive:e=>e.value===y,onBubbleClick:n,multiline:!f,grayStyles:!0},!f&&r.createElement("div",{className:xe.brokerButton},N)),!k.isSeparateSymbolSearchTabs&&f&&b.length>0&&s&&r.createElement("div",{className:xe.brokerButtonWrap},N),k.isSeparateSymbolSearchTabs&&r.createElement(ve,{brokerButton:f?N:void 0})),r.createElement(be,{listRef:d,listWrapRef:m,onTouchMove:l,items:_,getItemSize:()=>Le,onLoadMoreSymbols:p,canLoadMore:g,hideFeed:!I}),"loading"===L&&r.createElement("div",{className:xe.spinnerWrap},r.createElement(le.Spinner,null)),"symbolSearch"===v&&r.createElement(r.Fragment,null,!1,"empty"===L&&r.createElement(C,null)))}function Ce(e){const t=(0,N.useWatchedValueReadonly)({watchedValue:I.watchedTheme})===R.StdTheme.Dark?T:D;return r.createElement(y,{className:xe.noResultsDesktop},!we&&r.createElement(p.Icon,{icon:t,className:xe.emptyIcon}),r.createElement("div",{className:xe.emptyText},l.t(null,void 0,n(41379))))}const Ee=(0,k.getDefaultSearchSource)(),Le=52;function Ne(e){const{mode:t,setMode:o,setSelectedIndex:i,isMobile:c,selectedSearchSource:d,setSelectedSearchSource:m,isAllSearchSourcesSelected:p,selectedSymbolType:v,setSelectedSymbolType:f,symbolSearchContent:y,setSymbolSearchContent:b,searchRef:x,setSearchSpreads:w,showSpreadActions:C,selectedItem:E,forceUpdate:L,placeholder:N,initialScreen:I,footer:R,searchInput:T,upperCaseEnabled:D,externalInput:B,handleKeyDown:M,customSearchSymbols:O,filterDefinitions:V,filterQueryParams:Z,searchSources:K,symbolSearchState:q,setSymbolSearchState:j}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext),H=(0,r.useRef)(t);H.current=t;const G=(0,r.useRef)(new AbortController),[$,Y]=(0,r.useState)(0),J=(0,r.useRef)(0),[ee,te]=(0,r.useState)(y.token),ne=(0,r.useRef)(null),re=(0,r.useRef)(null),oe=(0,r.useRef)({selectedIndexValue:-1,searchTokenValue:"",searchSpreadsValue:!0}),ae=(0,r.useRef)(null),le=(0,r.useRef)(null),{broker:se=null,brokerId:ie,brokerTitle:ce,brokerLogoInfo:ue,isBrokerChecked:de=!1,setIsBrokerChecked:me=(()=>{}),unhideSymbolSearchGroups:pe=""}={brokerId:void 0,brokerTitle:void 0,brokerLogoInfo:void 0};(0,r.useEffect)((()=>()=>{G.current.abort(),_e()}),[]),(0,r.useEffect)((()=>{(null==x?void 0:x.current)&&te(x.current.value)}),[]),(0,r.useEffect)((()=>{const e=x.current;if(e)return e.addEventListener("input",Se),e.addEventListener("focus",Ie),e.addEventListener("select",be),e.addEventListener("click",be),e.addEventListener("keyup",Re),B&&M&&e.addEventListener("keydown",M),()=>{e&&(e.removeEventListener("input",Se),e.removeEventListener("focus",Ie),e.removeEventListener("select",be),e.removeEventListener("click",be),e.removeEventListener("keyup",Re),B&&M&&e.removeEventListener("keydown",M))}}),[M]),(0,r.useEffect)((()=>{
Boolean(I)&&""===ee.trim()||(b((e=>({...e,symbolStartIndex:0}))),Ce(ee,v,d).then((()=>{ne.current&&(ne.current.scrollTop=0)})))}),[ee,v,d,de,I,Z]),(0,r.useEffect)((()=>{var e;if(!E||!x.current)return;if(!u.enabled("show_spread_operators"))return x.current.value=E.symbol,void L();const t=F(E)?E.exchange:E.parent.exchange;let n;n="contracts"in E&&(null===(e=E.contracts)||void 0===e?void 0:e.length)?E.contracts[0]:E;const r={name:n.symbol,exchange:t,prefix:n.prefix,fullName:n.full_name},[o,a]=(0,s.getNextSymbolInputValueAndPosition)(x.current,r,D);x.current.value=o,x.current.setSelectionRange(a,a),L()}),[E]);const he=null!=I?I:"div",ge=Boolean(I)&&"symbolSearch"!==t,ve=null!=T?T:g.DialogSearch,fe=(0,r.useMemo)((()=>({listRef:re,resetRecommends:Ne,updateRecommends:Ce,searchToken:ee,emptyTextClassName:xe.emptyText,isBrokerChecked:de,symbolSearchState:q,currentMode:H})),[re,ee,de,q,H,Z]);return r.createElement(_.SymbolSearchDialogBodyContext.Provider,{value:fe},!(B&&"symbolSearch"===t)&&r.createElement(ve,{reference:x,className:a()(xe.search,D&&xe.upperCase),placeholder:N||l.t(null,void 0,n(52298))},C&&r.createElement(X,{state:oe,update:we})),ge?r.createElement(he,null):r.createElement(ke,{otherSymbolsCount:$,onListTouchMove:function(){var e;null===(e=x.current)||void 0===e||e.blur()},onChangeSymbolTypeFilter:function(e){const{value:t}=e;f(t),i(-1)},onResetFilters:function(){var e;k.isSeparateSymbolSearchTabs?"resetFilter"===q?f((0,k.getAllSymbolTypesValue)()):Ee&&m(Ee):(f((0,k.getAllSymbolTypesValue)()),Ee&&m(Ee));me(!1),c||null===(e=x.current)||void 0===e||e.focus()},brokerTitle:ce,brokerLogoInfo:ue,isBrokerActive:de,onBrokerToggle:me,listRef:re,listWrapRef:ne,onLoadMoreSymbols:void 0,canLoadMore:void 0}),R);function ye(){if(!x.current)return;const[e,t,n]=(0,s.getCurrentTokenParamsFromInput)(x.current,D);J.current=t,oe.current={selectedIndexValue:-1,searchSpreadsValue:(0,s.isSpread)(n),searchTokenValue:e},ae.current||(ae.current=setTimeout(we,0))}function be(){if(!x.current)return;const[,e]=(0,s.getCurrentTokenParamsFromInput)(x.current,D);e!==J.current&&ye()}function Se(){u.enabled("show_spread_operators")?ye():x.current&&(oe.current={selectedIndexValue:-1,searchSpreadsValue:!1,searchTokenValue:x.current.value},ae.current||(ae.current=setTimeout(we,0)))}function we(){const{selectedIndexValue:e,searchTokenValue:t,searchSpreadsValue:n}=oe.current;ae.current=null,(0,A.unstable_batchedUpdates)((()=>{w(n),i(e),te(D?t.toUpperCase():t)}))}async function Ce(e,t,n,r){var o,a,l;try{"noop"===q?j("loading"):r?j("loadingWithPaginated"):(_e(),le.current=setTimeout((()=>{b({token:e,canChangeExchange:Boolean(d&&K.length>1&&!(0,k.exchangeSelectDisabled)(t)),tabSelectFilters:null==V?void 0:V[t],withFilters:!!t,currentSymbolType:t,currentSelectedSearchSource:d,currentTabAvailableSearchSources:K,renderSymbolSearchList:[],symbolsRemaining:0,symbolStartIndex:0}),j("loading")}),500)),Te();(0,k.getAllSymbolTypesValue)();const i=!1;let c;if(de&&se){c=(await(0,z.respectAbort)(G.current.signal,se.accountMetainfo())).prefix}
const m=u.enabled("show_spread_operators")?null!==(a=null!==(o=(0,s.getExchange)(e))&&void 0!==o?o:c)&&void 0!==a?a:null==n?void 0:n.getRequestExchangeValue():null==d?void 0:d.getRequestExchangeValue(),p=(0,s.getExchange)(e)||null===(l=n||d)||void 0===l?void 0:l.getRequestCountryValue(),[h,g]=await Promise.all([Le(G.current.signal,e,t,n,m,p,r),i&&!r?getRecent():Promise.resolve([])]),v=g.filter((e=>{var t,n;return m?(null===(t=e.exchange)||void 0===t?void 0:t.toLowerCase())===m.toLowerCase():!p||(null===(n=e.country)||void 0===n?void 0:n.toLowerCase())===p.toLowerCase()})),f=new Set(v.map((e=>`${e.exchange}_${e.symbol}`))),S=h.symbols.filter((e=>!f.has(`${e.exchange}_${e.symbol}`)));let x=function(e,t=window.ChartApiInstance.symbolsGrouping()){var n;const r={},o=[];for(let a=0;a<e.length;++a){const l=e[a];if(l.prefix||Array.isArray(l.contracts))return e;const s=t[l.type];if(void 0===s){o.push(l);continue}const i=s.exec(l.symbol);if(i){const e=i[1];let t;r.hasOwnProperty(e)?t=r[e]:(t=o.length,r[e]=t,o.push({type:l.type,symbol:e,exchange:l.exchange,description:l.description,full_name:l.exchange+":"+e,contracts:[]})),null===(n=o[t].contracts)||void 0===n||n.push(l)}else o.push(l)}return o}([...v,...S]);if(r&&(x=[...y.renderSymbolSearchList,...x]),!x.length)return b((n=>({...n,canChangeExchange:Boolean(d&&K.length>1&&!(0,k.exchangeSelectDisabled)(t)),tabSelectFilters:null==V?void 0:V[t],token:e,symbolsRemaining:0,withFilters:!!t,currentSymbolType:t,currentSelectedSearchSource:d,currentTabAvailableSearchSources:K}))),_e(),void j("empty");_e(),b((n=>({...n,canChangeExchange:Boolean(d&&K.length>1&&!(0,k.exchangeSelectDisabled)(t)),tabSelectFilters:null==V?void 0:V[t],renderSymbolSearchList:x,token:e,symbolsRemaining:h.symbols_remaining,withFilters:!!t,currentSymbolType:t,currentSelectedSearchSource:d,currentTabAvailableSearchSources:K,symbolStartIndex:n.symbolStartIndex+h.symbols.length}))),j("good")}catch(e){(0,z.skipAbortError)(e)}}async function Le(e,t,n,r,o,a,l){var i;const c={serverHighlight:!1,text:u.enabled("show_spread_operators")?(0,s.shortName)(t):null===(i=x.current)||void 0===i?void 0:i.value,exchange:o,country:a,type:n,lang:window.language||"",sortByCountry:void 0,brokerId:ie,onlyTradable:Boolean(ie)&&de,unhideSymbolSearchGroups:pe,signal:e,start:l,filterQueryParams:Z},d=(0,P.getSearchRequestDelay)();return void 0!==d&&await(0,z.delay)(e,d),O?O(c):Q(c)}function Ne(){Te(),j("empty"),te(""),w(!1),b((e=>({...e,symbolStartIndex:0}))),_e()}function Ie(){"watchlist"===H.current&&(o("symbolSearch"),(0,U.trackEvent)("Watchlist","Mobile SS","Go to SS page"))}function Re(e){switch((0,W.hashFromEvent)(e)){case 37:case 39:be()}}function _e(){le.current&&clearTimeout(le.current)}function Te(){G.current.abort(),G.current=new AbortController}}var Ie=n(48199),Re=n(24658),_e=n(58442),Te=n(56840);function De(e){const[t,n]=(0,r.useState)((()=>{const{defaultSearchSource:t,searchSources:n}=e,r=Te.getValue("symboledit.exchangefilter","");return n.find((e=>e.value()===r))||t}));return[t,(0,r.useCallback)((e=>{var t;n(e),t=e,
Te.setValue("symboledit.exchangefilter",t.value())}),[])]}function Be(e){const[t,n]=(0,r.useState)((()=>{if(1===e.types.length)return e.types[0].value;const t=Te.getValue("symboledit.filter",(0,k.getAllSymbolTypesValue)());return e.types.find((e=>e.value===t))?t:(0,k.getAllSymbolTypesValue)()}));return[t,(0,r.useCallback)((e=>{var t;n(e),t=e,Te.setValue("symboledit.filter",t)}),[])]}var Me=n(37968),Oe=n(82708),Ae=n(77248),ze=n(76460),Fe=n(62393);const Qe=(0,k.getAvailableSearchSources)(),Pe=(0,k.getDefaultSearchSource)(),Ue=u.enabled("uppercase_instrument_names");function We(e){var t;const{onClose:o,initialMode:a,defaultValue:m="",showSpreadActions:p,hideMarkedListFlag:g,selectSearchOnInit:f=!0,onSearchComplete:y,dialogTitle:b=l.t(null,void 0,n(75905)),placeholder:S,fullscreen:x,initialScreen:w,wrapper:C,dialog:E,contentItem:L,footer:N,searchInput:I,emptyState:R,autofocus:_,dialogWidth:T,onKeyDown:D,searchSourcesScreen:B,customSearchSymbols:M,isDisableFiltering:A,disableRecents:z,shouldReturnFocus:P,onSymbolFiltersParamsChange:U}=e,V=(0,r.useMemo)((()=>A?[]:e.symbolTypes?e.symbolTypes:(0,k.getAvailableSymbolTypes)()),[]),Z=void 0!==e.input,K=A?[]:Qe,[q,j]=(0,r.useState)(a),H=(0,r.useRef)(m),[G,$]=De({searchSources:K,defaultSearchSource:Pe}),[Y,X]=[],[J,ee]=Be({types:V}),[te,ne]=[{},()=>{}],[re,oe]=(0,r.useState)(!1),[ae,le]=(0,r.useState)(-1),[se,ie]=(0,r.useState)("noop"),ce=k.isSeparateSymbolSearchTabs?TAB_SELECT_FILTER_MAP:void 0,ue=k.isSeparateSymbolSearchTabs?(null==Y?void 0:Y[J])||Pe:G,de=(0,r.useMemo)((()=>{if(!k.isSeparateSymbolSearchTabs)return K;return K.filter((e=>{const t=TAB_FILTER_MAP[J];if(!J)return!0;const n=e.group();return n===ExchangeGroup.AllExchanges||n&&t.value.includes(n)}))}),[K,J]),[me,pe]=(0,r.useState)((()=>({canChangeExchange:Boolean(G&&Qe.length>1&&!(0,k.exchangeSelectDisabled)(J)),tabSelectFilters:null==ce?void 0:ce[J],withFilters:!!J,renderSymbolSearchList:[],token:H.current,symbolsRemaining:0,currentSymbolType:J,currentSelectedSearchSource:ue,currentTabAvailableSearchSources:de,symbolStartIndex:0}))),he=(0,r.useCallback)((e=>{trackEvent("New SS",J,"Change sources"),null==X||X(J,e),pe((t=>({...t,currentSelectedSearchSource:e})))}),[J,pe]),ge=(0,r.useRef)(null!==(t=e.input)&&void 0!==t?t:null),[ve,fe]=(0,r.useState)(!1),ye=(0,Me.useForceUpdate)(),[be,Se]=(0,r.useState)(new Set),{broker:xe=null,brokerId:we,unhideSymbolSearchGroups:ke="",displayBrokerSymbol:Ce=!1}={brokerId:void 0};(0,r.useLayoutEffect)((()=>{var e;!(null==ge?void 0:ge.current)||!Z&&Boolean(null===(e=ge.current)||void 0===e?void 0:e.value)||(Z||"compare"===q||(ge.current.value=H.current),!_||Z&&"symbolSearch"!==q||ge.current.focus())}),[q]),(0,r.useEffect)((()=>{(null==ge?void 0:ge.current)&&f&&_&&ge.current.select()}),[]);const Ee=(0,r.useMemo)((()=>me.renderSymbolSearchList.reduce(((e,t)=>{const n=Ke(t),r=be.has(n);return e.push(t),r&&t.contracts&&e.push(...t.contracts.map((e=>({...e,parent:t})))),e}),[])),[me.renderSymbolSearchList,be]),Le=(0,r.useRef)(null);(0,r.useEffect)((()=>{var e
;-1!==ae&&(null===(e=Le.current)||void 0===e||e.scrollIntoView({block:"nearest"}))}),[ae,Le]);const Te=i.includes(J),We=(0,r.useMemo)((()=>Ee.map(((e,t)=>{var n,r,o,a;if(F(e)){const o=Ke(e),a=e.contracts?be.has(o):void 0,l=t===ae;return{key:t,id:o,title:Ze(e,Ce),description:e.description,isOffset:!1,onClick:rt.bind(null,e),providerId:e.provider_id,source:e.source,source2:e.source2,country:null===(n=e.country)||void 0===n?void 0:n.toLocaleLowerCase(),type:e.type,exchangeName:null===e.exchange?void 0:e.exchange,exchangeTooltip:"",prefix:e.prefix||void 0,marketType:(0,Re.marketType)(e.type,e.typespecs,!1),hideMarketType:Te,isEod:(null===(r=e.params)||void 0===r?void 0:r.includes("eod"))&&"economic"!==e.type,isYield:(0,Ae.isYield)(e),isExpanded:a,onExpandClick:e.contracts?ot.bind(null,o):void 0,fullSymbolName:e.contracts?_e.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):_e.QualifiedSources.fromSymbolSearchResult(e),itemRef:l?Le:void 0,isSelected:t===ae,hideMarkedListFlag:g,item:e,logoId:e.logoid,currencyLogoId:e["currency-logoid"],baseCurrencyLogoId:e["base-currency-logoid"],shortName:(0,Oe.safeShortName)(_e.QualifiedSources.fromSymbolSearchResult(e)),currencyCode:e.currency_code,isPrimary:e.is_primary_listing}}{const{parent:n}=e,r=Ke(n),l=t===ae;return{key:t,id:r+e.symbol,dangerousTitleHTML:Ze(e,Ce),dangerousDescriptionHTML:`${n.description}`+(e.description?` (${e.description})`:""),isOffset:!0,isEod:null===(o=e.params)||void 0===o?void 0:o.includes("eod"),isYield:(0,Ae.isYield)(e),onClick:at.bind(null,e.parent,e),providerId:n.provider_id,country:null===(a=n.country)||void 0===a?void 0:a.toLowerCase(),type:n.type,exchangeName:null===n.exchange?void 0:n.exchange,exchangeTooltip:"",marketType:(0,Re.marketType)(n.type,e.typespecs,!1),hideMarketType:Te,fullSymbolName:_e.QualifiedSources.fromSymbolSearchResult(e.parent,e),itemRef:l?Le:void 0,isSelected:l,hideMarkedListFlag:g,item:e}}}))),[me.renderSymbolSearchList,be,q,ae,D]),He=(0,r.useMemo)((()=>function(e,t,n){const r=null==t?void 0:t[e],o=new Map(null==r?void 0:r.map((e=>[e.id,e.urlParam]))),a=n[e];let l;if(a){l={};for(const[e,t]of Object.entries(a)){const n=o.get(e);n&&(l[n]=t)}}return l}(J,ce,te)),[J,ce,te]),Ge=(0,r.useMemo)((()=>me.renderSymbolSearchList.slice(0,20).map((e=>e.contracts?_e.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):_e.QualifiedSources.fromSymbolSearchResult(e)))),[me.renderSymbolSearchList]);(0,r.useEffect)((()=>{var e,t;if(!U)return;const n=["resetFilter","resetTabFilter","empty"].includes(se)?[]:Ge,r={...He,result_list:n};r.search_type||(r.search_type="bitcoin,crypto"===J?"crypto":J);const o=TAB_FILTER_MAP[J];J&&("country"===o.id?r.country=null!==(e=null==ue?void 0:ue.getRequestCountryValue())&&void 0!==e?e:null:r.exchange=null!==(t=null==ue?void 0:ue.getRequestExchangeValue())&&void 0!==t?t:null),U(r)}),[J,He,Ge,ue,se]);const $e=null!=E?E:je,Ye=$e!==je&&!Z,Xe=(e,t)=>{var n;return{mode:q,setMode:j,selectedSearchSource:ue,setSelectedSearchSource:k.isSeparateSymbolSearchTabs?he:$,
isAllSearchSourcesSelected:v.isAllSearchSourcesSelected,allSearchSourcesTitle:k.isSeparateSymbolSearchTabs?null===(n=TAB_FILTER_MAP[me.currentSymbolType])||void 0===n?void 0:n.allSearchSourcesTitle:void 0,selectedSymbolType:J,setSelectedSymbolType:ee,selectedIndex:ae,setSelectedIndex:le,onClose:o,setSymbolSearchContent:pe,symbolSearchContent:me,searchRef:ge,cachedInputValue:H,searchSpreads:re,setSearchSpreads:oe,handleListWidth:lt,isSmallWidth:ve,feedItems:We,isMobile:e,showSpreadActions:p,selectSearchOnInit:f,isTablet:t,selectedItem:Ee[ae],forceUpdate:ye,placeholder:S,initialScreen:w,toggleExpand:ot,openedItems:be,onSubmit:ct,onSearchComplete:y,footer:N,symbolTypes:V,contentItem:L,searchInput:I,emptyState:R,autofocus:_,upperCaseEnabled:Ue,externalInput:Z,handleKeyDown:Ye?void 0:it,customSearchSymbols:M,searchSources:de,filterDefinitions:ce,selectedFilterValues:te,setSelectedFilterValues:ne,filterQueryParams:He,symbolSearchState:se,setSymbolSearchState:ie}},Je=null!=B?B:O,et="exchange"===q,tt=et?{title:l.t(null,void 0,n(19724)),dataName:"exchanges-search",render:()=>r.createElement(Je,{searchSources:me.currentTabAvailableSearchSources}),additionalHeaderElement:r.createElement(Ie.BackButton,{onClick:()=>j("symbolSearch"),className:Fe.backButton,size:"medium","aria-label":l.t(null,{context:"input"},n(16936)),preservePaddings:!0}),additionalElementPos:"before"}:{title:b,dataName:"symbol-search-items-dialog",render:()=>r.createElement(Ne,null),additionalElementPos:"after"},nt=null!=C?C:"div";return r.createElement(nt,null,r.createElement(c.MatchMediaMap,{rules:d.DialogBreakpoints},(({TabletSmall:e,TabletNormal:t})=>r.createElement(h.SymbolSearchItemsDialogContext.Provider,{value:Xe(e,t)},r.createElement($e,{...tt,shouldReturnFocus:P,fullScreen:x,onClose:o,onClickOutside:o,onKeyDown:Ye?void 0:it,isOpened:!0})))));function rt(e,t){if(e.contracts)return e.contracts.length?void at(e,e.contracts[0],t):void ot(Ke(e));at(e,void 0,t)}function ot(e){const t=new Set(be);t.has(e)?t.delete(e):t.add(e),Se(t)}function at(e,t,n){const r=t||e,{exchange:a}=e;if(u.enabled("show_spread_operators")){const e={name:r.symbol,exchange:a,prefix:r.prefix,fullName:r.full_name};if(re)return st(e),void ye();if(ge.current&&ge.current.value.includes(","))return void st(e)}ut([{resolved:!0,symbol:_e.QualifiedSources.fromSymbolSearchResult(e,t),result:r}],n),o()}function lt(e){fe("fixed"===T||e<=640)}function st(e){if(!ge.current)return;const[t,n]=(0,s.getNextSymbolInputValueAndPosition)(ge.current,e,Ue);ge.current.value=t,ge.current.setSelectionRange(n,n),ge.current.focus()}function it(e){switch((0,W.hashFromEvent)(e)){case 38:if(e.preventDefault(),0===ae)return;if(-1===ae)return void le(0);le(ae-1);break;case 40:if(e.preventDefault(),ae===We.length-1)return;le(ae+1);break;case 37:{if(-1===ae)return;const t=We[ae],{id:n,isOffset:r,onExpandClick:o}=t;if(!r&&n&&be.has(n)&&Boolean(o)&&!Boolean(D)&&(e.preventDefault(),ot(n)),o)return void(null==D||D(e,!0));break}case 39:{if(-1===ae)return;const t=We[ae],{id:n,isOffset:r,onExpandClick:o}=t
;if(r||!n||be.has(n)||!Boolean(o)||Boolean(D)||(e.preventDefault(),ot(n)),o)return void(null==D||D(e,!0));break}case 13:e.preventDefault(),ct(!0);break;case 27:if(e.preventDefault(),et)return void j("symbolSearch");o()}null==D||D(e)}function ct(e){if(!ge.current)return;let t=ge.current.value;if(u.enabled("show_spread_operators")&&re&&t){const n=We[ae];if(n&&void 0!==n.isExpanded&&(n.onClick(),t=ge.current.value),t.includes(",")){return ut(qe(t).map(Ve)),void(e&&o())}return ut([{symbol:Ue?t.toUpperCase():t,resolved:!1}]),void(e&&o())}if(t.includes(","))return ut(qe(t).map(Ve)),void(e&&o());if(-1!==ae){We[ae].onClick()}else{const n=Ue?t.toUpperCase():t;if(n&&""!==n.trim()){const e=qe(n);if(void 0!==we&&-1===n.indexOf(":"))(function(e){let t=!1;return Promise.all(e.map((e=>-1!==e.indexOf(":")||t?Promise.resolve({symbol:e,resolved:!1}):(t=!0,async function(e){var t;null===(t=await(null==xe?void 0:xe.accountMetainfo()))||void 0===t||t.prefix;const n=void 0,r=await Q({strictMatch:!0,serverHighlight:!1,text:e,lang:window.language||"",brokerId:we,onlyTradable:!0,unhideSymbolSearchGroups:ke,exchange:n});if(0!==r.symbols.length){const e=r.symbols[0],{contracts:t}=e,n=t&&t.length>0?t[0]:void 0,o=e.prefix||e.exchange,a=n?n.symbol:e.symbol;if(o&&a)return{symbol:_e.QualifiedSources.fromSymbolSearchResult(e,n),resolved:!0,result:e}}return{symbol:e,resolved:!1}}(e)))))})(e).then((e=>ut(e)));else{ut(e.map(Ve))}}e&&o()}}async function ut(e,t){const n=!t||(0,ze.isKeyboardClick)(t);y(e,{symbolType:J,isKeyboardEvent:n})}}function Ve(e){return{symbol:Ue?e.toUpperCase():e,resolved:!1}}function Ze(e,t){const{broker_symbol:n,symbol:r,description:o}=e;return`${"spread"===e.type?o:r}${t&&n?` (${n})`:""}`}function Ke(e){return e.symbol+e.exchange+e.description}function qe(e){return e.split(",").map((e=>e.trim())).filter((e=>""!==e))}function je(e){const{isMobile:t,isTablet:n}=(0,S.useEnsuredContext)(h.SymbolSearchItemsDialogContext);return r.createElement(m.AdaptivePopupDialog,{...e,className:a()(Fe.dialog,!t&&(n?Fe.tabletDialog:Fe.desktopDialog)),backdrop:!0,draggable:!1})}},15983:(e,t,n)=>{"use strict";n.d(t,{flip:()=>s,getCurrentTokenParamsFromInput:()=>v,getExchange:()=>p,getNextSymbolInputValueAndPosition:()=>g,isSpread:()=>u,shortName:()=>m,stringifyTokens:()=>i,validate:()=>l});var r=n(14483),o=n(44254),a=n(81319);function l(e){const t={braceBalance:0,currentState:"var",warnings:[],errors:[]};if(r.enabled("charting_library_base")&&!r.enabled("show_spread_operators"))return t;let n="init";const o=[];for(let r=0;r<e.length;r++){const a=e[r];if("whitespace"!==a.type){if("incompleteSymbol"===a.type||"incompleteNumber"===a.type){const n=r!==e.length-1,o={status:n?"error":"incomplete",reason:"incomplete_token",offset:a.offset,token:a};if(n?t.errors.push(o):t.warnings.push(o),n)continue}switch(a.type){case"symbol":case"number":if("var"===n){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}n="var";break;case"plus":case"minus":case"multiply":case"divide":case"power":if("var"!==n){t.errors.push({
status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}n="operator";break;case"openBrace":if("var"===n){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}o.push(a),n="init";break;case"closeBrace":if("var"!==n){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}o.pop()||t.errors.push({status:"error",reason:"unbalanced_brace",offset:a.offset,token:a}),n="var";break;case"unparsed":t.errors.push({status:"error",reason:"unparsed_entity",offset:a.offset,token:a})}}}for(t.braceBalance=o.length,"var"!==n&&t.warnings.push({status:"incomplete",token:e[e.length-1]});o.length;){const e=o.pop();e&&t.warnings.push({status:"incomplete",reason:"unbalanced_brace",offset:e.offset,token:e})}return t.currentState=n,t}function s(e){const t=function(e){let t,n=0,r=0;for(let o=0;o<e.length;o++){const a=e[o];if("whitespace"!==a.type)switch(n){case 0:if("number"!==a.type||1!=+a.value)return[];n=1;break;case 1:if(1!==n||"divide"!==a.type)return[];n=2,t=o+1;break;case 2:if("openBrace"===a.type)n=3,r=1;else if(c(a.type))return[];break;case 3:"openBrace"===a.type?r++:"closeBrace"===a.type&&(r--,r<=0&&(n=2))}}return e.slice(t)}(e);return t.length?d(t):d((0,o.tokenize)("1/("+i(e)+")"))}function i(e){return e.reduce(((e,t)=>"symbol"===t.type&&o.symbolTokenEscapeRe.test(t.value)?e+`'${t.value}'`:e+t.value),"")}function c(e){return"plus"===e||"minus"===e||"multiply"===e||"divide"===e||"power"===e}function u(e){return e.length>1&&e.some((e=>c(e.type)))}function d(e){e=function(e){const t=[];for(const n of e)"whitespace"!==n.type&&t.push(n);return t}(e);const t=[],n=[];let r;for(let o=0;o<e.length;o++){const a=e[o];switch(a.type){case"plus":case"minus":case"multiply":case"divide":case"power":n.length&&n[n.length-1].minPrecedence>a.precedence&&(n[n.length-1].minPrecedence=a.precedence);break;case"openBrace":r={minPrecedence:1/0,openBraceIndex:o},n.push(r);break;case"closeBrace":{if(r=n.pop(),!r)break;const a=e[r.openBraceIndex-1],l=e[o+1],s=a&&("plus"===a.type||"multiply"===a.type);(!c(null==l?void 0:l.type)||(null==l?void 0:l.precedence)<=r.minPrecedence)&&(!c(null==a?void 0:a.type)||(null==a?void 0:a.precedence)<(null==r?void 0:r.minPrecedence)||(null==a?void 0:a.precedence)===(null==r?void 0:r.minPrecedence)&&s)&&(t.unshift(r.openBraceIndex),t.push(o),n.length&&n[n.length-1].minPrecedence>r.minPrecedence&&(n[n.length-1].minPrecedence=r.minPrecedence))}}}for(let n=t.length;n--;)e.splice(t[n],1);return e}function m(e){return d((0,o.tokenize)(e)).reduce(((e,t)=>{if("symbol"!==t.type)return e+t.value;const[,n]=h(t);return n?e+n:e}),"")}function p(e){const t=function(e){const t=(0,o.tokenize)(e),n=[];return t.forEach((e=>{if("symbol"!==e.type)return;const[t]=h(e);t&&n.push(t)})),n}(e);if(1===t.length)return t[0]}function h(e){const t=/^'?(?:([A-Z0-9_]+):)?(.*?)'?$/i.exec(e.value);return null===t?[void 0,void 0]:[t[1],t[2]]}function g(e,t,n){const r=e.value,[l,s]=v(e,n),i=(0,a.getSymbolFullName)(t),c=o.symbolTokenEscapeRe.test(i)?`'${i}'`:i
;return[r.substring(0,s)+c+r.substring(s+l.length),s+c.length]}function v(e,t){const{value:n,selectionStart:r}=e,a=(0,o.tokenize)(t?n.toUpperCase():n),l=function(e,t){for(let n=0;n<e.length;n++){const r=e[n],o="symbol"===r.type||"incompleteSymbol"===r.type||"number"===r.type;if(r.offset<=t&&t<=r.offset+r.value.length&&o)return r}return null}(a,r||0);return[(null==l?void 0:l.value)||"",l?l.offset:n.length,a]}},81319:(e,t,n)=>{"use strict";n.d(t,{exchangeSelectDisabled:()=>m,getAllSymbolTypesValue:()=>d,getAvailableSearchSources:()=>c,getAvailableSymbolTypes:()=>u,getDefaultSearchSource:()=>i,getSymbolFullName:()=>s,isSeparateSymbolSearchTabs:()=>p});var r=n(44352),o=n(20882);class a{constructor(e){this._exchange=e}value(){return this._exchange.value}name(){return(0,o.isAllSearchSourcesSelected)(this)?r.t(null,void 0,n(64498)):this._exchange.name}description(){return this._exchange.desc}country(){return this._exchange.country}providerId(){return this._exchange.providerId}group(){return this._exchange.group}includes(e){return function(e,t){const n=t.toLowerCase(),{name:r,desc:o,searchTerms:a}=e;return r.toLowerCase().includes(n)||o.toLowerCase().includes(n)||void 0!==a&&a.some((e=>e.toLowerCase().includes(n)))}(this._exchange,e)}getRequestExchangeValue(){return this._exchange.value}getRequestCountryValue(){}}var l=n(3685);function s(e){if(e.fullName)return e.fullName;let t;return t=e.prefix||e.exchange?(e.prefix||e.exchange)+":"+e.name:e.name,t.replace(/<\/?[^>]+(>|$)/g,"")}function i(){const e=c();return e.find(o.isAllSearchSourcesSelected)||e[0]||null}function c(){return(0,o.createSearchSources)(a,(0,l.getExchanges)())}function u(){return window.ChartApiInstance.supportedSymbolsTypes()}function d(){return""}function m(e){return!(!p||""!==e)}const p=!1},82708:(e,t,n)=>{"use strict";n.d(t,{safeShortName:()=>o});var r=n(79982);function o(e){try{return(0,r.shortName)(e)}catch(t){return e}}},44254:(e,t,n)=>{"use strict";n.d(t,{symbolTokenEscapeRe:()=>l,tokenize:()=>c});var r=n(14483),o=n(18429);const a=r.enabled("charting_library_base")?/(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0370-\u1FFF_\u2E80-\uFFFF^])(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0020\u0370-\u1FFF_\u2E80-\uFFFF_!:.&])*|'.+?'/:/(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0370-\u1FFF_\u2E80-\uFFFF])(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0020\u0370-\u1FFF_\u2E80-\uFFFF_!|:.&])*|'.+?'/,l=/[+\-/*]/,s={number:/\d+(?:\.\d*|(?![a-zA-Z0-9_!:.&]))|\.\d+/,incompleteNumber:/\./,symbol:a,incompleteSymbol:/'[^']*/,separatorPrefix:o.SEPARATOR_PREFIX,openBrace:"(",closeBrace:")",plus:"+",minus:"-",multiply:"*",divide:"/",power:"^",whitespace:/[\0-\x20\s]+/,unparsed:null},i=new RegExp(Object.values(s).map((e=>{return null===e?"":`(${"string"==typeof e?(t=e,t.replace(/[\^$()[\]{}*+?|\\]/g,"\\$&")):e.source})`;var t})).filter((e=>""!==e)).concat(".").join("|"),"g");function c(e){if(!e)return[];const t=[],n=Object.keys(s);let r;for(;r=i.exec(e);){let e=!1;for(let o=n.length;o--;)if(r[o+1]){n[o]&&t.push({value:r[o+1],type:n[o],precedence:0,offset:r.index}),e=!0;break}e||t.push({value:r[0],type:"unparsed",precedence:0,
offset:r.index})}return t}},93251:(e,t,n)=>{"use strict";n.d(t,{removeUsdFromCryptoPairLogos:()=>l,resolveLogoUrls:()=>a});var r=n(36279);const o=(0,r.getLogoUrlResolver)();function a(e,t=r.LogoSize.Medium){const n=e.logoid,a=e["base-currency-logoid"],l=e["currency-logoid"],s=n&&o.getSymbolLogoUrl(n,t);if(s)return[s];const i=a&&o.getSymbolLogoUrl(a,t),c=l&&o.getSymbolLogoUrl(l,t);return i&&c?[i,c]:i?[i]:c?[c]:[]}function l(e){return 2!==e.length?e:function(e){return e.some((e=>s(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!s(e)))}(e)?e.filter((e=>!s(e))):e}function s(e){return!1}},44747:(e,t,n)=>{"use strict";n.d(t,{getBlockStyleClasses:()=>o,getLogoStyleClasses:()=>a});var r=n(97754);function o(e,t){return r("tv-circle-logo-pair",`tv-circle-logo-pair--${e}`,t)}function a(e,t){return r("tv-circle-logo-pair__logo",`tv-circle-logo-pair__logo--${e}`,!t&&"tv-circle-logo-pair__logo-empty")}},76068:(e,t,n)=>{"use strict";n.d(t,{CircleLogo:()=>a});var r=n(50959),o=n(58492);n(45300);function a(e){var t,n;const a=(0,o.getStyleClasses)(e.size,e.className),l=null!==(n=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==n?n:"";return(0,o.isCircleLogoWithUrlProps)(e)?r.createElement("img",{className:a,src:e.logoUrl,alt:l,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):r.createElement("span",{className:a,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},58492:(e,t,n)=>{"use strict";n.d(t,{getStyleClasses:()=>o,isCircleLogoWithUrlProps:()=>a});var r=n(97754);function o(e,t){return r("tv-circle-logo",`tv-circle-logo--${e}`,t)}function a(e){return"logoUrl"in e&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},19785:(e,t,n)=>{"use strict";n.d(t,{createRegExpList:()=>a,getHighlightedChars:()=>l,rankedSearch:()=>o});var r=n(1722);function o(e){const{data:t,rules:n,queryString:o,isPreventedFromFiltering:a,primaryKey:l,secondaryKey:s=l,optionalPrimaryKey:i,tertiaryKey:c}=e;return t.map((e=>{const t=i&&e[i]?e[i]:e[l],a=e[s],u=c&&e[c];let d,m=0;return n.forEach((e=>{var n,l,s,i,c;const{re:p,fullMatch:h}=e;if(p.lastIndex=0,(0,r.isString)(t)&&t&&t.toLowerCase()===o.toLowerCase())return m=4,void(d=null===(n=t.match(h))||void 0===n?void 0:n.index);if((0,r.isString)(t)&&h.test(t))return m=3,void(d=null===(l=t.match(h))||void 0===l?void 0:l.index);if((0,r.isString)(a)&&h.test(a))return m=2,void(d=null===(s=a.match(h))||void 0===s?void 0:s.index);if((0,r.isString)(a)&&p.test(a))return m=2,void(d=null===(i=a.match(p))||void 0===i?void 0:i.index);if(Array.isArray(u))for(const e of u)if(h.test(e))return m=1,void(d=null===(c=e.match(h))||void 0===c?void 0:c.index)})),{matchPriority:m,matchIndex:d,item:e}})).filter((e=>a||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}
function a(e,t){const n=[],r=e.toLowerCase(),o=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${s(e)}`:s(e)})`)).join("(.*?)")+"(.*)";return n.push({fullMatch:new RegExp(`(${s(e)})`,"i"),re:new RegExp(`^${o}`,"i"),reserveRe:new RegExp(o,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(r)&&n.push({fullMatch:t[r],re:t[r],fuzzyHighlight:!1}),n}function l(e,t,n){const r=[];return e&&n?(n.forEach((e=>{const{fullMatch:n,re:o,reserveRe:a}=e;n.lastIndex=0,o.lastIndex=0;const l=n.exec(t),s=l||o.exec(t)||a&&a.exec(t);if(e.fuzzyHighlight=!l,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const n=s[t],o=s[t].length;if(t%2){const t=n.startsWith(" ")||n.startsWith("/")||n.startsWith("-");r[t?e+1:e]=!0}e+=o}}else for(let e=0;e<s[0].length;e++)r[s.index+e]=!0})),r):r}function s(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}},24637:(e,t,n)=>{"use strict";n.d(t,{HighlightedText:()=>s});var r=n(50959),o=n(97754),a=n(19785),l=n(75623);function s(e){const{queryString:t,rules:n,text:s,className:i}=e,c=(0,r.useMemo)((()=>(0,a.getHighlightedChars)(t,s,n)),[t,n,s]);return r.createElement(r.Fragment,null,c.length?s.split("").map(((e,t)=>r.createElement(r.Fragment,{key:t},c[t]?r.createElement("span",{className:o(l.highlighted,i)},e):r.createElement("span",null,e)))):s)}},78036:(e,t,n)=>{"use strict";n.d(t,{useEnsuredContext:()=>a});var r=n(50959),o=n(50151);function a(e){return(0,o.ensureNotNull)((0,r.useContext)(e))}},37968:(e,t,n)=>{"use strict";n.d(t,{useForceUpdate:()=>o});var r=n(50959);const o=()=>{const[,e]=(0,r.useReducer)((e=>e+1),0);return e}},29006:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>r.useResizeObserver});var r=n(67842)},77975:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>o});var r=n(50959);const o=(e,t=!1)=>{const n="watchedValue"in e?e.watchedValue:void 0,o="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[a,l]=(0,r.useState)(n?n.value():o);return(t?r.useLayoutEffect:r.useEffect)((()=>{if(n){l(n.value());const e=e=>l(e);return n.subscribe(e),()=>n.unsubscribe(e)}return()=>{}}),[n]),a}},84877:(e,t,n)=>{"use strict";n.d(t,{MatchMediaMap:()=>l});var r=n(50959),o=n(66783),a=n.n(o);class l extends r.Component{constructor(e){super(e),this._handleMediaChange=()=>{const e=i(this.state.queries,((e,t)=>t.matches));let t=!1;for(const n in e)if(e.hasOwnProperty(n)&&this.state.matches[n]!==e[n]){t=!0;break}t&&this.setState({matches:e})};const{rules:t}=this.props;this.state=s(t)}shouldComponentUpdate(e,t){return!a()(e,this.props)||(!a()(t.rules,this.state.rules)||!a()(t.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}componentDidUpdate(e,t){a()(e.rules,this.props.rules)||this._migrate(t.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(e,t){if(a()(e.rules,t.rules))return null;const{rules:n}=e;return s(n)}_migrate(e,t){null!==e&&i(e,((e,t)=>{t.removeListener(this._handleMediaChange)})),null!==t&&i(t,((e,t)=>{
t.addListener(this._handleMediaChange)}))}}function s(e){const t=i(e,((e,t)=>window.matchMedia(t)));return{queries:t,matches:i(t,((e,t)=>t.matches)),rules:{...e}}}function i(e,t){const n={};for(const r in e)e.hasOwnProperty(r)&&(n[r]=t(r,e[r]));return n}},1109:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>l});var r=n(50959),o=n(97754),a=n(45719);function l(e){return r.createElement("div",{className:o(a.separator,e.className)})}},63932:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>l});var r=n(50959),o=n(97754),a=n(58096);n(83135);function l(e){const t=o(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${a.spinnerSizeMap[e.size||a.DEFAULT_SIZE]}`);return r.createElement("div",{className:t,style:e.style,role:"progressbar"})}},10381:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>i});var r=n(50959),o=n(97754),a=n(9745),l=n(34587),s=n(578);function i(e){const{dropped:t,className:n}=e;return r.createElement(a.Icon,{className:o(n,l.icon,{[l.dropped]:t}),icon:s})}},78029:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},31409:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>s,ToolWidgetButton:()=>i});var r=n(50959),o=n(97754),a=n(9745),l=n(78029);const s=l,i=r.forwardRef(((e,t)=>{const{tag:n="div",icon:s,endIcon:i,isActive:c,isOpened:u,isDisabled:d,isGrouped:m,isHovered:p,onClick:h,text:g,textBeforeIcon:v,title:f,theme:y=l,className:b,forceInteractive:S,inactive:x,"data-name":w,"data-tooltip":k,...C}=e,E=o(b,y.button,(f||k)&&"apply-common-tooltip",{[y.isActive]:c,[y.isOpened]:u,[y.isInteractive]:(S||Boolean(h))&&!d&&!x,[y.isDisabled]:Boolean(d||x),[y.isGrouped]:m,[y.hover]:p}),L=s&&("string"==typeof s?r.createElement(a.Icon,{className:y.icon,icon:s}):r.cloneElement(s,{className:o(y.icon,s.props.className)}));return"button"===n?r.createElement("button",{...C,ref:t,type:"button",className:o(E,y.accessible),disabled:d&&!x,onClick:h,title:f,"data-name":w,"data-tooltip":k},v&&g&&r.createElement("div",{className:o("js-button-text",y.text)},g),L,!v&&g&&r.createElement("div",{className:o("js-button-text",y.text)},g)):r.createElement("div",{...C,ref:t,"data-role":"button",className:E,onClick:d?void 0:h,title:f,"data-name":w,"data-tooltip":k},v&&g&&r.createElement("div",{className:o("js-button-text",y.text)},g),L,!v&&g&&r.createElement("div",{className:o("js-button-text",y.text)},g),i&&r.createElement(a.Icon,{icon:i,className:l.endIcon}))}))},24658:(e,t,n)=>{"use strict";n.d(t,{marketType:()=>s});var r=n(44352);const o=new Map([["cfd",r.t(null,void 0,n(87592))],["dr",r.t(null,void 0,n(67245))],["index",r.t(null,void 0,n(12754))],["forex",r.t(null,void 0,n(39512))],["right",r.t(null,{context:"symbol_type"
},n(9898))],["bond",r.t(null,void 0,n(79852))],["bitcoin",r.t(null,void 0,n(8448))],["crypto",r.t(null,void 0,n(8448))],["economic",r.t(null,void 0,n(88720))],["indices",r.t(null,void 0,n(60804))],["futures",r.t(null,void 0,n(81859))],["stock",r.t(null,void 0,n(36931))],["commodity",r.t(null,void 0,n(12629))]]);n(42053);const a=new Map,l=new Set(["cfd","spreadbet","defi","sharia","yield","government","corporate","mutual","money","etf","unit","trust","reit","etn","convertible","closedend","crypto","oracle"]);function s(e,t=[],n=!0){const r=t.filter((e=>l.has(e))),s=`${e}_${r.sort().join("_")}`,i=a.get(s);if(void 0!==i)return i;const c=n?function(e){return o.get(e)||e}(e):e,u=Boolean(t.length)?[c,...r].join(" "):c;return a.set(s,u),u}},2948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M5.5 6.44a.75.75 0 1 0-1 1.12l1-1.12zM9 10.5l-.5.56c.29.25.71.25 1 0L9 10.5zm4.5-2.94a.75.75 0 0 0-1-1.12l1 1.12zm-9 0l4 3.5 1-1.12-4-3.5-1 1.12zm5 3.5l4-3.5-1-1.12-4 3.5 1 1.12z"/></svg>'},52019:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.35 5.35a.5.5 0 0 0-.7-.7L9 8.29 5.35 4.65a.5.5 0 1 0-.7.7L8.29 9l-3.64 3.65a.5.5 0 0 0 .7.7L9 9.71l3.65 3.64a.5.5 0 0 0 .7-.7L9.71 9l3.64-3.65z"/></svg>'},95694:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},49498:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},60176:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},35369:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},58478:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},73063:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},14127:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},18073:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},99243:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},42576:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},91540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M2.5 14.5c1.68-1.26 3.7-2 6.5-2s4.91.74 6.5 2m-13-11c1.68 1.26 3.7 2 6.5 2s4.91-.74 6.5-2"/><circle stroke="currentColor" cx="9" cy="9" r="8.5"/><path stroke="currentColor" d="M13.5 9c0 2.42-.55 4.58-1.4 6.12-.87 1.56-1.98 2.38-3.1 2.38s-2.23-.82-3.1-2.38c-.85-1.54-1.4-3.7-1.4-6.12s.55-4.58 1.4-6.12C6.77 1.32 7.88.5 9 .5s2.23.82 3.1 2.38c.85 1.54 1.4 3.7 1.4 6.12z"/></svg>'},7720:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 17" width="17" height="17" fill="currentColor"><path d="m.58 1.42.82-.82 15 15-.82.82z"/><path d="m.58 15.58 15-15 .82.82-15 15z"/></svg>'},66619:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#B2B5BE" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},67562:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#131722" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},69859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},69533:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},486:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h9"/><circle fill="currentColor" cx="7" cy="3" r="1"/><circle fill="currentColor" cx="7" cy="10" r="1"/></svg>'},63861:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><g fill="none" fill-rule="evenodd" stroke="currentColor"><path stroke-linecap="square" stroke-linejoin="round" d="M3.5 10V2.5L1 5"/><path stroke-linecap="square" d="M1.5 10.5h4"/><path d="M8 12l3-11"/></g></svg>'},81574:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8"/></svg>'},32617:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 10l7-7M3 3l7 7"/></svg>'},35119:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8m-4-4v8"/></svg>'},69135:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 7l3.5-3.5L10 7"/></svg>'},64494:(e,t,n)=>{"use strict";e.exports=n.p+"flag-square-mock-dark.16b5f3a431f502b03ae3.svg"},2495:(e,t,n)=>{"use strict";e.exports=n.p+"flag-square-mock.d201313017eb2c1b989f.svg"}}]);