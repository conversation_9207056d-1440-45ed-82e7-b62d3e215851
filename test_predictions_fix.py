"""
Test script to verify the predictions fix is working
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_safe_get_live_data():
    """Test the fixed safe_get_live_data function"""
    print("🧪 Testing safe_get_live_data Fix")
    print("=" * 40)
    
    try:
        # Import the fixed function
        from app.pages.predictions_consolidated import safe_get_live_data
        
        # Test with COMI symbol
        live_data = safe_get_live_data("COMI")
        
        if live_data is not None and not live_data.empty:
            print("✅ safe_get_live_data works!")
            print(f"   Columns: {list(live_data.columns)}")
            print(f"   Shape: {live_data.shape}")
            print(f"   Close price: {live_data['Close'].iloc[0]}")
            print(f"   Date: {live_data['Date'].iloc[0]}")
            print(f"   Source: {live_data['source'].iloc[0]}")
            
            # Check required columns for predictions
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in live_data.columns]
            
            if not missing_columns:
                print("✅ All required columns present for predictions")
                return True
            else:
                print(f"❌ Missing columns: {missing_columns}")
                return False
        else:
            print("❌ safe_get_live_data returned None or empty DataFrame")
            return False
            
    except Exception as e:
        print(f"❌ Error testing safe_get_live_data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_prediction_engine():
    """Test the unified prediction engine with live data"""
    print("\n🧪 Testing Unified Prediction Engine")
    print("=" * 40)
    
    try:
        # Set up session state for testing
        import streamlit as st
        
        # Mock session state
        class MockSessionState:
            def __init__(self):
                self.historical_data = None
                self.symbol = "COMI"
        
        # Load historical data
        from app.utils.common import load_stock_data
        historical_data = load_stock_data("COMI")
        
        if historical_data is None or historical_data.empty:
            print("❌ No historical data available for testing")
            return False
        
        # Mock streamlit session state
        st.session_state = MockSessionState()
        st.session_state.historical_data = historical_data
        
        print(f"✅ Historical data loaded: {len(historical_data)} rows")
        
        # Test the unified prediction engine
        from app.pages.predictions_consolidated import unified_prediction_engine
        
        # Test with a simple horizon
        horizons = [4]  # 4 minutes
        model_type = "rf"  # Random Forest
        
        print(f"Testing prediction with horizons: {horizons}, model: {model_type}")
        
        # This should now work without the "missing ['Close', 'Date']" error
        predictions = unified_prediction_engine(
            symbol="COMI",
            horizons=horizons,
            model_type=model_type,
            use_live_data=True
        )
        
        print(f"✅ Predictions generated: {predictions}")
        
        if 4 in predictions:
            predicted_price = predictions[4]
            print(f"✅ 4-minute prediction: {predicted_price:.2f} EGP")
            return True
        else:
            print("❌ No prediction for 4-minute horizon")
            return False
            
    except Exception as e:
        print(f"❌ Error testing unified prediction engine: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chromedriver_popup():
    """Test if ChromeDriver opens TradingView properly"""
    print("\n🧪 Testing ChromeDriver TradingView Popup")
    print("=" * 40)
    
    try:
        from scrapers.price_scraper import PriceScraper
        
        # Initialize scraper with visible browser (not headless)
        scraper = PriceScraper(source="tradingview")
        
        # Check if driver is initialized
        if scraper.driver:
            print("✅ ChromeDriver initialized successfully")
            
            # Test navigation to TradingView
            url = "https://www.tradingview.com/symbols/EGX:COMI/"
            print(f"🌐 Navigating to: {url}")
            
            scraper.driver.get(url)
            print("✅ TradingView page loaded successfully")
            
            # Check if page title contains TradingView
            title = scraper.driver.title
            print(f"📄 Page title: {title}")
            
            if "TradingView" in title or "COMI" in title:
                print("✅ TradingView page loaded correctly")
                
                # Keep browser open for 5 seconds so user can see it
                import time
                print("⏳ Keeping browser open for 5 seconds...")
                time.sleep(5)
                
                scraper.close_driver()
                print("✅ Browser closed successfully")
                return True
            else:
                print("❌ TradingView page not loaded correctly")
                scraper.close_driver()
                return False
        else:
            print("❌ ChromeDriver failed to initialize")
            return False
            
    except Exception as e:
        print(f"❌ Error testing ChromeDriver: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔧 Predictions Fix Test")
    print("=" * 50)
    
    tests = [
        test_safe_get_live_data,
        test_unified_prediction_engine,
        test_chromedriver_popup
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The predictions fix should work.")
        print("\n📝 What's fixed:")
        print("1. ✅ safe_get_live_data properly converts scraper data format")
        print("2. ✅ No more 'Incomplete live data: missing ['Close', 'Date']' errors")
        print("3. ✅ Unified prediction engine works with live data")
        print("4. ✅ ChromeDriver opens TradingView correctly")
        print("\n🚀 Your prediction errors should be completely resolved!")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
