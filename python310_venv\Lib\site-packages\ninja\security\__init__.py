from ninja.security.apikey import <PERSON><PERSON><PERSON><PERSON>ookie, APIKeyHeader, APIKeyQuery
from ninja.security.http import Http<PERSON><PERSON><PERSON><PERSON><PERSON>, HttpBearer
from ninja.security.session import SessionAuth, SessionAuthSuperUser

__all__ = [
    "APIKeyCookie",
    "API<PERSON>eyHeader",
    "APIKeyQuery",
    "HttpBasicAuth",
    "HttpBearer",
    "SessionAuth",
    "SessionAuthSuperUser",
    "django_auth",
    "django_auth_superuser",
]

django_auth = SessionAuth()
django_auth_superuser = SessionAuthSuperUser()
