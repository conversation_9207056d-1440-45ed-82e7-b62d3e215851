import streamlit as st
import pandas as pd
import requests
import json
from datetime import datetime
import logging

# Configure logging
logger = logging.getLogger(__name__)

# TradingView API Configuration
TRADINGVIEW_API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

# EGX Stock symbols
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology", 
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

def show_advanced_technical_analysis():
    """Display professional TradingView-powered technical analysis"""
    
    st.title("📊 Advanced Technical Analysis")
    st.markdown("### Professional TradingView-Powered Analysis for Egyptian Exchange")
    
    # Check API server status
    api_status = check_api_status()
    if not api_status:
        st.error("❌ TradingView API server is not running. Please start the server first.")
        st.info("💡 To start the server, run: `cd TradingViewScraper/src/apidemo && python manage.py runserver 8000`")
        return
    
    st.success("✅ TradingView API server is running")
    
    # Stock selection interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📈 Select Stocks for Analysis")
        
        # Multi-select for stocks
        selected_stocks = st.multiselect(
            "Choose EGX stocks to analyze:",
            options=list(EGX_STOCKS.keys()),
            default=["COMI"],
            format_func=lambda x: f"{x} - {EGX_STOCKS[x]}",
            help="Select one or more stocks for comprehensive technical analysis"
        )
    
    with col2:
        st.subheader("⏰ Time Intervals")
        
        # Time interval selection
        selected_intervals = st.multiselect(
            "Analysis timeframes:",
            options=["1D", "1W"],
            default=["1D", "1W"],
            help="Select timeframes for technical analysis"
        )
    
    if not selected_stocks:
        st.warning("⚠️ Please select at least one stock for analysis.")
        return
    
    if not selected_intervals:
        st.warning("⚠️ Please select at least one time interval.")
        return
    
    # Analysis button
    if st.button("🔍 Analyze EGX Stocks", type="primary", use_container_width=True):
        with st.spinner("🔄 Fetching professional technical analysis from TradingView..."):
            
            # Prepare API request
            egx_symbols = [f"EGX-{stock}" for stock in selected_stocks]
            analysis_data = fetch_technical_analysis(egx_symbols, selected_intervals)
            
            if analysis_data:
                display_analysis_results(analysis_data, selected_stocks, selected_intervals)
            else:
                st.error("❌ Failed to fetch technical analysis data. Please try again.")

def check_api_status():
    """Check if TradingView API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_technical_analysis(symbols, intervals):
    """Fetch technical analysis data from TradingView API"""
    try:
        payload = {
            "pairs": symbols,
            "intervals": intervals
        }
        
        response = requests.post(
            TRADINGVIEW_API_URL,
            json=payload,
            timeout=120  # 2 minutes timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('result', {})
        else:
            logger.error(f"API request failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Error fetching technical analysis: {str(e)}")
        return None

def display_analysis_results(data, selected_stocks, selected_intervals):
    """Display comprehensive technical analysis results"""
    
    if not data:
        st.warning("📭 No analysis data received")
        return
    
    st.success("🎯 Technical Analysis Complete!")
    
    # Display results for each stock
    for i, stock in enumerate(selected_stocks):
        egx_symbol = f"EGX-{stock}"
        stock_data = data.get(egx_symbol, [])
        
        if not stock_data:
            st.warning(f"❌ No data available for {stock}")
            continue
        
        # Create expandable section for each stock
        with st.expander(f"📊 {stock} - {EGX_STOCKS[stock]}", expanded=(i == 0)):
            display_stock_analysis(stock, stock_data, selected_intervals)

def display_stock_analysis(stock, stock_data, intervals):
    """Display detailed analysis for a single stock"""
    
    # Get current price from first timeframe data
    current_price = stock_data[0].get('price', 'N/A') if stock_data else 'N/A'
    
    # Price header
    st.markdown(f"### 💰 Current Price: **{current_price:,.0f} EGP**")
    
    # Create tabs for different timeframes
    if len(intervals) > 1:
        tabs = st.tabs([f"📈 {interval} Analysis" for interval in intervals])
        
        for i, interval in enumerate(intervals):
            with tabs[i]:
                timeframe_data = next((data for data in stock_data 
                                     if data.get('oscillators', [{}])[0].get('interval') == interval), None)
                if timeframe_data:
                    display_timeframe_analysis(stock, interval, timeframe_data)
                else:
                    st.warning(f"No data available for {interval} timeframe")
    else:
        # Single timeframe
        interval = intervals[0]
        timeframe_data = stock_data[0] if stock_data else None
        if timeframe_data:
            display_timeframe_analysis(stock, interval, timeframe_data)

def display_timeframe_analysis(stock, interval, data):
    """Display analysis for a specific timeframe"""

    oscillators = data.get('oscillators', [])
    moving_averages = data.get('moving_averages', [])
    pivots = data.get('pivots', [])  # Changed from pivot_points to pivots

    # Overall signal calculation
    osc_signals = calculate_signal_summary(oscillators)
    ma_signals = calculate_signal_summary(moving_averages)

    # Signal summary cards
    col1, col2, col3 = st.columns(3)

    with col1:
        total_buy = osc_signals['buy'] + ma_signals['buy']
        total_sell = osc_signals['sell'] + ma_signals['sell']
        total_neutral = osc_signals['neutral'] + ma_signals['neutral']

        if total_buy > total_sell:
            overall_signal = "🟢 BULLISH"
            signal_color = "green"
        elif total_sell > total_buy:
            overall_signal = "🔴 BEARISH"
            signal_color = "red"
        else:
            overall_signal = "⚪ NEUTRAL"
            signal_color = "gray"

        st.markdown(f"""
        <div style="padding: 1rem; border-radius: 0.5rem; border: 2px solid {signal_color}; text-align: center;">
            <h3 style="margin: 0; color: {signal_color};">{overall_signal}</h3>
            <p style="margin: 0.5rem 0 0 0;">Overall Signal</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.metric(
            "🔄 Oscillators",
            f"{osc_signals['buy']} Buy, {osc_signals['sell']} Sell",
            delta=f"{osc_signals['neutral']} Neutral"
        )

    with col3:
        st.metric(
            "📈 Moving Averages",
            f"{ma_signals['buy']} Buy, {ma_signals['sell']} Sell",
            delta=f"{ma_signals['neutral']} Neutral"
        )

    # Detailed indicators in two columns
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("🔄 Oscillators")
        display_indicators_table(oscillators)

    with col2:
        st.subheader("📈 Moving Averages")
        display_indicators_table(moving_averages)

    # Pivot Points
    if pivots:  # Changed from pivot_points to pivots
        st.subheader("🎯 Pivot Points")
        display_pivot_points(pivots)

def calculate_signal_summary(indicators):
    """Calculate signal summary for indicators"""
    signals = {'buy': 0, 'sell': 0, 'neutral': 0}
    
    for indicator in indicators:
        action = indicator.get('action', '').lower()
        if action == 'buy':
            signals['buy'] += 1
        elif action == 'sell':
            signals['sell'] += 1
        else:
            signals['neutral'] += 1
    
    return signals

def display_indicators_table(indicators):
    """Display indicators in a formatted table"""

    if not indicators:
        st.write("No data available")
        return

    # Create DataFrame for better display
    indicator_data = []
    for indicator in indicators:
        name = indicator.get('name', 'Unknown')
        value = indicator.get('value', 'N/A')
        action = indicator.get('action', 'N/A')

        # Format value
        if isinstance(value, (int, float)) and value != 'N/A':
            if value > 1000:
                formatted_value = f"{value:,.0f}"
            else:
                formatted_value = f"{value:.2f}"
        else:
            formatted_value = str(value)

        # Color code action
        if action.lower() == 'buy':
            action_display = "🟢 Buy"
        elif action.lower() == 'sell':
            action_display = "🔴 Sell"
        else:
            action_display = "⚪ Neutral"

        indicator_data.append({
            'Indicator': name,
            'Value': formatted_value,
            'Signal': action_display
        })

    # Display as DataFrame
    if indicator_data:
        df = pd.DataFrame(indicator_data)
        st.dataframe(df, use_container_width=True, hide_index=True)

def display_pivot_points(pivots):
    """Display pivot points in a formatted table matching the TradingView structure"""

    if not pivots:
        st.write("No pivot point data available")
        return

    # Create the pivot points table data
    pivot_data = []

    for pivot in pivots:
        # Extract values from the PivotDTO structure
        level = pivot.get('pivot', 'Unknown')
        classic = pivot.get('classic')
        fibo = pivot.get('fibo')
        camarilla = pivot.get('camarilla')
        woodie = pivot.get('woodie')
        dm = pivot.get('dm')

        # Format values (handle None values)
        def format_value(val):
            if val is None or val == 0:
                return "-"
            return f"{val:,.0f}" if isinstance(val, (int, float)) else str(val)

        pivot_data.append({
            'Level': level,
            'Classic': format_value(classic),
            'Fibonacci': format_value(fibo),
            'Camarilla': format_value(camarilla),
            'Woodie': format_value(woodie),
            'DM': format_value(dm)
        })

    if pivot_data:
        # Create DataFrame
        df = pd.DataFrame(pivot_data)

        # Display with custom styling to match the screenshot
        st.markdown("""
        <style>
        .pivot-table {
            background-color: #f0f2f6;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .pivot-header {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        </style>
        """, unsafe_allow_html=True)

        # Display the table with enhanced styling
        st.markdown('<div class="pivot-table">', unsafe_allow_html=True)
        st.markdown('<div class="pivot-header">🎯 Pivot Points</div>', unsafe_allow_html=True)

        # Display the dataframe
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Classic": st.column_config.TextColumn("Classic", width="medium"),
                "Fibonacci": st.column_config.TextColumn("Fibonacci", width="medium"),
                "Camarilla": st.column_config.TextColumn("Camarilla", width="medium"),
                "Woodie": st.column_config.TextColumn("Woodie", width="medium"),
                "DM": st.column_config.TextColumn("DM", width="medium")
            }
        )

        st.markdown('</div>', unsafe_allow_html=True)

        # Add explanation
        with st.expander("ℹ️ Understanding Pivot Points"):
            st.markdown("""
            **Pivot Points** are technical analysis indicators used to determine potential support and resistance levels:

            - **R3, R2, R1**: Resistance levels (potential selling points)
            - **P**: Main pivot point (key support/resistance level)
            - **S1, S2, S3**: Support levels (potential buying points)

            **Methods:**
            - **Classic**: Traditional pivot point calculation
            - **Fibonacci**: Uses Fibonacci ratios
            - **Camarilla**: Focuses on intraday trading
            - **Woodie**: Gives more weight to closing price
            - **DM**: Demark's pivot point method
            """)
    else:
        st.write("No pivot point data available")
