"""
Test script to verify the new advanced scraper is working in the app
"""

import sys
import os
import logging

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_scraper_import():
    """Test that the new scraper can be imported"""
    print("🧪 Testing Scraper Import")
    print("=" * 40)
    
    try:
        from scrapers.price_scraper import PriceScraper
        print("✅ Successfully imported PriceScraper")
        
        # Test initialization
        scraper = PriceScraper(source="tradingview")
        print("✅ Successfully initialized PriceScraper")
        
        # Check if it has the new methods
        methods_to_check = [
            'get_price',
            'get_advanced_data_sync', 
            'format_symbol_for_tradingview',
            '_generate_sample_price',
            'get_tradingview_price',
            'login_to_tradingview',
            'initialize_driver'
        ]
        
        for method in methods_to_check:
            if hasattr(scraper, method):
                print(f"✅ Method {method} exists")
            else:
                print(f"❌ Method {method} missing")
                
        return True
        
    except Exception as e:
        print(f"❌ Error importing scraper: {str(e)}")
        return False

def test_scraper_functionality():
    """Test basic scraper functionality"""
    print("\n🧪 Testing Scraper Functionality")
    print("=" * 40)
    
    try:
        from scrapers.price_scraper import PriceScraper
        
        # Initialize scraper
        scraper = PriceScraper(source="tradingview")
        
        # Test get_price method (should work with fallback)
        print("📊 Testing get_price method...")
        price_data = scraper.get_price("COMI")
        
        if price_data:
            print(f"✅ get_price returned data:")
            print(f"   Symbol: {price_data.get('symbol')}")
            print(f"   Price: {price_data.get('price')}")
            print(f"   Currency: {price_data.get('currency')}")
            print(f"   Source: {price_data.get('source')}")
            print(f"   Real-time: {price_data.get('real_time')}")
            
            if 'technical_analysis' in price_data:
                ta = price_data['technical_analysis']
                print(f"   Buy signals: {ta.get('buy_signals')}")
                print(f"   Sell signals: {ta.get('sell_signals')}")
                print(f"   Neutral signals: {ta.get('neutral_signals')}")
                
            return True
        else:
            print("❌ get_price returned no data")
            return False
            
    except Exception as e:
        print(f"❌ Error testing scraper functionality: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_legacy_compatibility():
    """Test legacy method compatibility"""
    print("\n🧪 Testing Legacy Compatibility")
    print("=" * 40)
    
    try:
        from scrapers.price_scraper import PriceScraper
        
        scraper = PriceScraper(source="tradingview")
        
        # Test legacy methods
        legacy_tests = [
            ("get_tradingview_price", "COMI"),
            ("login_to_tradingview", None),
            ("is_logged_in_to_tradingview", None),
            ("initialize_driver", None),
            ("close_driver", None)
        ]
        
        for method_name, symbol in legacy_tests:
            try:
                method = getattr(scraper, method_name)
                if symbol:
                    result = method(symbol)
                    print(f"✅ {method_name}({symbol}): Success")
                    if isinstance(result, dict) and 'price' in result:
                        print(f"   Price: {result['price']} {result.get('currency', 'EGP')}")
                else:
                    result = method()
                    print(f"✅ {method_name}(): {result}")
                    
            except Exception as e:
                print(f"❌ {method_name}: {str(e)}")
                
        return True
        
    except Exception as e:
        print(f"❌ Error testing legacy compatibility: {str(e)}")
        return False

def test_app_context():
    """Test scraper in app context (simulating Streamlit environment)"""
    print("\n🧪 Testing App Context")
    print("=" * 40)
    
    try:
        # Import app components that use the scraper
        from scrapers.price_scraper import PriceScraper
        
        # Simulate how the app uses the scraper
        print("📱 Simulating app usage...")
        
        # Initialize like the app does
        scraper = PriceScraper(
            source="tradingview",
            use_real_time=False,
            username=None,
            password=None,
            is_gmail=False
        )
        
        # Test the main method used by the app
        symbols = ["COMI", "CIB", "ETEL"]
        
        for symbol in symbols:
            try:
                print(f"📊 Testing {symbol}...")
                price_data = scraper.get_price(symbol, use_cache=True)
                
                if price_data:
                    print(f"✅ {symbol}: {price_data['price']} {price_data['currency']} ({price_data['source']})")
                else:
                    print(f"❌ No data for {symbol}")
                    
            except Exception as e:
                print(f"❌ Error with {symbol}: {str(e)}")
                
        return True
        
    except Exception as e:
        print(f"❌ Error testing app context: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_playwright_availability():
    """Test if Playwright is available"""
    print("\n🧪 Testing Playwright Availability")
    print("=" * 40)
    
    try:
        from scrapers.advanced_scraper import PLAYWRIGHT_AVAILABLE
        
        if PLAYWRIGHT_AVAILABLE:
            print("✅ Playwright is available - advanced scraping enabled")
            
            # Test if we can import playwright
            try:
                from playwright.async_api import async_playwright
                print("✅ Playwright async_api imported successfully")
            except Exception as e:
                print(f"⚠️ Playwright import issue: {str(e)}")
                
        else:
            print("⚠️ Playwright not available - will use sample data")
            print("💡 Install with: pip install playwright && playwright install chromium")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking Playwright: {str(e)}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Advanced Scraper Integration Test")
    print("=" * 50)
    
    tests = [
        test_scraper_import,
        test_playwright_availability,
        test_scraper_functionality,
        test_legacy_compatibility,
        test_app_context
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The new advanced scraper is ready to use.")
        print("\n📝 Next steps:")
        print("1. Restart your Streamlit app to clear any cached imports")
        print("2. Test the Live Trading page with real data")
        print("3. The scraper will automatically use advanced features when Playwright is available")
        print("4. Install Playwright for full functionality: pip install playwright && playwright install chromium")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
