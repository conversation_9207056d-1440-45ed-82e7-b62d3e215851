"""
<PERSON><PERSON><PERSON> to start the TradingViewScraper API for the stock prediction app
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_api_status():
    """Check if the TradingViewScraper API is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            return True
    except:
        pass
    return False

def test_api_with_egx():
    """Test the API with EGX stock"""
    try:
        test_data = {
            "pairs": ["EGX-COMI"],
            "intervals": ["1D"]
        }
        
        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ TradingViewScraper API is working with EGX stocks!")
                return True
        
    except Exception as e:
        print(f"⚠️ API test failed: {str(e)}")
    
    return False

def start_tradingview_scraper():
    """Start the TradingViewScraper API"""
    print("🚀 Starting TradingViewScraper API...")
    
    # Check if already running
    if check_api_status():
        print("✅ TradingViewScraper API is already running!")
        if test_api_with_egx():
            return True
        else:
            print("⚠️ API is running but not responding correctly to EGX requests")
    
    # Look for the TradingViewScraper directory
    scraper_dir = Path("TradingViewScraper")
    if not scraper_dir.exists():
        print("❌ TradingViewScraper directory not found!")
        print("💡 Make sure the TradingViewScraper is in the same directory as this script")
        return False
    
    # Look for main.py or app.py in the scraper directory
    possible_files = [
        scraper_dir / "main.py",
        scraper_dir / "app.py", 
        scraper_dir / "src" / "main.py",
        scraper_dir / "src" / "app.py"
    ]
    
    main_file = None
    for file_path in possible_files:
        if file_path.exists():
            main_file = file_path
            break
    
    if not main_file:
        print("❌ Could not find main.py or app.py in TradingViewScraper directory")
        print("📁 Available files:")
        for file in scraper_dir.rglob("*.py"):
            print(f"   {file}")
        return False
    
    try:
        print(f"📡 Starting API from: {main_file}")
        
        # Start the API server
        process = subprocess.Popen([
            sys.executable, str(main_file)
        ], cwd=str(scraper_dir))
        
        # Wait for the server to start
        print("⏳ Waiting for API to start...")
        for i in range(30):  # Wait up to 30 seconds
            time.sleep(1)
            if check_api_status():
                print("✅ TradingViewScraper API started successfully!")
                
                # Test with EGX stock
                if test_api_with_egx():
                    print("🎉 API is ready for EGX stock analysis!")
                    return True
                else:
                    print("⚠️ API started but EGX test failed")
                    return False
        
        print("❌ API failed to start within 30 seconds")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ Error starting API: {str(e)}")
        return False

def main():
    """Main function"""
    print("🌟 TradingViewScraper API Starter")
    print("=" * 50)
    
    success = start_tradingview_scraper()
    
    if success:
        print("\n🎯 TradingViewScraper API is ready!")
        print("📊 Your stock prediction app can now use real TradingView data")
        print("🔗 API URL: http://127.0.0.1:8000/api/scrape_pairs")
        print("\n💡 Keep this terminal open to maintain the API connection")
        print("⏹️ Press Ctrl+C to stop the API")
        
        try:
            # Keep the script running
            while True:
                time.sleep(60)
                if not check_api_status():
                    print("⚠️ API seems to have stopped. Attempting restart...")
                    start_tradingview_scraper()
        except KeyboardInterrupt:
            print("\n🛑 Stopping TradingViewScraper API...")
    else:
        print("\n❌ Failed to start TradingViewScraper API")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure TradingViewScraper directory exists")
        print("2. Check that main.py or app.py exists in TradingViewScraper")
        print("3. Verify all dependencies are installed")
        print("4. Check if port 8000 is available")

if __name__ == "__main__":
    main()
