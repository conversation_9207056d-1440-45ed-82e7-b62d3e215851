"""
Smart Money Concepts (SMC) Indicators
Core SMC analysis functions for professional trading
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class OrderBlock:
    """Represents an order block zone"""
    high: float
    low: float
    timestamp: int
    block_type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    tested: bool = False
    broken: bool = False
    volume: float = 0.0

@dataclass
class FairValueGap:
    """Represents a Fair Value Gap"""
    high: float
    low: float
    timestamp: int
    gap_type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    filled: bool = False
    partially_filled: bool = False
    volume: float = 0.0
    displacement: float = 0.0

@dataclass
class LiquidityZone:
    """Represents a liquidity zone"""
    high: float
    low: float
    timestamp: int
    zone_type: str  # 'buy_side' or 'sell_side'
    strength: float
    swept: bool = False
    volume: float = 0.0

def detect_order_blocks(df: pd.DataFrame, lookback: int = 20, min_strength: float = 0.3) -> List[OrderBlock]:
    """Detect order blocks in price data"""
    if len(df) < lookback * 2:
        return []
    
    order_blocks = []
    
    # Find swing highs and lows
    swing_highs = find_swing_points(df, 'high', lookback)
    swing_lows = find_swing_points(df, 'low', lookback)
    
    # Detect bullish order blocks (from swing lows)
    for idx, low_price in swing_lows.items():
        if idx < lookback or idx >= len(df) - 5:
            continue
            
        swing_candle = df.iloc[idx]
        
        # Check for strong rejection (long wick or strong volume)
        body_size = abs(swing_candle['close'] - swing_candle['open'])
        lower_wick = swing_candle['open'] - swing_candle['low'] if swing_candle['close'] > swing_candle['open'] else swing_candle['close'] - swing_candle['low']
        
        # Order block criteria
        if lower_wick > body_size * 0.5:  # Significant lower wick
            ob_high = max(swing_candle['open'], swing_candle['close'])
            ob_low = swing_candle['low']
            
            # Calculate strength
            volume_strength = min(swing_candle.get('volume', 0) / df['volume'].rolling(20).mean().iloc[idx], 2.0) / 2.0
            wick_strength = min(lower_wick / body_size, 3.0) / 3.0
            strength = (volume_strength + wick_strength) / 2
            
            if strength >= min_strength:
                order_blocks.append(OrderBlock(
                    high=ob_high,
                    low=ob_low,
                    timestamp=idx,
                    block_type='bullish',
                    strength=strength,
                    volume=swing_candle.get('volume', 0)
                ))
    
    # Detect bearish order blocks (from swing highs)
    for idx, high_price in swing_highs.items():
        if idx < lookback or idx >= len(df) - 5:
            continue
            
        swing_candle = df.iloc[idx]
        
        body_size = abs(swing_candle['close'] - swing_candle['open'])
        upper_wick = swing_candle['high'] - swing_candle['open'] if swing_candle['close'] < swing_candle['open'] else swing_candle['high'] - swing_candle['close']
        
        if upper_wick > body_size * 0.5:  # Significant upper wick
            ob_high = swing_candle['high']
            ob_low = min(swing_candle['open'], swing_candle['close'])
            
            volume_strength = min(swing_candle.get('volume', 0) / df['volume'].rolling(20).mean().iloc[idx], 2.0) / 2.0
            wick_strength = min(upper_wick / body_size, 3.0) / 3.0
            strength = (volume_strength + wick_strength) / 2
            
            if strength >= min_strength:
                order_blocks.append(OrderBlock(
                    high=ob_high,
                    low=ob_low,
                    timestamp=idx,
                    block_type='bearish',
                    strength=strength,
                    volume=swing_candle.get('volume', 0)
                ))
    
    # Update order block status
    order_blocks = update_order_block_status(df, order_blocks)
    
    # Sort by timestamp and return most recent
    order_blocks.sort(key=lambda x: x.timestamp, reverse=True)
    return order_blocks[:10]

def detect_fvg(df: pd.DataFrame, min_gap_size: float = 0.001) -> List[FairValueGap]:
    """Detect Fair Value Gaps"""
    if len(df) < 3:
        return []

    fvg_zones = []

    for i in range(2, len(df)):
        candle1 = df.iloc[i-2]  # First candle
        candle2 = df.iloc[i-1]  # Middle candle (displacement candle)
        candle3 = df.iloc[i]    # Third candle

        try:
            c1_high = float(candle1['high'])
            c1_low = float(candle1['low'])
            c2_open = float(candle2['open'])
            c2_close = float(candle2['close'])
            c2_volume = float(candle2.get('volume', 0))
            c3_high = float(candle3['high'])
            c3_low = float(candle3['low'])
        except (TypeError, ValueError):
            continue

        # Bullish FVG: Gap between candle1 high and candle3 low
        if c3_low > c1_high:
            gap_size = c3_low - c1_high
            gap_size_pct = gap_size / c1_high

            if gap_size_pct >= min_gap_size:
                displacement = abs(c2_close - c2_open) / c2_open if c2_open != 0 else 0

                avg_volume = float(df['volume'].rolling(20).mean().iloc[i-1])
                volume_strength = min(c2_volume / avg_volume if avg_volume > 0 else 0, 2.0) / 2.0
                gap_strength = min(gap_size_pct * 100, 5.0) / 5.0
                displacement_strength = min(displacement * 10, 1.0)

                strength = (volume_strength + gap_strength + displacement_strength) / 3

                fvg_zones.append(FairValueGap(
                    high=c3_low,
                    low=c1_high,
                    timestamp=i-1,
                    gap_type='bullish',
                    strength=strength,
                    volume=c2_volume,
                    displacement=displacement
                ))

        # Bearish FVG: Gap between candle1 low and candle3 high
        elif c3_high < c1_low:
            gap_size = c1_low - c3_high
            gap_size_pct = gap_size / c1_low

            if gap_size_pct >= min_gap_size:
                displacement = abs(c2_close - c2_open) / c2_open if c2_open != 0 else 0

                avg_volume = float(df['volume'].rolling(20).mean().iloc[i-1])
                volume_strength = min(c2_volume / avg_volume if avg_volume > 0 else 0, 2.0) / 2.0
                gap_strength = min(gap_size_pct * 100, 5.0) / 5.0
                displacement_strength = min(displacement * 10, 1.0)

                strength = (volume_strength + gap_strength + displacement_strength) / 3

                fvg_zones.append(FairValueGap(
                    high=c1_low,
                    low=c3_high,
                    timestamp=i-1,
                    gap_type='bearish',
                    strength=strength,
                    volume=c2_volume,
                    displacement=displacement
                ))

    # Update FVG status
    fvg_zones = update_fvg_status(df, fvg_zones)

    # Sort by timestamp and return most recent
    fvg_zones.sort(key=lambda x: x.timestamp, reverse=True)
    return fvg_zones[:15]

def detect_liquidity_zones(df: pd.DataFrame, lookback: int = 20) -> List[LiquidityZone]:
    """Detect liquidity zones (areas where stops are likely placed)"""
    if len(df) < lookback * 2:
        return []
    
    liquidity_zones = []
    
    # Find swing highs and lows (potential liquidity areas)
    swing_highs = find_swing_points(df, 'high', lookback)
    swing_lows = find_swing_points(df, 'low', lookback)
    
    # Buy-side liquidity (above swing highs)
    for idx, high_price in swing_highs.items():
        if idx < lookback:
            continue
            
        # Calculate strength based on how many times this level was tested
        test_count = 0
        for i in range(idx + 1, min(idx + 50, len(df))):
            if abs(df.iloc[i]['high'] - high_price) / high_price < 0.005:  # Within 0.5%
                test_count += 1
        
        strength = min(test_count / 3, 1.0)  # Normalize to 0-1
        
        if strength > 0.2:  # Minimum strength threshold
            liquidity_zones.append(LiquidityZone(
                high=high_price * 1.002,  # Small buffer above
                low=high_price * 0.998,   # Small buffer below
                timestamp=idx,
                zone_type='buy_side',
                strength=strength,
                volume=df.iloc[idx].get('volume', 0)
            ))
    
    # Sell-side liquidity (below swing lows)
    for idx, low_price in swing_lows.items():
        if idx < lookback:
            continue
            
        test_count = 0
        for i in range(idx + 1, min(idx + 50, len(df))):
            if abs(df.iloc[i]['low'] - low_price) / low_price < 0.005:
                test_count += 1
        
        strength = min(test_count / 3, 1.0)
        
        if strength > 0.2:
            liquidity_zones.append(LiquidityZone(
                high=low_price * 1.002,
                low=low_price * 0.998,
                timestamp=idx,
                zone_type='sell_side',
                strength=strength,
                volume=df.iloc[idx].get('volume', 0)
            ))
    
    # Sort by timestamp and return most recent
    liquidity_zones.sort(key=lambda x: x.timestamp, reverse=True)
    return liquidity_zones[:10]

def find_swing_points(df: pd.DataFrame, price_type: str, lookback: int) -> Dict[int, float]:
    """Find swing highs or lows"""
    swing_points = {}
    
    for i in range(lookback, len(df) - lookback):
        if price_type == 'high':
            current = df.iloc[i]['high']
            left_max = df.iloc[i-lookback:i]['high'].max()
            right_max = df.iloc[i+1:i+lookback+1]['high'].max()
            
            if current > left_max and current > right_max:
                swing_points[i] = current
                
        elif price_type == 'low':
            current = df.iloc[i]['low']
            left_min = df.iloc[i-lookback:i]['low'].min()
            right_min = df.iloc[i+1:i+lookback+1]['low'].min()
            
            if current < left_min and current < right_min:
                swing_points[i] = current
    
    return swing_points

def update_order_block_status(df: pd.DataFrame, order_blocks: List[OrderBlock]) -> List[OrderBlock]:
    """Update whether order blocks have been tested or broken"""
    for ob in order_blocks:
        start_idx = ob.timestamp + 1
        
        if start_idx >= len(df):
            continue
            
        subsequent_data = df.iloc[start_idx:]
        
        for idx, candle in subsequent_data.iterrows():
            if ob.block_type == 'bullish':
                if candle['low'] <= ob.high and candle['high'] >= ob.low:
                    ob.tested = True
                if candle['close'] < ob.low:
                    ob.broken = True
                    break
            elif ob.block_type == 'bearish':
                if candle['high'] >= ob.low and candle['low'] <= ob.high:
                    ob.tested = True
                if candle['close'] > ob.high:
                    ob.broken = True
                    break
    
    return order_blocks

def update_fvg_status(df: pd.DataFrame, fvg_zones: List[FairValueGap]) -> List[FairValueGap]:
    """Update whether FVGs have been filled or partially filled"""
    for fvg in fvg_zones:
        start_idx = fvg.timestamp + 1
        
        if start_idx >= len(df):
            continue
            
        subsequent_data = df.iloc[start_idx:]
        
        for idx, candle in subsequent_data.iterrows():
            if fvg.gap_type == 'bullish':
                if candle['low'] <= fvg.high and candle['high'] >= fvg.low:
                    fvg.partially_filled = True
                if candle['low'] <= fvg.low:
                    fvg.filled = True
                    break
            elif fvg.gap_type == 'bearish':
                if candle['high'] >= fvg.low and candle['low'] <= fvg.high:
                    fvg.partially_filled = True
                if candle['high'] >= fvg.high:
                    fvg.filled = True
                    break
    
    return fvg_zones
