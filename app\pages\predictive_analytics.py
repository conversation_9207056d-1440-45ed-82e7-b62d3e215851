"""
Predictive Analytics Page
Advanced machine learning-based price prediction and scenario analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Import components
try:
    from app.components.predictive_analytics import PredictiveAnalytics, PredictionResult, ScenarioAnalysis
    from app.components.smc_indicators import detect_order_blocks, detect_fvg, detect_liquidity_zones
    from app.components.advanced_smc_features import (
        detect_break_of_structure, detect_liquidity_sweeps,
        calculate_premium_discount_zones
    )
    # Use existing data loading functionality
    def get_stock_data(symbol: str, period: str = "1y") -> Optional[pd.DataFrame]:
        """Get stock data using existing data loading functionality"""
        try:
            import os
            data_path = f"data/stocks/{symbol}.csv"
            if os.path.exists(data_path):
                df = pd.read_csv(data_path)
                if 'Date' in df.columns:
                    df['Date'] = pd.to_datetime(df['Date'])
                    df.set_index('Date', inplace=True)
                elif 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)

                # Standardize column names
                df.columns = df.columns.str.lower()

                # Return recent data based on period
                if period == "6mo":
                    return df.tail(120)  # ~6 months of trading days
                elif period == "1y":
                    return df.tail(250)  # ~1 year of trading days
                else:
                    return df.tail(100)  # Default
            return None
        except Exception as e:
            logger.error(f"Error loading stock data: {e}")
            return None

    PREDICTIVE_AVAILABLE = True
except ImportError as e:
    logger.error(f"Predictive analytics components not available: {e}")
    PREDICTIVE_AVAILABLE = False

# EGX Stock symbols
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology", 
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

def show_predictive_analytics():
    """Display Predictive Analytics page"""
    
    st.title("🔮 Predictive Analytics")
    st.markdown("### AI-Powered Price Prediction & Scenario Analysis")
    
    if not PREDICTIVE_AVAILABLE:
        st.error("❌ Predictive analytics components are not available. Please check the installation.")
        return
    
    # Sidebar controls
    st.sidebar.header("🎯 Prediction Settings")
    
    # Stock selection
    selected_stock = st.sidebar.selectbox(
        "📈 Select Stock:",
        options=list(EGX_STOCKS.keys()),
        format_func=lambda x: f"{x} - {EGX_STOCKS[x]}",
        index=0
    )
    
    # Time horizon selection
    time_horizons = st.sidebar.multiselect(
        "⏰ Prediction Horizons:",
        options=['1D', '3D', '1W', '2W', '1M'],
        default=['1D', '1W', '2W'],
        help="Select time horizons for predictions"
    )
    
    # Model settings
    st.sidebar.subheader("🤖 Model Settings")
    auto_train = st.sidebar.checkbox("🔄 Auto-train models", value=True, help="Automatically train models on latest data")
    confidence_threshold = st.sidebar.slider("📊 Confidence Threshold", 0.0, 1.0, 0.6, 0.05)
    
    # Analysis type
    analysis_type = st.sidebar.radio(
        "📋 Analysis Type:",
        ["Quick Prediction", "Full Analysis", "Scenario Analysis"],
        help="Choose the depth of analysis"
    )
    
    # Main content
    if st.button("🚀 Generate Predictions", type="primary"):
        with st.spinner("🔮 Generating AI predictions..."):
            results = run_predictive_analysis(
                selected_stock, time_horizons, auto_train, 
                confidence_threshold, analysis_type
            )
            
            if results:
                display_prediction_results(results, analysis_type)
            else:
                st.error("❌ Failed to generate predictions. Please try again.")
    
    # Information section
    st.markdown("---")
    display_predictive_analytics_info()

def run_predictive_analysis(stock: str, horizons: List[str], auto_train: bool, 
                           confidence_threshold: float, analysis_type: str) -> Optional[Dict]:
    """Run comprehensive predictive analysis"""
    
    try:
        # Get stock data
        df = get_stock_data(stock, period="1y")  # Get 1 year of data for training
        if df is None or len(df) < 100:
            st.error(f"❌ Insufficient data for {stock}. Need at least 100 data points.")
            return None
        
        # Prepare SMC analysis for context
        smc_results = prepare_smc_context(df, stock)
        
        # Initialize predictive analytics
        predictor = PredictiveAnalytics()
        
        # Train models if requested
        if auto_train:
            with st.status("🧠 Training ML models...", expanded=True) as status:
                st.write("📊 Preparing training data...")
                training_results = predictor.train_models(df, smc_results)
                
                if "error" in training_results:
                    st.error(f"❌ Training failed: {training_results['error']}")
                    return None
                
                st.write(f"✅ Training completed:")
                st.write(f"   • Training samples: {training_results.get('training_samples', 0)}")
                st.write(f"   • Test samples: {training_results.get('test_samples', 0)}")
                st.write(f"   • Features: {training_results.get('features_count', 0)}")
                
                status.update(label="✅ Models trained successfully!", state="complete")
        
        # Generate predictions
        predictions = predictor.generate_predictions(df, smc_results, horizons)
        
        # Generate scenario analysis if requested
        scenario_analysis = None
        if analysis_type in ["Full Analysis", "Scenario Analysis"]:
            scenario_analysis = predictor.generate_scenario_analysis(df, smc_results)
        
        # Get confidence factors
        confidence_factors = predictor.get_prediction_confidence_factors(df, smc_results)
        
        # Get model performance
        model_performance = predictor.get_model_performance()
        
        return {
            'stock': stock,
            'current_price': df['close'].iloc[-1],
            'predictions': predictions,
            'scenario_analysis': scenario_analysis,
            'confidence_factors': confidence_factors,
            'model_performance': model_performance,
            'data_info': {
                'data_points': len(df),
                'date_range': f"{df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}",
                'last_update': df.index[-1].strftime('%Y-%m-%d %H:%M')
            }
        }
        
    except Exception as e:
        logger.error(f"Error in predictive analysis: {str(e)}")
        st.error(f"❌ Analysis failed: {str(e)}")
        return None

def prepare_smc_context(df: pd.DataFrame, stock: str) -> Dict:
    """Prepare SMC analysis context for predictions"""
    
    try:
        # Basic SMC analysis
        order_blocks = detect_order_blocks(df, lookback=20)
        fvgs = detect_fvg(df, min_gap_size=0.001)
        liquidity_zones = detect_liquidity_zones(df, lookback=20)
        bos_events = detect_break_of_structure(df, lookback=15)
        liquidity_sweeps = detect_liquidity_sweeps(df, liquidity_zones, lookback=10)
        premium_discount = calculate_premium_discount_zones(df, period=50)
        
        # Calculate basic confluence
        confluence_score = min((len(order_blocks) + len(fvgs) + len(liquidity_zones)) / 10, 1.0)
        
        # Determine market structure
        recent_trend = (df['close'].iloc[-1] - df['close'].iloc[-20]) / df['close'].iloc[-20]
        if recent_trend > 0.02:
            trend = 'bullish'
        elif recent_trend < -0.02:
            trend = 'bearish'
        else:
            trend = 'sideways'
        
        return {
            'symbol': stock,
            'current_price': df['close'].iloc[-1],
            'order_blocks': order_blocks[:5],  # Top 5
            'fvgs': fvgs[:5],
            'liquidity_zones': liquidity_zones[:3],
            'bos_events': bos_events[:3],
            'liquidity_sweeps': liquidity_sweeps[:3],
            'premium_discount': premium_discount,
            'confluence': {'total_score': confluence_score},
            'market_structure': {'trend': trend}
        }
        
    except Exception as e:
        logger.error(f"Error in SMC context preparation: {str(e)}")
        return {
            'symbol': stock,
            'current_price': df['close'].iloc[-1],
            'order_blocks': [],
            'fvgs': [],
            'liquidity_zones': [],
            'bos_events': [],
            'liquidity_sweeps': [],
            'premium_discount': None,
            'confluence': {'total_score': 0.0},
            'market_structure': {'trend': 'sideways'}
        }

def display_prediction_results(results: Dict, analysis_type: str):
    """Display comprehensive prediction results"""
    
    stock = results['stock']
    current_price = results['current_price']
    predictions = results['predictions']
    
    # Header with current price
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("📈 Current Price", f"{current_price:,.2f} EGP")
    with col2:
        st.metric("📊 Predictions", len(predictions))
    with col3:
        confidence_avg = np.mean([p.confidence for p in predictions]) if predictions else 0
        st.metric("🎯 Avg Confidence", f"{confidence_avg:.1%}")
    
    # Predictions table
    if predictions:
        st.subheader("🔮 Price Predictions")
        display_predictions_table(predictions, current_price)
        
        # Prediction chart
        st.subheader("📈 Prediction Visualization")
        display_prediction_chart(predictions, current_price, stock)
    
    # Scenario analysis
    if analysis_type in ["Full Analysis", "Scenario Analysis"] and results.get('scenario_analysis'):
        st.subheader("🎭 Scenario Analysis")
        display_scenario_analysis(results['scenario_analysis'], current_price)
    
    # Confidence factors
    if analysis_type == "Full Analysis":
        st.subheader("🎯 Prediction Confidence Analysis")
        display_confidence_factors(results['confidence_factors'])
        
        # Model performance
        st.subheader("🤖 Model Performance")
        display_model_performance(results['model_performance'])
    
    # Data information
    st.subheader("📊 Data Information")
    data_info = results['data_info']
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Data Points", data_info['data_points'])
    with col2:
        st.write(f"**Date Range:** {data_info['date_range']}")
    with col3:
        st.write(f"**Last Update:** {data_info['last_update']}")

def display_predictions_table(predictions: List[PredictionResult], current_price: float):
    """Display predictions in a formatted table"""
    
    table_data = []
    for pred in predictions:
        price_change = pred.predicted_price - current_price
        price_change_pct = (price_change / current_price) * 100
        
        # Direction emoji
        direction_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
        
        # Risk color
        risk_color = "🟢" if pred.risk_level == 'low' else "🟡" if pred.risk_level == 'medium' else "🔴"
        
        table_data.append({
            "Horizon": pred.time_horizon,
            "Predicted Price": f"{pred.predicted_price:,.2f} EGP",
            "Change": f"{direction_emoji} {price_change:+,.2f} EGP ({price_change_pct:+.1f}%)",
            "Confidence": f"{pred.confidence:.1%}",
            "Prob Up": f"{pred.probability_up:.1%}",
            "Prob Down": f"{pred.probability_down:.1%}",
            "Risk": f"{risk_color} {pred.risk_level.title()}",
            "Model": pred.model_used
        })
    
    df_display = pd.DataFrame(table_data)
    st.dataframe(df_display, use_container_width=True, hide_index=True)

def display_prediction_chart(predictions: List[PredictionResult], current_price: float, stock: str):
    """Display prediction visualization chart"""
    
    fig = go.Figure()
    
    # Current price line
    fig.add_hline(y=current_price, line_dash="dash", line_color="blue", 
                  annotation_text=f"Current: {current_price:,.2f} EGP")
    
    # Prediction points
    horizons = [pred.time_horizon for pred in predictions]
    prices = [pred.predicted_price for pred in predictions]
    confidences = [pred.confidence for pred in predictions]
    
    # Color based on direction
    colors = ['green' if p > current_price else 'red' if p < current_price else 'gray' for p in prices]
    
    fig.add_trace(go.Scatter(
        x=horizons,
        y=prices,
        mode='markers+lines',
        marker=dict(
            size=[c * 20 + 10 for c in confidences],  # Size based on confidence
            color=colors,
            opacity=0.7,
            line=dict(width=2, color='white')
        ),
        line=dict(width=3, color='purple'),
        name='Predictions',
        text=[f"Confidence: {c:.1%}" for c in confidences],
        hovertemplate="<b>%{x}</b><br>Price: %{y:,.2f} EGP<br>%{text}<extra></extra>"
    ))
    
    fig.update_layout(
        title=f"🔮 Price Predictions for {stock}",
        xaxis_title="Time Horizon",
        yaxis_title="Price (EGP)",
        height=400,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)

def display_scenario_analysis(scenario: ScenarioAnalysis, current_price: float):
    """Display scenario analysis results"""
    
    col1, col2, col3 = st.columns(3)
    
    # Bullish scenario
    with col1:
        st.success("📈 **Bullish Scenario**")
        bull_price = scenario.bullish_scenario['predicted_price']
        bull_change = (bull_price - current_price) / current_price * 100
        st.metric("Target Price", f"{bull_price:,.2f} EGP", f"{bull_change:+.1f}%")
        st.write(f"**Probability:** {scenario.bullish_scenario['probability']:.1%}")
        st.write(f"**Risk Level:** {scenario.bullish_scenario['risk_level'].title()}")
    
    # Base scenario
    with col2:
        st.info("🎯 **Base Scenario**")
        base_price = scenario.base_scenario['predicted_price']
        base_change = (base_price - current_price) / current_price * 100
        st.metric("Target Price", f"{base_price:,.2f} EGP", f"{base_change:+.1f}%")
        st.write(f"**Probability:** {scenario.base_scenario['probability']:.1%}")
        st.write(f"**Risk Level:** {scenario.base_scenario['risk_level'].title()}")
    
    # Bearish scenario
    with col3:
        st.error("📉 **Bearish Scenario**")
        bear_price = scenario.bearish_scenario['predicted_price']
        bear_change = (bear_price - current_price) / current_price * 100
        st.metric("Target Price", f"{bear_price:,.2f} EGP", f"{bear_change:+.1f}%")
        st.write(f"**Probability:** {scenario.bearish_scenario['probability']:.1%}")
        st.write(f"**Risk Level:** {scenario.bearish_scenario['risk_level'].title()}")
    
    # Risk metrics
    if scenario.risk_metrics:
        st.subheader("⚠️ Risk Metrics")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Expected Return", f"{scenario.risk_metrics.get('expected_return', 0):.1%}")
        with col2:
            st.metric("Volatility", f"{scenario.risk_metrics.get('volatility', 0):.1%}")
        with col3:
            st.metric("Max Upside", f"{scenario.risk_metrics.get('max_upside', 0):.1%}")
        with col4:
            st.metric("Max Downside", f"{scenario.risk_metrics.get('max_drawdown', 0):.1%}")

def display_confidence_factors(factors: Dict):
    """Display factors affecting prediction confidence"""
    
    if 'error' in factors:
        st.error(f"❌ Error calculating confidence factors: {factors['error']}")
        return
    
    col1, col2, col3 = st.columns(3)
    
    # Data quality
    with col1:
        st.subheader("📊 Data Quality")
        data_quality = factors.get('data_quality', {})
        st.metric("Data Points", data_quality.get('data_points', 0))
        st.metric("Completeness", f"{data_quality.get('data_completeness', 0):.1%}")
        st.metric("Recency (days)", data_quality.get('data_recency', 0))
    
    # Market conditions
    with col2:
        st.subheader("📈 Market Conditions")
        market_conditions = factors.get('market_conditions', {})
        st.write(f"**Volatility:** {market_conditions.get('volatility_level', 'unknown').title()}")
        st.write(f"**Trend Strength:** {market_conditions.get('trend_strength', 0):.1%}")
        st.write(f"**Market Regime:** {market_conditions.get('market_regime', 'unknown').title()}")
    
    # SMC factors
    with col3:
        st.subheader("🧠 SMC Factors")
        smc_factors = factors.get('smc_factors', {})
        st.metric("Confluence Score", f"{smc_factors.get('confluence_score', 0):.1%}")
        st.metric("Structure Count", smc_factors.get('structure_count', 0))
        st.write(f"**Quality:** {smc_factors.get('structure_quality', 'unknown').title()}")
    
    # Overall confidence
    overall = factors.get('overall_confidence', {})
    if overall:
        st.subheader("🎯 Overall Confidence Assessment")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Data Confidence", f"{overall.get('data_confidence', 0):.1%}")
        with col2:
            st.metric("Market Confidence", f"{overall.get('market_confidence', 0):.1%}")
        with col3:
            st.metric("SMC Confidence", f"{overall.get('smc_confidence', 0):.1%}")
        with col4:
            st.metric("Combined Confidence", f"{overall.get('combined_confidence', 0):.1%}")

def display_model_performance(performance: Dict):
    """Display model performance metrics"""
    
    if not performance:
        st.info("📊 Model performance data not available")
        return
    
    perf_data = []
    for model_name, metrics in performance.items():
        perf_data.append({
            "Model": model_name.replace('_', ' ').title(),
            "Accuracy": f"{metrics.accuracy:.1%}",
            "MSE": f"{metrics.mse:.4f}",
            "Directional Accuracy": f"{metrics.directional_accuracy:.1%}",
            "Avg Confidence": f"{metrics.avg_confidence:.1%}",
            "Predictions": metrics.predictions_count,
            "Last Updated": metrics.last_updated.strftime('%Y-%m-%d %H:%M')
        })
    
    if perf_data:
        df_perf = pd.DataFrame(perf_data)
        st.dataframe(df_perf, use_container_width=True, hide_index=True)

def display_predictive_analytics_info():
    """Display information about predictive analytics"""
    
    st.subheader("ℹ️ About Predictive Analytics")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **🔮 Prediction Models:**
        - **Random Forest** - Ensemble learning for robust predictions
        - **Support Vector Machine** - Pattern recognition and classification
        - **Ensemble Method** - Combines multiple models for accuracy
        
        **📊 Features Used:**
        - Price patterns and technical indicators
        - SMC structures (Order blocks, FVGs, BOS)
        - Volume and momentum indicators
        - Market structure analysis
        """)
    
    with col2:
        st.markdown("""
        **🎯 Prediction Types:**
        - **Price Predictions** - Future price targets
        - **Direction Probability** - Up/down movement likelihood
        - **Scenario Analysis** - Bull/bear/base case scenarios
        - **Risk Assessment** - Volatility and risk metrics
        
        **⚠️ Important Notes:**
        - Predictions are probabilistic, not guarantees
        - Higher confidence = more reliable predictions
        - Consider multiple time horizons
        - Use with proper risk management
        """)

if __name__ == "__main__":
    show_predictive_analytics()
