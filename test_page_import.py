"""
Test page import to verify no critical errors
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_page_import():
    """Test importing the predictive analytics page"""
    try:
        print("🔧 Testing page import...")
        
        # Test basic imports
        from app.components.predictive_analytics import PredictiveAnalytics, PredictionResult
        print("✅ Core components imported")
        
        # Test page import
        from app.pages.predictive_analytics import show_predictive_analytics
        print("✅ Page imported successfully")
        
        # Test creating predictor
        predictor = PredictiveAnalytics()
        print("✅ Predictor created")
        
        # Test fallback prediction
        smc_results = {
            'confluence': {'total_score': 0.7},
            'market_structure': {'trend': 'bullish'}
        }
        
        predictions = predictor._generate_fallback_predictions(82500.0, '1W', smc_results)
        print(f"✅ Fallback predictions: {len(predictions)} generated")
        
        if predictions:
            pred = predictions[0]
            print(f"   Price: {pred.predicted_price:,.0f}")
            print(f"   Confidence: {pred.confidence:.1%}")
            print(f"   Model: {pred.model_used}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Page Import Test")
    print("=" * 30)
    
    if test_page_import():
        print("\n🎉 ALL IMPORTS SUCCESSFUL!")
        print("✅ Predictive Analytics page should work now")
        print("\n💡 Try the page in your app:")
        print("   1. Go to Predictive Analytics")
        print("   2. Select a stock (COMI)")
        print("   3. Click 'Generate Predictions'")
        print("   4. Should show fallback predictions")
    else:
        print("\n❌ Import test failed")
