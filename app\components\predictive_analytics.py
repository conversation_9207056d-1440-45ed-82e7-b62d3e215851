"""
Predictive Analytics for SMC Analysis
Advanced machine learning-based price prediction and probability analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from sklearn.ensemble import <PERSON>ForestRegressor, RandomForestClassifier
from sklearn.svm import SVR, SVC
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, accuracy_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class PredictionResult:
    """Represents a price prediction result"""
    prediction_type: str  # 'price', 'direction', 'probability'
    time_horizon: str  # '1D', '3D', '1W', '2W', '1M'
    predicted_price: float
    confidence: float  # 0-1
    probability_up: float  # Probability of upward movement
    probability_down: float  # Probability of downward movement
    target_levels: List[float]  # Predicted support/resistance levels
    risk_level: str  # 'low', 'medium', 'high'
    model_used: str  # Which model generated this prediction
    prediction_date: datetime
    expected_accuracy: float  # Historical accuracy of this model

@dataclass
class ScenarioAnalysis:
    """Represents scenario-based predictions"""
    bullish_scenario: Dict  # Bull case prediction
    bearish_scenario: Dict  # Bear case prediction
    base_scenario: Dict  # Most likely scenario
    probability_distribution: Dict  # Price probability distribution
    risk_metrics: Dict  # Risk assessment metrics

@dataclass
class ModelPerformance:
    """Tracks model performance metrics"""
    model_name: str
    accuracy: float
    mse: float  # Mean squared error
    directional_accuracy: float  # % of correct direction predictions
    avg_confidence: float
    predictions_count: int
    last_updated: datetime

class PredictiveAnalytics:
    """Advanced predictive analytics system for SMC analysis"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.performance_metrics = {}
        self.prediction_history = []
        self._initialize_models()
        
    def _initialize_models(self):
        """Initialize machine learning models"""
        
        # Price prediction models
        self.models['rf_price'] = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        
        self.models['svm_price'] = SVR(
            kernel='rbf',
            C=1.0,
            gamma='scale'
        )
        
        # Direction prediction models
        self.models['rf_direction'] = RandomForestClassifier(
            n_estimators=100,
            max_depth=8,
            random_state=42,
            n_jobs=-1
        )
        
        self.models['svm_direction'] = SVC(
            kernel='rbf',
            C=1.0,
            gamma='scale',
            probability=True
        )
        
        # Initialize scalers
        self.scalers['price'] = StandardScaler()
        self.scalers['features'] = MinMaxScaler()
        
        # Initialize performance tracking
        for model_name in self.models.keys():
            self.performance_metrics[model_name] = ModelPerformance(
                model_name=model_name,
                accuracy=0.0,
                mse=0.0,
                directional_accuracy=0.0,
                avg_confidence=0.0,
                predictions_count=0,
                last_updated=datetime.now()
            )
    
    def prepare_features(self, df: pd.DataFrame, smc_results: Dict) -> np.ndarray:
        """Prepare features for machine learning models"""
        
        if len(df) < 50:
            raise ValueError("Insufficient data for prediction (minimum 50 bars required)")
        
        features = []
        
        # Price-based features
        df['returns'] = df['close'].pct_change()
        df['volatility'] = df['returns'].rolling(10).std()
        df['sma_5'] = df['close'].rolling(5).mean()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        
        # Technical indicators
        df['rsi'] = self._calculate_rsi(df['close'], 14)
        df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
        df['bb_upper'], df['bb_lower'] = self._calculate_bollinger_bands(df['close'])
        
        # Volume features (if available)
        if 'volume' in df.columns:
            df['volume_sma'] = df['volume'].rolling(10).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
        else:
            df['volume_ratio'] = 1.0
        
        # SMC-based features
        smc_features = self._extract_smc_features(smc_results, len(df))
        
        # Combine all features
        feature_columns = [
            'returns', 'volatility', 'sma_5', 'sma_20', 'ema_12',
            'rsi', 'macd', 'macd_signal', 'bb_upper', 'bb_lower', 'volume_ratio'
        ]
        
        # Get the last row features (most recent)
        latest_features = []
        for col in feature_columns:
            if col in df.columns:
                latest_features.append(df[col].iloc[-1])
            else:
                latest_features.append(0.0)
        
        # Add SMC features
        latest_features.extend(smc_features)
        
        # Handle NaN values
        latest_features = [0.0 if pd.isna(x) else x for x in latest_features]
        
        return np.array(latest_features).reshape(1, -1)
    
    def _extract_smc_features(self, smc_results: Dict, data_length: int) -> List[float]:
        """Extract SMC-based features for prediction"""
        
        features = []
        
        # Order blocks features
        order_blocks = smc_results.get('order_blocks', [])
        features.append(len(order_blocks))  # Number of order blocks
        features.append(sum(ob.strength for ob in order_blocks[:3]) / 3 if order_blocks else 0)  # Avg strength
        
        # FVG features
        fvgs = smc_results.get('fvgs', [])
        features.append(len(fvgs))  # Number of FVGs
        features.append(sum(1 for fvg in fvgs if not fvg.filled) if fvgs else 0)  # Unfilled FVGs
        
        # Liquidity zones features
        liquidity_zones = smc_results.get('liquidity_zones', [])
        features.append(len(liquidity_zones))  # Number of liquidity zones
        
        # BOS events features
        bos_events = smc_results.get('bos_events', [])
        features.append(len(bos_events))  # Number of BOS events
        features.append(sum(1 for bos in bos_events if bos.confirmed) if bos_events else 0)  # Confirmed BOS
        
        # Market structure features
        market_structure = smc_results.get('market_structure', {})
        trend = market_structure.get('trend', 'sideways')
        features.append(1.0 if trend == 'bullish' else -1.0 if trend == 'bearish' else 0.0)
        
        # Confluence features
        confluence = smc_results.get('confluence', {})
        features.append(confluence.get('total_score', 0.0))
        
        # Premium/discount features
        pd_zone = smc_results.get('premium_discount')
        if pd_zone:
            features.append(1.0 if pd_zone.current_zone == 'premium' else -1.0 if pd_zone.current_zone == 'discount' else 0.0)
            features.append(pd_zone.zone_strength)
        else:
            features.extend([0.0, 0.0])
        
        return features
    
    def train_models(self, df: pd.DataFrame, smc_results: Dict) -> Dict:
        """Train prediction models on historical data"""
        
        if len(df) < 100:
            return {"error": "Insufficient data for training (minimum 100 bars required)"}
        
        try:
            # Prepare training data
            X_list = []
            y_price_list = []
            y_direction_list = []
            
            # Create training samples
            for i in range(50, len(df) - 5):  # Leave 5 bars for future prediction
                # Get features for this point
                current_df = df.iloc[:i+1]
                current_smc = smc_results  # Simplified - in production, would have historical SMC
                
                try:
                    features = self.prepare_features(current_df, current_smc)
                    X_list.append(features.flatten())
                    
                    # Target: price 5 bars ahead
                    future_price = df['close'].iloc[i + 5]
                    current_price = df['close'].iloc[i]
                    
                    y_price_list.append(future_price)
                    y_direction_list.append(1 if future_price > current_price else 0)
                    
                except Exception:
                    continue
            
            if len(X_list) < 20:
                return {"error": "Insufficient valid training samples"}
            
            X = np.array(X_list)
            y_price = np.array(y_price_list)
            y_direction = np.array(y_direction_list)
            
            # Split data
            X_train, X_test, y_price_train, y_price_test, y_dir_train, y_dir_test = train_test_split(
                X, y_price, y_direction, test_size=0.2, random_state=42
            )
            
            # Scale features
            X_train_scaled = self.scalers['features'].fit_transform(X_train)
            X_test_scaled = self.scalers['features'].transform(X_test)
            
            # Train price prediction models
            self.models['rf_price'].fit(X_train_scaled, y_price_train)
            self.models['svm_price'].fit(X_train_scaled, y_price_train)
            
            # Train direction prediction models
            self.models['rf_direction'].fit(X_train_scaled, y_dir_train)
            self.models['svm_direction'].fit(X_train_scaled, y_dir_train)
            
            # Evaluate models
            results = {}
            
            # Price prediction evaluation
            rf_price_pred = self.models['rf_price'].predict(X_test_scaled)
            svm_price_pred = self.models['svm_price'].predict(X_test_scaled)
            
            results['rf_price_mse'] = mean_squared_error(y_price_test, rf_price_pred)
            results['svm_price_mse'] = mean_squared_error(y_price_test, svm_price_pred)
            
            # Direction prediction evaluation
            rf_dir_pred = self.models['rf_direction'].predict(X_test_scaled)
            svm_dir_pred = self.models['svm_direction'].predict(X_test_scaled)
            
            results['rf_direction_accuracy'] = accuracy_score(y_dir_test, rf_dir_pred)
            results['svm_direction_accuracy'] = accuracy_score(y_dir_test, svm_dir_pred)
            
            # Update performance metrics
            self.performance_metrics['rf_price'].mse = results['rf_price_mse']
            self.performance_metrics['rf_price'].directional_accuracy = results['rf_direction_accuracy']
            self.performance_metrics['rf_price'].last_updated = datetime.now()
            
            results['training_samples'] = len(X_train)
            results['test_samples'] = len(X_test)
            results['features_count'] = X.shape[1]
            
            return results
            
        except Exception as e:
            logger.error(f"Error in model training: {str(e)}")
            return {"error": f"Training failed: {str(e)}"}
    
    def generate_predictions(self, df: pd.DataFrame, smc_results: Dict, 
                           time_horizons: List[str] = None) -> List[PredictionResult]:
        """Generate comprehensive predictions for multiple time horizons"""
        
        if time_horizons is None:
            time_horizons = ['1D', '3D', '1W', '2W']
        
        predictions = []
        
        try:
            # Prepare features
            features = self.prepare_features(df, smc_results)

            # Check if scaler is fitted, if not, fit it with current features
            try:
                features_scaled = self.scalers['features'].transform(features)
            except Exception:
                # Scaler not fitted, fit it with current features
                self.scalers['features'].fit(features)
                features_scaled = self.scalers['features'].transform(features)

            current_price = df['close'].iloc[-1]

            for horizon in time_horizons:
                # Generate predictions from different models
                horizon_predictions = self._predict_for_horizon(
                    features_scaled, current_price, horizon, smc_results
                )
                predictions.extend(horizon_predictions)

        except Exception as e:
            logger.error(f"Error in prediction generation: {str(e)}")

        return predictions
    
    def _predict_for_horizon(self, features_scaled: np.ndarray, current_price: float,
                           horizon: str, smc_results: Dict) -> List[PredictionResult]:
        """Generate predictions for a specific time horizon"""

        predictions = []
        horizon_days = {'1D': 1, '3D': 3, '1W': 7, '2W': 14, '1M': 30}
        days = horizon_days.get(horizon, 7)

        try:
            # Check if models are fitted, if not, use fallback predictions
            try:
                # Random Forest predictions
                rf_price = self.models['rf_price'].predict(features_scaled)[0]
                rf_direction_proba = self.models['rf_direction'].predict_proba(features_scaled)[0]

                # SVM predictions
                svm_price = self.models['svm_price'].predict(features_scaled)[0]
                svm_direction_proba = self.models['svm_direction'].predict_proba(features_scaled)[0]

            except Exception as e:
                logger.warning(f"Models not fitted, using fallback predictions: {str(e)}")
                # Fallback predictions based on technical analysis
                return self._generate_fallback_predictions(current_price, horizon, smc_results)
            
            # Ensemble prediction (average of models)
            ensemble_price = (rf_price + svm_price) / 2
            ensemble_prob_up = (rf_direction_proba[1] + svm_direction_proba[1]) / 2
            ensemble_prob_down = (rf_direction_proba[0] + svm_direction_proba[0]) / 2
            
            # Calculate confidence based on model agreement
            price_agreement = 1 - abs(rf_price - svm_price) / current_price
            direction_agreement = 1 - abs(rf_direction_proba[1] - svm_direction_proba[1])
            confidence = (price_agreement + direction_agreement) / 2
            
            # Determine risk level
            volatility = abs(ensemble_price - current_price) / current_price
            if volatility < 0.02:
                risk_level = 'low'
            elif volatility < 0.05:
                risk_level = 'medium'
            else:
                risk_level = 'high'
            
            # Generate target levels based on SMC analysis
            target_levels = self._calculate_target_levels(current_price, ensemble_price, smc_results)
            
            # Create prediction result
            prediction = PredictionResult(
                prediction_type='ensemble',
                time_horizon=horizon,
                predicted_price=ensemble_price,
                confidence=confidence,
                probability_up=ensemble_prob_up,
                probability_down=ensemble_prob_down,
                target_levels=target_levels,
                risk_level=risk_level,
                model_used='RF+SVM Ensemble',
                prediction_date=datetime.now(),
                expected_accuracy=0.65  # Based on typical model performance
            )
            
            predictions.append(prediction)
            
        except Exception as e:
            logger.error(f"Error in horizon prediction: {str(e)}")
            # Return fallback predictions on error
            return self._generate_fallback_predictions(current_price, horizon, smc_results)

        return predictions

    def _generate_fallback_predictions(self, current_price: float, horizon: str,
                                     smc_results: Dict) -> List[PredictionResult]:
        """Generate fallback predictions when models are not available"""

        predictions = []

        try:
            # Technical analysis based predictions
            confluence_score = smc_results.get('confluence', {}).get('total_score', 0.5)
            market_trend = smc_results.get('market_structure', {}).get('trend', 'sideways')

            # Base prediction factors
            trend_factor = 0.02 if market_trend == 'bullish' else -0.02 if market_trend == 'bearish' else 0.0
            confluence_factor = (confluence_score - 0.5) * 0.03  # -1.5% to +1.5%

            # Time horizon adjustments
            horizon_multipliers = {'1D': 0.5, '3D': 1.0, '1W': 1.5, '2W': 2.0, '1M': 3.0}
            time_multiplier = horizon_multipliers.get(horizon, 1.0)

            # Calculate predicted price
            total_change = (trend_factor + confluence_factor) * time_multiplier
            predicted_price = current_price * (1 + total_change)

            # Calculate probability based on confluence and trend
            base_prob = 0.5
            if market_trend == 'bullish':
                prob_up = min(0.8, base_prob + confluence_score * 0.3)
            elif market_trend == 'bearish':
                prob_up = max(0.2, base_prob - confluence_score * 0.3)
            else:
                prob_up = base_prob

            # Calculate confidence based on data quality
            confidence = min(0.9, 0.6 + confluence_score * 0.3)

            # Create prediction result
            prediction = PredictionResult(
                time_horizon=horizon,
                predicted_price=predicted_price,
                confidence=confidence,
                probability_up=prob_up,
                model_type='technical_analysis',
                features_used=['confluence_score', 'market_trend', 'time_horizon'],
                prediction_range=(predicted_price * 0.95, predicted_price * 1.05)
            )

            predictions.append(prediction)

        except Exception as e:
            logger.error(f"Error in fallback predictions: {str(e)}")
            # Ultimate fallback - simple prediction
            prediction = PredictionResult(
                time_horizon=horizon,
                predicted_price=current_price,
                confidence=0.5,
                probability_up=0.5,
                model_type='fallback',
                features_used=['current_price'],
                prediction_range=(current_price * 0.98, current_price * 1.02)
            )
            predictions.append(prediction)

        return predictions
    
    def _calculate_target_levels(self, current_price: float, predicted_price: float, 
                               smc_results: Dict) -> List[float]:
        """Calculate target levels based on SMC analysis"""
        
        levels = []
        
        # Add predicted price
        levels.append(predicted_price)
        
        # Add SMC-based levels
        order_blocks = smc_results.get('order_blocks', [])
        for ob in order_blocks[:3]:
            levels.extend([ob.high, ob.low])
        
        fvgs = smc_results.get('fvgs', [])
        for fvg in fvgs[:2]:
            levels.extend([fvg.high, fvg.low])
        
        # Add technical levels
        price_range = abs(predicted_price - current_price)
        levels.extend([
            current_price + price_range * 0.5,
            current_price - price_range * 0.5,
            current_price + price_range * 1.5,
            current_price - price_range * 1.5
        ])
        
        # Remove duplicates and sort
        levels = sorted(list(set([level for level in levels if level > 0])))
        
        return levels[:10]  # Return top 10 levels
    
    # Technical indicator calculation methods
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """Calculate MACD indicator"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Tuple[pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        return upper_band, lower_band

    def generate_scenario_analysis(self, df: pd.DataFrame, smc_results: Dict) -> ScenarioAnalysis:
        """Generate comprehensive scenario analysis"""

        try:
            current_price = df['close'].iloc[-1]
            volatility = df['close'].pct_change().std() * np.sqrt(252)  # Annualized volatility

            # Generate base predictions
            predictions = self.generate_predictions(df, smc_results, ['1W'])
            base_prediction = predictions[0] if predictions else None

            if not base_prediction:
                raise ValueError("Could not generate base prediction")

            # Calculate scenario parameters
            price_change = base_prediction.predicted_price - current_price
            price_change_pct = price_change / current_price

            # Bullish scenario (optimistic case)
            bullish_multiplier = 1.5
            bullish_price = current_price + (price_change * bullish_multiplier)
            bullish_probability = base_prediction.probability_up * 0.8  # Reduce probability for extreme scenario

            # Bearish scenario (pessimistic case)
            bearish_multiplier = 1.5
            bearish_price = current_price - (abs(price_change) * bearish_multiplier)
            bearish_probability = base_prediction.probability_down * 0.8

            # Base scenario (most likely case)
            base_price = base_prediction.predicted_price
            base_probability = max(base_prediction.probability_up, base_prediction.probability_down)

            # Create scenario analysis
            scenario_analysis = ScenarioAnalysis(
                bullish_scenario={
                    'predicted_price': bullish_price,
                    'price_change_pct': (bullish_price - current_price) / current_price,
                    'probability': bullish_probability,
                    'target_levels': self._calculate_target_levels(current_price, bullish_price, smc_results),
                    'risk_level': 'high',
                    'description': 'Optimistic scenario with strong bullish momentum'
                },
                bearish_scenario={
                    'predicted_price': bearish_price,
                    'price_change_pct': (bearish_price - current_price) / current_price,
                    'probability': bearish_probability,
                    'target_levels': self._calculate_target_levels(current_price, bearish_price, smc_results),
                    'risk_level': 'high',
                    'description': 'Pessimistic scenario with strong bearish pressure'
                },
                base_scenario={
                    'predicted_price': base_price,
                    'price_change_pct': price_change_pct,
                    'probability': base_probability,
                    'target_levels': base_prediction.target_levels,
                    'risk_level': base_prediction.risk_level,
                    'description': 'Most likely scenario based on current analysis'
                },
                probability_distribution=self._calculate_probability_distribution(
                    current_price, base_prediction, volatility
                ),
                risk_metrics=self._calculate_risk_metrics(
                    current_price, [bullish_price, base_price, bearish_price], volatility
                )
            )

            return scenario_analysis

        except Exception as e:
            logger.error(f"Error in scenario analysis: {str(e)}")
            # Return default scenario
            return ScenarioAnalysis(
                bullish_scenario={'predicted_price': current_price * 1.05, 'probability': 0.3},
                bearish_scenario={'predicted_price': current_price * 0.95, 'probability': 0.3},
                base_scenario={'predicted_price': current_price, 'probability': 0.4},
                probability_distribution={},
                risk_metrics={}
            )

    def _calculate_probability_distribution(self, current_price: float,
                                          prediction: PredictionResult, volatility: float) -> Dict:
        """Calculate probability distribution for price ranges"""

        # Define price ranges around current price
        ranges = []
        probabilities = []

        # Create 10 price ranges
        for i in range(-5, 6):
            range_center = current_price * (1 + i * 0.02)  # 2% intervals
            range_low = range_center * 0.99
            range_high = range_center * 1.01

            # Calculate probability based on distance from prediction and volatility
            distance = abs(range_center - prediction.predicted_price) / current_price
            probability = np.exp(-distance / volatility) * prediction.confidence

            ranges.append(f"{range_low:.0f}-{range_high:.0f}")
            probabilities.append(probability)

        # Normalize probabilities
        total_prob = sum(probabilities)
        if total_prob > 0:
            probabilities = [p / total_prob for p in probabilities]

        return dict(zip(ranges, probabilities))

    def _calculate_risk_metrics(self, current_price: float, scenario_prices: List[float],
                              volatility: float) -> Dict:
        """Calculate comprehensive risk metrics"""

        returns = [(price - current_price) / current_price for price in scenario_prices]

        return {
            'value_at_risk_5pct': np.percentile(returns, 5),  # 5% VaR
            'expected_return': np.mean(returns),
            'volatility': volatility,
            'max_drawdown': min(returns),
            'max_upside': max(returns),
            'sharpe_ratio': np.mean(returns) / volatility if volatility > 0 else 0,
            'downside_probability': sum(1 for r in returns if r < 0) / len(returns),
            'upside_probability': sum(1 for r in returns if r > 0) / len(returns)
        }

    def get_model_performance(self) -> Dict[str, ModelPerformance]:
        """Get performance metrics for all models"""
        return self.performance_metrics

    def update_prediction_accuracy(self, prediction_id: str, actual_price: float):
        """Update prediction accuracy based on actual outcomes"""
        # This would be used to track and improve model performance over time
        # Implementation would store predictions and compare with actual results
        pass

    def get_prediction_confidence_factors(self, df: pd.DataFrame, smc_results: Dict) -> Dict:
        """Analyze factors affecting prediction confidence"""

        factors = {}

        try:
            # Data quality factors
            factors['data_quality'] = {
                'data_points': len(df),
                'data_completeness': 1.0 - df.isnull().sum().sum() / (len(df) * len(df.columns)),
                'data_recency': (datetime.now() - pd.to_datetime(df.index[-1])).days if hasattr(df.index[-1], 'date') else 0
            }

            # Market condition factors
            volatility = df['close'].pct_change().std()
            trend_strength = abs(df['close'].iloc[-1] - df['close'].iloc[-20]) / df['close'].iloc[-20]

            factors['market_conditions'] = {
                'volatility_level': 'high' if volatility > 0.03 else 'medium' if volatility > 0.015 else 'low',
                'trend_strength': trend_strength,
                'market_regime': 'trending' if trend_strength > 0.05 else 'ranging'
            }

            # SMC factors
            confluence_score = smc_results.get('confluence', {}).get('total_score', 0)
            structure_count = (
                len(smc_results.get('order_blocks', [])) +
                len(smc_results.get('fvgs', [])) +
                len(smc_results.get('liquidity_zones', []))
            )

            factors['smc_factors'] = {
                'confluence_score': confluence_score,
                'structure_count': structure_count,
                'structure_quality': 'high' if confluence_score > 0.7 else 'medium' if confluence_score > 0.4 else 'low'
            }

            # Overall confidence assessment
            data_score = min(factors['data_quality']['data_completeness'], 1.0)
            market_score = 0.8 if factors['market_conditions']['market_regime'] == 'trending' else 0.6
            smc_score = confluence_score

            factors['overall_confidence'] = {
                'data_confidence': data_score,
                'market_confidence': market_score,
                'smc_confidence': smc_score,
                'combined_confidence': (data_score + market_score + smc_score) / 3
            }

        except Exception as e:
            logger.error(f"Error calculating confidence factors: {str(e)}")
            factors = {'error': str(e)}

        return factors
