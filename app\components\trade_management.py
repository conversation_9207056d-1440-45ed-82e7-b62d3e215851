"""
Integrated Trade Management System
Comprehensive trade planning, execution, and monitoring for SMC analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import logging
import json

logger = logging.getLogger(__name__)

@dataclass
class TradeSetup:
    """Represents a complete trade setup"""
    setup_id: str
    symbol: str
    setup_type: str  # 'buy', 'sell'
    entry_price: float
    stop_loss: float
    take_profit_1: float
    take_profit_2: float
    take_profit_3: float
    position_size: float  # Number of shares
    risk_amount: float  # EGP amount at risk
    risk_percentage: float  # % of account
    risk_reward_ratio: float
    confidence_score: float
    setup_reason: str
    smc_confluence: Dict
    created_at: datetime
    status: str = 'planned'  # 'planned', 'active', 'closed', 'cancelled'

@dataclass
class ActiveTrade:
    """Represents an active trade"""
    trade_id: str
    setup: TradeSetup
    entry_time: datetime
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    max_profit: float
    max_loss: float
    duration: timedelta
    tp1_hit: bool = False
    tp2_hit: bool = False
    tp3_hit: bool = False
    trailing_stop: Optional[float] = None
    notes: List[str] = field(default_factory=list)

@dataclass
class TradeResult:
    """Represents a completed trade result"""
    trade_id: str
    setup: TradeSetup
    entry_time: datetime
    exit_time: datetime
    exit_price: float
    exit_reason: str
    realized_pnl: float
    realized_pnl_pct: float
    duration: timedelta
    max_adverse_excursion: float
    max_favorable_excursion: float
    trade_grade: str  # 'A', 'B', 'C', 'D', 'F'

@dataclass
class RiskManagement:
    """Risk management parameters"""
    account_balance: float
    max_risk_per_trade: float = 0.02  # 2%
    max_daily_loss: float = 0.05  # 5%
    max_weekly_loss: float = 0.10  # 10%
    max_open_positions: int = 5
    max_correlation_exposure: float = 0.15  # 15%
    current_daily_loss: float = 0.0
    current_weekly_loss: float = 0.0

class TradeManager:
    """Comprehensive trade management system"""
    
    def __init__(self, account_balance: float = 100000.0):
        self.risk_management = RiskManagement(account_balance=account_balance)
        self.active_trades: List[ActiveTrade] = []
        self.trade_history: List[TradeResult] = []
        self.trade_setups: List[TradeSetup] = []
        
    def create_trade_setup(self, symbol: str, smc_results: Dict, 
                          multi_timeframe_signal: Dict = None,
                          ai_patterns: List = None) -> TradeSetup:
        """
        Create a comprehensive trade setup based on SMC analysis
        
        Args:
            symbol: Stock symbol
            smc_results: SMC analysis results
            multi_timeframe_signal: Multi-timeframe analysis signal
            ai_patterns: AI pattern recognition results
            
        Returns:
            TradeSetup object
        """
        
        current_price = smc_results['current_price']
        
        # Determine trade direction and confidence
        trade_direction, confidence = self._analyze_trade_direction(
            smc_results, multi_timeframe_signal, ai_patterns
        )
        
        if trade_direction == 'wait':
            return None
        
        # Calculate entry and exit levels
        levels = self._calculate_trade_levels(
            current_price, trade_direction, smc_results, multi_timeframe_signal
        )
        
        # Calculate position size
        position_size, risk_amount = self._calculate_position_size(
            current_price, levels['stop_loss'], trade_direction
        )
        
        # Calculate risk/reward ratio
        risk = abs(current_price - levels['stop_loss'])
        reward = abs(levels['take_profit_1'] - current_price)
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        # Generate setup reason
        setup_reason = self._generate_setup_reason(
            smc_results, multi_timeframe_signal, ai_patterns
        )
        
        # Create setup
        setup = TradeSetup(
            setup_id=f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            setup_type=trade_direction,
            entry_price=current_price,
            stop_loss=levels['stop_loss'],
            take_profit_1=levels['take_profit_1'],
            take_profit_2=levels['take_profit_2'],
            take_profit_3=levels['take_profit_3'],
            position_size=position_size,
            risk_amount=risk_amount,
            risk_percentage=risk_amount / self.risk_management.account_balance,
            risk_reward_ratio=risk_reward_ratio,
            confidence_score=confidence,
            setup_reason=setup_reason,
            smc_confluence=smc_results.get('confluence', {}),
            created_at=datetime.now()
        )
        
        self.trade_setups.append(setup)
        return setup
    
    def validate_trade_setup(self, setup: TradeSetup) -> Tuple[bool, List[str]]:
        """
        Validate trade setup against risk management rules
        
        Args:
            setup: TradeSetup to validate
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        
        issues = []
        
        # Check risk per trade
        if setup.risk_percentage > self.risk_management.max_risk_per_trade:
            issues.append(f"Risk per trade ({setup.risk_percentage:.1%}) exceeds maximum ({self.risk_management.max_risk_per_trade:.1%})")
        
        # Check daily loss limit
        potential_daily_loss = self.risk_management.current_daily_loss + setup.risk_percentage
        if potential_daily_loss > self.risk_management.max_daily_loss:
            issues.append(f"Potential daily loss ({potential_daily_loss:.1%}) exceeds limit ({self.risk_management.max_daily_loss:.1%})")
        
        # Check maximum open positions
        if len(self.active_trades) >= self.risk_management.max_open_positions:
            issues.append(f"Maximum open positions ({self.risk_management.max_open_positions}) reached")
        
        # Check minimum risk/reward ratio
        if setup.risk_reward_ratio < 1.5:
            issues.append(f"Risk/reward ratio ({setup.risk_reward_ratio:.1f}) below minimum (1.5)")
        
        # Check confidence threshold
        if setup.confidence_score < 0.6:
            issues.append(f"Confidence score ({setup.confidence_score:.1%}) below minimum (60%)")
        
        return len(issues) == 0, issues
    
    def execute_trade(self, setup: TradeSetup) -> Optional[ActiveTrade]:
        """
        Execute a validated trade setup
        
        Args:
            setup: TradeSetup to execute
            
        Returns:
            ActiveTrade object if successful
        """
        
        # Validate setup
        is_valid, issues = self.validate_trade_setup(setup)
        if not is_valid:
            logger.warning(f"Trade setup validation failed: {issues}")
            return None
        
        # Create active trade
        active_trade = ActiveTrade(
            trade_id=setup.setup_id,
            setup=setup,
            entry_time=datetime.now(),
            current_price=setup.entry_price,
            unrealized_pnl=0.0,
            unrealized_pnl_pct=0.0,
            max_profit=0.0,
            max_loss=0.0,
            duration=timedelta(0)
        )
        
        self.active_trades.append(active_trade)
        setup.status = 'active'
        
        logger.info(f"Trade executed: {setup.symbol} {setup.setup_type} at {setup.entry_price}")
        return active_trade
    
    def update_active_trades(self, current_prices: Dict[str, float]):
        """
        Update all active trades with current market prices
        
        Args:
            current_prices: Dictionary of symbol -> current_price
        """
        
        for trade in self.active_trades:
            symbol = trade.setup.symbol
            if symbol in current_prices:
                self._update_trade(trade, current_prices[symbol])
    
    def _update_trade(self, trade: ActiveTrade, current_price: float):
        """Update individual trade with current price"""
        
        trade.current_price = current_price
        trade.duration = datetime.now() - trade.entry_time
        
        # Calculate P&L
        if trade.setup.setup_type == 'buy':
            pnl = (current_price - trade.setup.entry_price) * trade.setup.position_size
            pnl_pct = (current_price - trade.setup.entry_price) / trade.setup.entry_price
        else:  # sell
            pnl = (trade.setup.entry_price - current_price) * trade.setup.position_size
            pnl_pct = (trade.setup.entry_price - current_price) / trade.setup.entry_price
        
        trade.unrealized_pnl = pnl
        trade.unrealized_pnl_pct = pnl_pct
        
        # Update max profit/loss
        trade.max_profit = max(trade.max_profit, pnl)
        trade.max_loss = min(trade.max_loss, pnl)
        
        # Check take profit levels
        if trade.setup.setup_type == 'buy':
            if not trade.tp1_hit and current_price >= trade.setup.take_profit_1:
                trade.tp1_hit = True
                trade.notes.append(f"TP1 hit at {current_price} on {datetime.now()}")
            
            if not trade.tp2_hit and current_price >= trade.setup.take_profit_2:
                trade.tp2_hit = True
                trade.notes.append(f"TP2 hit at {current_price} on {datetime.now()}")
            
            if not trade.tp3_hit and current_price >= trade.setup.take_profit_3:
                trade.tp3_hit = True
                trade.notes.append(f"TP3 hit at {current_price} on {datetime.now()}")
        
        else:  # sell
            if not trade.tp1_hit and current_price <= trade.setup.take_profit_1:
                trade.tp1_hit = True
                trade.notes.append(f"TP1 hit at {current_price} on {datetime.now()}")
            
            if not trade.tp2_hit and current_price <= trade.setup.take_profit_2:
                trade.tp2_hit = True
                trade.notes.append(f"TP2 hit at {current_price} on {datetime.now()}")
            
            if not trade.tp3_hit and current_price <= trade.setup.take_profit_3:
                trade.tp3_hit = True
                trade.notes.append(f"TP3 hit at {current_price} on {datetime.now()}")
    
    def close_trade(self, trade_id: str, exit_price: float, exit_reason: str) -> TradeResult:
        """
        Close an active trade
        
        Args:
            trade_id: ID of trade to close
            exit_price: Exit price
            exit_reason: Reason for exit
            
        Returns:
            TradeResult object
        """
        
        # Find active trade
        trade = next((t for t in self.active_trades if t.trade_id == trade_id), None)
        if not trade:
            raise ValueError(f"Active trade {trade_id} not found")
        
        # Calculate final P&L
        if trade.setup.setup_type == 'buy':
            realized_pnl = (exit_price - trade.setup.entry_price) * trade.setup.position_size
            realized_pnl_pct = (exit_price - trade.setup.entry_price) / trade.setup.entry_price
        else:
            realized_pnl = (trade.setup.entry_price - exit_price) * trade.setup.position_size
            realized_pnl_pct = (trade.setup.entry_price - exit_price) / trade.setup.entry_price
        
        # Grade the trade
        trade_grade = self._grade_trade(trade, realized_pnl_pct, exit_reason)
        
        # Create trade result
        result = TradeResult(
            trade_id=trade_id,
            setup=trade.setup,
            entry_time=trade.entry_time,
            exit_time=datetime.now(),
            exit_price=exit_price,
            exit_reason=exit_reason,
            realized_pnl=realized_pnl,
            realized_pnl_pct=realized_pnl_pct,
            duration=datetime.now() - trade.entry_time,
            max_adverse_excursion=trade.max_loss,
            max_favorable_excursion=trade.max_profit,
            trade_grade=trade_grade
        )
        
        # Update records
        self.trade_history.append(result)
        self.active_trades.remove(trade)
        trade.setup.status = 'closed'
        
        # Update risk management
        self._update_risk_management(realized_pnl_pct)
        
        logger.info(f"Trade closed: {trade.setup.symbol} P&L: {realized_pnl:.2f} EGP ({realized_pnl_pct:.1%})")
        return result
    
    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary"""
        
        total_unrealized_pnl = sum(trade.unrealized_pnl for trade in self.active_trades)
        total_realized_pnl = sum(result.realized_pnl for result in self.trade_history)
        
        # Calculate win rate
        winning_trades = len([r for r in self.trade_history if r.realized_pnl > 0])
        total_trades = len(self.trade_history)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Calculate average win/loss
        winning_pnl = [r.realized_pnl for r in self.trade_history if r.realized_pnl > 0]
        losing_pnl = [r.realized_pnl for r in self.trade_history if r.realized_pnl < 0]
        
        avg_win = np.mean(winning_pnl) if winning_pnl else 0
        avg_loss = np.mean(losing_pnl) if losing_pnl else 0
        
        return {
            'account_balance': self.risk_management.account_balance,
            'active_trades_count': len(self.active_trades),
            'total_unrealized_pnl': total_unrealized_pnl,
            'total_realized_pnl': total_realized_pnl,
            'total_pnl': total_unrealized_pnl + total_realized_pnl,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'average_win': avg_win,
            'average_loss': avg_loss,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else float('inf'),
            'current_daily_loss': self.risk_management.current_daily_loss,
            'current_weekly_loss': self.risk_management.current_weekly_loss,
            'risk_utilization': len(self.active_trades) / self.risk_management.max_open_positions
        }
    
    def _analyze_trade_direction(self, smc_results: Dict, 
                               multi_timeframe_signal: Dict = None,
                               ai_patterns: List = None) -> Tuple[str, float]:
        """Analyze overall trade direction and confidence"""
        
        signals = []
        
        # SMC confluence signal
        confluence_score = smc_results.get('confluence', {}).get('total_score', 0)
        if confluence_score > 0.6:
            # Determine direction from market structure
            market_structure = smc_results.get('market_structure', {})
            if market_structure.get('trend') == 'bullish':
                signals.append(('buy', confluence_score))
            elif market_structure.get('trend') == 'bearish':
                signals.append(('sell', confluence_score))
        
        # Multi-timeframe signal
        if multi_timeframe_signal:
            mtf_signal = multi_timeframe_signal.get('signal_type', 'wait')
            mtf_confidence = multi_timeframe_signal.get('confidence', 0)
            if mtf_signal != 'wait' and mtf_confidence > 0.6:
                signals.append((mtf_signal, mtf_confidence))
        
        # AI pattern signals
        if ai_patterns:
            for pattern in ai_patterns[:3]:  # Top 3 patterns
                if pattern.confidence > 0.7:
                    direction = 'buy' if pattern.pattern_type in ['bullish', 'continuation'] else 'sell'
                    signals.append((direction, pattern.confidence))
        
        # Aggregate signals
        if not signals:
            return 'wait', 0.0
        
        # Count votes
        buy_votes = [conf for direction, conf in signals if direction == 'buy']
        sell_votes = [conf for direction, conf in signals if direction == 'sell']
        
        buy_strength = sum(buy_votes) / len(buy_votes) if buy_votes else 0
        sell_strength = sum(sell_votes) / len(sell_votes) if sell_votes else 0
        
        if buy_strength > sell_strength and buy_strength > 0.65:
            return 'buy', buy_strength
        elif sell_strength > buy_strength and sell_strength > 0.65:
            return 'sell', sell_strength
        else:
            return 'wait', max(buy_strength, sell_strength)
    
    def _calculate_trade_levels(self, current_price: float, direction: str,
                              smc_results: Dict, multi_timeframe_signal: Dict = None) -> Dict:
        """Calculate entry and exit levels"""
        
        if multi_timeframe_signal and 'entry_price' in multi_timeframe_signal:
            # Use multi-timeframe levels if available
            return {
                'stop_loss': multi_timeframe_signal.get('stop_loss', current_price * 0.98),
                'take_profit_1': multi_timeframe_signal.get('take_profit_1', current_price * 1.02),
                'take_profit_2': multi_timeframe_signal.get('take_profit_2', current_price * 1.04),
                'take_profit_3': multi_timeframe_signal.get('take_profit_3', current_price * 1.06)
            }
        
        # Use SMC-based levels
        if direction == 'buy':
            # Find nearest support for stop loss
            order_blocks = smc_results.get('order_blocks', [])
            support_levels = [ob.low for ob in order_blocks if ob.low < current_price and ob.block_type == 'bullish']
            stop_loss = max(support_levels) if support_levels else current_price * 0.98
            
            # Calculate take profits
            tp1 = current_price * 1.02
            tp2 = current_price * 1.04
            tp3 = current_price * 1.06
            
        else:  # sell
            # Find nearest resistance for stop loss
            order_blocks = smc_results.get('order_blocks', [])
            resistance_levels = [ob.high for ob in order_blocks if ob.high > current_price and ob.block_type == 'bearish']
            stop_loss = min(resistance_levels) if resistance_levels else current_price * 1.02
            
            # Calculate take profits
            tp1 = current_price * 0.98
            tp2 = current_price * 0.96
            tp3 = current_price * 0.94
        
        return {
            'stop_loss': stop_loss,
            'take_profit_1': tp1,
            'take_profit_2': tp2,
            'take_profit_3': tp3
        }
    
    def _calculate_position_size(self, entry_price: float, stop_loss: float, direction: str) -> Tuple[float, float]:
        """Calculate position size based on risk management"""
        
        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss)
        
        # Calculate maximum risk amount
        max_risk_amount = self.risk_management.account_balance * self.risk_management.max_risk_per_trade
        
        # Calculate position size
        position_size = max_risk_amount / risk_per_share
        
        # Round to appropriate lot size (EGX typically trades in lots of 100)
        position_size = round(position_size / 100) * 100
        
        # Calculate actual risk amount
        actual_risk_amount = position_size * risk_per_share
        
        return position_size, actual_risk_amount
    
    def _generate_setup_reason(self, smc_results: Dict, 
                             multi_timeframe_signal: Dict = None,
                             ai_patterns: List = None) -> str:
        """Generate human-readable setup reason"""
        
        reasons = []
        
        # SMC reasons
        confluence = smc_results.get('confluence', {})
        if confluence.get('total_score', 0) > 0.7:
            reasons.append("High SMC confluence")
        
        if smc_results.get('bos_events'):
            reasons.append("Recent BOS confirmation")
        
        if smc_results.get('liquidity_sweeps'):
            reasons.append("Liquidity sweep reversal")
        
        # Multi-timeframe reasons
        if multi_timeframe_signal and multi_timeframe_signal.get('confidence', 0) > 0.75:
            alignment = multi_timeframe_signal.get('timeframe_alignment', {})
            aligned_count = sum(1 for trend in alignment.values() if trend in ['bullish', 'bearish'])
            reasons.append(f"Multi-timeframe alignment ({aligned_count}/{len(alignment)})")
        
        # AI pattern reasons
        if ai_patterns:
            high_confidence_patterns = [p for p in ai_patterns if p.confidence > 0.8]
            if high_confidence_patterns:
                pattern_names = [p.pattern_name for p in high_confidence_patterns[:2]]
                reasons.append(f"AI patterns: {', '.join(pattern_names)}")
        
        return "; ".join(reasons) if reasons else "Standard SMC setup"
    
    def _grade_trade(self, trade: ActiveTrade, realized_pnl_pct: float, exit_reason: str) -> str:
        """Grade trade performance"""
        
        # Base grade on P&L
        if realized_pnl_pct >= 0.04:  # 4%+
            base_grade = 'A'
        elif realized_pnl_pct >= 0.02:  # 2-4%
            base_grade = 'B'
        elif realized_pnl_pct >= 0:  # 0-2%
            base_grade = 'C'
        elif realized_pnl_pct >= -0.02:  # 0 to -2%
            base_grade = 'D'
        else:  # -2%+
            base_grade = 'F'
        
        # Adjust based on exit reason
        if exit_reason == 'stop_loss' and realized_pnl_pct > -0.025:
            # Good risk management
            if base_grade == 'F':
                base_grade = 'D'
        elif exit_reason == 'take_profit_3':
            # Excellent execution
            if base_grade in ['B', 'C']:
                base_grade = 'A'
        
        return base_grade
    
    def _update_risk_management(self, realized_pnl_pct: float):
        """Update risk management tracking"""
        
        # Update daily loss
        if realized_pnl_pct < 0:
            self.risk_management.current_daily_loss += abs(realized_pnl_pct)
            self.risk_management.current_weekly_loss += abs(realized_pnl_pct)
        
        # Reset daily loss at start of new day (simplified)
        # In production, this would be handled by a scheduler
