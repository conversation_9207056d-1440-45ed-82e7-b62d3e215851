"""
Multi-Timeframe SMC Analysis
Analyze SMC structures across multiple timeframes for better confluence
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@dataclass
class TimeframeAnalysis:
    """Represents SMC analysis for a specific timeframe"""
    timeframe: str
    trend_direction: str  # 'bullish', 'bearish', 'sideways'
    trend_strength: float  # 0-1
    key_levels: List[float]
    order_blocks: List
    fvgs: List
    liquidity_zones: List
    bos_events: List
    market_structure_score: float
    confluence_score: float

@dataclass
class MultiTimeframeSignal:
    """Represents a multi-timeframe trading signal"""
    signal_type: str  # 'buy', 'sell', 'wait'
    confidence: float  # 0-1
    entry_price: float
    stop_loss: float
    take_profit_1: float
    take_profit_2: float
    take_profit_3: float
    risk_reward_ratio: float
    timeframe_alignment: Dict[str, str]
    supporting_factors: List[str]
    risk_level: str  # 'low', 'medium', 'high'

def analyze_multiple_timeframes(df: pd.DataFrame, symbol: str, timeframes: List[str] = None) -> Dict[str, TimeframeAnalysis]:
    """
    Analyze SMC structures across multiple timeframes
    
    Args:
        df: OHLCV DataFrame (highest resolution data)
        symbol: Stock symbol
        timeframes: List of timeframes to analyze ['1H', '4H', '1D', '1W']
        
    Returns:
        Dictionary of TimeframeAnalysis objects
    """
    if timeframes is None:
        timeframes = ['1H', '4H', '1D', '1W']
    
    analyses = {}
    
    try:
        # Import SMC functions
        from app.components.smc_indicators import detect_order_blocks, detect_fvg, detect_liquidity_zones
        from app.components.advanced_smc_features import detect_break_of_structure
        
        for tf in timeframes:
            # Resample data to timeframe
            tf_data = resample_to_timeframe(df, tf)
            
            if len(tf_data) < 50:  # Minimum data requirement
                continue
            
            # Run SMC analysis for this timeframe
            current_price = float(tf_data['close'].iloc[-1])
            
            # Detect structures
            order_blocks = detect_order_blocks(tf_data, lookback=20)
            fvgs = detect_fvg(tf_data, min_gap_size=0.001)
            liquidity_zones = detect_liquidity_zones(tf_data, lookback=20)
            bos_events = detect_break_of_structure(tf_data, lookback=15)
            
            # Analyze trend
            trend_analysis = analyze_trend_structure(tf_data)
            
            # Calculate key levels
            key_levels = extract_key_levels(tf_data, order_blocks, fvgs, liquidity_zones)
            
            # Calculate confluence score
            confluence_score = calculate_timeframe_confluence(
                order_blocks, fvgs, liquidity_zones, bos_events, trend_analysis
            )
            
            analyses[tf] = TimeframeAnalysis(
                timeframe=tf,
                trend_direction=trend_analysis['direction'],
                trend_strength=trend_analysis['strength'],
                key_levels=key_levels,
                order_blocks=order_blocks[:5],  # Top 5
                fvgs=fvgs[:5],
                liquidity_zones=liquidity_zones[:3],
                bos_events=bos_events[:3],
                market_structure_score=trend_analysis['structure_score'],
                confluence_score=confluence_score
            )
            
    except Exception as e:
        logger.error(f"Error in multi-timeframe analysis: {str(e)}")
    
    return analyses

def generate_multi_timeframe_signal(analyses: Dict[str, TimeframeAnalysis], current_price: float) -> MultiTimeframeSignal:
    """
    Generate trading signal based on multi-timeframe analysis
    
    Args:
        analyses: Dictionary of timeframe analyses
        current_price: Current market price
        
    Returns:
        MultiTimeframeSignal object
    """
    if not analyses:
        return None
    
    # Analyze timeframe alignment
    timeframe_alignment = {}
    bullish_count = 0
    bearish_count = 0
    
    for tf, analysis in analyses.items():
        timeframe_alignment[tf] = analysis.trend_direction
        if analysis.trend_direction == 'bullish':
            bullish_count += 1
        elif analysis.trend_direction == 'bearish':
            bearish_count += 1
    
    # Determine overall signal
    total_timeframes = len(analyses)
    bullish_ratio = bullish_count / total_timeframes
    bearish_ratio = bearish_count / total_timeframes
    
    # Signal logic
    if bullish_ratio >= 0.75:  # 75% or more timeframes bullish
        signal_type = 'buy'
        confidence = min(bullish_ratio + 0.2, 1.0)
    elif bearish_ratio >= 0.75:  # 75% or more timeframes bearish
        signal_type = 'sell'
        confidence = min(bearish_ratio + 0.2, 1.0)
    elif bullish_ratio >= 0.6:  # 60-74% bullish
        signal_type = 'buy'
        confidence = bullish_ratio
    elif bearish_ratio >= 0.6:  # 60-74% bearish
        signal_type = 'sell'
        confidence = bearish_ratio
    else:
        signal_type = 'wait'
        confidence = 0.5
    
    # Calculate entry and exit levels
    entry_exit_levels = calculate_entry_exit_levels(analyses, current_price, signal_type)
    
    # Determine supporting factors
    supporting_factors = identify_supporting_factors(analyses, signal_type)
    
    # Calculate risk level
    risk_level = calculate_risk_level(analyses, confidence)
    
    return MultiTimeframeSignal(
        signal_type=signal_type,
        confidence=confidence,
        entry_price=entry_exit_levels['entry'],
        stop_loss=entry_exit_levels['stop_loss'],
        take_profit_1=entry_exit_levels['tp1'],
        take_profit_2=entry_exit_levels['tp2'],
        take_profit_3=entry_exit_levels['tp3'],
        risk_reward_ratio=entry_exit_levels['risk_reward'],
        timeframe_alignment=timeframe_alignment,
        supporting_factors=supporting_factors,
        risk_level=risk_level
    )

def resample_to_timeframe(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """Resample data to specified timeframe"""
    
    # Timeframe mapping
    tf_map = {
        '1H': '1H',
        '4H': '4H', 
        '1D': '1D',
        '1W': '1W'
    }
    
    if timeframe not in tf_map:
        return df
    
    try:
        # Ensure datetime index
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)
        
        # Resample OHLCV data
        resampled = df.resample(tf_map[timeframe]).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        return resampled
        
    except Exception as e:
        logger.error(f"Error resampling to {timeframe}: {str(e)}")
        return df

def analyze_trend_structure(df: pd.DataFrame) -> Dict:
    """Analyze trend structure for a timeframe"""
    
    if len(df) < 20:
        return {'direction': 'sideways', 'strength': 0.5, 'structure_score': 0.5}
    
    # Moving averages for trend
    df['ma_20'] = df['close'].rolling(20).mean()
    df['ma_50'] = df['close'].rolling(50).mean() if len(df) >= 50 else df['close'].rolling(len(df)//2).mean()
    
    current_price = df['close'].iloc[-1]
    ma_20 = df['ma_20'].iloc[-1]
    ma_50 = df['ma_50'].iloc[-1]
    
    # Trend direction
    if current_price > ma_20 > ma_50:
        direction = 'bullish'
        strength = min((current_price - ma_50) / ma_50 * 10, 1.0)
    elif current_price < ma_20 < ma_50:
        direction = 'bearish'
        strength = min((ma_50 - current_price) / ma_50 * 10, 1.0)
    else:
        direction = 'sideways'
        strength = 0.5
    
    # Structure score based on higher highs/lower lows
    structure_score = calculate_structure_score(df)
    
    return {
        'direction': direction,
        'strength': strength,
        'structure_score': structure_score
    }

def calculate_structure_score(df: pd.DataFrame) -> float:
    """Calculate market structure score based on swing points"""
    
    if len(df) < 10:
        return 0.5
    
    # Find recent swing points
    highs = df['high'].rolling(5, center=True).max() == df['high']
    lows = df['low'].rolling(5, center=True).min() == df['low']
    
    recent_highs = df[highs]['high'].tail(3).tolist()
    recent_lows = df[lows]['low'].tail(3).tolist()
    
    # Check for higher highs/higher lows (bullish structure)
    bullish_structure = 0
    if len(recent_highs) >= 2:
        if recent_highs[-1] > recent_highs[-2]:
            bullish_structure += 0.5
    if len(recent_lows) >= 2:
        if recent_lows[-1] > recent_lows[-2]:
            bullish_structure += 0.5
    
    # Check for lower highs/lower lows (bearish structure)
    bearish_structure = 0
    if len(recent_highs) >= 2:
        if recent_highs[-1] < recent_highs[-2]:
            bearish_structure += 0.5
    if len(recent_lows) >= 2:
        if recent_lows[-1] < recent_lows[-2]:
            bearish_structure += 0.5
    
    # Return structure score (0 = bearish, 0.5 = sideways, 1 = bullish)
    if bullish_structure > bearish_structure:
        return 0.5 + (bullish_structure / 2)
    elif bearish_structure > bullish_structure:
        return 0.5 - (bearish_structure / 2)
    else:
        return 0.5

def extract_key_levels(df: pd.DataFrame, order_blocks: List, fvgs: List, liquidity_zones: List) -> List[float]:
    """Extract key price levels from SMC structures"""
    
    levels = []
    
    # Add order block levels
    for ob in order_blocks[:3]:
        levels.extend([ob.high, ob.low])
    
    # Add FVG levels
    for fvg in fvgs[:3]:
        levels.extend([fvg.high, fvg.low])
    
    # Add liquidity zone levels
    for lz in liquidity_zones[:2]:
        levels.extend([lz.high, lz.low])
    
    # Add recent swing highs/lows
    if len(df) >= 20:
        recent_data = df.tail(20)
        levels.append(recent_data['high'].max())
        levels.append(recent_data['low'].min())
    
    # Remove duplicates and sort
    levels = sorted(list(set(levels)))
    
    return levels

def calculate_timeframe_confluence(order_blocks: List, fvgs: List, liquidity_zones: List, 
                                 bos_events: List, trend_analysis: Dict) -> float:
    """Calculate confluence score for a timeframe"""
    
    score = 0.0
    
    # Structure count scoring
    score += min(len(order_blocks) / 5, 1.0) * 0.25
    score += min(len(fvgs) / 5, 1.0) * 0.20
    score += min(len(liquidity_zones) / 3, 1.0) * 0.15
    score += min(len(bos_events) / 3, 1.0) * 0.15
    
    # Trend strength scoring
    score += trend_analysis['strength'] * 0.15
    score += trend_analysis['structure_score'] * 0.10
    
    return min(score, 1.0)

def calculate_entry_exit_levels(analyses: Dict, current_price: float, signal_type: str) -> Dict:
    """Calculate entry and exit levels based on multi-timeframe analysis"""
    
    if signal_type == 'wait':
        return {
            'entry': current_price,
            'stop_loss': current_price * 0.98,
            'tp1': current_price * 1.02,
            'tp2': current_price * 1.04,
            'tp3': current_price * 1.06,
            'risk_reward': 1.0
        }
    
    # Get key levels from higher timeframes
    all_levels = []
    for tf, analysis in analyses.items():
        all_levels.extend(analysis.key_levels)
    
    all_levels = sorted(list(set(all_levels)))
    
    if signal_type == 'buy':
        # Find support levels below current price for stop loss
        support_levels = [level for level in all_levels if level < current_price]
        stop_loss = support_levels[-1] if support_levels else current_price * 0.98
        
        # Find resistance levels above current price for take profits
        resistance_levels = [level for level in all_levels if level > current_price]
        
        if len(resistance_levels) >= 3:
            tp1, tp2, tp3 = resistance_levels[:3]
        elif len(resistance_levels) == 2:
            tp1, tp2 = resistance_levels
            tp3 = tp2 * 1.02
        elif len(resistance_levels) == 1:
            tp1 = resistance_levels[0]
            tp2 = tp1 * 1.02
            tp3 = tp1 * 1.04
        else:
            tp1 = current_price * 1.02
            tp2 = current_price * 1.04
            tp3 = current_price * 1.06
            
    else:  # sell
        # Find resistance levels above current price for stop loss
        resistance_levels = [level for level in all_levels if level > current_price]
        stop_loss = resistance_levels[0] if resistance_levels else current_price * 1.02
        
        # Find support levels below current price for take profits
        support_levels = [level for level in all_levels if level < current_price]
        support_levels.reverse()  # Start from closest to current price
        
        if len(support_levels) >= 3:
            tp1, tp2, tp3 = support_levels[:3]
        elif len(support_levels) == 2:
            tp1, tp2 = support_levels
            tp3 = tp2 * 0.98
        elif len(support_levels) == 1:
            tp1 = support_levels[0]
            tp2 = tp1 * 0.98
            tp3 = tp1 * 0.96
        else:
            tp1 = current_price * 0.98
            tp2 = current_price * 0.96
            tp3 = current_price * 0.94
    
    # Calculate risk/reward ratio
    risk = abs(current_price - stop_loss)
    reward = abs(tp1 - current_price)
    risk_reward = reward / risk if risk > 0 else 1.0
    
    return {
        'entry': current_price,
        'stop_loss': stop_loss,
        'tp1': tp1,
        'tp2': tp2,
        'tp3': tp3,
        'risk_reward': risk_reward
    }

def identify_supporting_factors(analyses: Dict, signal_type: str) -> List[str]:
    """Identify supporting factors for the signal"""
    
    factors = []
    
    # Count timeframe alignment
    aligned_timeframes = sum(1 for analysis in analyses.values() 
                           if analysis.trend_direction == signal_type.replace('buy', 'bullish').replace('sell', 'bearish'))
    
    if aligned_timeframes >= 3:
        factors.append(f"Strong timeframe alignment ({aligned_timeframes}/{len(analyses)})")
    elif aligned_timeframes >= 2:
        factors.append(f"Good timeframe alignment ({aligned_timeframes}/{len(analyses)})")
    
    # Check for recent BOS events
    recent_bos = sum(1 for analysis in analyses.values() if analysis.bos_events)
    if recent_bos >= 2:
        factors.append("Multiple BOS confirmations")
    
    # Check confluence scores
    high_confluence = sum(1 for analysis in analyses.values() if analysis.confluence_score > 0.7)
    if high_confluence >= 2:
        factors.append("High confluence across timeframes")
    
    # Check market structure
    strong_structure = sum(1 for analysis in analyses.values() if analysis.market_structure_score > 0.7)
    if strong_structure >= 2:
        factors.append("Strong market structure")
    
    return factors

def calculate_risk_level(analyses: Dict, confidence: float) -> str:
    """Calculate overall risk level"""
    
    if confidence >= 0.8:
        return 'low'
    elif confidence >= 0.6:
        return 'medium'
    else:
        return 'high'
