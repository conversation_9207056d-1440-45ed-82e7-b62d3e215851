(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4403],{1414:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC","icon-only":"icon-only-D4RPB3ZC",link:"link-D4RPB3ZC","color-brand":"color-brand-D4RPB3ZC","variant-primary":"variant-primary-D4RPB3ZC","variant-secondary":"variant-secondary-D4RPB3ZC","color-gray":"color-gray-D4RPB3ZC","color-green":"color-green-D4RPB3ZC","color-red":"color-red-D4RPB3ZC","color-black":"color-black-D4RPB3ZC","size-xsmall":"size-xsmall-D4RPB3ZC","start-icon-wrap":"start-icon-wrap-D4RPB3ZC","end-icon-wrap":"end-icon-wrap-D4RPB3ZC","with-start-icon":"with-start-icon-D4RPB3ZC","with-end-icon":"with-end-icon-D4RPB3ZC","size-small":"size-small-D4RPB3ZC","size-medium":"size-medium-D4RPB3ZC","size-large":"size-large-D4RPB3ZC","size-xlarge":"size-xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC","adjust-position":"adjust-position-D4RPB3ZC","first-row":"first-row-D4RPB3ZC","first-col":"first-col-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC","text-wrap":"text-wrap-D4RPB3ZC","multiline-content":"multiline-content-D4RPB3ZC","secondary-text":"secondary-text-D4RPB3ZC","primary-text":"primary-text-D4RPB3ZC"}},81026:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},7236:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw",
"after-slot":"after-slot-W53jtLjw"}},30930:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},83073:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",footer:"footer-PhMf7PhQ",submitButton:"submitButton-PhMf7PhQ",buttons:"buttons-PhMf7PhQ"}},94720:(e,t,n)=>{"use strict";n.d(t,{Button:()=>R});var o=n(50959),r=n(97754),s=n(95604),l=n(9745),i=n(1414),a=n.n(i);function c(e){const{color:t="brand",size:n="medium",variant:o="primary",stretch:l=!1,icon:i,startIcon:c,endIcon:u,iconOnly:d=!1,className:h,isGrouped:m,cellState:f,disablePositionAdjustment:p=!1,primaryText:b,secondaryText:g,isAnchor:C=!1}=e,R=function(e){let t="";return 0!==e&&(1&e&&(t=r(t,a()["no-corner-top-left"])),2&e&&(t=r(t,a()["no-corner-top-right"])),4&e&&(t=r(t,a()["no-corner-bottom-right"])),8&e&&(t=r(t,a()["no-corner-bottom-left"]))),t}((0,s.getGroupCellRemoveRoundBorders)(f));return r(h,a().button,a()[`size-${n}`],a()[`color-${t}`],a()[`variant-${o}`],l&&a().stretch,(i||c)&&a()["with-start-icon"],u&&a()["with-end-icon"],d&&a()["icon-only"],R,m&&a().grouped,m&&!p&&a()["adjust-position"],m&&f.isTop&&a()["first-row"],m&&f.isLeft&&a()["first-col"],b&&g&&a()["multiline-content"],C&&a().link)}function u(e){const{startIcon:t,icon:n,iconOnly:r,children:s,endIcon:i,primaryText:c,secondaryText:u}=e,d=null!=t?t:n,h=!(t||n||i||r)&&!s&&c&&u;return o.createElement(o.Fragment,null,d&&o.createElement(l.Icon,{icon:d,className:a()["start-icon-wrap"]}),s&&o.createElement("span",{className:a().content},s),i&&!r&&o.createElement(l.Icon,{icon:i,className:a()["end-icon-wrap"]}),h&&function(e){return e.primaryText&&e.secondaryText&&o.createElement("div",{className:a()["text-wrap"]},o.createElement("span",{className:a()["primary-text"]}," ",e.primaryText," "),"string"==typeof e.secondaryText?o.createElement("span",{className:a()["secondary-text"]}," ",e.secondaryText," "):o.createElement("span",{className:a()["secondary-text"]},o.createElement("span",null,e.secondaryText.firstLine),o.createElement("span",null,e.secondaryText.secondLine)))}(e))}var d=n(86332),h=n(90186);function m(e){const{className:t,color:n,variant:o,size:r,stretch:s,animated:l,icon:i,iconOnly:a,startIcon:c,endIcon:u,primaryText:d,secondaryText:m,...f}=e;return{...f,...(0,h.filterDataProps)(e),...(0,h.filterAriaProps)(e)}}function f(e){const{reference:t,...n}=e,{isGrouped:r,cellState:s,disablePositionAdjustment:l}=(0,o.useContext)(d.ControlGroupContext),i=c({...n,isGrouped:r,cellState:s,disablePositionAdjustment:l});return o.createElement("button",{...m(n),className:i,ref:t},o.createElement(u,{...n}))}function p(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function b(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"
}}function g(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}function C(e){const{intent:t,size:n,appearance:o,useFullWidth:r,icon:s,...l}=e;return{...l,color:b(t),size:g(n),variant:p(o),stretch:r,startIcon:s}}function R(e){return o.createElement(f,{...C(e)})}},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>C,InputClasses:()=>p});var o=n(50959),r=n(97754),s=n(50151),l=n(38528),i=n(90186),a=n(86332),c=n(95604);var u=n(81026),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function m(e,t,n,o){const{removeRoundBorder:s,className:l,intent:i="default",borderStyle:a="thin",size:u,highlight:m,disabled:f,readonly:p,stretch:b,noReadonlyStyles:g,isFocused:C}=e,R=h(null!=s?s:(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${i}`],d()[`border-${a}`],u&&d()[`size-${u}`],R,m&&d()["with-highlight"],f&&d().disabled,p&&!g&&d().readonly,C&&d().focused,b&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],l)}function f(e,t,n){const{highlight:o,highlightRemoveRoundBorder:s}=e;if(!o)return d().highlight;const l=h(null!=s?s:(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],l)}const p={FontSizeMedium:(0,s.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,s.ensureDefined)(d()["font-size-large"])},b={passive:!1};function g(e,t){const{style:n,id:r,role:s,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:p,onMouseUp:g,onKeyDown:C,onClick:R,tabIndex:v,startSlot:y,middleSlot:x,endSlot:D,onWheel:P,onWheelNoPassive:S=null,size:w}=e,{isGrouped:N,cellState:B,disablePositionAdjustment:z=!1}=(0,o.useContext)(a.ControlGroupContext),Z=function(e,t=null,n){const r=(0,o.useRef)(null),s=(0,o.useRef)(null),l=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),i=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),a=(0,o.useCallback)((e=>{i(),r.current=e,l()}),[]);return(0,o.useEffect)((()=>(s.current=[e,t,n],l(),i)),[e,t,n]),a}("wheel",S,b);return o.createElement("span",{style:n,id:r,role:s,className:m(e,N,B,z),tabIndex:v,ref:(0,l.useMergedRefs)([t,Z]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:p,onMouseUp:g,onKeyDown:C,onClick:R,onWheel:P,...(0,i.filterDataProps)(e),...(0,i.filterAriaProps)(e)},y,x,D,o.createElement("span",{className:f(e,B,w)}))}g.displayName="ControlSkeleton";const C=o.forwardRef(g)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,
MiddleSlot:()=>a,StartSlot:()=>i});var o=n(50959),r=n(97754),s=n(7236),l=n.n(s);function i(e){const{className:t,interactive:n=!0,icon:s=!1,children:i}=e;return o.createElement("span",{className:r(l()["inner-slot"],n&&l().interactive,s&&l().icon,t)},i)}function a(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(l()["inner-slot"],l()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:s=!1,children:i}=e;return o.createElement("span",{className:r(l()["inner-slot"],n&&l().interactive,s&&l().icon,t)},i)}function u(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(l()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>C});var o=n(50959),r=n(97754),s=n(90186),l=n(47201),i=n(48907),a=n(38528),c=n(48027),u=n(29202),d=n(45812),h=n(67029),m=n(78274),f=n(30930),p=n.n(f);function b(e){return!(0,s.isAriaAttribute)(e)&&!(0,s.isDataAttribute)(e)}function g(e){const{id:t,title:n,role:l,tabIndex:i,placeholder:a,name:c,type:u,value:d,defaultValue:f,draggable:g,autoComplete:C,autoFocus:R,maxLength:v,min:y,max:x,step:D,pattern:P,inputMode:S,onSelect:w,onFocus:N,onBlur:B,onKeyDown:z,onKeyUp:Z,onKeyPress:E,onChange:k,onDragStart:F,size:W="small",className:M,inputClassName:_,disabled:I,readonly:K,containerTabIndex:O,startSlot:T,endSlot:A,reference:j,containerReference:H,onContainerFocus:L,...U}=e,G=(0,s.filterProps)(U,b),V={...(0,s.filterAriaProps)(U),...(0,s.filterDataProps)(U),id:t,title:n,role:l,tabIndex:i,placeholder:a,name:c,type:u,value:d,defaultValue:f,draggable:g,autoComplete:C,autoFocus:R,maxLength:v,min:y,max:x,step:D,pattern:P,inputMode:S,onSelect:w,onFocus:N,onBlur:B,onKeyDown:z,onKeyUp:Z,onKeyPress:E,onChange:k,onDragStart:F};return o.createElement(h.ControlSkeleton,{...G,disabled:I,readonly:K,tabIndex:O,className:r(p().container,M),size:W,ref:H,onFocus:L,startSlot:T,middleSlot:o.createElement(m.MiddleSlot,null,o.createElement("input",{...V,className:r(p().input,p()[`size-${W}`],_,T&&p()["with-start-slot"],A&&p()["with-end-slot"]),disabled:I,readOnly:K,ref:j})),endSlot:A})}function C(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:s,onBlur:h,reference:m,containerReference:f=null}=e,p=(0,o.useRef)(null),b=(0,o.useRef)(null),[C,R]=(0,u.useFocus)(),v=t?void 0:C?-1:r,y=t?void 0:C?r:-1,{isMouseDown:x,handleMouseDown:D,handleMouseUp:P}=(0,d.useIsMouseDown)(),S=(0,l.createSafeMulticastEventHandler)(R.onFocus,(function(e){n&&!x.current&&(0,i.selectAllContent)(e.currentTarget)}),s),w=(0,l.createSafeMulticastEventHandler)(R.onBlur,h),N=(0,o.useCallback)((e=>{p.current=e,m&&("function"==typeof m&&m(e),"object"==typeof m&&(m.current=e))}),[p,m]);return o.createElement(g,{...e,isFocused:C,containerTabIndex:v,tabIndex:y,onContainerFocus:function(e){b.current===e.target&&null!==p.current&&p.current.focus()},onFocus:S,onBlur:w,reference:N,containerReference:(0,a.useMergedRefs)([b,f]),onMouseDown:D,onMouseUp:P})}},66686:(e,t,n)=>{"use strict";n.d(t,{useComposedKeyboardActionHandlers:()=>i,
useKeyboardActionHandler:()=>l,useKeyboardClose:()=>u,useKeyboardEventHandler:()=>a,useKeyboardOpen:()=>d,useKeyboardToggle:()=>c});var o=n(50959),r=n(3343);const s=()=>!0;function l(e,t,n=s){return(0,o.useCallback)((o=>{const r=e.map((e=>"function"==typeof e?e():e));return!(!n(o)||!r.includes(o))&&(t(o),!0)}),[...e,t,n])}function i(...e){return(0,o.useCallback)((t=>{for(const n of e)if(n(t))return!0;return!1}),[...e])}function a(e,t=!0){const n=i(...e);return(0,o.useCallback)((e=>{n((0,r.hashFromEvent)(e))&&t&&e.preventDefault()}),[n])}function c(e,t=!0){return l([13,32],e,(function(e){if(13===e)return t;return!0}))}function u(e,t){return l([9,(0,o.useCallback)((()=>r.Modifiers.Shift+9),[]),27],t,(0,o.useCallback)((()=>e),[e]))}function d(e,t){return l([40,38],t,(0,o.useCallback)((()=>!e),[e]))}},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>s});var o=n(47201),r=n(29202);function s(e){const{onFocus:t,onBlur:n,intent:s,highlight:l,disabled:i}=e,[a,c]=(0,r.useFocus)(void 0,i),u=(0,o.createSafeMulticastEventHandler)(i?void 0:c.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(i?void 0:c.onBlur,n);return{...e,intent:s||(a?"primary":"default"),highlight:null!=l?l:a,onFocus:u,onBlur:d}}},7953:(e,t,n)=>{"use strict";n.d(t,{useDisclosure:()=>c});var o=n(50959),r=n(50151),s=n(54717),l=n(29202),i=n(47201),a=n(22064);function c(e){const{id:t,listboxId:n,disabled:c,buttonTabIndex:u=0,onFocus:d,onBlur:h,onClick:m}=e,[f,p]=(0,o.useState)(!1),[b,g]=(0,l.useFocus)(),C=b||f,R=(null!=n?n:void 0!==t)?(0,a.createDomId)(t,"listbox"):void 0,v=(0,o.useRef)(null),y=(0,o.useCallback)((e=>{var t;return null===(t=v.current)||void 0===t?void 0:t.focus(e)}),[v]),x=(0,o.useRef)(null),D=(0,o.useCallback)((()=>(0,r.ensureNotNull)(x.current).focus()),[x]),P=(0,o.useCallback)((()=>p(!0)),[p]),S=(0,o.useCallback)(((e=!1,t=!1)=>{p(!1);const{activeElement:n}=document;n&&(0,s.isTextEditingField)(n)||t||y({preventScroll:e})}),[p,y]),w=(0,o.useCallback)((()=>{f?S():P()}),[f,S,P]),N=c?[]:[d,g.onFocus],B=c?[]:[h,g.onBlur],z=c?[]:[m,w],Z=(0,i.createSafeMulticastEventHandler)(...N),E=(0,i.createSafeMulticastEventHandler)(...B),k=(0,i.createSafeMulticastEventHandler)(...z);return{listboxId:R,isOpened:f,isFocused:C,buttonTabIndex:c?-1:u,listboxTabIndex:-1,open:P,close:S,toggle:w,onOpen:D,buttonFocusBindings:{onFocus:Z,onBlur:E},onButtonClick:k,buttonRef:v,listboxRef:x,buttonAria:{"aria-controls":f?R:void 0,"aria-expanded":f,"aria-disabled":c}}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const s={onFocus:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,s]}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},
36762:(e,t,n)=>{"use strict";n.d(t,{useItemsKeyboardNavigation:()=>l});var o=n(50959),r=n(66686);function s(e,t){return e>=0?e%t:(t-Math.abs(e)%t)%t}function l(e,t,n,l,i,a={},c){const u=(0,o.useCallback)((e=>{const o=t.findIndex(n);if(o===t.length-1&&!i)return void((null==c?void 0:c.onFailNext)&&c.onFailNext(e));const r=s(o+1,t.length);l&&l(t[r],"next")}),[t,n,l,i]),d=(0,o.useCallback)((e=>{const o=t.findIndex(n);if(0===o&&!i)return void((null==c?void 0:c.onFailPrev)&&c.onFailPrev(e));const r=s(o-1,t.length);l&&l(t[r],"previous")}),[t,n,l,i]),h=(0,o.useCallback)((()=>{l&&l(t[0],"first")}),[l,t]),m=(0,o.useCallback)((()=>{l&&l(t[t.length-1],"last")}),[l,t]),f=(0,o.useMemo)((()=>(e=>({next:[40,()=>e()?37:39],previous:[38,()=>e()?39:37],first:[33,()=>e()?35:36],last:[34,()=>e()?36:35]}))(e)),[e]),{next:p=f.next,previous:b=f.previous,first:g=f.first,last:C=f.last}=a;return(0,r.useComposedKeyboardActionHandlers)((0,r.useKeyboardActionHandler)(p,u),(0,r.useKeyboardActionHandler)(b,d),(0,r.useKeyboardActionHandler)(g,h),(0,r.useKeyboardActionHandler)(C,m))}},16921:(e,t,n)=>{"use strict";n.d(t,{useKeepActiveItemIntoView:()=>d});var o=n(50959),r=n(50151),s=n(74991);const l={duration:200,additionalScroll:0},i={vertical:{scrollSize:"scrollHeight",clientSize:"clientHeight",start:"top",end:"bottom",size:"height"},horizontal:{scrollSize:"scrollWidth",clientSize:"clientWidth",start:"left",end:"right",size:"width"}};function a(e,t){const n=i[e];return t[n.scrollSize]>t[n.clientSize]}function c(e,t,n,o,r,l){const a=function(e,t,n,o=0){const r=i[e];return{start:-1*o,middle:-1*(Math.floor(n[r.size]/2)-Math.floor(t[r.size]/2)),end:-1*(n[r.size]-t[r.size])+o}}(e,o,r,l.additionalScroll);let c=0;if(function(e,t,n){const o=i[e];return t[o.start]<n[o.start]-n[o.size]/2||t[o.end]>n[o.end]+n[o.size]/2}(e,o,r))c=a.middle;else{const t=function(e,t,n,o=0){const r=i[e],s=t[r.start]+Math.floor(t[r.size]/2),l=n[r.start]+Math.floor(n[r.size]/2);return{start:t[r.start]-n[r.start]-o,middle:s-l,end:t[r.end]-n[r.end]+o}}(e,o,r,l.additionalScroll),n=function(e){const{start:t,middle:n,end:o}=e,r=new Map([[Math.abs(t),{key:"start",value:Math.sign(t)}],[Math.abs(n),{key:"middle",value:Math.sign(n)}],[Math.abs(o),{key:"end",value:Math.sign(o)}]]),s=Math.min(...r.keys());return r.get(s)}(t);c=void 0!==n?a[n.key]:0}return function(e){const{additionalScroll:t=0,duration:n=s.dur,func:o=s.easingFunc.easeInOutCubic,onScrollEnd:r,target:l,wrap:i,direction:a="vertical"}=e;let{targetRect:c,wrapRect:u}=e;c=null!=c?c:l.getBoundingClientRect(),u=null!=u?u:i.getBoundingClientRect();const d=("vertical"===a?c.top-u.top:c.left-u.left)+t,h="vertical"===a?"scrollTop":"scrollLeft",m=i?i[h]:0;let f,p=0;return p=window.requestAnimationFrame((function e(t){let s;if(f?s=t-f:(s=0,f=t),s>=n)return i[h]=m+d,void(r&&r());const l=m+d*o(s/n);i[h]=Math.floor(l),p=window.requestAnimationFrame(e)})),function(){window.cancelAnimationFrame(p),r&&r()}}({...l,target:t,targetRect:o,wrap:n,wrapRect:r,additionalScroll:c,direction:e})}class u{constructor(e=null){this._container=null,
this._lastScrolledElement=null,this._stopVerticalScroll=null,this._stopHorizontalScroll=null,this._container=e}scrollTo(e,t=l){if(null!==this._container&&null!==e&&!function(e,t){const n=e.getBoundingClientRect(),o=t.getBoundingClientRect();return n.top>=o.top&&n.bottom<=o.bottom&&n.left>=o.left&&n.right<=o.right}(e,this._container)){const n=e.getBoundingClientRect(),o=this._container.getBoundingClientRect();this.stopScroll(),a("vertical",this._container)&&(this._stopVerticalScroll=c("vertical",e,this._container,n,o,this._modifyOptions("vertical",t))),a("horizontal",this._container)&&(this._stopHorizontalScroll=c("horizontal",e,this._container,n,o,this._modifyOptions("horizontal",t)))}this._lastScrolledElement=e}scrollToLastElement(e){this.scrollTo(this._lastScrolledElement,e)}stopScroll(){null!==this._stopVerticalScroll&&this._stopVerticalScroll(),null!==this._stopHorizontalScroll&&this._stopHorizontalScroll()}getContainer(){return this._container}setContainer(e){var t;this._container=e,(null===(t=this._container)||void 0===t?void 0:t.contains(this._lastScrolledElement))||(this._lastScrolledElement=null)}destroy(){this.stopScroll(),this._container=null,this._lastScrolledElement=null}_handleScrollEnd(e){"vertical"===e?this._stopVerticalScroll=null:this._stopHorizontalScroll=null}_modifyOptions(e,t){return Object.assign({},t,{onScrollEnd:()=>{this._handleScrollEnd(e),void 0!==t.onScrollEnd&&t.onScrollEnd()}})}}function d(e={}){const{activeItem:t,getKey:n,...s}=e,l=(0,o.useRef)(null),i=(0,o.useRef)(new Map),a=function(e){const t=(0,o.useRef)(null);return(0,o.useEffect)((()=>(t.current=new u(e),()=>(0,r.ensureNotNull)(t.current).destroy())),[]),t}(l.current),c=(0,o.useCallback)((()=>{null!==a.current&&null!==l.current&&a.current.getContainer()!==l.current&&a.current.setContainer(l.current)}),[a,l]),d=(0,o.useCallback)((e=>{l.current=e}),[l]),h=(0,o.useCallback)(((e,t)=>{const o=n?n(e):e;t?i.current.set(o,t):i.current.delete(o)}),[i,n]),m=(0,o.useCallback)(((e,t)=>{if(!e)return;const o=n?n(e):e,s=i.current.get(o);s&&(c(),(0,r.ensureNotNull)(a.current).scrollTo(s,t))}),[i,a,n]);return(0,o.useEffect)((()=>m(t,s)),[m,t]),[d,h,m]}},38528:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>s});var o=n(50959),r=n(53017);function s(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},22064:(e,t,n)=>{"use strict";n.d(t,{createDomId:()=>a,joinDomIds:()=>c});const o=/\s/g;function r(e){return"string"==typeof e}function s(e){switch(typeof e){case"string":return e;case"number":case"bigint":return e.toString(10);case"boolean":case"symbol":return e.toString();default:return null}}function l(e){return e.trim().length>0}function i(e){return e.replace(o,"-")}function a(...e){const t=e.map(s).filter(r).filter(l).map(i);return(t.length>0&&t[0].startsWith("id_")?t:["id",...t]).join("_")}function c(...e){return e.map(s).filter(r).filter(l).join(" ")}},48907:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},50182:(e,t,n)=>{"use strict";n.d(t,{AdaptiveConfirmDialog:()=>h})
;var o=n(50959),r=n(97754),s=n.n(r),l=n(94720),i=n(50151),a=n(44352),c=n(68335),u=n(35057),d=n(83073);class h extends o.PureComponent{constructor(){super(...arguments),this._dialogRef=o.createRef(),this._handleClose=()=>{const{defaultActionOnClose:e,onSubmit:t,onCancel:n,onClose:o}=this.props;switch(e){case"submit":t();break;case"cancel":n()}o()},this._handleCancel=()=>{this.props.onCancel(),this.props.onClose()},this._handleKeyDown=e=>{const{onSubmit:t,submitButtonDisabled:n,submitOnEnterKey:o}=this.props;13===(0,c.hashFromEvent)(e)&&o&&(e.preventDefault(),n||t())}}render(){const{render:e,onClose:t,onSubmit:n,onCancel:r,footerLeftRenderer:s,submitButtonText:l,submitButtonDisabled:i,defaultActionOnClose:a,submitOnEnterKey:c,...d}=this.props;return o.createElement(u.AdaptivePopupDialog,{...d,ref:this._dialogRef,onKeyDown:this._handleKeyDown,render:this._renderChildren(),onClose:this._handleClose})}focus(){(0,i.ensureNotNull)(this._dialogRef.current).focus()}_renderChildren(){return e=>{const{render:t,footerLeftRenderer:r,additionalButtons:i,submitButtonText:c,submitButtonDisabled:u,onSubmit:h,cancelButtonText:m,showCancelButton:f=!0,submitButtonClassName:p,cancelButtonClassName:b,buttonsWrapperClassName:g}=this.props;return o.createElement(o.Fragment,null,t(e),o.createElement("div",{className:d.footer},r&&r(e.isSmallWidth),o.createElement("div",{className:s()(d.buttons,g)},i,f&&o.createElement(l.Button,{className:b,name:"cancel",appearance:"stroke",onClick:this._handleCancel},null!=m?m:a.t(null,void 0,n(20036))),o.createElement("span",{className:d.submitButton},o.createElement(l.Button,{className:p,disabled:u,name:"submit",onClick:h,"data-name":"submit-button"},null!=c?c:a.t(null,void 0,n(68988)))))))}}}h.defaultProps={defaultActionOnClose:"submit",submitOnEnterKey:!0}},4523:(e,t,n)=>{"use strict";n.d(t,{PopupMenuDisclosureView:()=>u});var o=n(50959),r=n(20520),s=n(50151);const l={x:0,y:0};function i(e,t,n){return(0,o.useCallback)((()=>function(e,t,{x:n=l.x,y:o=l.y}=l){const r=(0,s.ensureNotNull)(e).getBoundingClientRect(),i={x:r.left+n,y:r.top+r.height+o,indentFromWindow:{top:4,bottom:4,left:4,right:4}};return t&&(i.overrideWidth=r.width),i}(e.current,t,n)),[e,t])}var a=n(86240);const c=parseInt(a["size-header-height"]);function u(e){const{button:t,popupChildren:n,buttonRef:s,listboxId:l,listboxClassName:a,listboxTabIndex:u,matchButtonAndListboxWidths:d,isOpened:h,scrollWrapReference:m,listboxReference:f,onClose:p,onOpen:b,onListboxFocus:g,onListboxBlur:C,onListboxKeyDown:R,listboxAria:v,repositionOnScroll:y=!0,closeOnHeaderOverlap:x=!1,popupPositionCorrection:D={x:0,y:0},popupPosition:P}=e,S=i(s,d,D),w=x?c:0;return o.createElement(o.Fragment,null,t,o.createElement(r.PopupMenu,{...v,id:l,className:a,tabIndex:u,isOpened:h,position:P||S,repositionOnScroll:y,onClose:p,onOpen:b,doNotCloseOn:s.current,reference:f,scrollWrapReference:m,onFocus:g,onBlur:C,onKeyDown:R,closeOnScrollOutsideOffset:w},n))}},26597:(e,t,n)=>{"use strict";n.d(t,{useKeyboardActionHandler:()=>o.useKeyboardActionHandler,
useKeyboardClose:()=>o.useKeyboardClose,useKeyboardEventHandler:()=>o.useKeyboardEventHandler,useKeyboardOpen:()=>o.useKeyboardOpen,useKeyboardToggle:()=>o.useKeyboardToggle});var o=n(66686)},86240:e=>{"use strict";e.exports=JSON.parse('{"size-header-height":"64px","media-mf-phone-landscape":"screen and (min-width: 568px)"}')}}]);