(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3732],{20747:a=>{a.exports=["Lakukan semula"]},9846:a=>{a.exports="A"},55765:a=>{a.exports="L"},14642:a=>{a.exports=["Gelap"]},69841:a=>{a.exports=["Cerah"]},673:a=>{a.exports=Object.create(null),a.exports.d_dates="d",a.exports.h_dates="h",a.exports.m_dates="m",a.exports.s_dates="s",a.exports.in_dates=["dalam"]},97840:a=>{a.exports="d"},64302:a=>{a.exports="h"},79442:a=>{a.exports="m"},22448:a=>{a.exports="s"},16493:a=>{a.exports=["{title} salin"]},13395:a=>{a.exports="D"},37720:a=>{a.exports="M"},69838:a=>{a.exports="R"},59231:a=>{a.exports="T"},85521:a=>{a.exports="W"},13994:a=>{a.exports="h"},6791:a=>{a.exports="m"},2949:a=>{a.exports="s"},77297:a=>{a.exports="C"},56723:a=>{a.exports="H"},5801:a=>{a.exports="HL2"},98865:a=>{a.exports="HLC3"},42659:a=>{a.exports="OHLC4"},4292:a=>{a.exports="L"},78155:a=>{a.exports="O"},88601:a=>{a.exports=Object.create(null),a.exports.Close_input=["Tutup"],a.exports.Back_input=["Kembali"],a.exports.Minimize_input=["Kurangkan"],a.exports["Hull MA_input"]="Hull MA",a.exports["{number} item_combobox_input"]="{number} item",a.exports.Length_input=["Panjang"],a.exports.Plot_input="Plot",a.exports.Zero_input=["Sifar"],a.exports.Signal_input=["Isyarat"],a.exports.Long_input=["Panjang"],a.exports.Short_input=["Singkat"],a.exports.UpperLimit_input=["Had Atas"],a.exports.LowerLimit_input=["Had Bawah"],a.exports.Offset_input=["Ofset"],a.exports.length_input=["panjang"],a.exports.mult_input="mult",a.exports.short_input=["singkat"],a.exports.long_input=["panjang"],a.exports.Limit_input=["Had"],a.exports.Move_input=["Bergerak"],a.exports.Value_input=["Nilai"],a.exports.Method_input=["Kaedah"],a.exports["Values in status line_input"]=["Nilai di garisan status"],a.exports["Labels on price scale_input"]=["Label-label pada skala harga"],a.exports["Accumulation/Distribution_input"]="Accumulation/Distribution",a.exports.ADR_B_input="ADR_B",a.exports["Equality Line_input"]=["Garis Kesamaan"],a.exports["Window Size_input"]=["Saiz Tetingkap"],a.exports.Sigma_input="Sigma",a.exports["Aroon Up_input"]="Aroon Up",a.exports["Aroon Down_input"]="Aroon Down",a.exports.Upper_input=["Atas"],a.exports.Lower_input=["Bawah"],a.exports.Deviation_input=["Sisihan"],a.exports["Levels Format_input"]=["Tahap Format"],a.exports["Labels Position_input"]=["Posisi Label"],a.exports["0 Level Color_input"]=["0 Aras Warna"],a.exports["0.236 Level Color_input"]=["0.236 Aras Warna"],a.exports["0.382 Level Color_input"]=["0.382 Aras Warna"],a.exports["0.5 Level Color_input"]=["0.5 Aras Warna"],a.exports["0.618 Level Color_input"]=["0.618 Aras Warna"],a.exports["0.65 Level Color_input"]=["0.65 Aras Warna"],a.exports["0.786 Level Color_input"]=["0.786 Aras Warna"],a.exports["1 Level Color_input"]=["1 Aras Warna"],a.exports["1.272 Level Color_input"]=["1.272 Aras Warna"],a.exports["1.414 Level Color_input"]=["1.414 Aras Warna"],a.exports["1.618 Level Color_input"]=["1.618 Aras Warna"],a.exports["1.65 Level Color_input"]=["1.65 Aras Warna"],
a.exports["2.618 Level Color_input"]=["2.618 Aras Warna"],a.exports["2.65 Level Color_input"]=["2.65 Aras Warna"],a.exports["3.618 Level Color_input"]=["3.618 Aras Warna"],a.exports["3.65 Level Color_input"]=["3.65 Tahap Warna"],a.exports["4.236 Level Color_input"]=["4.236 Tahap Warna"],a.exports["-0.236 Level Color_input"]=["-0.236 Tahap Warna"],a.exports["-0.382 Level Color_input"]=["-0.382 Tahap Warna"],a.exports["-0.618 Level Color_input"]=["-0.618 Tahap Warna"],a.exports["-0.65 Level Color_input"]=["-0.65 Tahap Warna"],a.exports.ADX_input="ADX",a.exports["ADX Smoothing_input"]=["Memudahkan ADX"],a.exports["DI Length_input"]=["Jarak DI"],a.exports.Smoothing_input=["Memudahkan"],a.exports.ATR_input="ATR",a.exports.Growing_input=["Berkembang"],a.exports.Falling_input=["Jatuh"],a.exports["Color 0_input"]=["Warna 0"],a.exports["Color 1_input"]=["Warna 1"],a.exports.Source_input=["Sumber"],a.exports.StdDev_input=["Sisihan Piawai"],a.exports.Basis_input=["Asas"],a.exports.Median_input="Median",a.exports["Bollinger Bands %B_input"]="Bollinger Bands %B",a.exports.Overbought_input=["Terlebih Beli"],a.exports.Oversold_input=["Terlebih Jual"],a.exports["Bollinger Bands Width_input"]="Bollinger Bands Width",a.exports["RSI Length_input"]=["Panjang RSI"],a.exports["UpDown Length_input"]=["Panjang Naik Turun"],a.exports["ROC Length_input"]=["Panjang ROC"],a.exports.MF_input="MF",a.exports.resolution_input=["resolusi"],a.exports["Fast Length_input"]=["Jarak cepat"],a.exports["Slow Length_input"]=["Jarak perlahan"],a.exports["Chaikin Oscillator_input"]="Chaikin Oscillator",a.exports.P_input="P",a.exports.X_input="X",a.exports.Q_input="Q",a.exports.p_input="p",a.exports.x_input="x",a.exports.q_input="q",a.exports.Price_input=["Harga"],a.exports["Chande MO_input"]="Chande MO",a.exports["Zero Line_input"]=["Garis Sifar"],a.exports["Color 2_input"]=["Warna 2"],a.exports["Color 3_input"]=["Warna 3"],a.exports["Color 4_input"]=["Warna 4"],a.exports["Color 5_input"]=["Warna 5"],a.exports["Color 6_input"]=["Warna 6"],a.exports["Color 7_input"]=["Warna 7"],a.exports["Color 8_input"]=["Warna 8"],a.exports.CHOP_input="CHOP",a.exports["Upper Band_input"]=["Jalur Atas"],a.exports["Lower Band_input"]=["Jalur Bawah"],a.exports.CCI_input="CCI",a.exports["Smoothing Line_input"]=["Garisan Pelicinan"],a.exports["Smoothing Length_input"]=["Panjang Pelicinan"],a.exports["WMA Length_input"]=["Panjang WMA"],a.exports["Long RoC Length_input"]=["Jarak Panjang RoC"],a.exports["Short RoC Length_input"]=["Jarak RoC Pendek"],a.exports.sym_input="sym",a.exports.Symbol_input=["Simbol"],a.exports.Correlation_input=["Korelasi"],a.exports.Period_input=["Tempoh"],a.exports.Centered_input=["Terpusat"],a.exports["Detrended Price Oscillator_input"]="Detrended Price Oscillator",a.exports.isCentered_input="isCentered",a.exports.DPO_input="DPO",a.exports["ADX smoothing_input"]=["Memudahkan ADX"],a.exports["+DI_input"]="+DI",a.exports["-DI_input"]="-DI",a.exports.DEMA_input="DEMA",a.exports["Multi timeframe_input"]=["Pelbagai rangka masa"],
a.exports.Timeframe_input=["Rangka Masa"],a.exports["Wait for timeframe closes_input"]=["Tunggu rangka masa menutup"],a.exports.Divisor_input=["Pembahagi"],a.exports.EOM_input="EOM",a.exports["Elder's Force Index_input"]=["Indeks Daya Elder"],a.exports.Percent_input=["Peratus"],a.exports.Exponential_input=["Eksponen"],a.exports.Average_input=["Purata"],a.exports["Upper Percentage_input"]=["Peratus Atas"],a.exports["Lower Percentage_input"]=["Peratus Bawah"],a.exports.Fisher_input="Fisher",a.exports.Trigger_input=["Picu"],a.exports.Level_input=["Aras"],a.exports["Trader EMA 1 length_input"]=["Panjang EMA 1 pedagang"],a.exports["Trader EMA 2 length_input"]=["Panjang EMA 2 pedagang"],a.exports["Trader EMA 3 length_input"]=["Panjang EMA 3 pedagang"],a.exports["Trader EMA 4 length_input"]=["Panjang EMA 4 pedagang"],a.exports["Trader EMA 5 length_input"]=["Panjang EMA 5 pedagang"],a.exports["Trader EMA 6 length_input"]=["Panjang EMA 6 pedagang"],a.exports["Investor EMA 1 length_input"]=["Panjang EMA 1 pelabur"],a.exports["Investor EMA 2 length_input"]=["Panjang EMA 2 pelabur"],a.exports["Investor EMA 3 length_input"]=["Panjang EMA 3 pelabur"],a.exports["Investor EMA 4 length_input"]=["Panjang EMA 4 pelabur"],a.exports["Investor EMA 5 length_input"]=["Panjang EMA 5 pelabur"],a.exports["Investor EMA 6 length_input"]=["Panjang EMA 6 pelabur"],a.exports.HV_input="HV",a.exports["Conversion Line Periods_input"]=["Tempoh Garis Penukaran"],a.exports["Base Line Periods_input"]=["Tempoh Garis Dasar"],a.exports["Lagging Span_input"]=["Rentasan Pembebat"],a.exports["Conversion Line_input"]=["Garis Penukaran"],a.exports["Base Line_input"]=["Garis Dasar"],a.exports["Leading Span A_input"]=["Pendulu 1"],a.exports["Leading Span B_input"]=["Rentasan Pembebat 2 Tempoh"],a.exports["Plots Background_input"]=["Latar Belakang Plot"],a.exports["yay Color 0_input"]=["yay Warna 0"],a.exports["yay Color 1_input"]=["yay Warna 1"],a.exports.Multiplier_input=["Pengganda"],a.exports["Bands style_input"]=["Gaya jalur"],a.exports.Middle_input=["Pertengahan"],a.exports.useTrueRange_input=["gunakan TrueRange"],a.exports.ROCLen1_input="ROCLen1",a.exports.ROCLen2_input="ROCLen2",a.exports.ROCLen3_input="ROCLen3",a.exports.ROCLen4_input="ROCLen4",a.exports.SMALen1_input="SMALen1",a.exports.SMALen2_input="SMALen2",a.exports.SMALen3_input="SMALen3",a.exports.SMALen4_input="SMALen4",a.exports.SigLen_input="SigLen",a.exports.KST_input="KST",a.exports.Sig_input="Sig",a.exports.roclen1_input="roclen1",a.exports.roclen2_input="roclen2",a.exports.roclen3_input="roclen3",a.exports.roclen4_input="roclen4",a.exports.smalen1_input="smalen1",a.exports.smalen2_input="smalen2",a.exports.smalen3_input="smalen3",a.exports.smalen4_input="smalen4",a.exports.siglen_input="siglen",a.exports["Upper Deviation_input"]=["Sisihan Atas"],a.exports["Lower Deviation_input"]=["Sisihan Bawah"],a.exports["Use Upper Deviation_input"]=["Gunakan Sisihan Atas"],a.exports["Use Lower Deviation_input"]=["Gunakan Sisihan Bawah"],a.exports.Count_input=["Bilangan"],a.exports.Crosses_input=["Silang"],
a.exports.MOM_input="MOM",a.exports.MA_input="MA",a.exports["Length EMA_input"]=["Panjang EMA"],a.exports["Length MA_input"]=["MA Panjang"],a.exports["Fast length_input"]=["Jarak cepat"],a.exports["Slow length_input"]=["Jarak perlahan"],a.exports["Signal smoothing_input"]=["Memudahkan isyarat"],a.exports["Simple ma(oscillator)_input"]="Simple ma(oscillator)",a.exports["Simple ma(signal line)_input"]=["Ma mudah(garis isyarat)"],a.exports.Histogram_input="Histogram",a.exports.MACD_input="MACD",a.exports.fastLength_input="fastLength",a.exports.slowLength_input="slowLength",a.exports.signalLength_input="signalLength",a.exports.NV_input="NV",a.exports.OnBalanceVolume_input="OnBalanceVolume",a.exports.Start_input=["Mula"],a.exports.Increment_input=["Kenaikan"],a.exports["Max value_input"]=["Nilai Maksimum"],a.exports.ParabolicSAR_input="ParabolicSAR",a.exports.start_input=["mula"],a.exports.increment_input=["kenaikan"],a.exports.maximum_input=["maksimum"],a.exports["Short length_input"]=["Jarak pendek"],a.exports["Long length_input"]=["Jarak panjang"],a.exports.OSC_input="OSC",a.exports.shortlen_input="shortlen",a.exports.longlen_input="longlen",a.exports.PVT_input="PVT",a.exports.ROC_input="ROC",a.exports.RSI_input="RSI",a.exports.RVGI_input="RVGI",a.exports.RVI_input="RVI",a.exports["Long period_input"]=["Tempoh panjang"],a.exports["Short period_input"]=["Tempoh yang singkat"],a.exports["Signal line period_input"]=["Tempoh garis isyarat"],a.exports.SMI_input="SMI",a.exports["SMI Ergodic Oscillator_input"]="SMI Ergodic Oscillator",a.exports.Indicator_input=["Penunjuk"],a.exports.Oscillator_input="Oscillator",a.exports.K_input="K",a.exports.D_input="D",a.exports.smoothK_input="smoothK",a.exports.smoothD_input="smoothD",a.exports["%K_input"]="%K",a.exports["%D_input"]="%D",a.exports["Stochastic Length_input"]=["Panjang Stokastik"],a.exports["RSI Source_input"]=["Sumber RSI"],a.exports.lengthRSI_input=["RSI panjang"],a.exports.lengthStoch_input=["Stokastik panjang"],a.exports.TRIX_input="TRIX",a.exports.TEMA_input="TEMA",a.exports["Long Length_input"]=["Jarak Panjang"],a.exports["Short Length_input"]=["Jarak Pendek"],a.exports["Signal Length_input"]=["Panjang Isyarat"],a.exports.Length1_input=["Panjang1"],a.exports.Length2_input=["Panjang2"],a.exports.Length3_input=["Panjang3"],a.exports.length7_input=["panjang7"],a.exports.length14_input=["panjang14"],a.exports.length28_input=["panjang28"],a.exports.UO_input="UO",a.exports.VWMA_input="VWMA",a.exports.len_input="len",a.exports["VI +_input"]="VI +",a.exports["VI -_input"]="VI -",a.exports["%R_input"]="%R",a.exports["Jaw Length_input"]=["Panjang Rahang"],a.exports["Teeth Length_input"]=["Panjang Gigi"],a.exports["Lips Length_input"]=["Panjang Bibir"],a.exports.Jaw_input=["Rahang"],a.exports.Teeth_input=["Gigi"],a.exports.Lips_input=["Bibir"],a.exports["Jaw Offset_input"]="Jaw Offset",a.exports["Teeth Offset_input"]="Teeth Offset",a.exports["Lips Offset_input"]="Lips Offset",a.exports["Down fractals_input"]="Down fractals",a.exports["Up fractals_input"]="Up fractals",
a.exports.Periods_input=["Tempoh"],a.exports.Shapes_input=["Bentuk"],a.exports["show MA_input"]=["tunjuk MA"],a.exports["MA Length_input"]=["Panjang MA"],a.exports["Color based on previous close_input"]=["Warna berdasarkan penutup sebelumnya"],a.exports["Rows Layout_input"]=["Susun Atur Baris"],a.exports["Row Size_input"]=["Saiz Baris"],a.exports.Volume_input=["Volum"],a.exports["Value Area volume_input"]=["Volum Kawasan Nilai"],a.exports["Extend Right_input"]=["Lanjutkan ke Kanan"],a.exports["Extend POC Right_input"]=["Melanjutkan Hak POC"],a.exports["Extend VAH Right_input"]=["Lanjutkan VAH ke Kanan"],a.exports["Extend VAL Right_input"]=["Lanjutkan VAL ke Kanan"],a.exports["Value Area Volume_input"]=["Volum Kawasan Nilai"],a.exports.Placement_input=["Penempatan"],a.exports.POC_input="POC",a.exports["Developing Poc_input"]=["Membangunkan Poc"],a.exports["Up Volume_input"]=["Volum Naik"],a.exports["Down Volume_input"]=["Volum Turun"],a.exports["Value Area_input"]=["Kawasan Nilai"],a.exports["Histogram Box_input"]=["Kotak Histogram"],a.exports["Value Area Up_input"]=["Kawasan Nilai Naik"],a.exports["Value Area Down_input"]=["Kawasan Nilai Turun"],a.exports["Number Of Rows_input"]=["Bilangan Baris"],a.exports["Ticks Per Row_input"]=["Tanda Setiap Baris"],a.exports["Up/Down_input"]=["Naik/Turun"],a.exports.Total_input=["Jumlah"],a.exports.Delta_input="Delta",a.exports.Bar_input="Bar",a.exports.Day_input=["Hari"],a.exports["Deviation (%)_input"]=["Sisihan (%)"],a.exports.Depth_input=["Kedalaman"],a.exports["Extend to last bar_input"]=["Sambungkan kepada bar terakhir"],a.exports.Simple_input=["Mudah"],a.exports.Weighted_input=["Pemberat"],a.exports["Wilder's Smoothing_input"]=["Pelicinan Wilder"],a.exports["1st Period_input"]=["Tempoh Masa Pertama"],a.exports["2nd Period_input"]=["Tempoh Masa kedua"],a.exports["3rd Period_input"]=["Tempoh masa ketiga"],a.exports["4th Period_input"]=["Tempoh masa keempat"],a.exports["5th Period_input"]=["Tempoh masa kelima"],a.exports["6th Period_input"]=["Tempoh masa keenam"],a.exports["Rate of Change Lookback_input"]=["Kadar Perubahan Pandang Belakang"],a.exports["Instrument 1_input"]=["Instrumen 1"],a.exports["Instrument 2_input"]=["Instrumen 2"],a.exports["Rolling Period_input"]=["Tempoh pusingan"],a.exports["Standard Errors_input"]=["Ralat piawai"],a.exports["Averaging Periods_input"]=["Tempoh pemurataan"],a.exports["Days Per Year_input"]=["Hari Setiap Tahun"],a.exports["Market Closed Percentage_input"]=["Peratusan Penutupan Pasaran"],a.exports["ATR Mult_input"]="ATR Mult",a.exports.VWAP_input="VWAP",a.exports["Anchor Period_input"]=["Tempoh Ikatan"],a.exports.Session_input=["Sesi"],a.exports.Week_input=["Minggu"],a.exports.Month_input=["Bulan"],a.exports.Year_input=["Tahun"],a.exports.Decade_input=["Dekad"],a.exports.Century_input=["Abad"],a.exports.Sessions_input=["Sesi"],a.exports["Each (pre-market, market, post-market)_input"]=["Setiap (pra pasaran, pasaran, pasca pasaran)"],a.exports["Pre-market only_input"]=["Pra pasaran sahaja"],
a.exports["Market only_input"]=["Pasaran sahaja"],a.exports["Post-market only_input"]=["Pasca pasaran sahaja"],a.exports["Main chart symbol_input"]=["Simbol utama carta"],a.exports["Another symbol_input"]=["Simbol lain"],a.exports.Line_input=["Garisan"],a.exports["Nothing selected_combobox_input"]=["Tiada dipilih"],a.exports["All items_combobox_input"]=["Semua benda"],a.exports.Cancel_input=["Batal"],a.exports.Open_input=["Buka"]},54138:a=>{a.exports=["Songsangkan skala"]},47807:a=>{a.exports=["Diindeks kepada 100"]},34727:a=>{a.exports=["Logaritma"]},19238:a=>{a.exports=["Tiada Label Bertindih"]},70361:a=>{a.exports=["Peratus"]},72116:a=>{a.exports=["Biasa"]},33021:a=>{a.exports="ETH"},75610:a=>{a.exports=["Jam Dagangan Elektronik"]},97442:a=>{a.exports=["Waktu dagangan dilanjutkan"]},32929:a=>{a.exports=["post"]},56137:a=>{a.exports=["pre"]},98801:a=>{a.exports=["Pasca pasaran"]},56935:a=>{a.exports=["Pra pasaran"]},63798:a=>{a.exports="RTH"},24380:a=>{a.exports=["Jam dagangan biasa"]},27991:a=>{a.exports=["Mei"]},68716:a=>{a.exports=Object.create(null),a.exports.Technicals_study=["Teknikal"],a.exports["Average Day Range_study"]=["Julat Purata Harian"],a.exports["Bull Bear Power_study"]="Bull Bear Power",a.exports["Capital expenditures_study"]=["Perbelanjaan Kapital"],a.exports["Cash to debt ratio_study"]=["Nisbah tunai kepada hutang"],a.exports["Debt to EBITDA ratio_study"]=["Nisbah hutang kepada EBITDA"],a.exports["Directional Movement Index_study"]="Directional Movement Index",a.exports.DMI_study="DMI",a.exports["Dividend payout ratio %_study"]=["% nisbah pembayaran dividen"],a.exports["Equity to assets ratio_study"]=["Nisbah ekuiti kepada aset"],a.exports["Enterprise value to EBIT ratio_study"]=["Nisbah nilai perusahaan kepada EBIT"],a.exports["Enterprise value to EBITDA ratio_study"]=["Nisbah nilai perusahaan kepada EBITDA"],a.exports["Enterprise value to revenue ratio_study"]=["Nisbah nilai perusahaan kepada hasil"],a.exports["Goodwill, net_study"]=["Muhibah, bersih"],a.exports["Ichimoku Cloud_study"]="Ichimoku Cloud",a.exports.Ichimoku_study="Ichimoku",a.exports["Moving Average Convergence Divergence_study"]="Moving Average Convergence Divergence",a.exports["Operating income_study"]=["Pendapatan kendalian"],a.exports["Price to book ratio_study"]=["Nisbah harga kepada nilai buku"],a.exports["Price to cash flow ratio_study"]=["Nisbah harga kepada aliran tunai"],a.exports["Price to earnings ratio_study"]=["Nisbah harga kepada keuntungan"],a.exports["Price to free cash flow ratio_study"]=["Nisbah harga kepada aliran tunai bebas"],a.exports["Price to sales ratio_study"]=["Nisbah harga kepada jualan"],a.exports["Float shares outstanding_study"]=["Saham terapung belum jelas"],a.exports["Total common shares outstanding_study"]=["Jumlah Saham Biasa Belum Jelas"],a.exports["Volume Weighted Average Price_study"]="Volume Weighted Average Price",a.exports["Volume Weighted Moving Average_study"]="Volume Weighted Moving Average",a.exports["Williams Percent Range_study"]="Williams Percent Range",a.exports.Doji_study="Doji",
a.exports["Spinning Top Black_study"]="Spinning Top Black",a.exports["Spinning Top White_study"]="Spinning Top White",a.exports["Accounts payable_study"]=["Akaun belum bayar"],a.exports["Accounts receivables, gross_study"]=["Akaun belum terima, kasar"],a.exports["Accounts receivable - trade, net_study"]=["Akaun belum terima - dagangan, bersih"],a.exports.Accruals_study=["Akruan"],a.exports["Accrued payroll_study"]=["Penggajian terakru"],a.exports["Accumulated depreciation, total_study"]=["Susut nilai terkumpul, jumlah"],a.exports["Additional paid-in capital/Capital surplus_study"]=["Modal di bayar tambahan/Modal lebihan"],a.exports["After tax other income/expense_study"]=["Pendapatan/perbelanjaan lain selepas cukai"],a.exports["Altman Z-score_study"]=["Skor Altman Z"],a.exports.Amortization_study=["Pelunasan"],a.exports["Amortization of intangibles_study"]=["Pelunasan Aset Tak Ketara"],a.exports["Amortization of deferred charges_study"]=["Pelunasan Penundaan Kos-kos"],a.exports["Asset turnover_study"]=["Perolehan aset"],a.exports["Average basic shares outstanding_study"]=["Purata saham asas belum jelas"],a.exports["Bad debt / Doubtful accounts_study"]=["Hutang lapuk / Akaun ragu"],a.exports["Basic EPS_study"]=["EPS asas"],a.exports["Basic earnings per share (Basic EPS)_study"]=["Perolehan asas sesaham (EPS Asas)"],a.exports["Beneish M-score_study"]=["Skor Beneish M"],a.exports["Book value per share_study"]=["Nilai buku setiap saham"],a.exports["Buyback yield %_study"]=["Kadar hasil beli balik %"],a.exports["Capital and operating lease obligations_study"]=["Kewajipan pajakan modal dan kendalian"],a.exports["Capital expenditures - fixed assets_study"]=["Perbelanjaan modal - aset tetap"],a.exports["Capital expenditures - other assets_study"]=["Modal perbelanjaan - aset lain"],a.exports["Capitalized lease obligations_study"]=["Kewajipan pajakan bermodal"],a.exports["Cash and short term investments_study"]=["Tunai dan pelaburan jangka pendek"],a.exports["Cash conversion cycle_study"]=["Kitaran penukaran tunai"],a.exports["Cash & equivalents_study"]=["Tunai & kesetaraan"],a.exports["Cash from financing activities_study"]=["Tunai Daripada Aktiviti Pembiayaan"],a.exports["Cash from investing activities_study"]=["Tunai Daripada Aktiviti Pelaburan"],a.exports["Cash from operating activities_study"]=["Tunai daripada Aktiviti Operasi"],a.exports["Change in accounts payable_study"]=["Perubahan dalam akaun belum bayar"],a.exports["Change in accounts receivable_study"]=["Perubahan dalam akaun belum terima"],a.exports["Change in accrued expenses_study"]=["Perubahan dalam perbelanjaan terakru"],a.exports["Change in inventories_study"]=["Perubahan dalam inventori"],a.exports["Change in other assets/liabilities_study"]=["Perubahan dalam aset/liabiliti lain"],a.exports["Change in taxes payable_study"]=["Perubahan dalam cukai belum bayar"],a.exports["Changes in working capital_study"]=["Perubahan pada modal bekerja"],a.exports["COGS to revenue ratio_study"]=["Nisbah COGS kepada hasil"],
a.exports["Common dividends paid_study"]=["Dividen sepunya telah dibayar"],a.exports["Common equity, total_study"]=["Ekuiti sepunya, jumlah"],a.exports["Common stock par/Carrying value_study"]=["Par saham sepunya/Membawa nilai"],a.exports["Cost of goods_study"]=["Kos Barangan"],a.exports["Cost of goods sold_study"]=["Kos barangan yang dijual"],a.exports["Current portion of LT debt and capital leases_study"]=["Bahagian semasa hutang dan pajakan modal jangka panjang"],a.exports["Current ratio_study"]=["Nisbah semasa"],a.exports["Days inventory_study"]=["Inventori harian"],a.exports["Days payable_study"]=["Harian perlu bayar"],a.exports["Days sales outstanding_study"]=["Jualan harian belum jelas"],a.exports["Debt to assets ratio_study"]=["Nisbah hutang kepada aset"],a.exports["Debt to equity ratio_study"]=["Nisbah hutang kepada ekuiti"],a.exports["Debt to revenue ratio_study"]=["Nisbah hutang kepada hasil"],a.exports["Deferred income, current_study"]=["Pendapatan tertunda, semasa"],a.exports["Deferred income, non-current_study"]=["Pendapatan tertunda, bukan semasa"],a.exports["Deferred tax assets_study"]=["Aset cukai tertunda"],a.exports["Deferred taxes (cash flow)_study"]=["Cukai tertunda (aliran tunai)"],a.exports["Deferred tax liabilities_study"]=["Liabiliti cukai tertunda"],a.exports.Depreciation_study=["Susut nilai"],a.exports["Deprecation and amortization_study"]=["Susut nilai dan pelupusan"],a.exports["Depreciation & amortization (cash flow)_study"]=["Susut nilai & penulasan (aliran tunai)"],a.exports["Depreciation/depletion_study"]=["Susut nilai/pengurangan"],a.exports["Diluted EPS_study"]=["Cairan EPS"],a.exports["Diluted earnings per share (Diluted EPS)_study"]=["Perolehan sesaham dicairkan sesaham (EPS Dicairkan)"],a.exports["Diluted net income available to common stockholders_study"]=["Pendapatan bersih dicairkan sedia ada untuk pemegang saham biasa"],a.exports["Diluted shares outstanding_study"]=["Saham dicairkan belum jelas"],a.exports["Dilution adjustment_study"]=["Pelarasan pencairan"],a.exports["Discontinued operations_study"]=["Operasi dihentikan"],a.exports["Dividends payable_study"]=["Dividen dibayar"],a.exports["Dividends per share - common stock primary issue_study"]=["Dividen per saham - isu utama saham sepunya"],a.exports["Dividend yield %_study"]=["Kadar hasil dividen %"],a.exports["Earnings yield_study"]=["Kadar hasil perolehan"],a.exports.EBIT_study="EBIT",a.exports.EBITDA_study="EBITDA",a.exports["EBITDA margin %_study"]=["% margin EBITDA"],a.exports["Effective interest rate on debt %_study"]=["Kadar faedah efektif pada hutang %"],a.exports["Enterprise value_study"]=["Nilai perusahaan"],a.exports["EPS basic one year growth_study"]=["Pertumbuhan satu tahun EPS asas"],a.exports["EPS diluted one year growth_study"]=["Pertumbuhan satu tahun EPS dicairkan"],a.exports["EPS estimates_study"]=["Anggaran EPS"],a.exports["Equity in earnings_study"]=["Ekuiti dalam perolehan"],a.exports["Financing activities – other sources_study"]=["Aktiviti pembiayaan - sumber lain"],
a.exports["Financing activities – other uses_study"]=["Aktiviti pembiayaan - kegunaan lain"],a.exports["Free cash flow_study"]=["Aliran Tunai Bebas"],a.exports["Free cash flow margin %_study"]=["Margin aliran tunai bebas %"],a.exports["Fulmer H factor_study"]=["Faktor Fulmer H"],a.exports["Funds from operations_study"]=["Dana daripada operasi"],a.exports["Goodwill to assets ratio_study"]=["Nisbah muhibah kepada aset"],a.exports["Graham's number_study"]=["Nombor Graham"],a.exports["Gross margin %_study"]=["Margin kasar %"],a.exports["Gross profit_study"]=["Untung Kasar"],a.exports["Gross profit to assets ratio_study"]=["Nisbah untung kasar kepada aset"],a.exports["Gross property/plant/equipment_study"]=["Nilai kasar harta tanah/loji/peralatan"],a.exports.Impairments_study=["Rosotnilai"],a.exports["Income Tax Credits_study"]=["Kredit Cukai Pendapatan"],a.exports["Income tax, current_study"]=["Cukai pendapatan, semasa"],a.exports["Income tax, current - domestic_study"]=["Cukai pendapatan, semasa - domestik"],a.exports["Income Tax, current - foreign_study"]=["Cukai Pendapatan, semasa - luar negara"],a.exports["Income tax, deferred_study"]=["Cukai pendapatan, tertunda"],a.exports["Income tax, deferred - domestic_study"]=["Cukai pendapatan, tertunda - domestik"],a.exports["Income tax, deferred - foreign_study"]=["Cukai pendapatan, tertunda - luar negara"],a.exports["Income tax payable_study"]=["Cukai pendapatan belum bayar"],a.exports["Interest capitalized_study"]=["Faedah dipermodalkan"],a.exports["Interest coverage_study"]=["Perlindungan faedah"],a.exports["Interest expense, net of interest capitalized_study"]=["Perbelanjaan faedah, nilai bersih untuk faedah dipermodalkan"],a.exports["Interest expense on debt_study"]=["Perbelanjaan faedah pada hutang"],a.exports["Inventories - finished goods_study"]=["Inventori - barang siap"],a.exports["Inventories - progress payments & other_study"]=["Inventori - proses pembayaran & lain-lain"],a.exports["Inventories - raw materials_study"]=["Inventori - bahan mentah"],a.exports["Inventories - work in progress_study"]=["Inventori - kerja dalam proses"],a.exports["Inventory to revenue ratio_study"]=["Nisbah inventori kepada hasil"],a.exports["Inventory turnover_study"]=["Perolehan inventori"],a.exports["Investing activities – other sources_study"]=["Aktiviti pelaburan - sumber lain"],a.exports["Investing activities – other uses_study"]=["Aktiviti pelaburan - kegunaan lain"],a.exports["Investments in unconsolidated subsidiaries_study"]=["Pelaburan dalam anak syarikat yang tidak disatukan"],a.exports["Issuance of long term debt_study"]=["Terbitan hutang jangka panjang"],a.exports["Issuance/retirement of debt, net_study"]=["Hutang terbitan/persaraan, nilai bersih"],a.exports["Issuance/retirement of long term debt_study"]=["Terbitan/persaraan untuk hutang jangka panjang"],a.exports["Issuance/retirement of other debt_study"]=["Terbitan/persaraan untuk hutang lain"],a.exports["Issuance/retirement of short term debt_study"]=["Terbitan/persaraan untuk hutang jangka pendek"],
a.exports["Issuance/retirement of stock, net_study"]=["Terbitan/persaraan untuk saham, nilai bersih"],a.exports["KZ index_study"]=["Indeks KZ"],a.exports["Legal claim expense_study"]=["Perbelanjaan tuntutan undang-undang"],a.exports["Long term debt_study"]=["Hutang Jangka Panjang"],a.exports["Long term debt excl. lease liabilities_study"]=["Hutang jangka panjang tidak termasuk liabiliti pajakan"],a.exports["Long term debt to total assets ratio_study"]=["Nisbah hutang jangka panjang kepada jumlah asset"],a.exports["Long term debt to total equity ratio_study"]=["Nisbah hutang jangka panjang kepada jumlah ekuiti"],a.exports["Long term investments_study"]=["Pelaburan jangka panjang"],a.exports["Market capitalization_study"]=["Permodalan Pasaran"],a.exports["Minority interest_study"]=["Faedah Minoriti"],a.exports["Miscellaneous non-operating expense_study"]=["Pelbagai perbelanjaan bukan kendalian"],a.exports["Net current asset value per share_study"]=["Nilai bersih aset semasa per saham"],a.exports["Net debt_study"]=["Hutang bersih"],a.exports["Net income_study"]=["Pendapatan bersih"],a.exports["Net income before discontinued operations_study"]=["Pendapatan bersih sebelum kendalian dihentikan"],a.exports["Net income (cash flow)_study"]=["Pendapatan bersih (aliran tunai)"],a.exports["Net income per employee_study"]=["Pendapatan bersih untuk setiap pekerja"],a.exports["Net intangible assets_study"]=["Aset tak ketara bersih"],a.exports["Net margin %_study"]=["Margin bersih %"],a.exports["Net property/plant/equipment_study"]=["Harta tanah/kilang/alatan bersih"],a.exports["Non-cash items_study"]=["Item bukan tunai"],a.exports["Non-controlling/minority interest_study"]=["Kepentingan tak mengawal/minoriti"],a.exports["Non-operating income, excl. interest expenses_study"]=["Pendapatan bukan kendalian, tidak termasuk faedah belanja"],a.exports["Non-operating income, total_study"]=["Pendapatan bukan kendalian, jumlah"],a.exports["Non-operating interest income_study"]=["Pendapatan faedah bukan kendalian"],a.exports["Note receivable - long term_study"]=["Nota belum terima - jangka panjang"],a.exports["Notes payable_study"]=["Nota belum bayar"],a.exports["Number of employees_study"]=["Bilangan pekerja"],a.exports["Number of shareholders_study"]=["Bilangan pemegang saham"],a.exports["Operating earnings yield %_study"]=["Kadar hasil perolehan kendalian %"],a.exports["Operating expenses (excl. COGS)_study"]=["Perbelanjaan kendalian (tidak termasuk COGS)"],a.exports["Operating lease liabilities_study"]=["Liabiliti pajakan kendalian"],a.exports["Operating margin %_study"]=["% margin operasi"],a.exports["Other COGS_study"]=["COGS lain"],a.exports["Other common equity_study"]=["Ekuiti sepunya yang lain"],a.exports["Other current assets, total_study"]=["Aset semasa lain, jumlah"],a.exports["Other current liabilities_study"]=["Liabiliti semasa lain"],a.exports["Other cost of goods sold_study"]=["Kos barangan yang dijual lain"],a.exports["Other exceptional charges_study"]=["Kos-kos luar biasa lain"],
a.exports["Other financing cash flow items, total_study"]=["Lain-lain Butiran Aliran Tunai Kewangan, Jumlah"],a.exports["Other intangibles, net_study"]=["Tidak ketara lain, bersih"],a.exports["Other investing cash flow items, total_study"]=["Lain-lain Butiran Aliran Tunai Pelaburan, Jumlah"],a.exports["Other investments_study"]=["Pelaburan-pelaburan lain"],a.exports["Other liabilities, total_study"]=["Liabiliti lain, jumlah"],a.exports["Other long term assets, total_study"]=["Aset jangka panjang lain, jumlah"],a.exports["Other non-current liabilities, total_study"]=["Liabiliti lain bukan semasa, jumlah"],a.exports["Other operating expenses, total_study"]=["Belanja kendalian lain, jumlah"],a.exports["Other receivables_study"]=["Belum terima lain"],a.exports["Other short term debt_study"]=["Hutang jangka pendek lain"],a.exports["Paid in capital_study"]=["Modal dibayar"],a.exports["PEG ratio_study"]=["Nisbah PEG"],a.exports["Piotroski F-score_study"]=["Skor Piotroski F"],a.exports["Preferred dividends_study"]=["Dividen keutamaan"],a.exports["Preferred dividends paid_study"]=["Dividen keutamaan dibayar"],a.exports["Preferred stock, carrying value_study"]=["Saham keutamaan, membawa nilai"],a.exports["Prepaid expenses_study"]=["Perbelanjaan prabayar"],a.exports["Pretax equity in earnings_study"]=["Ekuiti pracukai dalam perolehan"],a.exports["Pretax income_study"]=["Pendapatan pracukai"],a.exports["Price earnings ratio forward_study"]=["Nisbah harga perolehan ke hadapan"],a.exports["Price sales ratio forward_study"]=["Nisbah harga jualan ke hadapan"],a.exports["Price to tangible book ratio_study"]=["Nisbah harga kepada nilai buku ketara"],a.exports["Provision for risks & charge_study"]=["Peruntukan untuk risiko & cas"],a.exports["Purchase/acquisition of business_study"]=["Pembelian/perolehan perniagaan"],a.exports["Purchase of investments_study"]=["Pembelian pelaburan"],a.exports["Purchase/sale of business, net_study"]=["Belian/Jualan Perniagaan, Nilai Bersih"],a.exports["Purchase/sale of investments, net_study"]=["Belian/Jualan Pelaburan, Nilai Bersih"],a.exports["Quality ratio_study"]=["Nisbah kualiti"],a.exports["Quick ratio_study"]=["Nisbah pantas"],a.exports["Reduction of long term debt_study"]=["Pengurangan hutang jangka panjang"],a.exports["Repurchase of common & preferred stock_study"]=["Pembelian semula saham sepunya & keutamaan"],a.exports["Research & development_study"]=["Penyelidikan & pembangunan"],a.exports["Research & development to revenue ratio_study"]=["Nisbah penyelidikan & pembangunan kepada hasil"],a.exports["Restructuring charge_study"]=["Cas penstrukturan semula"],a.exports["Retained earnings_study"]=["Perolehan tertahan"],a.exports["Return on assets %_study"]=["Pulangan aset %"],a.exports["Return on equity %_study"]=["Pulangan ekuiti %"],a.exports["Return on equity adjusted to book value %_study"]=["Pulangan pada ekuiti dilaraskan kepada nilai buku %"],a.exports["Return on invested capital %_study"]=["Pulangan pada modal yang dilaburkan %"],
a.exports["Return on tangible assets %_study"]=["Pulangan aset ketara %"],a.exports["Return on tangible equity %_study"]=["Pulangan ekuiti ketara %"],a.exports["Revenue estimates_study"]=["Anggaran hasil"],a.exports["Revenue one year growth_study"]=["Pertumbuhan hasil satu tahun"],a.exports["Revenue per employee_study"]=["Hasil setiap pekerja"],a.exports["Sale/maturity of investments_study"]=["Jualan/kematangan pelaburan"],a.exports["Sale of common & preferred stock_study"]=["Jualan saham sepunya & keutamaan"],a.exports["Sale of fixed assets & businesses_study"]=["Jualan aset tetap & perniagaan-perniagaan"],a.exports["Selling/general/admin expenses, other_study"]=["Perbelanjaan Jualan/Umum/Pentadbiran; Lain-lain"],a.exports["Selling/general/admin expenses, total_study"]=["Perbelanjaan Jualan/Am/Pentadbiran, Jumlah"],a.exports["Shareholders' equity_study"]=["Ekuiti pemegang saham"],a.exports["Shares buyback ratio %_study"]=["Nisbah pembelian balik saham %"],a.exports["Short term debt_study"]=["Hutang jangka pendek"],a.exports["Short term debt excl. current portion of LT debt_study"]=["Hutang jangka pendek tidak termasuk bahagian semasa hutang jangka pendek"],a.exports["Short term investments_study"]=["Pelaburan jangka pendek"],a.exports["Sloan ratio %_study"]=["% nisbah Sloan"],a.exports["Springate score_study"]=["Skor Springate"],a.exports["Sustainable growth rate_study"]=["Kadar pertumbuhan lestari"],a.exports["Tangible book value per share_study"]=["Nilai buku ketara per saham"],a.exports["Tangible common equity ratio_study"]=["Nisbah ekuiti sepunya ketara"],a.exports.Taxes_study=["Cukai-cukai"],a.exports["Tobin's Q (approximate)_study"]=["Tobin Q (anggaran)"],a.exports["Total assets_study"]=["Jumlah Aset"],a.exports["Total cash dividends paid_study"]=["Jumlah Dividen Tunai Dibayar"],a.exports["Total current assets_study"]=["Jumlah Aset Semasa"],a.exports["Total current liabilities_study"]=["Jumlah Tanggungan Semasa"],a.exports["Total debt_study"]=["Jumlah Hutang"],a.exports["Total equity_study"]=["Jumlah Ekuiti"],a.exports["Total inventory_study"]=["Kesemua inventori"],a.exports["Total liabilities_study"]=["Jumlah Liabiliti"],a.exports["Total liabilities & shareholders' equities_study"]=["Jumlah liabiliti & ekuiti pemegang saham"],a.exports["Total non-current assets_study"]=["Jumlah Aset Bukan-semasa"],a.exports["Total non-current liabilities_study"]=["Jumlah Liabiliti Bukan-semasa"],a.exports["Total operating expenses_study"]=["Jumlah Belanja Operasi"],a.exports["Total receivables, net_study"]=["Jumlah belum terima, bersih"],a.exports["Total revenue_study"]=["Jumlah hasil"],a.exports["Treasury stock - common_study"]=["Saham Perbendaharaan - sepunya"],a.exports["Unrealized gain/loss_study"]=["Keuntungan/kerugian yang tidak direalisasikan"],a.exports["Unusual income/expense_study"]=["Pendapatan/perbelanjaan tidak lazim"],a.exports["Zmijewski score_study"]=["Skor Zmijewski"],a.exports["Valuation ratios_study"]=["Nisbah penilaian"],a.exports["Profitability ratios_study"]=["Nisbah keuntungan"],
a.exports["Liquidity ratios_study"]=["Nisbah kecairan"],a.exports["Solvency ratios_study"]=["Nisbah kesolvenan"],a.exports["Key stats_study"]=["Statistik utama"],a.exports["Accumulation/Distribution_study"]="Accumulation/Distribution",a.exports["Accumulative Swing Index_study"]="Accumulative Swing Index",a.exports["Advance/Decline_study"]="Advance/Decline",a.exports["Arnaud Legoux Moving Average_study"]="Arnaud Legoux Moving Average",a.exports.Aroon_study="Aroon",a.exports.ASI_study="ASI",a.exports["Average Directional Index_study"]="Average Directional Index",a.exports["Average True Range_study"]="Average True Range",a.exports["Awesome Oscillator_study"]="Awesome Oscillator",a.exports["Balance of Power_study"]="Balance of Power",a.exports["Bollinger Bands %B_study"]="Bollinger Bands %B",a.exports["Bollinger Bands Width_study"]="Bollinger Bands Width",a.exports["Bollinger Bands_study"]="Bollinger Bands",a.exports["Chaikin Money Flow_study"]="Chaikin Money Flow",a.exports["Chaikin Oscillator_study"]="Chaikin Oscillator",a.exports["Chande Kroll Stop_study"]="Chande Kroll Stop",a.exports["Chande Momentum Oscillator_study"]="Chande Momentum Oscillator",a.exports["Chop Zone_study"]="Chop Zone",a.exports["Choppiness Index_study"]="Choppiness Index",a.exports["Commodity Channel Index_study"]="Commodity Channel Index",a.exports["Connors RSI_study"]="Connors RSI",a.exports["Coppock Curve_study"]="Coppock Curve",a.exports["Correlation Coefficient_study"]="Correlation Coefficient",a.exports.CRSI_study="CRSI",a.exports["Detrended Price Oscillator_study"]="Detrended Price Oscillator",a.exports["Directional Movement_study"]="Directional Movement",a.exports["Donchian Channels_study"]="Donchian Channels",a.exports["Double EMA_study"]="Double EMA",a.exports["Ease Of Movement_study"]="Ease Of Movement",a.exports["Elder Force Index_study"]=["Elder's Force Index"],a.exports["EMA Cross_study"]=["Silang EMA"],a.exports.Envelopes_study=["Sampul"],a.exports["Fisher Transform_study"]="Fisher Transform",a.exports["Fixed Range_study"]=["Julat Tetap"],a.exports["Fixed Range Volume Profile_study"]=["Profil Volum Julat Tetap"],a.exports["Guppy Multiple Moving Average_study"]="Guppy Multiple Moving Average",a.exports["Historical Volatility_study"]="Historical Volatility",a.exports["Hull Moving Average_study"]="Hull Moving Average",a.exports["Keltner Channels_study"]="Keltner Channels",a.exports["Klinger Oscillator_study"]="Klinger Oscillator",a.exports["Know Sure Thing_study"]="Know Sure Thing",a.exports["Least Squares Moving Average_study"]="Least Squares Moving Average",a.exports["Linear Regression Curve_study"]=["Keluk Regresi Linear"],a.exports["MA Cross_study"]="MA Cross",a.exports["MA with EMA Cross_study"]=["MA dengan Silang EMA"],a.exports["MA/EMA Cross_study"]=["MA/Silang EMA"],a.exports.MACD_study="MACD",a.exports["Mass Index_study"]="Mass Index",a.exports["McGinley Dynamic_study"]="McGinley Dynamic",a.exports.Median_study="Median",a.exports.Momentum_study="Momentum",a.exports["Money Flow_study"]="Money Flow",
a.exports["Moving Average Channel_study"]="Moving Average Channel",a.exports["Moving Average Exponential_study"]="Moving Average Exponential",a.exports["Moving Average Weighted_study"]="Moving Average Weighted",a.exports["Moving Average Simple_study"]="Moving Average Simple",a.exports["Net Volume_study"]="Net Volume",a.exports["On Balance Volume_study"]="On Balance Volume",a.exports["Parabolic SAR_study"]="Parabolic SAR",a.exports["Pivot Points Standard_study"]="Pivot Points Standard",a.exports["Periodic Volume Profile_study"]=["Profil Volum Berkala"],a.exports["Price Channel_study"]="Price Channel",a.exports["Price Oscillator_study"]="Price Oscillator",a.exports["Price Volume Trend_study"]="Price Volume Trend",a.exports["Rate Of Change_study"]="Rate Of Change",a.exports["Relative Strength Index_study"]="Relative Strength Index",a.exports["Relative Vigor Index_study"]="Relative Vigor Index",a.exports["Relative Volatility Index_study"]="Relative Volatility Index",a.exports["Session Volume_study"]=["Volum Sesi"],a.exports["Session Volume HD_study"]=["Volum Sesi HD"],a.exports["Session Volume Profile_study"]=["Profil Volum Sesi"],a.exports["Session Volume Profile HD_study"]=["Profil Volum Sesi HD"],a.exports["SMI Ergodic Indicator/Oscillator_study"]="SMI Ergodic Indicator/Oscillator",a.exports["Smoothed Moving Average_study"]="Smoothed Moving Average",a.exports.Stoch_study="Stoch",a.exports["Stochastic RSI_study"]="Stochastic RSI",a.exports.Stochastic_study="Stochastic",a.exports["Triple EMA_study"]="Triple EMA",a.exports.TRIX_study="TRIX",a.exports["True Strength Indicator_study"]="True Strength Indicator",a.exports["Ultimate Oscillator_study"]="Ultimate Oscillator",a.exports["Visible Range_study"]=["Julat Boleh Dilihat"],a.exports["Visible Range Volume Profile_study"]=["Profil Volum Julat Boleh Dilihat"],a.exports["Volume Oscillator_study"]="Volume Oscillator",a.exports.Volume_study=["Volum"],a.exports.Vol_study=["Volum"],a.exports["Vortex Indicator_study"]=["Penunjuk Vorteks"],a.exports.VWAP_study="VWAP",a.exports.VWMA_study="VWMA",a.exports["Williams %R_study"]="Williams %R",a.exports["Williams Alligator_study"]="Williams Alligator",a.exports["Williams Fractal_study"]="Williams Fractal",a.exports["Zig Zag_study"]="Zig Zag",a.exports["24-hour Volume_study"]=["Volum 24 jam"],a.exports["Ease of Movement_study"]=["Ease Of Movement"],a.exports["Elders Force Index_study"]=["Indeks Elders Force"],a.exports.Envelope_study="Envelope",a.exports.Gaps_study=["Jurang"],a.exports["Linear Regression Channel_study"]="Linear Regression Channel",a.exports["Moving Average Ribbon_study"]="Moving Average Ribbon",a.exports["Multi-Time Period Charts_study"]="Multi-Time Period Charts",a.exports["Open Interest_study"]="Open Interest",a.exports["Rob Booker - Intraday Pivot Points_study"]="Rob Booker - Intraday Pivot Points",a.exports["Rob Booker - Knoxville Divergence_study"]="Rob Booker - Knoxville Divergence",a.exports["Rob Booker - Missed Pivot Points_study"]="Rob Booker - Missed Pivot Points",
a.exports["Rob Booker - Reversal_study"]="Rob Booker - Reversal",a.exports["Rob Booker - Ziv Ghost Pivots_study"]="Rob Booker - Ziv Ghost Pivots",a.exports.Supertrend_study=["Arah Aliran Super"],a.exports["Technical Ratings_study"]=["Penarafan Teknikal"],a.exports["True Strength Index_study"]="True Strength Index",a.exports["Up/Down Volume_study"]=["Volum Naik/Turun"],a.exports["Visible Average Price_study"]="Visible Average Price",a.exports["Williams Fractals_study"]="Williams Fractals",a.exports["Keltner Channels Strategy_study"]="Keltner Channels Strategy",a.exports["Rob Booker - ADX Breakout_study"]="Rob Booker - ADX Breakout",a.exports["Supertrend Strategy_study"]="Supertrend Strategy",a.exports["Technical Ratings Strategy_study"]=["Strategi Penarafan Teknikal"],a.exports["Auto Anchored Volume Profile_study"]="Auto Anchored Volume Profile",a.exports["Auto Fib Extension_study"]=["Anjakan Fib Automatik"],a.exports["Auto Fib Retracement_study"]=["Anjakan Fib Automatik"],a.exports["Auto Pitchfork_study"]=["Pitchfork Automatik"],a.exports["Bearish Flag Chart Pattern_study"]=["Corak Carta Bendera Bearis"],a.exports["Bullish Flag Chart Pattern_study"]=["Corak Carta Bendera Bulis"],a.exports["Bearish Pennant Chart Pattern_study"]=["Corak Carta Panji Bearis"],a.exports["Bullish Pennant Chart Pattern_study"]=["Corak Carta Bendera Bulis"],a.exports["Double Bottom Chart Pattern_study"]=["Corak Carta Dua Jurang"],a.exports["Double Top Chart Pattern_study"]=["Corak Carta Dua Puncak"],a.exports["Elliott Wave Chart Pattern_study"]=["Corak Carta Elliott Wave"],a.exports["Falling Wedge Chart Pattern_study"]=["Corak Carta Baji Jatuh"],a.exports["Head And Shoulders Chart Pattern_study"]=["Corak Carta Kepala Dan Bahu"],a.exports["Inverse Head And Shoulders Chart Pattern_study"]=["Corak Carta Kepala Dan Bahu Songsang"],a.exports["Rectangle Chart Pattern_study"]=["Corak Carta Segi Empat"],a.exports["Rising Wedge Chart Pattern_study"]=["Corak Carta Baji Menaik"],a.exports["Triangle Chart Pattern_study"]=["Corak Carta Segi Tiga"],a.exports["Triple Bottom Chart Pattern_study"]=["Corak Carta Tiga Jurang"],a.exports["Triple Top Chart Pattern_study"]=["Corak Carta Tiga Puncak"],a.exports["VWAP Auto Anchored_study"]="VWAP Auto Anchored",a.exports["*All Candlestick Patterns*_study"]=["*Semua Corak Carta Candlestick*"],a.exports["Abandoned Baby - Bearish_study"]=["Abandoned Baby - Bearis"],a.exports["Abandoned Baby - Bullish_study"]=["Abandoned Baby - Bulis"],a.exports["Dark Cloud Cover - Bearish_study"]=["Dark Cloud Cover - Bearis"],a.exports["Doji Star - Bearish_study"]=["Doji Star - Bearis"],a.exports["Doji Star - Bullish_study"]=["Doji Star - Bulis"],a.exports["Downside Tasuki Gap - Bearish_study"]=["Downside Tasuki Gap - Bearis"],a.exports["Dragonfly Doji - Bullish_study"]=["Dragonfly Doji - Bulis"],a.exports["Engulfing - Bearish_study"]=["Engulfing - Bearis"],a.exports["Engulfing - Bullish_study"]=["Engulfing - Bulis"],a.exports["Evening Doji Star - Bearish_study"]=["Evening Doji Star - Bearis"],
a.exports["Evening Star - Bearish_study"]=["Evening Star - Bearis"],a.exports["Falling Three Methods - Bearish_study"]=["Falling Three Methods - Bearis"],a.exports["Falling Window - Bearish_study"]=["Falling Window - Bearis"],a.exports["Gravestone Doji - Bearish_study"]=["Gravestone Doji - Bearis"],a.exports["Hammer - Bullish_study"]=["Hammer - Bulis"],a.exports["Hanging Man - Bearish_study"]=["Hanging Man - Bearis"],a.exports["Harami - Bearish_study"]=["Harami - Bearis"],a.exports["Harami - Bullish_study"]=["Harami - Bulis"],a.exports["Inverted Hammer - Bullish_study"]=["Inverted Hammer - Bulis"],a.exports["Kicking - Bearish_study"]=["Kicking - Bearis"],a.exports["Kicking - Bullish_study"]=["Kicking - Bulis"],a.exports["Long Lower Shadow - Bullish_study"]=["Long Lower Shadow - Bulis"],a.exports["Long Upper Shadow - Bearish_study"]=["Long Upper Shadow - Bearis"],a.exports["Marubozu Black - Bearish_study"]=["Marubozu Black - Bearis"],a.exports["Marubozu White - Bullish_study"]=["Marubozu White - Bulis"],a.exports["Morning Doji Star - Bullish_study"]=["Morning Doji Star - Bulis"],a.exports["Morning Star - Bullish_study"]=["Morning Star - Bulis"],a.exports["On Neck - Bearish_study"]=["On Neck - Bearis"],a.exports["Piercing - Bullish_study"]=["Piercing - Bulis"],a.exports["Rising Three Methods - Bullish_study"]=["Rising Three Methods - Bulis"],a.exports["Rising Window - Bullish_study"]=["Rising Window - Bulis"],a.exports["Shooting Star - Bearish_study"]=["Shooting Star - Bearis"],a.exports["Three Black Crows - Bearish_study"]=["Three Black Crows - Bearis"],a.exports["Three White Soldiers - Bullish_study"]=["Three White Soldiers - Bulis"],a.exports["Tri-Star - Bearish_study"]=["Tri-Star - Bearis"],a.exports["Tri-Star - Bullish_study"]=["Tri-Star - Bulis"],a.exports["Tweezer Top - Bearish_study"]=["Tweezer Top - Bearis"],a.exports["Upside Tasuki Gap - Bullish_study"]=["Upside Tasuki Gap - Bulis"],a.exports.SuperTrend_study=["Arah Aliran Super"],a.exports["Average Price_study"]=["Harga purata"],a.exports["Typical Price_study"]=["Harga Biasa"],a.exports["Median Price_study"]=["Harga Median"],a.exports["Money Flow Index_study"]=["Indeks Aliran Wang"],a.exports["Moving Average Double_study"]="Moving Average Double",a.exports["Moving Average Triple_study"]="Moving Average Triple",a.exports["Moving Average Adaptive_study"]=["Purata Bergerak Boleh Suai"],a.exports["Moving Average Hamming_study"]=["Purata Bergerak Hamming"],a.exports["Moving Average Modified_study"]=["Purata Bergerak Terubasuai"],a.exports["Moving Average Multiple_study"]=["Purata Bergerak Berganda"],a.exports["Linear Regression Slope_study"]=["Cerun Regresi Linear"],a.exports["Standard Error_study"]=["Ralat piawai"],a.exports["Standard Error Bands_study"]=["Jalur Ralat Piawai"],a.exports["Correlation - Log_study"]=["Hubungkait - Log"],a.exports["Standard Deviation_study"]=["Sisihan Piawai"],a.exports["Chaikin Volatility_study"]=["Ketidakstabilan Chaikin"],a.exports["Volatility Close-to-Close_study"]=["Ketidakstabilan Penutup-ke-Penutup"],
a.exports["Volatility Zero Trend Close-to-Close_study"]=["Ketidakstabilan Pergerakan Kosong Penutup-ke-Penutup"],a.exports["Volatility O-H-L-C_study"]=["Ketidakstabilan O-H-L-C"],a.exports["Volatility Index_study"]=["Indeks Ketidakstabilan"],a.exports["Trend Strength Index_study"]=["Indeks Kekuatan Trend"],a.exports["Majority Rule_study"]=["Peraturan majoriti"],a.exports["Advance Decline Line_study"]="Advance Decline Line",a.exports["Advance Decline Ratio_study"]="Advance Decline Ratio",a.exports["Advance/Decline Ratio (Bars)_study"]="Advance/Decline Ratio (Bars)",a.exports["BarUpDn Strategy_study"]="BarUpDn Strategy",a.exports["Bollinger Bands Strategy directed_study"]="Bollinger Bands Strategy directed",a.exports["Bollinger Bands Strategy_study"]="Bollinger Bands Strategy",a.exports.ChannelBreakOutStrategy_study="ChannelBreakOutStrategy",a.exports.Compare_study="Compare",a.exports["Conditional Expressions_study"]="Conditional Expressions",a.exports.ConnorsRSI_study="ConnorsRSI",a.exports["Consecutive Up/Down Strategy_study"]="Consecutive Up/Down Strategy",a.exports["Cumulative Volume Index_study"]="Cumulative Volume Index",a.exports["Divergence Indicator_study"]="Divergence Indicator",a.exports["Greedy Strategy_study"]="Greedy Strategy",a.exports["InSide Bar Strategy_study"]="InSide Bar Strategy",a.exports["Keltner Channel Strategy_study"]="Keltner Channel Strategy",a.exports["Linear Regression_study"]="Linear Regression",a.exports["MACD Strategy_study"]="MACD Strategy",a.exports["Momentum Strategy_study"]="Momentum Strategy",a.exports["Moon Phases_study"]="Moon Phases",a.exports["Moving Average Convergence/Divergence_study"]="Moving Average Convergence/Divergence",a.exports["MovingAvg Cross_study"]="MovingAvg Cross",a.exports["MovingAvg2Line Cross_study"]="MovingAvg2Line Cross",a.exports["OutSide Bar Strategy_study"]="OutSide Bar Strategy",a.exports.Overlay_study=["Tindanan"],a.exports["Parabolic SAR Strategy_study"]="Parabolic SAR Strategy",a.exports["Pivot Extension Strategy_study"]="Pivot Extension Strategy",a.exports["Pivot Points High Low_study"]="Pivot Points High Low",a.exports["Pivot Reversal Strategy_study"]="Pivot Reversal Strategy",a.exports["Price Channel Strategy_study"]=["Keltner Channels"],a.exports["RSI Strategy_study"]="RSI Strategy",a.exports["SMI Ergodic Indicator_study"]="SMI Ergodic Indicator",a.exports["SMI Ergodic Oscillator_study"]="SMI Ergodic Oscillator",a.exports["Stochastic Slow Strategy_study"]="Stochastic Slow Strategy",a.exports["Volatility Stop_study"]="Volatility Stop",a.exports["Volty Expan Close Strategy_study"]="Volty Expan Close Strategy",a.exports["Woodies CCI_study"]="Woodies CCI"},59791:a=>{a.exports="Anchored Volume Profile"},40434:a=>{a.exports=["Profil Volum Julat Tetap"]},32819:a=>{a.exports=["Volum"]},66051:a=>{a.exports="Minor"},86054:a=>{a.exports=["Minit"]},20936:a=>{a.exports=["Teks"]},98478:a=>{a.exports=["Tidak dapat menyalin"]},34004:a=>{a.exports=["Tidak dapat memotong"]},96260:a=>{a.exports=["Tidak dapat menampal"]},94370:a=>{
a.exports=["Pengiraan Detik Ke Bar Penutup"]},15168:a=>{a.exports="Colombo"},36018:a=>{a.exports=["Lajur"]},19372:a=>{a.exports=["Komen"]},20229:a=>{a.exports=["Bandingkan atau Tambah Simbol"]},46689:a=>{a.exports=["Sahkan input"]},43432:a=>{a.exports="Copenhagen"},35216:a=>{a.exports=["Salin"]},87898:a=>{a.exports=["Salin Susun Atur Carta"]},28851:a=>{a.exports=["Salin harga"]},94099:a=>{a.exports=["Kaherah"]},64149:a=>{a.exports=["Petak Bual"]},63528:a=>{a.exports=["Lilin"]},46837:a=>{a.exports="Caracas"},53705:a=>{a.exports="Casablanca"},49329:a=>{a.exports=["Perubahan"]},28089:a=>{a.exports=["Tukar Simbol"]},13737:a=>{a.exports="Change alerts color"},99374:a=>{a.exports=["Tukar selang masa"]},14412:a=>{a.exports=["Sifat Carta"]},26619:a=>{a.exports=["Carta oleh TradingView"]},12011:a=>{a.exports=["Imej carta disalin ke papan keratan {emoji}"]},79393:a=>{a.exports=["Kod tertanam imej carta disalin ke papan keratan {emoji}"]},59884:a=>{a.exports=["Kepulauan Chatham"]},28244:a=>{a.exports="Chicago"},49648:a=>{a.exports="Chongqing"},90068:a=>{a.exports=["Bulatan"]},32234:a=>{a.exports=["Klik untuk tetapkan titik"]},52977:a=>{a.exports=["Klon"]},31691:a=>{a.exports=["Tutup"]},50493:a=>{a.exports=["Buat Pesanan"]},52302:a=>{a.exports=["Buat Pesanan Had"]},29908:a=>{a.exports=["Silang"]},60997:a=>{a.exports=["Garisan Silang"]},81520:a=>{a.exports=["Mata Wang"]},98486:a=>{a.exports=["Selang masa terkini dan di atas"]},73106:a=>{a.exports=["Selang waktu terkini dan di bawah"]},85964:a=>{a.exports=["Selang masa terkini sahaja"]},17206:a=>{a.exports=["Lengkung"]},95176:a=>{a.exports=["Kitaran"]},87761:a=>{a.exports=["Garis Kitaran"]},27891:a=>{a.exports=["Corak Cypher"]},56996:a=>{a.exports=["Nama susun atur itu sudah digunakan"]},30192:a=>{a.exports=["Nama susun atur itu sudah digunakan. Anda mahu tulis gantikannya?"]},32852:a=>{a.exports=["Corak ABCD"]},88010:a=>{a.exports="Amsterdam"},37422:a=>{a.exports=["Analisa Persediaan Dagangan"]},99873:a=>{a.exports="Anchorage"},66828:a=>{a.exports=["Nota Berlabuh"]},94782:a=>{a.exports=["Teks Berlabuh"]},61704:a=>{a.exports=["VWAP Berlabuh"]},63597:a=>{a.exports=["Tambah Garisan Mendatar"]},45743:a=>{a.exports=["Tambah Simbol"]},8700:a=>{a.exports=["Tambah pemberitahuan"]},64615:a=>{a.exports=["Tambah Pemberitahuan Bagi {title}"]},7005:a=>{a.exports=["Tambah pemberitahuan mengenai {title} pada {price}"]},3612:a=>{a.exports=["Tambahkan metrik kewangan untuk {instrumentName}"]},92206:a=>{a.exports=["Tambahkan penunjuk/strategi pada {studyTitle}"]},34810:a=>{a.exports=["Tambah Catatan Teks untuk {symbol}"]},75669:a=>{a.exports=["Tambah Metrik Kewangan ini kepada seluruh Susun Atur"]},64288:a=>{a.exports=["Tambah Petunjuk ini kepada seluruh Susun Atur"]},77920:a=>{a.exports=["Tambah Strategi ini kepada seluruh Susun Atur"]},34059:a=>{a.exports=["Tambah Simbol ini kepada seluruh Susun Atur"]},17365:a=>{a.exports="Adelaide"},9408:a=>{a.exports=["Sentiasa Tidak Kelihatan"]},71997:a=>{a.exports=["Sentiasa Kelihatan"]},97305:a=>{a.exports=["Semua Penunjuk Dan Alat Lukisan"]},59192:a=>{
a.exports=["Semua selang masa"]},14452:a=>{a.exports="Almaty"},5716:a=>{a.exports=["Guna Pakai Gelombang Elliot"]},19263:a=>{a.exports=["Guna Pakai Gelombang Elliot Major"]},15818:a=>{a.exports=["Guna Pakai Gelombang Elliot Minor"]},50352:a=>{a.exports=["Guna Pakai Gelombang Elliot Pertengahan"]},66631:a=>{a.exports=["Guna Pakai Titik Keputusan Manual"]},15682:a=>{a.exports=["Guna Pakai Risiko/Ganjaran Manual"]},15644:a=>{a.exports=["Guna Pakai Gelombang Menurun WPT"]},5897:a=>{a.exports=["Guna Pakai Gelombang Menaik WPT"]},13345:a=>{a.exports=["Guna Pakai Lalai"]},95910:a=>{a.exports=["Sila gunakan Petunjuk-petunjuk ini untuk Kesemua Susun Atur"]},42762:a=>{a.exports="Apr"},45104:a=>{a.exports=["Lengkuk"]},42097:a=>{a.exports=["Kawasan"]},96237:a=>{a.exports=["Anak Panah"]},48732:a=>{a.exports=["Anak Panah ke Bawah"]},82473:a=>{a.exports=["Penanda Anak Panah"]},8738:a=>{a.exports=["Anak Panah Tunjuk Bawah"]},35062:a=>{a.exports=["Anak Panah Tunjuk Kiri"]},92163:a=>{a.exports=["Anak Panah Tunjuk Kanan"]},33196:a=>{a.exports=["Anak Panah Tunjuk Atas"]},10650:a=>{a.exports=["Anak Panah ke Atas"]},59340:a=>{a.exports=["Ashkhabad"]},13468:a=>{a.exports=["Pada penutup"]},21983:a=>{a.exports="Athens"},86951:a=>{a.exports="Auto"},50834:a=>{a.exports=["Auto (Sesuaikan Data Ke Skrin)"]},38465:a=>{a.exports=["Ogos"]},8975:a=>{a.exports=["Label harga tutup purata"]},87899:a=>{a.exports=["Garisan harga purata tutup"]},22554:a=>{a.exports=["Purata"]},54173:a=>{a.exports="Bogota"},53260:a=>{a.exports="Bahrain"},40664:a=>{a.exports=["Belon"]},32376:a=>{a.exports="Bangkok"},19149:a=>{a.exports=["Bar Ulangan tidak tersedia untuk jenis carta ini. Adakah anda mahu keluar dari Bar Ulangan?"]},38660:a=>{a.exports=["Ulangan Bar tidak tersedia untuk selang masa ini. Adakah anda mahu keluar dari Ulangan Bar?"]},16812:a=>{a.exports=["Bar"]},98838:a=>{a.exports=["Corak Bar"]},17712:a=>{a.exports=["Garis Dasar"]},54861:a=>{a.exports="Belgrade"},26825:a=>{a.exports="Berlin"},30251:a=>{a.exports=["Berus"]},90204:a=>{a.exports="Brussels"},5262:a=>{a.exports="Bratislava"},59901:a=>{a.exports=["Bawa ke Hadapan"]},26354:a=>{a.exports=["Bawa ke Bahagian Hadapan"]},11741:a=>{a.exports="Brisbane"},37728:a=>{a.exports="Bucharest"},87143:a=>{a.exports="Budapest"},82446:a=>{a.exports="Buenos Aires"},82128:a=>{a.exports=["Oleh TradingView"]},75190:a=>{a.exports=["Pergi ke tarikh"]},38342:a=>{a.exports=["Pergi ke {lineToolName}"]},75139:a=>{a.exports=["Faham"]},81180:a=>{a.exports=["Kotak Gann"]},68102:a=>{a.exports=["Kipas Gann"]},66321:a=>{a.exports=["Segi Empat Gann"]},87107:a=>{a.exports=["Segi Empat Gann Tetap"]},7914:a=>{a.exports=["Suapan Bayang"]},18367:a=>{a.exports=["Grand Supercycle"]},97065:a=>{a.exports=["Anda benar-benar mahu memadam Templat Kajian '{name}'?"]},59368:a=>{a.exports=["Lengkung Kembar"]},35273:a=>{a.exports=["Klik dua kali mana-mana bucu untuk set semula grid susun atur"]},5828:a=>{a.exports=["Tekan dua kali untuk menyudahkan Laluan"]},63898:a=>{a.exports=["Tekan dua kali untuk menyudahkan Poligaris"]},42660:a=>{
a.exports=["Gelombang Ke Bawah 1 atau A"]},44788:a=>{a.exports=["Gelombang Ke Bawah 2 atau B"]},71263:a=>{a.exports=["Gelombang Ke Bawah 3"]},70573:a=>{a.exports=["Gelombang Ke Bawah 4"]},59560:a=>{a.exports=["Gelombang Ke Bawah 5"]},70437:a=>{a.exports=["Gelombang Ke Bawah C"]},53831:a=>{a.exports=["Tetingkap data..."]},93345:a=>{a.exports=["Data disumbangkan oleh"]},76912:a=>{a.exports=["Tarikh"]},60222:a=>{a.exports=["Julat Tarikh"]},79859:a=>{a.exports=["Julat Tarikh dan Harga"]},92203:a=>{a.exports=["Dis"]},69479:a=>{a.exports=["Darjah"]},57701:a=>{a.exports="Denver"},24477:a=>{a.exports="Dhaka"},73720:a=>{a.exports=["Berlian"]},3556:a=>{a.exports=["Saluran Tak Bercantum"]},62764:a=>{a.exports=["Anjakan"]},22903:a=>{a.exports=["Bar Alat Lukisan"]},8338:a=>{a.exports=["Lukis Garisan Mendatar"]},22429:a=>{a.exports="Dubai"},9497:a=>{a.exports="Dublin"},85223:a=>{a.exports="Emoji"},24435:a=>{a.exports=["Masukkan nama baharu susun atur carta"]},93512:a=>{a.exports=["Menyunting {title} Pemberitahuan"]},91215:a=>{a.exports=["Gelombang Pembetulan Elliott (ABC)"]},80983:a=>{a.exports=["Gelombang Kombo Berganda Elliott (WXY)"]},74118:a=>{a.exports=["Gelombang Impuls Elliott (12345)"]},95840:a=>{a.exports=["Gelombang Segitiga Elliott (ABCDE)"]},66637:a=>{a.exports=["Gelombang Kombo Tiga Kali Ganda Elliott (WXYXZ)"]},69418:a=>{a.exports=["Elips"]},27558:a=>{a.exports=["Panjangkan Garis Pemberitahuan"]},2578:a=>{a.exports=["Garisan Dipanjangkan"]},77295:a=>{a.exports=["Bursa"]},2899:a=>{a.exports=["Anak Tetingkap Sedia Ada Di Atas"]},53387:a=>{a.exports=["Anak Tetingkap Sedia Ada Di Bawah"]},36972:a=>{a.exports=["Ramalan"]},17994:a=>{a.exports=["Gagal untuk menyimpan perpustakaan"]},87375:a=>{a.exports=["Gagal untuk menyimpan skrip"]},35050:a=>{a.exports="Feb"},82719:a=>{a.exports=["Saluran Fib"]},64192:a=>{a.exports=["Bulatan Fib"]},63835:a=>{a.exports=["Anjakan Fib"]},18072:a=>{a.exports=["Lengkuk Rintangan Kelajuan Fib"]},20877:a=>{a.exports=["Kipas Rintangan Kelajuan Fib"]},76783:a=>{a.exports="Fib Spiral"},89037:a=>{a.exports=["Zon Waktu Fib"]},72489:a=>{a.exports=["Baji Fib"]},21524:a=>{a.exports=["Tanda"]},55678:a=>{a.exports=["Tanda Bendera"]},29230:a=>{a.exports=["Atas/Bawah Rata"]},92754:a=>{a.exports=["Terbalik"]},42015:a=>{a.exports=["Bahagian pecahan tidak sah."]},47542:a=>{a.exports=["Kajian Fundamental tidak lagi boleh didapati pada carta"]},16245:a=>{a.exports="Kolkata"},3155:a=>{a.exports="Kathmandu"},92901:a=>{a.exports="Kagi"},2693:a=>{a.exports="Karachi"},72374:a=>{a.exports="Kuwait"},34911:a=>{a.exports=["Kawasan HLC"]},87338:a=>{a.exports="Ho Chi Minh"},61582:a=>{a.exports=["Lilin Berongga"]},32918:a=>{a.exports="Hong Kong"},61351:a=>{a.exports="Honolulu"},60049:a=>{a.exports=["Garisan Mendatar"]},76604:a=>{a.exports=["Sinaran Mendatar"]},42616:a=>{a.exports=["Kepala dan Bahu"]},40530:a=>{a.exports="Heikin Ashi"},99820:a=>{a.exports="Helsinki"},31971:a=>{a.exports=["Sembunyi"]},33911:a=>{a.exports=["Sembunyi semua"]},95551:a=>{a.exports=["Sembunyi semua alatan lukisan"]},44312:a=>{
a.exports=["Sembunyi semua lukisan dan penunjuk"]},67927:a=>{a.exports=["Sembunyi semua lukisan, penunjuk, posisi & pesanan"]},86306:a=>{a.exports=["Sembunyi semua penunjuk"]},70803:a=>{a.exports=["Sembunyi semua posisi & pesanan"]},13277:a=>{a.exports=["Sembunyi lukisan"]},8251:a=>{a.exports=["Sembunyi peristiwa pada carta"]},44177:a=>{a.exports=["Sembunyi penunjuk"]},2441:a=>{a.exports=["Sembunyi tanda atas bar"]},90540:a=>{a.exports=["Sembunyi posisi & pesanan"]},30777:a=>{a.exports=["Tinggi"]},31994:a=>{a.exports=["Tinggi-Rendah"]},60259:a=>{a.exports=["Label Harga Tinggi dan Rendah"]},21803:a=>{a.exports=["Garisan harga tinggi dan rendah"]},31895:a=>{a.exports=["Penyerlah"]},69085:a=>{a.exports=['Histogram adalah terlalu besar, sila naikkan input "Saiz Baris".']},8122:a=>{a.exports=['Histogram adalah terlalu besar, sila kecilkan input "Saiz Baris".']},23450:a=>{a.exports=["Imej"]},71778:a=>{a.exports=["Perantaraan"]},14177:a=>{a.exports=["Simbol Tidak Sah"]},53239:a=>{a.exports=["Songsangkan skala"]},20062:a=>{a.exports=["Diindeks kepada 100"]},81584:a=>{a.exports=["Label nilai penunjuk"]},31485:a=>{a.exports=["Label nama penunjuk"]},27677:a=>{a.exports=["Garis Maklumat"]},98767:a=>{a.exports=["Masukkan Penunjuk"]},9114:a=>{a.exports=["Dalam"]},12354:a=>{a.exports=["Pitchfork Dalaman"]},26579:a=>{a.exports=["Ikon"]},37885:a=>{a.exports="Istanbul"},87469:a=>{a.exports="Johannesburg"},52707:a=>{a.exports="Jakarta"},95425:a=>{a.exports="Jan"},42890:a=>{a.exports=["Jurusalem"]},6215:a=>{a.exports="Jul"},15224:a=>{a.exports="Jun"},36253:a=>{a.exports="Juneau"},15241:a=>{a.exports=["Pada Sebelah Kiri"]},29404:a=>{a.exports=["Pada Sebelah Kanan"]},850:a=>{a.exports="Oops!"},675:a=>{a.exports=["Salasilah Objek"]},73546:a=>{a.exports=["Okt"]},39280:a=>{a.exports=["Buka"]},25595:a=>{a.exports=["Asal"]},82906:a=>{a.exports="Oslo"},8136:a=>{a.exports=["Rendah"]},42284:a=>{a.exports=["Kunci"]},1441:a=>{a.exports=["Kunci/Buka Kunci"]},82232:a=>{a.exports=["Kuncikan garisan kursor menaik dengan masa"]},18219:a=>{a.exports=["Nisbah Kunci Harga Kepada Bar"]},12285:a=>{a.exports=["Logaritma"]},50286:a=>{a.exports="London"},44604:a=>{a.exports=["Kedudukan Panjang"]},87604:a=>{a.exports="Los Angeles"},18528:a=>{a.exports=["Label ke Bawah"]},13046:a=>{a.exports=["Label ke Atas"]},94420:a=>{a.exports=["Label"]},89155:a=>{a.exports="Lagos"},25846:a=>{a.exports="Lima"},1277:a=>{a.exports=["Garisan"]},38397:a=>{a.exports=["Garis Dengan Penanda"]},63492:a=>{a.exports=["Garisan Putus"]},83182:a=>{a.exports=["Garisan"]},78104:a=>{a.exports=["Pautan ke Imej carta yang disalin ke papan keratan {emoji}"]},50091:a=>{a.exports="Lisbon"},64352:a=>{a.exports="Luxembourg"},11156:a=>{a.exports="MTPredictor"},67861:a=>{a.exports=["Gerakkan titik untuk meletakkan sauh kemudian ketik untuk meletakkannya."]},45828:a=>{a.exports=["Bergerak ke"]},44302:a=>{a.exports=["Gerakkan Skala Ke Kiri"]},94338:a=>{a.exports=["Gerakkan Skala Ke Kanan"]},66276:a=>{a.exports=["Schiff Diubah Suai"]},18559:a=>{a.exports=["Schiff Pitchfork Diubahsuai"]},18665:a=>{
a.exports="Moscow"},58038:a=>{a.exports="Madrid"},34190:a=>{a.exports="Malta"},90271:a=>{a.exports="Manila"},51369:a=>{a.exports=["Mac"]},85095:a=>{a.exports=["Bandar Mexico"]},75633:a=>{a.exports=["Gabungkan Kesemua Skala Manjadi Satu"]},95093:a=>{a.exports=["Bercampur"]},10931:a=>{a.exports=["Mikro"]},58397:a=>{a.exports=["Alaf"]},85884:a=>{a.exports=["Minuet"]},9632:a=>{a.exports="Minuscule"},63158:a=>{a.exports=["Dicerminkan"]},42769:a=>{a.exports="Muscat"},43088:a=>{a.exports="N/A"},3485:a=>{a.exports=["Tiada Skala (Skrin Penuh)"]},8886:a=>{a.exports=["Tidak segerak"]},16971:a=>{a.exports=["Tiada data volum"]},75549:a=>{a.exports=["Nota"]},71230:a=>{a.exports="Nov"},99203:a=>{a.exports="Norfolk Island"},79023:a=>{a.exports="Nairobi"},91203:a=>{a.exports="New York"},24143:a=>{a.exports="New Zealand"},40887:a=>{a.exports=["Tetingkap baharu di atas"]},96712:a=>{a.exports=["Tetingkap baharu di bawah"]},33566:a=>{a.exports="Nicosia"},56670:a=>{a.exports=["Ada berlaku masalah"]},64968:a=>{a.exports=["Berlaku sesuatu masalah. Sila cuba sebentar lagi."]},10520:a=>{a.exports=["Simpan Susun Atur Carta Baharu"]},9908:a=>{a.exports=["Simpan Sebagai"]},68553:a=>{a.exports="San Salvador"},65412:a=>{a.exports="Santiago"},13538:a=>{a.exports="Sao Paulo"},37207:a=>{a.exports=["Carta Harga Berskala Sahaja"]},51464:a=>{a.exports="Schiff"},98114:a=>{a.exports="Schiff Pitchfork"},1535:a=>{a.exports=["Skrip mungkin tidak dikemaskini jika anda tinggalkan halaman ini."]},89517:a=>{a.exports=["Tetapan"]},43247:a=>{a.exports=["Bahagian pecahan kedua tidak sah"]},19796:a=>{a.exports=["Hantar ke Belakang"]},23221:a=>{a.exports=["Hantar Ke Arah Belakang"]},5961:a=>{a.exports="Seoul"},57902:a=>{a.exports="Sep"},25866:a=>{a.exports=["Sesi"]},59827:a=>{a.exports=["Sesi Berehat Sementara"]},69240:a=>{a.exports="Shanghai"},37819:a=>{a.exports=["Kedudukan Singkat"]},81428:a=>{a.exports=["Tunjuk"]},98116:a=>{a.exports=["Tunjuk semua lukisan"]},39046:a=>{a.exports=["Tunjuk semua lukisan dan penunjuk"]},38293:a=>{a.exports=["Tunjuk semua lukisan, penunjuk, posisi & pesanan"]},49982:a=>{a.exports=["Tunjuk semua penunjuk"]},48284:a=>{a.exports=["Tunjuk semua idea"]},62632:a=>{a.exports=["Tunjuk semua posisi & pesanan"]},24620:a=>{a.exports=["Tunjuk pertukaran kontrak berterusan"]},84813:a=>{a.exports=["Tunjuk tamatan kontrak"]},66263:a=>{a.exports=["Tunjuk dividen"]},46771:a=>{a.exports=["Tunjuk perolehan"]},87933:a=>{a.exports=["Tunjuk idea pengguna yang diikuti"]},72973:a=>{a.exports=["Tunjukkan berita dan minda terkini"]},58669:a=>{a.exports=["Tunjuk idea saya sahaja"]},30816:a=>{a.exports=["Tunjukkan pemisahan"]},68161:a=>{a.exports=["Tiang Tanda"]},56683:a=>{a.exports=["Singapura"]},69502:a=>{a.exports=["Garisan Sinus"]},44904:a=>{a.exports=["Empat Segi Sama"]},70213:a=>{a.exports=["Kajian melebihi had: {number} kajian setiap susun atur.\nSila, alih keluar beberapa kajian anda."]},32733:a=>{a.exports=["Gaya"]},65323:a=>{a.exports=["Kumpulkan Ke Kiri"]},14113:a=>{a.exports=["Kumpulkan Ke Kanan"]},93161:a=>{a.exports=["Kekal di Mod Lukisan"]},
79511:a=>{a.exports=["Garis Tangga"]},84573:a=>{a.exports=["Pelekat"]},48767:a=>{a.exports="Stockholm"},29662:a=>{a.exports=["Submikro"]},9753:a=>{a.exports=["Sub-alaf"]},71722:a=>{a.exports="Subminuette"},91889:a=>{a.exports=["Kitaran Super"]},33820:a=>{a.exports=["Superalaf"]},11020:a=>{a.exports="Sydney"},89659:a=>{a.exports=["Ralat simbol"]},90932:a=>{a.exports=["Label nama simbol"]},65986:a=>{a.exports=["Maklumat Simbol"]},52054:a=>{a.exports=["Label harga akhir simbol"]},33606:a=>{a.exports=["Segerakkan secara sejagat"]},18008:a=>{a.exports=["Segerakkan Ke Semua Carta"]},99969:a=>{a.exports=["Titik & Angka"]},53047:a=>{a.exports=["Poligaris"]},34402:a=>{a.exports=["Laluan"]},70394:a=>{a.exports=["Saluran Selari"]},95995:a=>{a.exports="Paris"},29682:a=>{a.exports=["Tampal"]},51102:a=>{a.exports=["Peratus"]},35590:a=>{a.exports="Perth"},19093:a=>{a.exports="Phoenix"},22293:a=>{a.exports="Pitchfan"},43852:a=>{a.exports="Pitchfork"},37680:a=>{a.exports=["Pin Pada Skala Kiri Baru"]},43707:a=>{a.exports=["Pin Pada Skala Kanan Baru"]},91130:a=>{a.exports=["Pin Pada Skala Kiri"]},61201:a=>{a.exports=["Pin Pada Skala Kiri (Tersembunyi)"]},764:a=>{a.exports=["pin pada skala kanan"]},20207:a=>{a.exports=["Pin Pada Skala Kanan (Tersembunyi)"]},66156:a=>{a.exports=["Pin Pada Skala (Kini Kiri)"]},54727:a=>{a.exports=["Pin Pada Skala (Kini Tiada Skala)"]},76598:a=>{a.exports=["Pin Pada Skala (Kini Kanan)"]},39065:a=>{a.exports=["Pin Pada Skala (Kini {label})"]},97324:a=>{a.exports=["Pin Pada Skala {label}"]},56948:a=>{a.exports=["Pin Pada Skala {label} (Tersembunyi)"]},32156:a=>{a.exports=["Dipin Pada Skala Kiri"]},8128:a=>{a.exports=["Dipin pada skala kiri (tersembunyi)"]},3822:a=>{a.exports=["Dipin pada skala kanan"]},44538:a=>{a.exports=["Pin Pada Skala Kanan (Tersembunyi)"]},65810:a=>{a.exports=["Dipin Pada Skala {label}"]},14125:a=>{a.exports=["Dipin Pada Skala {label} (Tersembunyi)"]},97378:a=>{a.exports=["Butang Tambah"]},46669:a=>{a.exports=["Sila berikan kami kebenaran menulis papan keratan di penyemak imbas anda atau tekan {keystroke}"]},35963:a=>{a.exports=["Kekalkan menekan {key} apabila mengezum untuk mengekalkan kedudukan carta."]},95921:a=>{a.exports=["Label Harga"]},28625:a=>{a.exports=["Nota Harga"]},2032:a=>{a.exports=["Julat Harga"]},32061:a=>{a.exports=["Format harga tidak sah."]},91492:a=>{a.exports=["Garisan harga"]},48404:a=>{a.exports=["Utama"]},87086:a=>{a.exports=["Unjuran"]},10160:a=>{a.exports=["Diterbitkan pada {customer}, {date}"]},19056:a=>{a.exports="Qatar"},9998:a=>{a.exports=["Segi Empat Tepat Diputarkan"]},74214:a=>{a.exports="Rome"},50470:a=>{a.exports=["Sinar"]},90357:a=>{a.exports=["Julat"]},26833:a=>{a.exports="Reykjavik"},328:a=>{a.exports=["Segi Empat Tepat"]},41615:a=>{a.exports=["Buat Semula"]},35001:a=>{a.exports=["Arah Aliran Regresi"]},34596:a=>{a.exports=["Buang"]},1434:a=>{a.exports=["Buang lukisan"]},13951:a=>{a.exports=["Buang penunjuk"]},4142:a=>{a.exports=["Namakan Semula Susun Atur Carta"]},20801:a=>{a.exports="Renko"},34301:a=>{a.exports=["Set semula paparan carta"]},
18001:a=>{a.exports=["Set semua titik"]},17258:a=>{a.exports=["Set Semula Skala Harga"]},25333:a=>{a.exports=["Tetapkan Semula Skala Masa"]},52588:a=>{a.exports="Riyadh"},5871:a=>{a.exports="Riga"},33603:a=>{a.exports=["Amaran"]},48474:a=>{a.exports="Warsaw"},20466:a=>{a.exports="Tokelau"},94284:a=>{a.exports="Tokyo"},83836:a=>{a.exports="Toronto"},38788:a=>{a.exports="Taipei"},39108:a=>{a.exports="Tallinn"},37229:a=>{a.exports=["Teks"]},16267:a=>{a.exports="Tehran"},19611:a=>{a.exports=["Templat"]},29198:a=>{a.exports=["Pengeluar data tidak memberikan volum data untuk simbol ini."]},8162:a=>{a.exports=["Pratonton penerbitan tidak dapat dimuat. Sila nyahaktifkan pelanjutan penyemak imbas anda dan cuba lagi."]},65943:a=>{a.exports=["Penunjuk ini tidak boleh digunapakai pada penunjuk lain"]},74986:a=>{a.exports=["Skrip ini adalah jemputan sahaja. Untuk memohon akses, sila hubungi penulisnya."]},58018:a=>{a.exports=["Simbol hanya boleh didapati di {linkStart}TradingView{linkEnd}."]},98538:a=>{a.exports=["Corak Tiga Pemacu"]},30973:a=>{a.exports=["Tanda"]},31976:a=>{a.exports=["Masa"]},64375:a=>{a.exports=["Zon Masa"]},95005:a=>{a.exports=["Kitaran Masa"]},87085:a=>{a.exports=["Dagang"]},94770:a=>{a.exports=["Sudut Arah Aliran"]},23104:a=>{a.exports=["Garisan Arah Aliran"]},15501:a=>{a.exports=["Lanjutan Fib Berdasarkan Arah Aliran"]},31196:a=>{a.exports=["Masa Fib Berdasarkan Arah Aliran"]},29245:a=>{a.exports=["Segitiga"]},83356:a=>{a.exports=["Segitiga Menurun"]},12390:a=>{a.exports=["Corak Segitiga"]},28340:a=>{a.exports=["Segitiga Menaik"]},93855:a=>{a.exports="Tunis"},50406:a=>{a.exports="UTC"},81320:a=>{a.exports=["Buat asal"]},25933:a=>{a.exports=["Unit"]},15101:a=>{a.exports=["Buka kunci"]},34150:a=>{a.exports=["Gelombang Menaik 4"]},83927:a=>{a.exports=["Gelombang Menaik 5"]},58976:a=>{a.exports=["Gelombang Menaik 1 atau A"]},11661:a=>{a.exports=["Gelombang Menaik 2 atau B"]},53958:a=>{a.exports=["Gelombang Menaik 3"]},66560:a=>{a.exports=["Gelombang Menaik C"]},18426:a=>{a.exports="Volume Profile Fixed Range"},61022:a=>{a.exports=["Penunjuk Profil Volum adalah tersedia hanya pada pelan-pelan naik taraf kami."]},15771:a=>{a.exports="Vancouver"},56211:a=>{a.exports=["Garisan Menegak"]},75354:a=>{a.exports="Vilnius"},21852:a=>{a.exports=["Kebolehlihatan"]},27557:a=>{a.exports=["Kebolehlihatan pada selang masa"]},89960:a=>{a.exports=["Boleh dilihat pada Mouse Over"]},22198:a=>{a.exports=["Order Visual"]},7050:a=>{a.exports=["Silang X"]},66527:a=>{a.exports=["Corak XABCD"]},17126:a=>{a.exports=["Anda tidak dapat melihat rangka masa pangsi pada resolusi ini"]},69293:a=>{a.exports="Yangon"},84301:a=>{a.exports="Zurich"},76020:a=>{a.exports=["tukar darjah Elliott"]},83935:a=>{a.exports=["tukar tiada label bertindih"]},39402:a=>{a.exports=["tukar kebolehlihatan label harga tutup purata"]},98866:a=>{a.exports=["tukar kebolehlihatan garisan harga tutup purata"]},5100:a=>{a.exports=["tukar keterlihatan label bidaan dan permintaan"]},32311:a=>{a.exports=["tukar keterlihatan garisan bidaan dan permintaan"]},22641:a=>{
a.exports=["tukar mata wang"]},30501:a=>{a.exports=["tukar susun atur carta ke {title} ="]},7017:a=>{a.exports=["tukar kebolehlihatan pertukaran kontrak berterusan"]},58108:a=>{a.exports=["tukar keterlihatan kiraan detik ke bar tutup"]},7151:a=>{a.exports=["tukar julat tarikh"]},84944:a=>{a.exports=["tukar keterlihatan dividen"]},79574:a=>{a.exports=["tukar keterlihatan peristiwa di atas carta"]},88217:a=>{a.exports=["tukar keterlihatan perolehan"]},28288:a=>{a.exports=["tukar kebolehlihatan tempoh tamat kontrak hadapan"]},66805:a=>{a.exports=["tukar kebolehlihatan label harga tinggi dan rendah"]},92556:a=>{a.exports=["tukar kebolehlihatan garisan harga tinggi dan rendah"]},87027:a=>{a.exports=["ubah keterlihatan nama label penunjuk"]},14922:a=>{a.exports=["ubah keterlihatan nilai label penunjuk"]},19839:a=>{a.exports=["tukar kebolehlihatan berita dan minda terkini"]},23783:a=>{a.exports=["tukar kumpulan pautan"]},87510:a=>{a.exports=["tukar tinggi jendela"]},50190:a=>{a.exports=["tukar keterlihatan butang tambah"]},49889:a=>{a.exports=["tukar keterlihatan label pra/pasca harga pasaran"]},16750:a=>{a.exports=["tukar keterlihatan garisan pra/pasca harga pasaran"]},59883:a=>{a.exports=["tukar keterlihatan garisan harga tutup sebelumnya"]},67761:a=>{a.exports=["Tukar Garis Harga"]},69510:a=>{a.exports=["tukar nisbah harga ke bar"]},32303:a=>{a.exports=["Tukar Resolusi"]},526:a=>{a.exports=["Tukar simbol"]},9402:a=>{a.exports=["tukar kebolehlihatan label simbol"]},53150:a=>{a.exports=["tukar keterlihatan nilai akhir simbol"]},12707:a=>{a.exports=["tukar keterlihatan nilai penutup simbol sebelumnya"]},65303:a=>{a.exports=["tukar sesi"]},15403:a=>{a.exports=["tukar kebolehlihatan rehat sesi"]},53438:a=>{a.exports=["tukar gaya siri"]},74488:a=>{a.exports=["tukar keterlihatan pemisahan"]},20505:a=>{a.exports=["tukar zon masa"]},39028:a=>{a.exports=["tukar unit"]},21511:a=>{a.exports=["Tukar Kebolehlihatan"]},16698:a=>{a.exports=["tukar kebolehlihatan pada selang masa terkini"]},78422:a=>{a.exports=["tukar kebolehlihatan pada selang masa terkini dan keatas"]},49529:a=>{a.exports=["tukar kebolehlihatan pada selang masa terkini dan kebawah"]},66927:a=>{a.exports=["tukar kebolehlihatan pada semua selang masa"]},74428:a=>{a.exports=["tukar {title} gaya"]},72032:a=>{a.exports=["tukar {pointIndex} titik"]},65911:a=>{a.exports=["carta oleh TradingView"]},5179:a=>{a.exports=["klon alat-alat garisan"]},3195:a=>{a.exports=["Cipta kumpulan garisan alatan"]},92659:a=>{a.exports=["cipta garisan alat-alat kumpulan daripada pilihan"]},81791:a=>{a.exports=["cipta {tool}"]},63649:a=>{a.exports=["potong sumber"]},78755:a=>{a.exports=["potong {title}"]},99113:a=>{a.exports=["Tambah alatan garisan {lineTool} kepada kumpulan {name}"]},40242:a=>{a.exports=["tambah alat() garisan kepada kumpulan {group}"]},22856:a=>{a.exports=["Tambah Metrik Kewangan ini kepada seluruh Susun Atur"]},82388:a=>{a.exports=["Tambah Petunjuk ini kepada seluruh Susun Atur"]},94292:a=>{a.exports=["Tambah Strategi ini kepada seluruh Susun Atur"]},27982:a=>{
a.exports=["Tambah Simbol ini kepada seluruh Susun Atur"]},66568:a=>{a.exports=["aplikasikan tema carta"]},64034:a=>{a.exports=["gunakan semua sifat carta"]},49037:a=>{a.exports=["gunakan templat lukisan"]},96996:a=>{a.exports=["gunakan asas kilang kepada sumber terpilih"]},44547:a=>{a.exports=["guna penunjuk ke seluruh susun atur"]},26065:a=>{a.exports=["Gunakan templat kajian {template}"]},58570:a=>{a.exports=["gunakan tema alat bar"]},27195:a=>{a.exports=["hantar kumpulan {title} ke depan"]},78246:a=>{a.exports=["bawa {title} ke depan"]},56763:a=>{a.exports=["Bawa {title} ke hadapan"]},5607:a=>{a.exports=["oleh TradingView"]},90621:a=>{a.exports=["kunci julat tarikh"]},12962:a=>{a.exports=["padam garisan tahap"]},63391:a=>{a.exports=["keluarkan garis alatan daripada kumpulan {group}"]},59942:a=>{a.exports=["terbalikkan corak bar"]},70301:a=>{a.exports=["sembunyi {title}"]},91842:a=>{a.exports=["sembunyi label garis pemberitahuan"]},54781:a=>{a.exports=["sembunyi semua alat lukisan"]},44974:a=>{a.exports=["sembunyi tanda atas bar"]},28916:a=>{a.exports=["kunci selang masa"]},94245:a=>{a.exports=["Songsangkan skala"]},90743:a=>{a.exports=["isi {title}"]},53146:a=>{a.exports=["masukkan {title} selepas {targetTitle}"]},74055:a=>{a.exports=["Masukkan {title} selepas {target}"]},11231:a=>{a.exports=["Masukkan {title} sebelum {target}"]},67176:a=>{a.exports=["Masukkan {title} sebelum {targetTitle}"]},54597:a=>{a.exports=["muatkan templat lukisan asal"]},30295:a=>{a.exports=["memuatkan..."]},50193:a=>{a.exports=["Kunci {title}"]},4963:a=>{a.exports=["kunci kumpulan {group}"]},68163:a=>{a.exports=["kunci objek-objek"]},47107:a=>{a.exports=["gerak"]},11303:a=>{a.exports=["gerakkan {title} ke skala kiri baru"]},45544:a=>{a.exports=["gerakkan {title} ke skala kanan baru"]},81898:a=>{a.exports=["Gerakkan Kesemua Skala Ke Kiri"]},22863:a=>{a.exports=["Gerakkan Kesemua Skala Ke Kanan"]},45356:a=>{a.exports=["Pindahkan Lukisan(s)"]},15086:a=>{a.exports=["gerak kiri"]},61711:a=>{a.exports=["gerak kanan"]},4184:a=>{a.exports=["Gerakkan skala"]},74642:a=>{a.exports=["Jadikan {title} Tanpa Skala (Skrin Penuh)"]},45223:a=>{a.exports=["Jadikan kumpulan {group} tidak dapat dilihat"]},87927:a=>{a.exports=["jadikan kumpulan {group} dapat dilihat"]},62153:a=>{a.exports=["cantum ke bawah"]},70746:a=>{a.exports=["cantum ke tingkap"]},66143:a=>{a.exports=["cantum ke atas"]},81870:a=>{a.exports=["cerminkan corak bar"]},16542:a=>{a.exports="n/a"},47222:a=>{a.exports=["skalakan harga"]},99042:a=>{a.exports=["Carta Harga Berskala Sahaja"]},35962:a=>{a.exports=["skala masa"]},68193:a=>{a.exports=["tatal"]},70009:a=>{a.exports=["tatal masa"]},69485:a=>{a.exports=["setkan strategi pilihan skala harga ke {title}"]},16259:a=>{a.exports=["Hantarkan {title} ke belakang"]},66781:a=>{a.exports=["hantar {title} ke belakang"]},4998:a=>{a.exports=["hantar kumpulan {title} ke belakang"]},64704:a=>{a.exports=["kongsi alat garisan keseluruh dunia"]},77554:a=>{a.exports=["kongsi alat garisan di dalam susun atur"]},16237:a=>{
a.exports=["tunjuk label garisan pemberitahuan"]},13622:a=>{a.exports=["tunjuk semua idea"]},26267:a=>{a.exports=["tunjuk idea pengguna yang diikuti"]},40061:a=>{a.exports=["tunjuk idea saya sahaja"]},52010:a=>{a.exports=["kekal di mod lukisan"]},98784:a=>{a.exports=["henti segerakkan lukisan"]},57011:a=>{a.exports=["henti segerakkan alat(s) garisan"]},92831:a=>{a.exports=["kunci simbol"]},60635:a=>{a.exports=["segerakkan masa"]},99769:a=>{a.exports=["dikuasakan oleh"]},68111:a=>{a.exports=["dikuasakan oleh TradingView"]},96916:a=>{a.exports=["tampal lukisan"]},80611:a=>{a.exports=["tampal penunjuk"]},41601:a=>{a.exports=["tampal {title}"]},84018:a=>{a.exports=["pin pada skala kiri"]},22615:a=>{a.exports=["Pin Pada Skala Kanan"]},56015:a=>{a.exports=["pin pada skala {label}"]},33348:a=>{a.exports=["susun semula tingkap"]},15516:a=>{a.exports=["buang semua kajian"]},80171:a=>{a.exports=["buang semua kajian dan alat lukisan"]},59211:a=>{a.exports=["buang alatan garisan kosong yang tidak dipilih"]},44656:a=>{a.exports=["buang lukisan"]},70653:a=>{a.exports=["buang kumpulan lukisan"]},66414:a=>{a.exports=["buang sumber-sumber data garisan"]},47637:a=>{a.exports=["buang tingkap"]},39859:a=>{a.exports=["buang {title}"]},78811:a=>{a.exports=["buang kumpulan alat garisan {name}"]},16338:a=>{a.exports=["Namakan kumpulan {group} kepada {newName}"]},30910:a=>{a.exports=["set semula saiz-saiz susun atur"]},21948:a=>{a.exports=["set semula skala"]},55064:a=>{a.exports=["Tetapkan Semula Skala Masa"]},13034:a=>{a.exports=["ubah saiz susun atur"]},9608:a=>{a.exports=["kembali ke asal"]},63060:a=>{a.exports=["togol skala auto"]},98860:a=>{a.exports=["togol skala diindekskan ke 100"]},21203:a=>{a.exports=["togol skala kunci"]},60166:a=>{a.exports=["togol skala log"]},68642:a=>{a.exports=["Skala Peratusan Togol"]},33714:a=>{a.exports=["togol skala biasa"]},47122:a=>{a.exports=["jejak masa"]},28068:a=>{a.exports=["hentikan perkongsian alat garisan"]},66824:a=>{a.exports=["nyahkunci objek-objek"]},51114:a=>{a.exports=["Buka kunci kumpulan {group}"]},92421:a=>{a.exports=["nyahkunci {title}"]},20057:a=>{a.exports=["nyahcantum ke tingkap bawah baru"]},52540:a=>{a.exports=["nyahcantum ke atas"]},86949:a=>{a.exports=["nyahcantum ke bawah"]},50728:a=>{a.exports=["Kemaskini Skrip {title}"]},33355:a=>{a.exports=["{count} bar"]},88841:a=>{a.exports=["{symbol} kewangan oleh TradingView"]},38641:a=>{a.exports=["{userName} diterbitkan pada {customer}, {date}"]},59833:a=>{a.exports=["zum"]},19813:a=>{a.exports=["zum kedalam"]},9645:a=>{a.exports=["zum keluar"]},30572:a=>{a.exports=["hari"]},52254:a=>{a.exports=["jam"]},99062:a=>{a.exports=["bulan"]},69143:a=>{a.exports=["minit"]},71787:a=>{a.exports=["saat"]},82797:a=>{a.exports=["julat"]},47966:a=>{a.exports=["minggu"]},99136:a=>{a.exports="tick"},18562:a=>{a.exports=Object.create(null),a.exports["#AAPL-symbol-description"]=["Apple Inc."],a.exports["#AUDCAD-symbol-description"]=["Dolar Australia/Dolar Kanada"],a.exports["#AUDCHF-symbol-description"]=["Dolar Australia / Franc Swiss"],
a.exports["#AUDJPY-symbol-description"]=["Dolar Australia / Yen Jepun"],a.exports["#AUDNZD-symbol-description"]=["Dolar Australia / Dolar New Zealand"],a.exports["#AUDRUB-symbol-description"]=["Dolar Australia / Ruble Russia"],a.exports["#AUDUSD-symbol-description"]=["Dolar Australia / Dolar A.S."],a.exports["#BRLJPY-symbol-description"]=["Real Brazil / Yen Jepun"],a.exports["#BTCCAD-symbol-description"]=["Bitcoin / Dolar Kanada"],a.exports["#BTCCNY-symbol-description"]=["Bitcoin / Yuan China"],a.exports["#BTCEUR-symbol-description"]="Bitcoin / Euro",a.exports["#BTCKRW-symbol-description"]=["Bitcoin/ Won Korea Selatan"],a.exports["#BTCRUR-symbol-description"]="Bitcoin / Ruble",a.exports["#BTCUSD-symbol-description"]=["Bitcoin / Dolar A.S."],a.exports["#BVSP-symbol-description"]=["Indeks Brazil Bovespa"],a.exports["#CADJPY-symbol-description"]=["Dolar Kanada / Yen Jepun"],a.exports["#CB1!-symbol-description"]=["Minyak Mentah Brent"],a.exports["#CHFJPY-symbol-description"]=["Franc Swiss / Yen Jepun"],a.exports["#COPPER-symbol-description"]=["CFD untuk Tembaga"],a.exports["#ES1-symbol-description"]=["Hadapan S&P 500 E-Mini"],a.exports["#ESP35-symbol-description"]=["Indeks IBEX 35"],a.exports["#EUBUND-symbol-description"]="Euro Bund",a.exports["#EURAUD-symbol-description"]=["Euro / Dolar Australia"],a.exports["#EURBRL-symbol-description"]=["Euro / Real Brazil"],a.exports["#EURCAD-symbol-description"]=["Euro / Dolar Kanada"],a.exports["#EURCHF-symbol-description"]=["Euro / Franc Swiss"],a.exports["#EURGBP-symbol-description"]=["Euro / Paun British"],a.exports["#EURJPY-symbol-description"]=["Euro / Yen Jepun"],a.exports["#EURNZD-symbol-description"]=["Euro / Dolar New Zealand"],a.exports["#EURRUB-symbol-description"]=["Euro / Ruble Russia"],a.exports["#EURRUB_TOM-symbol-description"]=["Euro / Ruble Russia TOM"],a.exports["#EURSEK-symbol-description"]=["Euro / Krona Sweden"],a.exports["#EURTRY-symbol-description"]=["Euro / Lira Turki"],a.exports["#EURUSD-symbol-description"]=["Euro / Dolar A.S."],a.exports["#EUSTX50-symbol-description"]=["Indeks Euro Stoxx 50"],a.exports["#FRA40-symbol-description"]=["Indeks CAC 40"],a.exports["#GB10-symbol-description"]=["Bon Kerajaan UK 10 tahun"],a.exports["#GBPAUD-symbol-description"]=["Paun British / Dolar Australia"],a.exports["#GBPCAD-symbol-description"]=["Paun British / Dolar Kanada"],a.exports["#GBPCHF-symbol-description"]=["Paun British / Franc Swiss"],a.exports["#GBPEUR-symbol-description"]=["Paun British / Euro"],a.exports["#GBPJPY-symbol-description"]=["Paun British / Yen Jepun"],a.exports["#GBPNZD-symbol-description"]=["Paun British / Dolar New Zealand"],a.exports["#GBPRUB-symbol-description"]=["Paun British / Ruble Russia"],a.exports["#GBPUSD-symbol-description"]=["Paun British / Dolar A.S."],a.exports["#GER30-symbol-description"]=["Indeks DAX"],a.exports["#GOOGL-symbol-description"]=["Alphabet Inc (Google) Kelas A"],a.exports["#ITA40-symbol-description"]=["Indeks FTSE MIB"],a.exports["#JPN225-symbol-description"]=["Indeks Nikkei 225"],
a.exports["#JPYKRW-symbol-description"]=["Yen Jepun / Won Korea Selatan"],a.exports["#JPYRUB-symbol-description"]=["Yen Jepun / Ruble Russia"],a.exports["#KA1-symbol-description"]=["Gula #11 Hadapan"],a.exports["#KG1-symbol-description"]=["Kapas Hadapan"],a.exports["#KT1-symbol-description"]="Key Tronic Corр.",a.exports["#LKOH-symbol-description"]="LUKOIL",a.exports["#LTCBTC-symbol-description"]="Litecoin / Bitcoin",a.exports["#MGNT-symbol-description"]="Magnit",a.exports["#MICEX-symbol-description"]=["Indeks MICEX"],a.exports["#MNOD_ME.EQRP-symbol-description"]=["Saham Biasa ADR GMK NORILSKIYNIKEL [REPO]"],a.exports["#MSFT-symbol-description"]="Microsoft Corp.",a.exports["#NAS100-symbol-description"]=["CFD Tunai US 100"],a.exports["#NGAS-symbol-description"]=["Gas Asli (Henry Hub)"],a.exports["#NKY-symbol-description"]=["Indeks Nikkei 225"],a.exports["#NZDJPY-symbol-description"]=["Dolar New Zealand / Yen Jepun"],a.exports["#NZDUSD-symbol-description"]=["Dolar New Zealand / Dolar A.S."],a.exports["#RB1-symbol-description"]=["Hadapan RBOB Gasoline"],a.exports["#RTS-symbol-description"]=["Indeks RTS Russia"],a.exports["#SBER-symbol-description"]="SBERBANK",a.exports["#SPX500-symbol-description"]=["Indeks S&P 500"],a.exports["#TWTR-symbol-description"]=["TWITTER INC"],a.exports["#UK100-symbol-description"]=["Indeks FTSE 100"],a.exports["#USDBRL-symbol-description"]=["Dolar A.S. / Real Brazil"],a.exports["#USDCAD-symbol-description"]=["Dolar A.S. / Dolar Kanada"],a.exports["#USDCHF-symbol-description"]=["Dolar A.S. / Franc Swiss"],a.exports["#USDCNY-symbol-description"]=["Dolar A.S. / Yuan China"],a.exports["#USDDKK-symbol-description"]=["Dolar A.S. / Krone Denmark"],a.exports["#USDHKD-symbol-description"]=["Dolar A.S. / Dolar Hong Kong"],a.exports["#USDIDR-symbol-description"]=["Dolar A.S. / Rupiah"],a.exports["#USDINR-symbol-description"]=["Dolar A.S / Rupee India"],a.exports["#USDJPY-symbol-description"]=["Dolar A.S. / Yen Jepun"],a.exports["#USDKRW-symbol-description"]=["Dolar A.S. / Korea Selatan"],a.exports["#USDMXN-symbol-description"]=["Dolar A.S. / Peso Mexico"],a.exports["#USDPHP-symbol-description"]=["Dolar A.S. / Peso Filipina"],a.exports["#USDRUB-symbol-description"]=["Dolar A.S. / Ruble Rusia"],a.exports["#USDRUB_TOM-symbol-description"]=["Dolar A.S. / Ruble Rusia TOM"],a.exports["#USDSEK-symbol-description"]=["Dolar A.S. / Krona Sweden"],a.exports["#USDSGD-symbol-description"]=["Dolar A.S. / Dolar Singapura"],a.exports["#USDTRY-symbol-description"]=["Dolar A.S. / Lira TurkI"],a.exports["#VTBR-symbol-description"]="VTB",a.exports["#XAGUSD-symbol-description"]=["Perak / Dolar A.S."],a.exports["#XAUUSD-symbol-description"]=["Emas Semerta / Dolar A.S."],a.exports["#XPDUSD-symbol-description"]=["CFD untuk Palladium"],a.exports["#XPTUSD-symbol-description"]=["Platinum / Dolar A.S."],a.exports["#ZS1-symbol-description"]=["Kacang Soya Hadapan - ECBT"],a.exports["#ZW1-symbol-description"]=["Hadapan Gandum  - ECBT"],a.exports["#BTCGBP-symbol-description"]=["Bitcoin / Paun British"],
a.exports["#MICEXINDEXCF-symbol-description"]=["Indeks Rusia MOEX"],a.exports["#BTCAUD-symbol-description"]=["Bitcon / Dolar Australia"],a.exports["#BTCJPY-symbol-description"]=["Bitcoin / Yen Jepun"],a.exports["#BTCBRL-symbol-description"]="Bitcoin / Brazilian Real",a.exports["#PT10-symbol-description"]=["Bon 10 Tahun Kerajaan Portugal"],a.exports["#TXSX-symbol-description"]=["Indeks TSX 60"],a.exports["#VIXC-symbol-description"]=["Indeks TSX 60 VIX"],a.exports["#USDPLN-symbol-description"]=["Dolar A.S. / Zloty Poland"],a.exports["#EURPLN-symbol-description"]=["Euro / Zloty Poland"],a.exports["#BTCPLN-symbol-description"]=["Bitcoin / Zloty Poland"],a.exports["#CAC40-symbol-description"]=["Indeks CAC 40"],a.exports["#XBTCAD-symbol-description"]=["Bitcoin / Dolar Kanada"],a.exports["#ITI2!-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIF2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIF2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIF2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIG2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIG2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIG2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIH2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIH2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIH2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIJ2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIJ2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIJ2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIK2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIK2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIK2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIM2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIM2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIM2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIM2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIN2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIN2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIN2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIN2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIQ2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIQ2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIQ2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIQ2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIU2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIU2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIU2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIU2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIV2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIV2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIV2019-symbol-description"]=["Bijih Besi Hadapan"],
a.exports["#ITIV2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIX2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIX2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIX2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIX2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIZ2017-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIZ2018-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIZ2019-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#ITIZ2020-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#AMEX:GXF-symbol-description"]="Global x FTSE Nordic Region ETF",a.exports["#ASX:XAF-symbol-description"]=["Indeks S&P/ASX All Australian 50"],a.exports["#ASX:XAT-symbol-description"]=["Indeks S&P/ASX All Australian 200"],a.exports["#BIST:XU100-symbol-description"]=["Indeks BIST 100"],a.exports["#GPW:WIG20-symbol-description"]=["Indeks WIG20"],a.exports["#INDEX:JKSE-symbol-description"]=["Indeks Komposit Jakarta"],a.exports["#INDEX:KLSE-symbol-description"]=["Indeks Bursa Malaysia KLCI"],a.exports["#INDEX:NZD-symbol-description"]=["Indeks NZX 50"],a.exports["#INDEX:STI-symbol-description"]=["Indeks STI"],a.exports["#INDEX:XLY0-symbol-description"]=["Indeks Komposit Shanghai"],a.exports["#MOEX:MICEXINDEXCF-symbol-description"]=["Indeks Rusia MOEX"],a.exports["#NYMEX:KT1!-symbol-description"]=["Kopi Hadapan"],a.exports["#OANDA:NATGASUSD-symbol-description"]=["CFDs di Gas Asli"],a.exports["#OANDA:USDPLN-symbol-description"]=["Dolar A.S. / Zloty Poland"],a.exports["#TSX:TX60-symbol-description"]=["Indeks S&P/TSX 60"],a.exports["#TSX:VBU-symbol-description"]=["Vanguard US Aggregate BND INDX ETF(CAD-HEG)UN"],a.exports["#TSX:VIXC-symbol-description"]=["Indeks S&P/TSX 60 VIX"],a.exports["#TVC:CAC40-symbol-description"]=["Indeks CAC40"],a.exports["#TVC:ES10-symbol-description"]=["Bon Kerajaan Sepanyol 10 Tahun"],a.exports["#TVC:EUBUND-symbol-description"]="Euro Bund",a.exports["#TVC:GB02-symbol-description"]=["Bon Kerajaan UK 2 Tahun"],a.exports["#TVC:GB10-symbol-description"]=["Bon Kerajaan UK 10 Tahun"],a.exports["#TVC:GOLD-symbol-description"]=["CFD untuk Emas (US$ / Auns)"],a.exports["#TVC:ID03-symbol-description"]=["Bon Kerajaan Indonesia 3 Tahun"],a.exports["#TVC:ID10-symbol-description"]=["Bon Kerajaan Indonesia 10 Tahun"],a.exports["#TVC:PALLADIUM-symbol-description"]=["CFD untuk Palladium (US$ / Auns)"],a.exports["#TVC:PT10-symbol-description"]=["Bon Kerajaan Portugal 10 Tahun"],a.exports["#TVC:SILVER-symbol-description"]=["CFD untuk Perak (US$ / Auns)"],a.exports["#TVC:RUT-symbol-description"]=["Indeks Russell 2000"],a.exports["#TSX:TSX-symbol-description"]=["Indeks Komposit S&P/TSX"],a.exports["#OANDA:CH20CHF-symbol-description"]=["Indeks Swiss 20"],a.exports["#TVC:SHCOMP-symbol-description"]=["Indeks Komposit Shanghai"],a.exports["#NZX:ALLC-symbol-description"]=["Indeks S&P/NZX ALL ( Indeks Capital )"],a.exports["#AMEX:SHYG-symbol-description"]=["Saham 0-5 TAHUN Bon Korporat Hasil Tinggi ETF"],
a.exports["#TVC:AU10-symbol-description"]=["Bon Kerajaan Australia 10 Tahun"],a.exports["#TVC:CN10-symbol-description"]=["Bon Kerajaan China 10 Tahun"],a.exports["#TVC:KR10-symbol-description"]=["Bon Kerajaan Korea 10 Tahun"],a.exports["#NYMEX:RB1!-symbol-description"]=["RBOB Gasoline Hadapan"],a.exports["#NYMEX:HO1!-symbol-description"]=["NY Harbor ULSD Hadapan"],a.exports["#NYMEX:AEZ1!-symbol-description"]=["NY Ethanol Hadapan"],a.exports["#OANDA:XCUUSD-symbol-description"]=["CFD untuk Tembaga (US$ / paun)"],a.exports["#COMEX:ZA1!-symbol-description"]=["Zing Hadapan"],a.exports["#CBOT:ZW1!-symbol-description"]=["Hadapan Gandum"],a.exports["#NYMEX:KA1!-symbol-description"]=["Gula #11 Hadapan"],a.exports["#CBOT:QBC1!-symbol-description"]=["Harapan Jagung"],a.exports["#CME:E61!-symbol-description"]=["Pasaran Hadapan Euro"],a.exports["#CME:B61!-symbol-description"]=["Hadapan Paun British"],a.exports["#CME:QJY1!-symbol-description"]=["Hadapan Yen Jepun"],a.exports["#CME:A61!-symbol-description"]=["Hadapan Dolar Australia"],a.exports["#CME:D61!-symbol-description"]=["Hadapan Dolar Kanada"],a.exports["#CME:SP1!-symbol-description"]=["Hadapan S&P 500"],a.exports["#CME_MINI:NQ1!-symbol-description"]=["Hadapan NASDAQ 100 E-MINI"],a.exports["#CBOT_MINI:YM1!-symbol-description"]=["Hadapan E-MINI DOW JONES ($5)"],a.exports["#CME:NY1!-symbol-description"]=["Hadapan NIKKEI 225"],a.exports["#EUREX:DY1!-symbol-description"]=["Indeks DAX"],a.exports["#CME:IF1!-symbol-description"]=["Hadapan IBOVESPA Indeks-US$"],a.exports["#CBOT:TY1!-symbol-description"]=["Hadapan T-Note 10 Tahun"],a.exports["#CBOT:FV1!-symbol-description"]=["Hadapan T-Note 5 Tahun"],a.exports["#CBOT:ZE1!-symbol-description"]=["Nota Perbendaharaan - Hadapan 3 Tahun"],a.exports["#CBOT:TU1!-symbol-description"]=["Hadapan T-Note 2 Tahun"],a.exports["#CBOT:FF1!-symbol-description"]=["30-Hari Kadar Faedah Dana FED Hadapan"],a.exports["#CBOT:US1!-symbol-description"]=["Hadapan T-Bond"],a.exports["#TVC:EXY-symbol-description"]=["Indeks Mata Wang Euro"],a.exports["#TVC:JXY-symbol-description"]=["Indeks Mata Wang Yen Jepun"],a.exports["#TVC:BXY-symbol-description"]=["Indeks Mata Wang Paun British"],a.exports["#TVC:AXY-symbol-description"]=["Indeks Mata Wang Dolar Australia"],a.exports["#TVC:CXY-symbol-description"]=["Indeks Mata Wang Dolar Kanada"],a.exports["#FRED:GDP-symbol-description"]=["Produk Domestik Kasar, 1 Perpuluhan"],a.exports["#FRED:UNRATE-symbol-description"]=["Kadar Pengangguran Awam"],a.exports["#FRED:POP-symbol-description"]=["Jumlah Penduduk: Semua Umur Termasuk Angkatan Bersenjata Di Luar Negara"],a.exports["#ETHUSD-symbol-description"]=["Ethereum / Dolar A.S."],a.exports["#BMFBOVESPA:IBOV-symbol-description"]=["Indeks IBovespa"],a.exports["#BMFBOVESPA:IBRA-symbol-description"]=["Indeks IBrasil"],a.exports["#BMFBOVESPA:IBXL-symbol-description"]=["Indeks IBRX 50"],a.exports["#COMEX:HG1!-symbol-description"]=["Hadapan Tembaga"],a.exports["#INDEX:HSCE-symbol-description"]=["Indeks Hang Seng China Enterprises"],
a.exports["#NYMEX:CL1!-symbol-description"]=["Hadapan Minyak Mentah Ringan"],a.exports["#OTC:IHRMF-symbol-description"]="Ishares MSCI Japan SHS",a.exports["#TVC:DAX-symbol-description"]=["Indeks 30 Syarikat Utama Jerman"],a.exports["#TVC:DE10-symbol-description"]=["Bon Kerajaan Jerman 10 Tahun"],a.exports["#TVC:DJI-symbol-description"]=["Indeks Purata Industri Dow Jones"],a.exports["#TVC:DXY-symbol-description"]=["Indeks Mata Wang Dolar A.S."],a.exports["#TVC:FR10-symbol-description"]=["Bon Kerajaan Perancis 10 Tahun"],a.exports["#TVC:HSI-symbol-description"]=["Indeks Hang Seng"],a.exports["#TVC:IBEX35-symbol-description"]=["Indeks IBEX 35"],a.exports["#FX:AUS200-symbol-description"]=["Indeks S&P/ASX"],a.exports["#AMEX:SHY-symbol-description"]=["Ishares 1-3 Tahun Bon Perbendaharaan ETF"],a.exports["#ASX:XJO-symbol-description"]=["Indeks S&P/ASX 200"],a.exports["#BSE:SENSEX-symbol-description"]=["Indeks S&P BSE Sensex"],a.exports["#INDEX:MIB-symbol-description"]=["Indeks MIB"],a.exports["#INDEX:MOY0-symbol-description"]=["Indeks Euro Stoxx 50"],a.exports["#MOEX:RTSI-symbol-description"]=["Indeks RTS"],a.exports["#NSE:NIFTY-symbol-description"]=["Indeks Nifty 50"],a.exports["#NYMEX:NG1!-symbol-description"]=["Hadapan Gas Asli"],a.exports["#NYMEX:ZC1!-symbol-description"]=["Kontrak Masa Hadapan Jagung"],a.exports["#TVC:IN10-symbol-description"]=["Bon Kerajaan India 10 Tahun"],a.exports["#TVC:IT10-symbol-description"]=["Bon Kerajaan Itali 10 Tahun"],a.exports["#TVC:JP10-symbol-description"]=["Bon Kerajaan Jepun 10 Tahun"],a.exports["#TVC:NDX-symbol-description"]=["Indeks US 100"],a.exports["#TVC:NI225-symbol-description"]=["NIKKEI 225"],a.exports["#TVC:SPX-symbol-description"]=["Indeks S&P 500"],a.exports["#TVC:SX5E-symbol-description"]=["Indeks Euro Stoxx 50"],a.exports["#TVC:TR10-symbol-description"]=["Bon Kerajaan Turki 10 Tahun"],a.exports["#TVC:UKOIL-symbol-description"]=["CFDs di Minyak Mentah Brent"],a.exports["#TVC:UKX-symbol-description"]=["Index UK 100"],a.exports["#TVC:US02-symbol-description"]=["Bon Kerajaan AS 2 Tahun"],a.exports["#TVC:US05-symbol-description"]=["Bon Kerajaan AS 5 Tahun"],a.exports["#TVC:US10-symbol-description"]=["Bon Kerajaan AS 10 Tahun"],a.exports["#TVC:USOIL-symbol-description"]=["CFDs di Minyak Mentah WTI"],a.exports["#NYMEX:ITI1!-symbol-description"]=["Bijih Besi Hadapan"],a.exports["#NASDAQ:SHY-symbol-description"]=["Ishares 1-3 Tahun Bon Perbendaharaan ETF"],a.exports["#AMEX:ALD-symbol-description"]=["Hutang Lokal ETF WisdomTree Asia"],a.exports["#NASDAQ:AMD-symbol-description"]="Advanced Micro Devices Inc",a.exports["#NYSE:BABA-symbol-description"]="Alibaba Group Holdings Ltd.",a.exports["#ICEEUR:CB-symbol-description"]=["Minyak Mentah Brent"],a.exports["#ICEEUR:CB1!-symbol-description"]=["Minyak Mentah Brent"],a.exports["#ICEUSA:CC-symbol-description"]=["Koko"],a.exports["#NYMEX:CL-symbol-description"]=["Minyak Mentah WTI"],a.exports["#ICEUSA:CT-symbol-description"]=["Kapas #2"],a.exports["#NASDAQ:CTRV-symbol-description"]="ContraVir Pharmaceuticals Inc",
a.exports["#CME:DL-symbol-description"]=["Susu Kelas III"],a.exports["#NYSE:F-symbol-description"]="FORD MTR CO DEL",a.exports["#MOEX:GAZP-symbol-description"]="GAZPROM",a.exports["#COMEX:GC-symbol-description"]=["Emas"],a.exports["#CME:GF-symbol-description"]=["Lembu Feeder"],a.exports["#CME:HE-symbol-description"]="Lean Hogs",a.exports["#NASDAQ:IEF-symbol-description"]=["Ishares 7-10 Tahun Bon Perbendaharaan ETF"],a.exports["#NASDAQ:IEI-symbol-description"]=["Ishares 3-7 Tahun Bon Perbendaharaan ETF"],a.exports["#NYMEX:KA1-symbol-description"]=["Hadapan Gula #11"],a.exports["#ICEUSA:KC-symbol-description"]=["Kopi"],a.exports["#NYMEX:KG1-symbol-description"]=["Hadapan Kapas"],a.exports["#FWB:KT1-symbol-description"]="Key Tronic Corр.",a.exports["#CME:LE-symbol-description"]=["Lembu Hidup"],a.exports["#ICEEUR:LO-symbol-description"]=["Minyak Pemanas ICE"],a.exports["#CME:LS-symbol-description"]=["Balak"],a.exports["#MOEX:MGNT-symbol-description"]="MAGNIT",a.exports["#LSIN:MNOD-symbol-description"]=["Saham Biasa ADR GMK NORILSKIYNIKEL [REPO]"],a.exports["#NYMEX:NG-symbol-description"]=["Gas Asli"],a.exports["#ICEUSA:OJ-symbol-description"]=["Jus Oren"],a.exports["#NYMEX:PA-symbol-description"]="Palladium",a.exports["#NYSE:PBR-symbol-description"]="PETROLEO BRASILEIRO SA PETROBR",a.exports["#NYMEX:PL-symbol-description"]="Platinum",a.exports["#COMEX_MINI:QC-symbol-description"]=["Tembaga E-Mini"],a.exports["#NYMEX:RB-symbol-description"]=["Petrol RBOB"],a.exports["#NYMEX:RB1-symbol-description"]=["Hadapan RBOB Gasoline"],a.exports["#MOEX:SBER-symbol-description"]="SBERBANK",a.exports["#AMEX:SCHO-symbol-description"]=["Schwab Perbendaharaan U.S. Jangka Pendek ETF"],a.exports["#COMEX:SI-symbol-description"]=["Perak"],a.exports["#NASDAQ:TLT-symbol-description"]=["Ishares 20+ Tahun Bon Perbendaharaan ETF"],a.exports["#TVC:VIX-symbol-description"]=["Indeks Ketidakstabilan S&P 500"],a.exports["#MOEX:VTBR-symbol-description"]="VTB",a.exports["#COMEX:ZA-symbol-description"]=["Zink"],a.exports["#CBOT:ZC-symbol-description"]=["Jagung"],a.exports["#CBOT:ZK-symbol-description"]=["Hadapan Ethanol"],a.exports["#CBOT:ZL-symbol-description"]=["Minyak Kacang Soya"],a.exports["#CBOT:ZO-symbol-description"]="Oats",a.exports["#CBOT:ZR-symbol-description"]=["Beras Kasar"],a.exports["#CBOT:ZS-symbol-description"]=["Kacang soya"],a.exports["#CBOT:ZS1-symbol-description"]=["Kontrak Masa Hadapan Kacang Soya"],a.exports["#CBOT:ZW-symbol-description"]=["Gandum"],a.exports["#CBOT:ZW1-symbol-description"]=["Hadapan Gandum - ECBT"],a.exports["#NASDAQ:ITI-symbol-description"]=["Iteris Inc."],a.exports["#NYMEX:ITI2!-symbol-description"]=["Hadapan Bijih Besi"],a.exports["#CADUSD-symbol-description"]=["Dolar Kanada / Dolar A.S."],a.exports["#CHFUSD-symbol-description"]=["Franc Swiss / Dolar A.S."],a.exports["#GPW:ACG-symbol-description"]="Acautogaz",a.exports["#JPYUSD-symbol-description"]=["Yen Jepun / Dolar A.S."],a.exports["#USDAUD-symbol-description"]=["Dolar A.S. / Dolar Australia"],a.exports["#USDEUR-symbol-description"]=["Dolar A.S. / Euro"],
a.exports["#USDGBP-symbol-description"]=["Dolar A.S. / Paun Sterling"],a.exports["#USDNZD-symbol-description"]=["Dolar A.S. / Dolar New Zealand"],a.exports["#UKOIL-symbol-description"]=["CFDs di Minyak Mentah (Brent)"],a.exports["#USOIL-symbol-description"]=["CFDs di Minyak Mentah (WTI)"],a.exports["#US30-symbol-description"]=["Indeks Dow Jones Industrial Average"],a.exports["#BCHUSD-symbol-description"]=["Bitcoin Cash / Dolar A.S."],a.exports["#ETCUSD-symbol-description"]=["Ethereum Classic / Dolar A.S."],a.exports["#GOOG-symbol-description"]=["Alphabet Inc (Google) Kelas C"],a.exports["#LTCUSD-symbol-description"]=["Litecoin / Dolar A.S."],a.exports["#XRPUSD-symbol-description"]=["XRP / Dolar A.S."],a.exports["#SP:SPX-symbol-description"]=["Indeks S&P 500"],a.exports["#ETCBTC-symbol-description"]="Ethereum Classic / Bitcoin",a.exports["#ETHBTC-symbol-description"]="Ethereum / Bitcoin",a.exports["#XRPBTC-symbol-description"]="XRP / Bitcoin",a.exports["#TVC:US30-symbol-description"]=["Bon Kerajaan AS 30 Tahun"],a.exports["#COMEX:SI1!-symbol-description"]=["Hadapan Perak"],a.exports["#BTGUSD-symbol-description"]=["Bitcoin Emas / Dolar A.S."],a.exports["#IOTUSD-symbol-description"]=["IOTA  / Dolar A.S."],a.exports["#CME:BTC1!-symbol-description"]=["Hadapan Bitcoin CME"],a.exports["#COMEX:GC1!-symbol-description"]=["Hadapan Emas"],a.exports["#CORNUSD-symbol-description"]=["CFD untuk Jagung"],a.exports["#COTUSD-symbol-description"]=["CFD untuk Kapas"],a.exports["#DJ:DJA-symbol-description"]=["Indeks Purata Komposit Dow Jones"],a.exports["#DJ:DJI-symbol-description"]=["Indeks Dow Jones Industrial Average"],a.exports["#ETHEUR-symbol-description"]="Ethereum / Euro",a.exports["#ETHGBP-symbol-description"]=["Ethereum / Paun British"],a.exports["#ETHJPY-symbol-description"]=["Ethereum / Yen Jepun"],a.exports["#EURNOK-symbol-description"]=["Euro / Krone Norway"],a.exports["#GBPPLN-symbol-description"]=["Paun British / Zloty Poland"],a.exports["#MOEX:BR1!-symbol-description"]=["Hadapan Minyak Brent"],a.exports["#NYMEX:KG1!-symbol-description"]=["Hadapan Kapas"],a.exports["#NYMEX:PL1!-symbol-description"]=["Hadapan Platinum"],a.exports["#SOYBNUSD-symbol-description"]=["CFD untuk Kacang soya"],a.exports["#SUGARUSD-symbol-description"]=["CFD untuk Gula"],a.exports["#TVC:IXIC-symbol-description"]=["Indeks Komposit AS"],a.exports["#TVC:RU-symbol-description"]="Russell 1000 Index",a.exports["#USDZAR-symbol-description"]=["Dolar A.S. / Rand Afrika Selatan"],a.exports["#WHEATUSD-symbol-description"]=["CFD untuk Gandum"],a.exports["#XRPEUR-symbol-description"]="XRP / Euro",a.exports["#CBOT:S1!-symbol-description"]=["Hadapan Kacang soya"],a.exports["#SP:MID-symbol-description"]=["Indeks S&P 400"],a.exports["#TSX:XCUUSD-symbol-description"]=["CFD untuk Tembaga"],a.exports["#TVC:NYA-symbol-description"]=["Indeks Komposit NYSE"],a.exports["#TVC:PLATINUM-symbol-description"]=["CFD untuk Platinum (US$ / Auns)"],a.exports["#TVC:SSMI-symbol-description"]=["Indeks Pasaran Switzerland"],
a.exports["#TVC:SXY-symbol-description"]=["Indeks Mata Wang Franc Swiss"],a.exports["#TVC:RUI-symbol-description"]=["Indeks Russell 1000"],a.exports["#MOEX:RI1!-symbol-description"]=["Hadapan Indeks RTS"],a.exports["#MOEX:MX1!-symbol-description"]=["Hadapan Indeks MICEX"],a.exports["#CBOE:BG1!-symbol-description"]=["Hadapan Bitcoin CBOE"],a.exports["#TVC:MY10-symbol-description"]=["Bon Kerajaan Malaysia 10 Tahun"],a.exports["#CME:S61!-symbol-description"]=["Hadapan Franc Swiss"],a.exports["#TVC:DEU30-symbol-description"]=["Indeks DAX"],a.exports["#BCHEUR-symbol-description"]=["Bitcoin Tunai / Euro"],a.exports["#TVC:ZXY-symbol-description"]=["Indek Mata Wang Dolar New Zealand"],a.exports["#MIL:FTSEMIB-symbol-description"]=["Indeks FTSE MIB"],a.exports["#XETR:DAX-symbol-description"]=["Indeks DAX"],a.exports["#MOEX:IMOEX-symbol-description"]=["Indeks Russia MOEX"],a.exports["#FX:US30-symbol-description"]="Dow Jones Industrial Average Index",a.exports["#MOEX:RUAL-symbol-description"]="United Company RUSAL PLC",a.exports["#MOEX:MX2!-symbol-description"]=["Indeks Niaga Hadapan MICEX"],a.exports["#NEOUSD-symbol-description"]=["NEO / Dolar A.S."],a.exports["#XMRUSD-symbol-description"]=["Monero / Dolar A.S."],a.exports["#ZECUSD-symbol-description"]=["Zcash / Dolar A.S."],a.exports["#TVC:CAC-symbol-description"]=["Indeks CAC 40"],a.exports["#NASDAQ:ZS-symbol-description"]="Zscaler Inc",a.exports["#TVC:GB10Y-symbol-description"]=["Hasil Bon Kerajaan UK 10 Tahun"],a.exports["#TVC:AU10Y-symbol-description"]=["Bon Kerajaan Australia 10 Tahun"],a.exports["#TVC:CN10Y-symbol-description"]=["Hasil Bon Kerajaan China 10 Tahun"],a.exports["#TVC:DE10Y-symbol-description"]=["Hasil Bon Kerajaan Jerman 10 Tahun"],a.exports["#TVC:ES10Y-symbol-description"]=["Hasil Bon Kerajaan Sepanyol 10 Tahun"],a.exports["#TVC:FR10Y-symbol-description"]=["Hasil Bon Kerajaan Perancis 10 Tahun"],a.exports["#TVC:IN10Y-symbol-description"]=["Hasil Bon Kerajaan India 10 tahun"],a.exports["#TVC:IT10Y-symbol-description"]=["Hasil Bon Kerajaan Itali 10 tahun"],a.exports["#TVC:JP10Y-symbol-description"]=["Hasil Bon Kerajaan Jepun 10 Tahun"],a.exports["#TVC:KR10Y-symbol-description"]=["Hasil Bon Kerajaan Korea 10 Tahun"],a.exports["#TVC:MY10Y-symbol-description"]=["Hasil Bon Kerajaan Malaysia 10 Tahun"],a.exports["#TVC:PT10Y-symbol-description"]=["Hasil Bon Kerajaan Portugal 10 Tahun"],a.exports["#TVC:TR10Y-symbol-description"]=["Hasil Bon Kerajaan Turki 10 Tahun"],a.exports["#TVC:US02Y-symbol-description"]=["Hasil Bon Kerajaan Amerika Syarikat 2 Tahun"],a.exports["#TVC:US05Y-symbol-description"]=["Hasil Bon Kerajaan Amerika Syarikat 5 tahun"],a.exports["#TVC:US10Y-symbol-description"]=["Hasil Bon Kerajaan Amerika Syarikat 10 Tahun"],a.exports["#INDEX:TWII-symbol-description"]="Taiwan Weighted Index",a.exports["#CME:J61!-symbol-description"]=["Hadapan Yen Jepun"],a.exports["#CME_MINI:J71!-symbol-description"]=["Hadapan E-mini Yen Jepun"],a.exports["#CME_MINI:WM1!-symbol-description"]=["Yen Jepun E-micro / Hadapan Dolar A.S."],
a.exports["#CME:M61!-symbol-description"]=["Hadapan Peso Mexico"],a.exports["#CME:T61!-symbol-description"]=["Hadapan Rand Afrika Selatan"],a.exports["#CME:SK1!-symbol-description"]=["Hadapan Krona Sweden"],a.exports["#CME:QT1!-symbol-description"]=["Hadapan Renminbi China / Dolar A.S."],a.exports["#COMEX:AUP1!-symbol-description"]=["Hadapan Aluminum MW U.S. Transaction Premium Platts (25MT)"],a.exports["#CME:L61!-symbol-description"]=["Hadapan Sebenar Brazil"],a.exports["#CME:WP1!-symbol-description"]=["Hadapan Zloty Poland"],a.exports["#CME:N61!-symbol-description"]=["Hadapan Dolar New Zealand"],a.exports["#CME_MINI:MG1!-symbol-description"]=["Dolar Australia E-micro / Hadapan Dolar A.S."],a.exports["#CME_MINI:WN1!-symbol-description"]=["Franc Swiss E-micro / Hadapan Dolar A.S."],a.exports["#CME_MINI:MF1!-symbol-description"]=["Hadapan Euro E-micro / Dolar A.S."],a.exports["#CME_MINI:E71!-symbol-description"]=["Hadapan E-mini Euro"],a.exports["#CBOT:ZK1!-symbol-description"]=["Hadapan Etanol Bahan Api Ternyahasli"],a.exports["#CME_MINI:MB1!-symbol-description"]=["Paun British E-micro / Hadapan Dolar A.S."],a.exports["#NYMEX_MINI:QU1!-symbol-description"]=["Hadapan Gasolin E-mini"],a.exports["#NYMEX_MINI:QX1!-symbol-description"]=["Hadapan Minyak Pemanasan E-mini"],a.exports["#COMEX_MINI:QC1!-symbol-description"]=["Hadapan Tembaga E-mini"],a.exports["#NYMEX_MINI:QG1!-symbol-description"]=["Hadapan Gas Mentah E-mini"],a.exports["#CME:E41!-symbol-description"]=["Hadapan Dolar A.S. / Lira Turki"],a.exports["#COMEX_MINI:QI1!-symbol-description"]=["Niaga Hadapan (Mini) Perak"],a.exports["#CME:DL1!-symbol-description"]=["Susu, Niaga Hadapan Kelas III"],a.exports["#NYMEX:UX1!-symbol-description"]=["Niaga Hadapan Uranium"],a.exports["#CBOT:BO1!-symbol-description"]=["Hadapan Minyak Kacang Soya"],a.exports["#CME:HE1!-symbol-description"]=["Hadapan Lean Hogs"],a.exports["#NYMEX:IAC1!-symbol-description"]=["Niaga Hadapan Batu Arang Newcastle"],a.exports["#NYMEX_MINI:QM1!-symbol-description"]=["Hadapan Minyak Mentah Ringan E-mini"],a.exports["#NYMEX:JMJ1!-symbol-description"]=["Hadapan Kewangan Brent Mini"],a.exports["#COMEX:AEP1!-symbol-description"]=["Hadapan Premium Aluminium Eropah"],a.exports["#CBOT:ZQ1!-symbol-description"]=["Hadapan Kadar Faedah Dana Persekutuan 30 Hari"],a.exports["#CME:LE1!-symbol-description"]=["Hadapan Lembu Hidup"],a.exports["#CME:UP1!-symbol-description"]=["Hadapan Franc Swiss / Yen Jepun"],a.exports["#CBOT:ZN1!-symbol-description"]=["Hadapan T-Notes 10 Tahun"],a.exports["#CBOT:ZB1!-symbol-description"]=["Hadapan T-Bond"],a.exports["#CME:GF1!-symbol-description"]=["Hadapan Penyuap Lembu"],a.exports["#CBOT:UD1!-symbol-description"]=["Hadapan Ultra T-Bon"],a.exports["#CME:I91!-symbol-description"]=["Hadapan Perumahan CME - Washington DC"],a.exports["#CBOT:ZO1!-symbol-description"]=["Hadapan Oat"],a.exports["#CBOT:ZM1!-symbol-description"]=["Hadapan Makanan Kacang Soya"],a.exports["#CBOT_MINI:XN1!-symbol-description"]=["Hadapan Mini Jagung"],
a.exports["#CBOT:ZC1!-symbol-description"]=["Niaga Hadapan Jagung"],a.exports["#CME:LS1!-symbol-description"]=["Niaga Hadapan Kayu"],a.exports["#CBOT_MINI:XW1!-symbol-description"]=["Hadapan Mini Gandum"],a.exports["#CBOT_MINI:XK1!-symbol-description"]=["Hadapan Mini Kacang Soy"],a.exports["#CBOT:ZS1!-symbol-description"]=["Niaga Hadapan Kacang Soya"],a.exports["#NYMEX:PA1!-symbol-description"]=["Niaga Hadapan Paladium"],a.exports["#CME:FTU1!-symbol-description"]=["Hadapan E-mini FTSE 100 Index USD"],a.exports["#CBOT:ZR1!-symbol-description"]=["Hadapan Beras"],a.exports["#COMEX_MINI:GR1!-symbol-description"]=["Niaga Hadapan (E-micro) Emas"],a.exports["#COMEX_MINI:QO1!-symbol-description"]=["Niaga Hadapan (Mini) Emas"],a.exports["#CME_MINI:RL1!-symbol-description"]=["Hadapan E-mini Russell"],a.exports["#CME_MINI:EW1!-symbol-description"]=["Hadapan S&P 400 Midcap E-mini"],a.exports["#COMEX:LD1!-symbol-description"]=["Hadapan Plumbum"],a.exports["#CME_MINI:ES1!-symbol-description"]=["Hadapan S&P 500 E-mini"],a.exports["#TVC:SA40-symbol-description"]=["Indeks 40 Teratas Afrika Selatan"],a.exports["#BMV:ME-symbol-description"]=["Indeks IPC Mexico"],a.exports["#BCBA:IMV-symbol-description"]=["Indeks MERVAL"],a.exports["#HSI:HSI-symbol-description"]=["Indeks Hang Seng"],a.exports["#BVL:SPBLPGPT-symbol-description"]=["Indeks Umum S&P / BVL Peru (PEN)"],a.exports["#EGX:EGX30-symbol-description"]=["Indeks EGX 30 Price Return"],a.exports["#BVC:IGBC-symbol-description"]=["Indeks General de la Bolsa de Valores de Colombia"],a.exports["#TWSE:TAIEX-symbol-description"]=["Indeks Saham Terwajar Permodalan Taiwan"],a.exports["#QSE:GNRI-symbol-description"]=["Indeks QE"],a.exports["#BME:IBC-symbol-description"]=["Indeks IBEX  35"],a.exports["#NZX:NZ50G-symbol-description"]=["Indeks Kasar S&P / NZX 50"],a.exports["#SIX:SMI-symbol-description"]=["Indeks Pasaran Switzerland"],a.exports["#SZSE:399001-symbol-description"]=["Indeks Komponen SZSE"],a.exports["#TADAWUL:TASI-symbol-description"]=["Indeks Tadawul All Shares"],a.exports["#IDX:COMPOSITE-symbol-description"]=["Indeks Komposit IDX"],a.exports["#EURONEXT:PX1-symbol-description"]=["Indeks CAC 40"],a.exports["#OMXHEX:OMXH25-symbol-description"]=["Indeks OMX Helsinki 25"],a.exports["#EURONEXT:BEL20-symbol-description"]=["Indeks BEL 20"],a.exports["#TVC:STI-symbol-description"]=["Indeks Straits Times"],a.exports["#DFM:DFMGI-symbol-description"]=["Indeks DFM"],a.exports["#TVC:KOSPI-symbol-description"]=["Indeks Harga Saham Komposit Korea"],a.exports["#FTSEMYX:FBMKLCI-symbol-description"]=["Indeks FTSE Bursa Malaysia KLCI"],a.exports["#TASE:TA35-symbol-description"]=["Indeks TA-35"],a.exports["#OMXSTO:OMXS30-symbol-description"]=["Indeks OMX Stockholm 30"],a.exports["#OMXICE:OMXI8-symbol-description"]=["Indeks OMX Iceland 8"],a.exports["#NSENG:NSE30-symbol-description"]=["Indeks NSE 30"],a.exports["#BAHRAIN:BSEX-symbol-description"]=["Indeks Bahrain All Share"],a.exports["#OMXTSE:OMXTGI-symbol-description"]="OMX Tallinn GI",
a.exports["#OMXCOP:OMXC25-symbol-description"]=["Indeks OMX Copenhagen 25"],a.exports["#OMXRSE:OMXRGI-symbol-description"]="OMX Riga GI",a.exports["#BELEX:BELEX15-symbol-description"]=["Indeks BELEX 15"],a.exports["#OMXVSE:OMXVGI-symbol-description"]="OMX Vilnius GI",a.exports["#EURONEXT:AEX-symbol-description"]=["Indeks AEX"],a.exports["#CBOE:VIX-symbol-description"]=["Indeks Ketidakstabilan S&P 500"],a.exports["#NASDAQ:XAU-symbol-description"]=["Indeks Sektor PHLX Emas dan Perak"],a.exports["#DJ:DJUSCL-symbol-description"]=["Indeks Batu Arang Dow Jones A.S"],a.exports["#DJ:DJCIKC-symbol-description"]=["Indeks Komoditi Dow Jones Kopi"],a.exports["#DJ:DJCIEN-symbol-description"]=["Indeks Komoditi Dow Jones Tenaga"],a.exports["#NASDAQ:OSX-symbol-description"]=["Indeks Sektor Perkhidmatan Minyak PHLX"],a.exports["#DJ:DJCISB-symbol-description"]=["Indeks Komoditi Dow Jones Gula"],a.exports["#DJ:DJCICC-symbol-description"]=["Indeks Komoditi Dow Jones Koko"],a.exports["#DJ:DJCIGR-symbol-description"]=["Indeks Komoditi Dow Jones Bijian"],a.exports["#DJ:DJCIAGC-symbol-description"]=["Indeks Komoditi Dow Jones Komponen Bermodal Pertanian"],a.exports["#DJ:DJCISI-symbol-description"]=["Indeks Komoditi Dow Jones Perak"],a.exports["#DJ:DJCIIK-symbol-description"]=["Indeks Komoditi Dow Jones Nikel"],a.exports["#NASDAQ:HGX-symbol-description"]=["Indeks Sektor Perumahan PHLX"],a.exports["#DJ:DJCIGC-symbol-description"]=["Indeks Komoditi Dow Jones Emas"],a.exports["#SP:SPGSCI-symbol-description"]=["Indeks Komoditi Goldman Sachs S&P"],a.exports["#NASDAQ:UTY-symbol-description"]=["Indeks Sektor Perkhidmatan Awam PHLX"],a.exports["#DJ:DJU-symbol-description"]=["Indeks Purata Perkhidmatan Awam Dow Jones"],a.exports["#SP:SVX-symbol-description"]=["Indeks Nilai S&P 500"],a.exports["#SP:OEX-symbol-description"]=["Indeks S&P 100"],a.exports["#CBOE:OEX-symbol-description"]=["Indeks S&P 100"],a.exports["#NASDAQ:SOX-symbol-description"]=["Indeks Semicondutor Philadelphia"],a.exports["#RUSSELL:RUI-symbol-description"]=["Indeks Russell  1000"],a.exports["#RUSSELL:RUA-symbol-description"]=["Indeks Russell 3000"],a.exports["#RUSSELL:RUT-symbol-description"]=["Indeks Russell 2000"],a.exports["#NYSE:XMI-symbol-description"]=["Indeks Pasaran Major NYSE ARCA"],a.exports["#NYSE:XAX-symbol-description"]=["Indeks Komposit AMEX"],a.exports["#NASDAQ:NDX-symbol-description"]=["Indeks 100 Nasdaq"],a.exports["#NASDAQ:IXIC-symbol-description"]=["Indeks Komposit Nasdaq"],a.exports["#DJ:DJT-symbol-description"]=["Indeks Purata Pengangkutan Dow Jones"],a.exports["#NYSE:NYA-symbol-description"]=["Indeks Komposit NYSE"],a.exports["#NYMEX:CJ1!-symbol-description"]=["Niaga Hadapan Koko"],a.exports["#USDILS-symbol-description"]=["Dolar A.S. / Shekel Israel"],a.exports["#TSXV:F-symbol-description"]="Fiore Gold Inc",a.exports["#SIX:F-symbol-description"]=["Syarikat Ford Motor"],a.exports["#BMV:F-symbol-description"]=["Syarikat Ford Motor"],a.exports["#TWII-symbol-description"]=["Indeks Taiwan Berwajaran"],
a.exports["#TVC:PL10Y-symbol-description"]=["Hasil Bon Kerajaan Poland 10 Tahun"],a.exports["#TVC:PL05Y-symbol-description"]=["Hasil Bon Kerajaan Poland 5 Tahun"],a.exports["#SET:GC-symbol-description"]=["Syarikat Awam Sambungan Global"],a.exports["#TSX:GC-symbol-description"]=["Syarikat Pertaruhan Kanada"],a.exports["#TVC:FTMIB-symbol-description"]=["Indeks Milano Italia Borsa"],a.exports["#OANDA:SPX500USD-symbol-description"]=["Indeks S&P 500"],a.exports["#BMV:CT-symbol-description"]="China SX20 RT",a.exports["#TSXV:CT-symbol-description"]="Centenera Mining Corporation",a.exports["#BYBIT:ETHUSD-symbol-description"]=["Kontrak Niaga Hadapan Kekal ETHUSD"],a.exports["#BYBIT:XRPUSD-symbol-description"]=["Kontrak Kekal XRPUSD"],a.exports["#BYBIT:BTCUSD-symbol-description"]=["Kontrak Kekal BTCUSD"],a.exports["#BITMEX:ETHUSD-symbol-description"]=["Ethereum / Dolar A.S."],a.exports["#DERIBIT:BTCUSD-symbol-description"]=["Kontrak Hadapan Kekal BTCUSD"],a.exports["#DERIBIT:ETHUSD-symbol-description"]=["Kontrak Niaga Hadapan Kekal ETHUSD"],a.exports["#USDHUF-symbol-description"]=["Dolar A.S. / Forint Hungary"],a.exports["#USDTHB-symbol-description"]=["Dolar A.S. / Baht Thailand"],a.exports["#FOREXCOM:US2000-symbol-description"]="US Small Cap 2000",a.exports["#TSXV:PBR-symbol-description"]="Para Resources Inc",a.exports["#NYSE:SI-symbol-description"]="Silvergate Capital Corporation",a.exports["#NASDAQ:LE-symbol-description"]="Lands' End Inc",a.exports["#CME:CB1!-symbol-description"]="Butter Futures-Cash (Continuous: Current contract in front)",a.exports["#LSE:SCHO-symbol-description"]=["Scholium Group Ord 1P"],a.exports["#NEO:HE-symbol-description"]="Hanwei Energy Services Corp.",a.exports["#NYSE:HE-symbol-description"]="Hawaiian Electric Industries",a.exports["#OMXCOP:SCHO-symbol-description"]="Schouw & Co A/S",a.exports["#TSX:HE-symbol-description"]="Hanwei Energy Services Corp.",a.exports["#BSE:ITI-symbol-description"]="ITI Ltd",a.exports["#NSE:ITI-symbol-description"]="Indian Telephone Industries Limited",a.exports["#TSX:LS-symbol-description"]=["Dana Dividend Middlefield Healthcare & Life Sciences"],a.exports["#BITMEX:XBT-symbol-description"]=["Indeks Bitcoin / Dolar A.S."],a.exports["#CME_MINI:RTY1!-symbol-description"]=["Indeks Pasaran Hadapan E-Mini Russell 2000"],a.exports["#CRYPTOCAP:TOTAL-symbol-description"]=["Nilai Keseluruhan Modal Pasaran Krypto, $"],a.exports["#ICEUS:DX1!-symbol-description"]=["Hadapan Indeks Dolar A.S."],a.exports["#NYMEX:TT1!-symbol-description"]=["Pasaran Hadapan Kapas"],a.exports["#PHEMEX:BTCUSD-symbol-description"]=["Kontrak Pasaran Hadapan BTC Perpetual"],a.exports["#PHEMEX:ETHUSD-symbol-description"]=["Kontrak Pasaran Hadapan ETH Perpetual"],a.exports["#PHEMEX:XRPUSD-symbol-description"]=["Kontrak Pasaran Hadapan XRP Perpetual"],a.exports["#PHEMEX:LTCUSD-symbol-description"]=["Kontrak Pasaran Hadapan LTC Perpetual"],a.exports["#BITCOKE:BCHUSD-symbol-description"]=["Swap BCH Quanto"],a.exports["#BITCOKE:BTCUSD-symbol-description"]=["Swap BTC Quanto"],
a.exports["#BITCOKE:ETHUSD-symbol-description"]=["Swap ETH Quanto"],a.exports["#BITCOKE:LTCUSD-symbol-description"]=["Swap LTC Quanto"],a.exports["#TVC:CA10-symbol-description"]=["Bon Kerajaan Kanada, 10 YR"],a.exports["#TVC:CA10Y-symbol-description"]=["Hasil Bon Kerajaan Kanada 10 Tahun"],a.exports["#TVC:ID10Y-symbol-description"]=["Hasil Bon Kerajaan Indonesia 10 Tahun"],a.exports["#TVC:NL10-symbol-description"]=["Bon Kerajaan Belanda, 10 YR"],a.exports["#TVC:NL10Y-symbol-description"]=["Hasil Bon Kerajaan Belanda 10 Tahun"],a.exports["#TVC:NZ10-symbol-description"]=["Bon Kerajaan New Zealand, 10 YR"],a.exports["#TVC:NZ10Y-symbol-description"]=["Hasil Bon Kerajaan New Zealand 10 Tahun"],a.exports["#SOLUSD-symbol-description"]=["Solana / Dolar A.S."],a.exports["#LUNAUSD-symbol-description"]=["Luna / Dolar A.S."],a.exports["#UNIUSD-symbol-description"]=["Uniswap / Dolar A.S."],a.exports["#LTCBRL-symbol-description"]="Litecoin / Brazilian Real",a.exports["#ETCEUR-symbol-description"]="Ethereum Classic / Euro",a.exports["#ETHKRW-symbol-description"]="Ethereum / South Korean Won",a.exports["#BTCRUB-symbol-description"]="Bitcoin / Russian Ruble",a.exports["#BTCTHB-symbol-description"]="Bitcoin / Thai Baht",a.exports["#ETHTHB-symbol-description"]="Ethereum / Thai Baht",a.exports["#TVC:EU10YY-symbol-description"]=["Hasil Bon Kerajaan Eropah 10 Tahun"]}}]);