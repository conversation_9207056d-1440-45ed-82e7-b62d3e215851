(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3732],{20747:e=>{e.exports="Re"},9846:e=>{e.exports="A"},55765:e=>{e.exports="L"},14642:e=>{e.exports=["Dunkel"]},69841:e=>{e.exports=["Hell"]},673:e=>{e.exports=Object.create(null),e.exports.d_dates=["t"],e.exports.h_dates="h",e.exports.m_dates="m",e.exports.s_dates="s",e.exports.in_dates="in"},97840:e=>{e.exports=["t"]},64302:e=>{e.exports="h"},79442:e=>{e.exports="m"},22448:e=>{e.exports="s"},16493:e=>{e.exports=["{title} Kopieren"]},13395:e=>{e.exports=["T"]},37720:e=>{e.exports="M"},69838:e=>{e.exports="R"},59231:e=>{e.exports="T"},85521:e=>{e.exports="W"},13994:e=>{e.exports=["Std."]},6791:e=>{e.exports="m"},2949:e=>{e.exports="s"},77297:e=>{e.exports="C"},56723:e=>{e.exports="H"},5801:e=>{e.exports="HL2"},98865:e=>{e.exports="HLC3"},42659:e=>{e.exports="OHLC4"},4292:e=>{e.exports="L"},78155:e=>{e.exports="O"},88601:e=>{e.exports=Object.create(null),e.exports.Close_input="Close",e.exports.Back_input=["Zurück"],e.exports.Minimize_input=["Minimieren"],e.exports["Hull MA_input"]="Hull MA",e.exports["{number} item_combobox_input"]=["{number} Objekt","{number} Objekte"],e.exports.Length_input=["Länge"],e.exports.Plot_input="Plot",e.exports.Zero_input=["Null"],e.exports.Signal_input="Signal",e.exports.Long_input="Long",e.exports.Short_input="Short",e.exports.UpperLimit_input=["OberesLimit"],e.exports.LowerLimit_input=["UntereBegrenzung"],e.exports.Offset_input="Offset",e.exports.length_input=["Länge"],e.exports.mult_input="mult",e.exports.short_input="short",e.exports.long_input="long",e.exports.Limit_input="Limit",e.exports.Move_input=["Bewegung"],e.exports.Value_input=["Wert"],e.exports.Method_input=["Methode"],e.exports["Values in status line_input"]=["Werte in der Statuszeile"],e.exports["Labels on price scale_input"]=["Labels auf der Preisskala"],e.exports["Accumulation/Distribution_input"]=["Akkumulation/Distribution"],e.exports.ADR_B_input="ADR_B",e.exports["Equality Line_input"]=["Gleichheitslinie"],e.exports["Window Size_input"]=["Fenstergröße"],e.exports.Sigma_input="Sigma",e.exports["Aroon Up_input"]="Aroon Up",e.exports["Aroon Down_input"]="Aroon Down",e.exports.Upper_input=["Oberes Band"],e.exports.Lower_input=["Unteres Band"],e.exports.Deviation_input=["Abweichung"],e.exports["Levels Format_input"]=["Level Format"],e.exports["Labels Position_input"]=["Label Position"],e.exports["0 Level Color_input"]=["0 Level Farbe"],e.exports["0.236 Level Color_input"]=["0.236 Level Farbe"],e.exports["0.382 Level Color_input"]=["0.382 Level Farbe"],e.exports["0.5 Level Color_input"]=["0.5 Level Farbe"],e.exports["0.618 Level Color_input"]=["0.618 Level Farbe"],e.exports["0.65 Level Color_input"]=["0.65 Level Farbe"],e.exports["0.786 Level Color_input"]=["0.786 Level Farbe"],e.exports["1 Level Color_input"]=["1 Level Farbe"],e.exports["1.272 Level Color_input"]=["1.272 Level Farbe"],e.exports["1.414 Level Color_input"]=["1.414 Level Farbe"],e.exports["1.618 Level Color_input"]=["1.618 Level Farbe"],
e.exports["1.65 Level Color_input"]=["1.65 Level Farbe"],e.exports["2.618 Level Color_input"]=["2.618 Level Farbe"],e.exports["2.65 Level Color_input"]=["2.65 Level Farbe"],e.exports["3.618 Level Color_input"]=["3.618 Level Farbe"],e.exports["3.65 Level Color_input"]=["3.65 Level Farbe"],e.exports["4.236 Level Color_input"]=["4.236 Level Farbe"],e.exports["-0.236 Level Color_input"]=["-0.236 Level Farbe"],e.exports["-0.382 Level Color_input"]=["-0.382 Level Farbe"],e.exports["-0.618 Level Color_input"]=["-0.618 Level Farbe"],e.exports["-0.65 Level Color_input"]=["-0.65 Level Farbe"],e.exports.ADX_input="ADX",e.exports["ADX Smoothing_input"]=["ADX Glättung"],e.exports["DI Length_input"]=["DI-Länge"],e.exports.Smoothing_input=["Glättung"],e.exports.ATR_input="ATR",e.exports.Growing_input=["Steigend"],e.exports.Falling_input=["Fallend"],e.exports["Color 0_input"]=["Farbe 0"],e.exports["Color 1_input"]=["Farbe 1"],e.exports.Source_input=["Quelle"],e.exports.StdDev_input=["Abweichungen"],e.exports.Basis_input="Basis",e.exports.Median_input="Median",e.exports["Bollinger Bands %B_input"]="Bollinger Bands %B",e.exports.Overbought_input=["Überkauft"],e.exports.Oversold_input=["Überverkauft"],e.exports["Bollinger Bands Width_input"]=["Bollinger Bands-Breite"],e.exports["RSI Length_input"]=["RSI Länge"],e.exports["UpDown Length_input"]="UpDown Length",e.exports["ROC Length_input"]="ROC Length",e.exports.MF_input="MF",e.exports.resolution_input=["Auflösung"],e.exports["Fast Length_input"]=["Schnelle Periode"],e.exports["Slow Length_input"]=["Langsame Periode"],e.exports["Chaikin Oscillator_input"]=["Chaikin-Oszillator"],e.exports.P_input="P",e.exports.X_input="X",e.exports.Q_input="Q",e.exports.p_input="p",e.exports.x_input="x",e.exports.q_input="q",e.exports.Price_input=["Kurs"],e.exports["Chande MO_input"]="Chande MO",e.exports["Zero Line_input"]=["Nulllinie"],e.exports["Color 2_input"]=["Farbe 2"],e.exports["Color 3_input"]=["Farbe 3"],e.exports["Color 4_input"]=["Farbe 4"],e.exports["Color 5_input"]=["Farbe 5"],e.exports["Color 6_input"]=["Farbe 6"],e.exports["Color 7_input"]=["Farbe 7"],e.exports["Color 8_input"]=["Farbe 8"],e.exports.CHOP_input="CHOP",e.exports["Upper Band_input"]=["Oberes Band"],e.exports["Lower Band_input"]=["Unteres Band"],e.exports.CCI_input="CCI",e.exports["Smoothing Line_input"]=["Glättung der Linie"],e.exports["Smoothing Length_input"]=["Glättung der Länge"],e.exports["WMA Length_input"]=["WMA Länge"],e.exports["Long RoC Length_input"]="Long RoC Length",e.exports["Short RoC Length_input"]="Short RoC Length",e.exports.sym_input="sym",e.exports.Symbol_input="Symbol",e.exports.Correlation_input=["Korrelation"],e.exports.Period_input=["Zeitraum"],e.exports.Centered_input=["Zentriert"],e.exports["Detrended Price Oscillator_input"]=["Detrended Price-Oszillator"],e.exports.isCentered_input="isCentered",e.exports.DPO_input="DPO",e.exports["ADX smoothing_input"]=["ADX Glättung"],e.exports["+DI_input"]="+DI",e.exports["-DI_input"]="-DI",e.exports.DEMA_input="DEMA",
e.exports["Multi timeframe_input"]=["Multi-Zeitrahmen"],e.exports.Timeframe_input=["Zeitrahmen"],e.exports["Wait for timeframe closes_input"]=["Warten Sie auf die Zeitrahmenschließung"],e.exports.Divisor_input="Divisor",e.exports.EOM_input="EOM",e.exports["Elder's Force Index_input"]=["Elder's Force-Index"],e.exports.Percent_input=["Prozent"],e.exports.Exponential_input=["Exponentiell"],e.exports.Average_input=["Durchschnitt"],e.exports["Upper Percentage_input"]=["Höhere Prozentzahl"],e.exports["Lower Percentage_input"]=["Niedrigere Prozentzahl"],e.exports.Fisher_input="Fisher",e.exports.Trigger_input=["Auslöser"],e.exports.Level_input="Level",e.exports["Trader EMA 1 length_input"]=["Trader EMA 1-Länge"],e.exports["Trader EMA 2 length_input"]=["Trader EMA 2-Länge"],e.exports["Trader EMA 3 length_input"]=["Trader EMA 3-Länge"],e.exports["Trader EMA 4 length_input"]=["Trader EMA 4-Länge"],e.exports["Trader EMA 5 length_input"]=["Trader EMA 5-Länge"],e.exports["Trader EMA 6 length_input"]=["Trader EMA 6-Länge"],e.exports["Investor EMA 1 length_input"]=["Investor EMA 1 Länge"],e.exports["Investor EMA 2 length_input"]=["Investor EMA 2 Länge"],e.exports["Investor EMA 3 length_input"]=["Investor EMA 3 Länge"],e.exports["Investor EMA 4 length_input"]=["Investor EMA 4 Länge"],e.exports["Investor EMA 5 length_input"]=["Investor EMA 5 Länge"],e.exports["Investor EMA 6 length_input"]=["Investor EMA 6 Länge"],e.exports.HV_input="HV",e.exports["Conversion Line Periods_input"]=["Conversion Line Perioden"],e.exports["Base Line Periods_input"]="Base Line Periods",e.exports["Lagging Span_input"]="Lagging Span",e.exports["Conversion Line_input"]="Conversion Line",e.exports["Base Line_input"]=["Grundlinie"],e.exports["Leading Span A_input"]="Leading Span A",e.exports["Leading Span B_input"]="Leading Span B",e.exports["Plots Background_input"]="Plots Background",e.exports["yay Color 0_input"]=["yay Farbe 0"],e.exports["yay Color 1_input"]=["yay Farbe 1"],e.exports.Multiplier_input=["Multiplikator"],e.exports["Bands style_input"]=["Bänder-Stil"],e.exports.Middle_input=["Mitte"],e.exports.useTrueRange_input="useTrueRange",e.exports.ROCLen1_input="ROCLen1",e.exports.ROCLen2_input="ROCLen2",e.exports.ROCLen3_input="ROCLen3",e.exports.ROCLen4_input="ROCLen4",e.exports.SMALen1_input="SMALen1",e.exports.SMALen2_input="SMALen2",e.exports.SMALen3_input="SMALen3",e.exports.SMALen4_input="SMALen4",e.exports.SigLen_input="SigLen",e.exports.KST_input="KST",e.exports.Sig_input="Sig",e.exports.roclen1_input="roclen1",e.exports.roclen2_input="roclen2",e.exports.roclen3_input="roclen3",e.exports.roclen4_input="roclen4",e.exports.smalen1_input="smalen1",e.exports.smalen2_input="smalen2",e.exports.smalen3_input="smalen3",e.exports.smalen4_input="smalen4",e.exports.siglen_input="siglen",e.exports["Upper Deviation_input"]=["Obere Abweichung"],e.exports["Lower Deviation_input"]=["Untere Abweichung"],e.exports["Use Upper Deviation_input"]=["Benutze obere Abweichung"],e.exports["Use Lower Deviation_input"]=["Benutze untere Abweichung"],
e.exports.Count_input=["Anzahl"],e.exports.Crosses_input=["Kreuzt"],e.exports.MOM_input="MOM",e.exports.MA_input="MA",e.exports["Length EMA_input"]=["Länge EMA"],e.exports["Length MA_input"]=["Länge MA"],e.exports["Fast length_input"]="Fast length",e.exports["Slow length_input"]="Slow length",e.exports["Signal smoothing_input"]=["Signalglättung"],e.exports["Simple ma(oscillator)_input"]=["Simple ma(Oszillator)"],e.exports["Simple ma(signal line)_input"]=["Simple ma(Signallinie)"],e.exports.Histogram_input=["Histogramm"],e.exports.MACD_input="MACD",e.exports.fastLength_input="fastLength",e.exports.slowLength_input="slowLength",e.exports.signalLength_input=["signalLänge"],e.exports.NV_input="NV",e.exports.OnBalanceVolume_input=["OnBalanceVolumen"],e.exports.Start_input="Start",e.exports.Increment_input=["Schrittweite"],e.exports["Max value_input"]=["Maximalwert"],e.exports.ParabolicSAR_input="ParabolicSAR",e.exports.start_input=["Start"],e.exports.increment_input=["Zuwachs"],e.exports.maximum_input=["Maximum"],e.exports["Short length_input"]="Short length",e.exports["Long length_input"]="Long length",e.exports.OSC_input="OSC",e.exports.shortlen_input="shortlen",e.exports.longlen_input="longlen",e.exports.PVT_input="PVT",e.exports.ROC_input="ROC",e.exports.RSI_input="RSI",e.exports.RVGI_input="RVGI",e.exports.RVI_input="RVI",e.exports["Long period_input"]=["Long Periode"],e.exports["Short period_input"]=["Kurzer Zeitraum"],e.exports["Signal line period_input"]=["Singnallinienperiode"],e.exports.SMI_input="SMI",e.exports["SMI Ergodic Oscillator_input"]=["SMI Ergodic-Oszillator"],e.exports.Indicator_input=["Indikator"],e.exports.Oscillator_input=["Oszillator"],e.exports.K_input="K",e.exports.D_input="D",e.exports.smoothK_input="smoothK",e.exports.smoothD_input="smoothD",e.exports["%K_input"]="%K",e.exports["%D_input"]="%D",e.exports["Stochastic Length_input"]=["Stochastische Länge"],e.exports["RSI Source_input"]=["RSI Quelle"],e.exports.lengthRSI_input=["LängeRSI"],e.exports.lengthStoch_input=["LängeStoch"],e.exports.TRIX_input="TRIX",e.exports.TEMA_input="TEMA",e.exports["Long Length_input"]="Long Length",e.exports["Short Length_input"]="Short Length",e.exports["Signal Length_input"]=["Signallänge"],e.exports.Length1_input="Length1",e.exports.Length2_input="Length2",e.exports.Length3_input="Length3",e.exports.length7_input=["Länge7"],e.exports.length14_input=["Länge14"],e.exports.length28_input=["Länge28"],e.exports.UO_input="UO",e.exports.VWMA_input="VWMA",e.exports.len_input="len",e.exports["VI +_input"]="VI +",e.exports["VI -_input"]="VI -",e.exports["%R_input"]="%R",e.exports["Jaw Length_input"]="Jaw Length",e.exports["Teeth Length_input"]="Teeth Length",e.exports["Lips Length_input"]="Lips Length",e.exports.Jaw_input="Jaw",e.exports.Teeth_input="Teeth",e.exports.Lips_input="Lips",e.exports["Jaw Offset_input"]="Jaw Offset",e.exports["Teeth Offset_input"]="Teeth Offset",e.exports["Lips Offset_input"]="Lips Offset",e.exports["Down fractals_input"]="Down fractals",e.exports["Up fractals_input"]="Up fractals",
e.exports.Periods_input=["Zeiträume"],e.exports.Shapes_input=["Formen"],e.exports["show MA_input"]=["MA anzeigen"],e.exports["MA Length_input"]=["MA Länge"],e.exports["Color based on previous close_input"]=["Farben basierend auf vorherigem Schlusskurs"],e.exports["Rows Layout_input"]=["Zeilenlayout"],e.exports["Row Size_input"]=["Zeilengröße"],e.exports.Volume_input=["Volumen"],e.exports["Value Area volume_input"]=["Volumen der Value-Area"],e.exports["Extend Right_input"]=["Nach rechts erweitern"],e.exports["Extend POC Right_input"]="Extend POC Right",e.exports["Extend VAH Right_input"]=["VAH nach rechts erweitern"],e.exports["Extend VAL Right_input"]=["VAL nach rechts erweitern"],e.exports["Value Area Volume_input"]=["Value Area Volumen"],e.exports.Placement_input=["Platzierung"],e.exports.POC_input="POC",e.exports["Developing Poc_input"]=["Entwicklung des Poc"],e.exports["Up Volume_input"]=["Aufwärts-Volumen"],e.exports["Down Volume_input"]=["Abwärts-Volumen"],e.exports["Value Area_input"]="Value Area",e.exports["Histogram Box_input"]=["Histogram-Box"],e.exports["Value Area Up_input"]=["Value-Area aufwärts"],e.exports["Value Area Down_input"]=["Value-Area abwärts"],e.exports["Number Of Rows_input"]=["Zeilenanzahl"],e.exports["Ticks Per Row_input"]=["Ticks pro Zeile"],e.exports["Up/Down_input"]=["Auf/Ab"],e.exports.Total_input=["Gesamt"],e.exports.Delta_input="Delta",e.exports.Bar_input=["Balken"],e.exports.Day_input=["Tage"],e.exports["Deviation (%)_input"]=["Abweichung (%)"],e.exports.Depth_input=["Tiefe"],e.exports["Extend to last bar_input"]=["Erweitern bis zur letzten Bar"],e.exports.Simple_input=["Einfach"],e.exports.Weighted_input=["Gewichtet"],e.exports["Wilder's Smoothing_input"]=["Wilder'sche Glättung"],e.exports["1st Period_input"]=["Erste Periode"],e.exports["2nd Period_input"]=["2te Periode"],e.exports["3rd Period_input"]=["3te Periode"],e.exports["4th Period_input"]=["4te Periode"],e.exports["5th Period_input"]=["5te Periode"],e.exports["6th Period_input"]=["6te Periode"],e.exports["Rate of Change Lookback_input"]=["Rate of Change zurückliegende Perioden"],e.exports["Instrument 1_input"]="Instrument 1",e.exports["Instrument 2_input"]="Instrument 2",e.exports["Rolling Period_input"]=["Rollende Periode"],e.exports["Standard Errors_input"]=["Standart-Abweichung"],e.exports["Averaging Periods_input"]=["Durchschnittsperioden"],e.exports["Days Per Year_input"]=["Tage pro Jahr"],e.exports["Market Closed Percentage_input"]=["Markt-Close prozentual"],e.exports["ATR Mult_input"]="ATR Mult",e.exports.VWAP_input="VWAP",e.exports["Anchor Period_input"]=["Verankerungszeitraum"],e.exports.Session_input="Session",e.exports.Week_input=["Woche"],e.exports.Month_input=["Monat"],e.exports.Year_input=["Jahr"],e.exports.Decade_input=["Jahrzehnt"],e.exports.Century_input=["Jahrhundert"],e.exports.Sessions_input="Sessions",e.exports["Each (pre-market, market, post-market)_input"]=["Jede (Vorbörslich, Hauptsitzung, Nachbörslich)"],e.exports["Pre-market only_input"]=["Nur vorbörslicher Markt"],
e.exports["Market only_input"]=["Nur die Hauptsitzung"],e.exports["Post-market only_input"]=["Nur nachbörsliche Sitzung"],e.exports["Main chart symbol_input"]=["Symbol im Hauptchart"],e.exports["Another symbol_input"]=["Weiteres Symbol"],e.exports.Line_input=["Linie"],e.exports["Nothing selected_combobox_input"]=["Keine Auswahl getroffen"],e.exports["All items_combobox_input"]=["Alle Objekte"],e.exports.Cancel_input="Cancel",e.exports.Open_input=["Öffnen"]},54138:e=>{e.exports=["Skala invertieren"]},47807:e=>{e.exports=["Auf 100 indexiert"]},34727:e=>{e.exports=["Logarithmisch"]},19238:e=>{e.exports=["Keine überlappenden Labels"]},70361:e=>{e.exports=["Prozent"]},72116:e=>{e.exports=["Regulär"]},33021:e=>{e.exports="ETH"},75610:e=>{e.exports=["Elektronische Handelszeiten"]},97442:e=>{e.exports=["Verlängerte Handelszeit"]},32929:e=>{e.exports=["post"]},56137:e=>{e.exports=["pre"]},98801:e=>{e.exports=["Nach der Markteröffnung"]},56935:e=>{e.exports=["Vor der Markteröffnung"]},63798:e=>{e.exports="RTH"},24380:e=>{e.exports=["Reguläre Handelszeiten"]},27991:e=>{e.exports=["Mai"]},68716:e=>{e.exports=Object.create(null),e.exports.Technicals_study=["Technische Analyse"],e.exports["Average Day Range_study"]=["Durchschnittliche Tagesspanne"],e.exports["Bull Bear Power_study"]="Bull Bear Power",e.exports["Capital expenditures_study"]=["Investitionen in Sachanlagen"],e.exports["Cash to debt ratio_study"]=["Verhältnis von Barmitteln zu Schulden"],e.exports["Debt to EBITDA ratio_study"]=["Verhältnis Schulden zu EBITDA"],e.exports["Directional Movement Index_study"]="Directional Movement Index",e.exports.DMI_study="DMI",e.exports["Dividend payout ratio %_study"]=["Ausschüttungsquote in %"],e.exports["Equity to assets ratio_study"]=["Eigenkapital zu Aktiva Verhältnis"],e.exports["Enterprise value to EBIT ratio_study"]=["Unternehmenswert zu EBIT Verhältnis"],e.exports["Enterprise value to EBITDA ratio_study"]=["Unternehmenswert zu EBITDA Verhältnis"],e.exports["Enterprise value to revenue ratio_study"]=["Unternehmenswert zu Umsatz Verhältnis"],e.exports["Goodwill, net_study"]=["Geschäftswert, netto"],e.exports["Ichimoku Cloud_study"]="Ichimoku Cloud",e.exports.Ichimoku_study="Ichimoku",e.exports["Moving Average Convergence Divergence_study"]="Moving Average Convergence Divergence",e.exports["Operating income_study"]=["Betriebsergebnis"],e.exports["Price to book ratio_study"]=["Kurs-Buchwert-Verhältnis"],e.exports["Price to cash flow ratio_study"]=["Kurs-Cashflow-Verhältnis"],e.exports["Price to earnings ratio_study"]=["Kurs-Gewinn-Verhältnis"],e.exports["Price to free cash flow ratio_study"]=["Verhältnis Preis zu freiem Cashflow"],e.exports["Price to sales ratio_study"]=["Kurs-Umsatz-Verhältnis"],e.exports["Float shares outstanding_study"]=["Im Umlauf befindliche Aktien"],e.exports["Total common shares outstanding_study"]=["Ausstehende Stammaktien gesamt"],e.exports["Volume Weighted Average Price_study"]=["Volumengewichteter Durchschnittspreis"],
e.exports["Volume Weighted Moving Average_study"]=["Volumengewichteter gleitender Durchschnitt"],e.exports["Williams Percent Range_study"]="Williams Percent Range",e.exports.Doji_study="Doji",e.exports["Spinning Top Black_study"]="Spinning Top Black",e.exports["Spinning Top White_study"]="Spinning Top White",e.exports["Accounts payable_study"]=["Verbindlichkeiten"],e.exports["Accounts receivables, gross_study"]=["Forderungen aus Lieferungen und Leistungen, brutto"],e.exports["Accounts receivable - trade, net_study"]=["Forderungen aus Lieferungen und Leistungen, netto"],e.exports.Accruals_study=["Rückstellungen"],e.exports["Accrued payroll_study"]=["Rückstellungen für Gehaltsabrechnungen"],e.exports["Accumulated depreciation, total_study"]=["Kumulierte Abschreibungen, gesamt"],e.exports["Additional paid-in capital/Capital surplus_study"]=["Zusätzliche Kapitaleinlage/Kapitalrücklage"],e.exports["After tax other income/expense_study"]=["Sonstige Erträge/Aufwendungen nach Steuern"],e.exports["Altman Z-score_study"]=["Altman Z-Score"],e.exports.Amortization_study=["Amortisation"],e.exports["Amortization of intangibles_study"]=["Amortisation von immateriellen Vermögenswerten"],e.exports["Amortization of deferred charges_study"]=["Amortisation von Rechnungsabgrenzungsposten"],e.exports["Asset turnover_study"]=["Kapitalumschlag"],e.exports["Average basic shares outstanding_study"]=["Durchschnittlich unverwässerte Aktien im Umlauf"],e.exports["Bad debt / Doubtful accounts_study"]=["Zahlungsunfähige Forderungen / Zweifelhafte Forderungen"],e.exports["Basic EPS_study"]=["Gewinn je Aktie"],e.exports["Basic earnings per share (Basic EPS)_study"]=["Unverwässerter Gewinn je Aktie (Basic EPS)"],e.exports["Beneish M-score_study"]="Beneish M-score",e.exports["Book value per share_study"]=["Buchwert pro Aktie"],e.exports["Buyback yield %_study"]=["Rückkaufrendite %"],e.exports["Capital and operating lease obligations_study"]=["Verpflichtungen aus Finanzierungs- und Operating-Leasingverhältnissen"],e.exports["Capital expenditures - fixed assets_study"]=["Investitionen - Sachanlagen"],e.exports["Capital expenditures - other assets_study"]=["Investitionen - sonstige Vermögenswerte"],e.exports["Capitalized lease obligations_study"]=["Kapitalisierte Leasing-Verpflichtungen"],e.exports["Cash and short term investments_study"]=["Bargeld und kurzfristige Anlagen"],e.exports["Cash conversion cycle_study"]=["Zyklus der Bargeldumwandlung"],e.exports["Cash & equivalents_study"]=["Bargeld und Äquivalente"],e.exports["Cash from financing activities_study"]=["Barmittel aus Finanzierungstätigkeit"],e.exports["Cash from investing activities_study"]=["Barmittel aus Investitionstätigkeit"],e.exports["Cash from operating activities_study"]=["Barmittel aus laufender Geschäftstätigkeit"],e.exports["Change in accounts payable_study"]=["Veränderung der Forderungen und Verbindlichkeiten"],e.exports["Change in accounts receivable_study"]=["Veränderung der Forderungen aus Lieferungen und Leistungen"],
e.exports["Change in accrued expenses_study"]=["Veränderung der aufgelaufenen Kosten"],e.exports["Change in inventories_study"]=["Veränderung des Inventars"],e.exports["Change in other assets/liabilities_study"]=["Veränderung der sonstigen Aktiva/Passiva"],e.exports["Change in taxes payable_study"]=["Veränderung der Steuerverbindlichkeiten"],e.exports["Changes in working capital_study"]=["Veränderungen im Working Capital"],e.exports["COGS to revenue ratio_study"]=["Verhältnis von COGS zu Umsatz"],e.exports["Common dividends paid_study"]=["Gezahlte Stammdividenden"],e.exports["Common equity, total_study"]=["Stammkapital, gesamt"],e.exports["Common stock par/Carrying value_study"]=["Stammaktien Nennwert/Buchwert"],e.exports["Cost of goods_study"]=["Kosten der Güter"],e.exports["Cost of goods sold_study"]=["Kosten der verkauften Güter"],e.exports["Current portion of LT debt and capital leases_study"]=["Kurzfristiger Anteil an langfristigen Verbindlichkeiten und Finanzierungsleasingverträgen"],e.exports["Current ratio_study"]=["Aktuelles Verhältnis"],e.exports["Days inventory_study"]=["Tages Bestand"],e.exports["Days payable_study"]=["Zahlbare Tage"],e.exports["Days sales outstanding_study"]=["Außenstandsdauer der Verkäufe"],e.exports["Debt to assets ratio_study"]=["Verschuldung im Verhältnis zum Vermögen"],e.exports["Debt to equity ratio_study"]=["Fremdkapital zu Eigenkapital Verhältnis"],e.exports["Debt to revenue ratio_study"]=["Verschuldung im Verhältnis zu den Einnahmen"],e.exports["Deferred income, current_study"]=["Passive Rechnungsabgrenzung, kurzfristig"],e.exports["Deferred income, non-current_study"]=["Passive Rechnungsabgrenzung, langfristig"],e.exports["Deferred tax assets_study"]=["Aktive latente Steuern"],e.exports["Deferred taxes (cash flow)_study"]=["Latente Steuern (Cashflow)"],e.exports["Deferred tax liabilities_study"]=["Latente Steuerverbindlichkeiten"],e.exports.Depreciation_study=["Abschreibung"],e.exports["Deprecation and amortization_study"]=["Abschreibungen und Amortisation"],e.exports["Depreciation & amortization (cash flow)_study"]=["Abschreibungen (Cashflow)"],e.exports["Depreciation/depletion_study"]=["Abschreibung/Verluste"],e.exports["Diluted EPS_study"]=["Verwässerter EPS"],e.exports["Diluted earnings per share (Diluted EPS)_study"]=["Verwässertes Ergebnis je Aktie (Diluted EPS)"],e.exports["Diluted net income available to common stockholders_study"]=["Verwässerter, den Stammaktionären zustehender Nettogewinn"],e.exports["Diluted shares outstanding_study"]=["Verwässerte ausstehende Aktien"],e.exports["Dilution adjustment_study"]=["Einstellung der Verdünnung"],e.exports["Discontinued operations_study"]=["Aufgegebene Geschäftsbereiche"],e.exports["Dividends payable_study"]=["Zu zahlende Dividende"],e.exports["Dividends per share - common stock primary issue_study"]=["Dividende pro Aktie - Stammaktien Erstausgabe"],e.exports["Dividend yield %_study"]=["Dividendenrendite in %"],e.exports["Earnings yield_study"]=["Ergebnis Rendite"],e.exports.EBIT_study="EBIT",e.exports.EBITDA_study="EBITDA",
e.exports["EBITDA margin %_study"]="EBITDA margin %",e.exports["Effective interest rate on debt %_study"]=["Effektiver Zinssatz für Fremdkapital %"],e.exports["Enterprise value_study"]=["Unternehmenswert"],e.exports["EPS basic one year growth_study"]=["EPS basic einjähriges Wachstum"],e.exports["EPS diluted one year growth_study"]=["EPS verwässertes einjähriges Wachstum"],e.exports["EPS estimates_study"]=["EPS-Schätzungen"],e.exports["Equity in earnings_study"]=["Anteil am Gewinn"],e.exports["Financing activities – other sources_study"]=["Finanzierungstätigkeit - andere Quellen"],e.exports["Financing activities – other uses_study"]=["Finanzierungstätigkeit - sonstige Verwendung"],e.exports["Free cash flow_study"]=["Freier Cashflow"],e.exports["Free cash flow margin %_study"]=["Freie Cashflow-Marge in %"],e.exports["Fulmer H factor_study"]=["Fulmer H-Faktor"],e.exports["Funds from operations_study"]=["Betriebsmittel aus der Geschäftstätigkeit"],e.exports["Goodwill to assets ratio_study"]=["Verhältnis Firmenwert zu Vermögenswerten"],e.exports["Graham's number_study"]=["Grahams Nummer"],e.exports["Gross margin %_study"]=["Bruttomarge in %"],e.exports["Gross profit_study"]=["Bruttogewinn"],e.exports["Gross profit to assets ratio_study"]=["Verhältnis Bruttogewinn zu Aktiva"],e.exports["Gross property/plant/equipment_study"]=["Bruttosachwerte/Anlagen/Ausrüstung"],e.exports.Impairments_study=["Wertminderungen"],e.exports["Income Tax Credits_study"]=["Einkommenssteuergutschriften"],e.exports["Income tax, current_study"]=["Einkommensteuer, laufend"],e.exports["Income tax, current - domestic_study"]=["Einkommensteuer, laufend - Inland"],e.exports["Income Tax, current - foreign_study"]=["Einkommensteuer, laufend - Ausland"],e.exports["Income tax, deferred_study"]=["Einkommensteuer, latent"],e.exports["Income tax, deferred - domestic_study"]=["Einkommensteuer, latent - Inland"],e.exports["Income tax, deferred - foreign_study"]=["Einkommensteuer, latent - Ausland"],e.exports["Income tax payable_study"]=["Zu zahlende Einkommensteuer"],e.exports["Interest capitalized_study"]=["Kapitalisierte Zinsen"],e.exports["Interest coverage_study"]=["Zinsdeckung"],e.exports["Interest expense, net of interest capitalized_study"]=["Zinsaufwendungen, abzüglich der kapitalisierten Zinsen"],e.exports["Interest expense on debt_study"]=["Zinsaufwand für Fremdkapital"],e.exports["Inventories - finished goods_study"]=["Bestände - Fertige Erzeugnisse"],e.exports["Inventories - progress payments & other_study"]=["Bestände - Abschlagszahlungen & Sonstiges"],e.exports["Inventories - raw materials_study"]=["Bestände - Rohmaterialien"],e.exports["Inventories - work in progress_study"]=["Vorräte - laufende Arbeiten"],e.exports["Inventory to revenue ratio_study"]=["Verhältnis von Beständen zu Umsatz"],e.exports["Inventory turnover_study"]=["Umschlagshäufigkeit der Bestände"],e.exports["Investing activities – other sources_study"]=["Investitionstätigkeit - andere Quellen"],
e.exports["Investing activities – other uses_study"]=["Investitionstätigkeit - sonstige Verwendung"],e.exports["Investments in unconsolidated subsidiaries_study"]=["Anteile an nicht konsolidierten Tochterunternehmen"],e.exports["Issuance of long term debt_study"]=["Emission von langfristigen Verbindlichkeiten"],e.exports["Issuance/retirement of debt, net_study"]=["Emission/Tilgung von Schulden, netto"],e.exports["Issuance/retirement of long term debt_study"]=["Ausgabe/Tilgung von langfristigen Verbindlichkeiten"],e.exports["Issuance/retirement of other debt_study"]=["Emission/Tilgung von sonstigen Schulden"],e.exports["Issuance/retirement of short term debt_study"]=["Ausgabe/Tilgung von kurzfristigen Verbindlichkeiten"],e.exports["Issuance/retirement of stock, net_study"]=["Ausgabe/Abgabe von Kapital, netto"],e.exports["KZ index_study"]=["KZ Index"],e.exports["Legal claim expense_study"]=["Kosten für Rechtsansprüche"],e.exports["Long term debt_study"]=["Langfristige Verbindlichkeiten"],e.exports["Long term debt excl. lease liabilities_study"]=["Langfristige Schulden ohne Leasing-Verbindlichkeiten"],e.exports["Long term debt to total assets ratio_study"]=["Langfristige Schulden im Verhältnis zur Bilanzsumme"],e.exports["Long term debt to total equity ratio_study"]=["Langfristige Fremdkapitalquote"],e.exports["Long term investments_study"]=["Langfristige Investitionen"],e.exports["Market capitalization_study"]=["Marktkapitalisierung"],e.exports["Minority interest_study"]=["Minderheitsanteil"],e.exports["Miscellaneous non-operating expense_study"]=["Verschiedene nicht-operative Aufwendungen"],e.exports["Net current asset value per share_study"]=["Aktueller Nettovermögenswert pro Aktie"],e.exports["Net debt_study"]=["Nettoverschuldung"],e.exports["Net income_study"]=["Nettoeinkommen"],e.exports["Net income before discontinued operations_study"]=["Reingewinn vor aufgegebenen Geschäftsbereichen"],e.exports["Net income (cash flow)_study"]=["Nettoeinkommen (Cashflow)"],e.exports["Net income per employee_study"]=["Nettoeinkommen pro Mitarbeiter"],e.exports["Net intangible assets_study"]=["Immaterielle Vermögenswerte, netto"],e.exports["Net margin %_study"]=["Nettogewinnmarge in %"],e.exports["Net property/plant/equipment_study"]=["Netto-Sachanlagen/Ausstattung"],e.exports["Non-cash items_study"]=["Nicht monetäre Posten"],e.exports["Non-controlling/minority interest_study"]=["Nicht beherrschende Anteile/Minderheitsanteile"],e.exports["Non-operating income, excl. interest expenses_study"]=["Nicht-operative Erträge, exkl. Zinsaufwendungen"],e.exports["Non-operating income, total_study"]=["Nicht-operative Erträge, gesamt"],e.exports["Non-operating interest income_study"]=["Nicht-operative Zinserträge"],e.exports["Note receivable - long term_study"]=["Schuldscheindarlehen - langfristig"],e.exports["Notes payable_study"]=["Schuldverschreibungen"],e.exports["Number of employees_study"]=["Anzahl der Mitarbeiter"],e.exports["Number of shareholders_study"]=["Anzahl der Aktionäre"],
e.exports["Operating earnings yield %_study"]=["Operative Ergebnisrendite %"],e.exports["Operating expenses (excl. COGS)_study"]=["Betriebliche Aufwendungen (exkl. COGS)"],e.exports["Operating lease liabilities_study"]=["Verbindlichkeiten aus Operating Leasing"],e.exports["Operating margin %_study"]=["Operative Gewinnmarge in %"],e.exports["Other COGS_study"]=["Sonstige COGS"],e.exports["Other common equity_study"]=["Sonstiges allgemeines Eigenkapital"],e.exports["Other current assets, total_study"]=["Sonstige kurzfristige Vermögenswerte, gesamt"],e.exports["Other current liabilities_study"]=["Sonstige kurzfristige Verbindlichkeiten"],e.exports["Other cost of goods sold_study"]=["Sonstige Kosten der verkauften Waren"],e.exports["Other exceptional charges_study"]=["Sonstige außergewöhnliche Belastungen"],e.exports["Other financing cash flow items, total_study"]=["Sonstige Finanzierungs-Cashflow-Positionen, gesamt"],e.exports["Other intangibles, net_study"]=["Sonstige immaterielle Vermögensgegenstände, netto"],e.exports["Other investing cash flow items, total_study"]=["Sonstige Cashflow-Positionen, gesamt"],e.exports["Other investments_study"]=["Sonstige Investitionen"],e.exports["Other liabilities, total_study"]=["Sonstige Verbindlichkeiten, gesamt"],e.exports["Other long term assets, total_study"]=["Sonstiges langfristiges Vermögen, gesamt"],e.exports["Other non-current liabilities, total_study"]=["Sonstige langfristige Verbindlichkeiten, Gesamtbetrag"],e.exports["Other operating expenses, total_study"]=["Sonstige betriebliche Aufwendungen, gesamt"],e.exports["Other receivables_study"]=["Sonstige Forderungen"],e.exports["Other short term debt_study"]=["Sonstige kurzfristige Verbindlichkeiten"],e.exports["Paid in capital_study"]=["Eingezahltes Kapital"],e.exports["PEG ratio_study"]=["PEG-Verhältnis"],e.exports["Piotroski F-score_study"]=["Piotroski F-Score"],e.exports["Preferred dividends_study"]=["Vorzugsdividenden"],e.exports["Preferred dividends paid_study"]=["Gezahlte Vorzugsdividenden"],e.exports["Preferred stock, carrying value_study"]=["Vorzugsaktien, Buchwert"],e.exports["Prepaid expenses_study"]=["Vorausbezahlte Aufwendungen"],e.exports["Pretax equity in earnings_study"]=["Anteil am Gewinn vor Steuern"],e.exports["Pretax income_study"]=["Ergebnis vor Steuern"],e.exports["Price earnings ratio forward_study"]=["Kurs-Gewinn-Verhältnis vorwärts"],e.exports["Price sales ratio forward_study"]=["Preis-Umsatz-Verhältnis vorwärts"],e.exports["Price to tangible book ratio_study"]=["Kurs-Buchwert-Verhältnis"],e.exports["Provision for risks & charge_study"]=["Rückstellung für Risiken & Kosten"],e.exports["Purchase/acquisition of business_study"]=["Kauf/Akquisition von Unternehmen"],e.exports["Purchase of investments_study"]=["Erwerb von Beteiligungen"],e.exports["Purchase/sale of business, net_study"]=["Kauf/Verkauf von Unternehmen, netto"],e.exports["Purchase/sale of investments, net_study"]=["Kauf/Verkauf von Beteiligungen, netto"],e.exports["Quality ratio_study"]=["Qualitätsverhältnis"],
e.exports["Quick ratio_study"]=["Kurzfristiges Verhältnis"],e.exports["Reduction of long term debt_study"]=["Reduktion der langfristigen Verschuldung"],e.exports["Repurchase of common & preferred stock_study"]=["Rückkauf von Stamm- und Vorzugsaktien"],e.exports["Research & development_study"]=["Forschung & Entwicklung"],e.exports["Research & development to revenue ratio_study"]=["Verhältnis Forschung & Entwicklung zum Umsatz"],e.exports["Restructuring charge_study"]=["Umstrukturierungskosten"],e.exports["Retained earnings_study"]=["Einbehaltene Gewinne"],e.exports["Return on assets %_study"]=["Gesamtkapitalrentabilität in %"],e.exports["Return on equity %_study"]=["Eigenkapitalrendite in %"],e.exports["Return on equity adjusted to book value %_study"]=["Eigenkapitalrendite angepasst an den Buchwert %"],e.exports["Return on invested capital %_study"]=["Rendite auf das investierte Kapital in %"],e.exports["Return on tangible assets %_study"]=["Rendite auf das Sachanlagevermögen %"],e.exports["Return on tangible equity %_study"]=["Rentabilität des materiellen Eigenkapitals %"],e.exports["Revenue estimates_study"]=["Umsatzschätzungen"],e.exports["Revenue one year growth_study"]=["Umsatz ein Jahr Wachstum"],e.exports["Revenue per employee_study"]=["Umsatz pro Mitarbeiter"],e.exports["Sale/maturity of investments_study"]=["Verkauf/Fälligkeit von Kapitalanlagen"],e.exports["Sale of common & preferred stock_study"]=["Verkauf von Stamm- und Vorzugsaktien"],e.exports["Sale of fixed assets & businesses_study"]=["Verkauf von Anlagevermögen & Unternehmen"],e.exports["Selling/general/admin expenses, other_study"]=["Vertriebs-/Allgemeines/Verwaltungskosten, Sonstiges"],e.exports["Selling/general/admin expenses, total_study"]=["Vertriebs-/Allgemeine/Administrationskosten, gesamt"],e.exports["Shareholders' equity_study"]=["Eigenkapital der Aktionäre"],e.exports["Shares buyback ratio %_study"]=["Aktienrückkaufquote %"],e.exports["Short term debt_study"]=["Kurzfristige Verbindlichkeiten"],e.exports["Short term debt excl. current portion of LT debt_study"]=["Kurzfristige Schulden ohne kurzfristigen Anteil an langfristigen Schulden"],e.exports["Short term investments_study"]=["Kurzfristige Investitionen"],e.exports["Sloan ratio %_study"]=["Sloan-Verhältnis %"],e.exports["Springate score_study"]=["Springate-Punktzahl"],e.exports["Sustainable growth rate_study"]=["Nachhaltige Wachstumsrate"],e.exports["Tangible book value per share_study"]=["Materieller Buchwert pro Aktie"],e.exports["Tangible common equity ratio_study"]=["Materielle Eigenkapitalquote"],e.exports.Taxes_study=["Steuern"],e.exports["Tobin's Q (approximate)_study"]=["Tobin's Q (annähernd)"],e.exports["Total assets_study"]=["Gesamtkapital"],e.exports["Total cash dividends paid_study"]=["Insgesamt gezahlte Bardividenden"],e.exports["Total current assets_study"]=["Umlaufvermögen"],e.exports["Total current liabilities_study"]=["Summe der kurzfristige Verbindlichkeiten"],e.exports["Total debt_study"]=["Gesamtverschuldung"],e.exports["Total equity_study"]=["Eigenkapital gesamt"],
e.exports["Total inventory_study"]=["Gesamtbestand"],e.exports["Total liabilities_study"]=["Summe Verbindlichkeiten"],e.exports["Total liabilities & shareholders' equities_study"]=["Gesamtverbindlichkeiten & Eigenkapital der Aktionäre"],e.exports["Total non-current assets_study"]=["Summe Langfristiger Vermögenswerte"],e.exports["Total non-current liabilities_study"]=["Summe Langfristiger Verbindlichkeiten"],e.exports["Total operating expenses_study"]=["Betriebskosten insgesamt"],e.exports["Total receivables, net_study"]=["Summe Forderungen, netto"],e.exports["Total revenue_study"]=["Gesamtumsatz"],e.exports["Treasury stock - common_study"]=["Eigene Aktien - Stammaktien"],e.exports["Unrealized gain/loss_study"]=["Unrealisierte Gewinne/Verluste"],e.exports["Unusual income/expense_study"]=["Ungewöhnliche Erträge/Aufwendungen"],e.exports["Zmijewski score_study"]=["Zmijewski-Punktzahl"],e.exports["Valuation ratios_study"]=["Bewertungskennzahlen"],e.exports["Profitability ratios_study"]=["Rentabilitätskennzahlen"],e.exports["Liquidity ratios_study"]=["Liquiditätskennzahlen"],e.exports["Solvency ratios_study"]=["Solvenz-Kennzahlen"],e.exports["Key stats_study"]=["Schlüsseldaten"],e.exports["Accumulation/Distribution_study"]=["Kumulierung / Distribution"],e.exports["Accumulative Swing Index_study"]="Accumulative Swing Index",e.exports["Advance/Decline_study"]=["Anstieg/Rückgang"],e.exports["Arnaud Legoux Moving Average_study"]="Arnaud Legoux Moving Average",e.exports.Aroon_study="Aroon",e.exports.ASI_study="ASI",e.exports["Average Directional Index_study"]="Average Directional Index",e.exports["Average True Range_study"]="Average True Range",e.exports["Awesome Oscillator_study"]="Awesome Oscillator",e.exports["Balance of Power_study"]="Balance of Power",e.exports["Bollinger Bands %B_study"]="Bollinger Bands %B",e.exports["Bollinger Bands Width_study"]="Bollinger Bands Width",e.exports["Bollinger Bands_study"]="Bollinger Bands",e.exports["Chaikin Money Flow_study"]="Chaikin Money Flow",e.exports["Chaikin Oscillator_study"]=["Chaikin-Oszillator"],e.exports["Chande Kroll Stop_study"]="Chande Kroll Stop",e.exports["Chande Momentum Oscillator_study"]="Chande Momentum Oscillator",e.exports["Chop Zone_study"]="Chop Zone",e.exports["Choppiness Index_study"]="Choppiness Index",e.exports["Commodity Channel Index_study"]="Commodity Channel Index",e.exports["Connors RSI_study"]="Connors RSI",e.exports["Coppock Curve_study"]="Coppock Curve",e.exports["Correlation Coefficient_study"]=["Korrelations-Koeffizient"],e.exports.CRSI_study="CRSI",e.exports["Detrended Price Oscillator_study"]="Detrended Price Oscillator",e.exports["Directional Movement_study"]="Directional Movement",e.exports["Donchian Channels_study"]="Donchian Channels",e.exports["Double EMA_study"]="Double EMA",e.exports["Ease Of Movement_study"]="Ease Of Movement",e.exports["Elder Force Index_study"]=["Elder's Force Index"],e.exports["EMA Cross_study"]="EMA Cross",e.exports.Envelopes_study=["Umrandungen"],e.exports["Fisher Transform_study"]="Fisher Transform",
e.exports["Fixed Range_study"]="Fixed Range",e.exports["Fixed Range Volume Profile_study"]="Fixed Range Volume Profile",e.exports["Guppy Multiple Moving Average_study"]=["Guppy Mehrfacher gleitender Durchschnitt"],e.exports["Historical Volatility_study"]=["Historische Volatilität"],e.exports["Hull Moving Average_study"]="Hull Moving Average",e.exports["Keltner Channels_study"]="Keltner Channels",e.exports["Klinger Oscillator_study"]=["Klinger Oszillator"],e.exports["Know Sure Thing_study"]=["Know-Sure-Thing"],e.exports["Least Squares Moving Average_study"]=["Least-Squares Moving Average"],e.exports["Linear Regression Curve_study"]=["Linear Regression-Kurve"],e.exports["MA Cross_study"]="MA Cross",e.exports["MA with EMA Cross_study"]=["MA mit EMA Cross"],e.exports["MA/EMA Cross_study"]="MA/EMA Cross",e.exports.MACD_study="MACD",e.exports["Mass Index_study"]=["Mass-Index"],e.exports["McGinley Dynamic_study"]=["McGinley-Dynamik"],e.exports.Median_study=["Medianer Wert"],e.exports.Momentum_study="Momentum",e.exports["Money Flow_study"]="Money Flow",e.exports["Moving Average Channel_study"]="Moving Average Channel",e.exports["Moving Average Exponential_study"]=["Gleitender Durchschnitt exponentiell"],e.exports["Moving Average Weighted_study"]=["Gleitender Durchschnitt gewichtet"],e.exports["Moving Average Simple_study"]=["Einfacher gleitender Durchschnitt"],e.exports["Net Volume_study"]=["Nettovolumen"],e.exports["On Balance Volume_study"]="On Balance Volume",e.exports["Parabolic SAR_study"]="Parabolic SAR",e.exports["Pivot Points Standard_study"]="Pivot Points Standard",e.exports["Periodic Volume Profile_study"]=["Periodisches Volumenprofil"],e.exports["Price Channel_study"]="Price Channel",e.exports["Price Oscillator_study"]=["Price-Oszillator"],e.exports["Price Volume Trend_study"]=["Price Volume-Trend"],e.exports["Rate Of Change_study"]="Rate Of Change",e.exports["Relative Strength Index_study"]="Relative Strength Index",e.exports["Relative Vigor Index_study"]="Relative Vigor Index",e.exports["Relative Volatility Index_study"]="Relative Volatility Index",e.exports["Session Volume_study"]="Session Volume",e.exports["Session Volume HD_study"]="Session Volume HD",e.exports["Session Volume Profile_study"]=["Volumenprofil der Session"],e.exports["Session Volume Profile HD_study"]=["Volumenprofil der Session HD"],e.exports["SMI Ergodic Indicator/Oscillator_study"]=["SMI Ergodic Indikator/Oszillator"],e.exports["Smoothed Moving Average_study"]="Smoothed Moving Average",e.exports.Stoch_study="Stoch",e.exports["Stochastic RSI_study"]="Stochastic RSI",e.exports.Stochastic_study=["Stochastik"],e.exports["Triple EMA_study"]="Triple EMA",e.exports.TRIX_study="TRIX",e.exports["True Strength Indicator_study"]=["True Strength-Indikator"],e.exports["Ultimate Oscillator_study"]=["Ultimate-Oszillator"],e.exports["Visible Range_study"]=["Sichtbare Spanne"],e.exports["Visible Range Volume Profile_study"]=["Volumenprofil der sichtbaren Range"],e.exports["Volume Oscillator_study"]=["Volumen Oszillator"],e.exports.Volume_study=["Volumen"],
e.exports.Vol_study="Vol",e.exports["Vortex Indicator_study"]=["Vortex-Indikator"],e.exports.VWAP_study="VWAP",e.exports.VWMA_study="VWMA",e.exports["Williams %R_study"]="Williams %R",e.exports["Williams Alligator_study"]="Williams Alligator",e.exports["Williams Fractal_study"]="Williams Fractal",e.exports["Zig Zag_study"]="Zig Zag",e.exports["24-hour Volume_study"]=["24-Stunden Volumen"],e.exports["Ease of Movement_study"]=["Ease Of Movement"],e.exports["Elders Force Index_study"]=["Elders Force-Index"],e.exports.Envelope_study="Envelope",e.exports.Gaps_study=["Lücken"],e.exports["Linear Regression Channel_study"]=["Linearer Regressionskanal"],e.exports["Moving Average Ribbon_study"]=["Band des gleitenden Durchschnitts"],e.exports["Multi-Time Period Charts_study"]=["Multi-Time-Period Charts"],e.exports["Open Interest_study"]="Open Interest",e.exports["Rob Booker - Intraday Pivot Points_study"]=["Rob Booker - Intraday Pivotpunkte"],e.exports["Rob Booker - Knoxville Divergence_study"]=["Rob Booker - Knoxville-Divergenz"],e.exports["Rob Booker - Missed Pivot Points_study"]=["Rob Booker - Verpasste Pivotpunkte"],e.exports["Rob Booker - Reversal_study"]=["Rob Booker - Umkehrungen"],e.exports["Rob Booker - Ziv Ghost Pivots_study"]=["Rob Booker - Ziv Ghost Drehpunkte"],e.exports.Supertrend_study="Supertrend",e.exports["Technical Ratings_study"]=["Technische Ratings"],e.exports["True Strength Index_study"]="True Strength Index",e.exports["Up/Down Volume_study"]=["Aufwärts/Abwärts Volumen"],e.exports["Visible Average Price_study"]=["Sichtbarer Durchschnittspreis"],e.exports["Williams Fractals_study"]=["Williams Fraktale"],e.exports["Keltner Channels Strategy_study"]=["Keltner-Channels Strategie"],e.exports["Rob Booker - ADX Breakout_study"]="Rob Booker - ADX Breakout",e.exports["Supertrend Strategy_study"]=["Supertrend-Strategie"],e.exports["Technical Ratings Strategy_study"]=["Technische Ratings Strategie"],e.exports["Auto Anchored Volume Profile_study"]=["Auto-Anchored Volumenprofil"],e.exports["Auto Fib Extension_study"]=["Auto-Fib Extension"],e.exports["Auto Fib Retracement_study"]="Auto Fib Retracement",e.exports["Auto Pitchfork_study"]=["Auto-Pitchfork"],e.exports["Bearish Flag Chart Pattern_study"]=["Bearish-Flag Chartmuster"],e.exports["Bullish Flag Chart Pattern_study"]=["Chartmuster: Bullish Flag"],e.exports["Bearish Pennant Chart Pattern_study"]=["Chartmuster: Bearish Pennant"],e.exports["Bullish Pennant Chart Pattern_study"]=["Bullish-Pennant Chartmuster"],e.exports["Double Bottom Chart Pattern_study"]=["Doppelboden Chartmuster"],e.exports["Double Top Chart Pattern_study"]=["Doppeltop Chartmuster"],e.exports["Elliott Wave Chart Pattern_study"]=["Elliott-Wellen Chartmuster"],e.exports["Falling Wedge Chart Pattern_study"]=["Fallende Keile Chartmuster"],e.exports["Head And Shoulders Chart Pattern_study"]=["Kopf und Schultern Chartmuster"],e.exports["Inverse Head And Shoulders Chart Pattern_study"]=["Kopf und Schultern invers Chartmuster"],e.exports["Rectangle Chart Pattern_study"]=["Rechteck Chartmuster"],
e.exports["Rising Wedge Chart Pattern_study"]=["Steigende Keile Chartmuster"],e.exports["Triangle Chart Pattern_study"]=["Dreieck Chartmuster"],e.exports["Triple Bottom Chart Pattern_study"]=["Dreifachboden Chartmuster"],e.exports["Triple Top Chart Pattern_study"]=["Dreifachtop Chartmuster"],e.exports["VWAP Auto Anchored_study"]=["VWAP Auto-Anchored"],e.exports["*All Candlestick Patterns*_study"]=["*Alle Candlestick-Muster*"],e.exports["Abandoned Baby - Bearish_study"]="Abandoned Baby - Bearish",e.exports["Abandoned Baby - Bullish_study"]=["Abandoned Baby -Bullish"],e.exports["Dark Cloud Cover - Bearish_study"]="Dark Cloud Cover - Bearish",e.exports["Doji Star - Bearish_study"]="Doji Star - Bearish",e.exports["Doji Star - Bullish_study"]="Doji Star - Bullish",e.exports["Downside Tasuki Gap - Bearish_study"]=["Downside Tasuki Tasuki - Bearish"],e.exports["Dragonfly Doji - Bullish_study"]="Dragonfly Doji - Bullish",e.exports["Engulfing - Bearish_study"]="Engulfing - Bearish",e.exports["Engulfing - Bullish_study"]="Engulfing - Bullish",e.exports["Evening Doji Star - Bearish_study"]="Evening Doji Star - Bearish",e.exports["Evening Star - Bearish_study"]="Evening Star - Bearish",e.exports["Falling Three Methods - Bearish_study"]="Falling Three Methods - Bearish",e.exports["Falling Window - Bearish_study"]="Falling Window - Bearish",e.exports["Gravestone Doji - Bearish_study"]="Gravestone Doji - Bearish",e.exports["Hammer - Bullish_study"]="Hammer - Bullish",e.exports["Hanging Man - Bearish_study"]="Hanging Man - Bearish",e.exports["Harami - Bearish_study"]="Harami - Bearish",e.exports["Harami - Bullish_study"]="Harami - Bullish",e.exports["Inverted Hammer - Bullish_study"]="Inverted Hammer - Bullish",e.exports["Kicking - Bearish_study"]="Kicking - Bearish",e.exports["Kicking - Bullish_study"]="Kicking - Bullish",e.exports["Long Lower Shadow - Bullish_study"]="Long Lower Shadow - Bullish",e.exports["Long Upper Shadow - Bearish_study"]="Long Upper Shadow - Bearish",e.exports["Marubozu Black - Bearish_study"]="Marubozu Black - Bearish",e.exports["Marubozu White - Bullish_study"]="Marubozu White - Bullish",e.exports["Morning Doji Star - Bullish_study"]="Morning Doji Star - Bullish",e.exports["Morning Star - Bullish_study"]="Morning Star - Bullish",e.exports["On Neck - Bearish_study"]="On Neck - Bearish",e.exports["Piercing - Bullish_study"]="Piercing - Bullish",e.exports["Rising Three Methods - Bullish_study"]="Rising Three Methods - Bullish",e.exports["Rising Window - Bullish_study"]="Rising Window - Bullish",e.exports["Shooting Star - Bearish_study"]="Shooting Star - Bearish",e.exports["Three Black Crows - Bearish_study"]="Three Black Crows - Bearish",e.exports["Three White Soldiers - Bullish_study"]="Three White Soldiers - Bullish",e.exports["Tri-Star - Bearish_study"]="Tri-Star - Bearish",e.exports["Tri-Star - Bullish_study"]="Tri-Star - Bullish",e.exports["Tweezer Top - Bearish_study"]="Tweezer Top - Bearish",e.exports["Upside Tasuki Gap - Bullish_study"]="Upside Tasuki Gap - Bullish",
e.exports.SuperTrend_study="SuperTrend",e.exports["Average Price_study"]=["Durchschnittlicher Preis"],e.exports["Typical Price_study"]=["Typischer Preis"],e.exports["Median Price_study"]=["Median Preis"],e.exports["Money Flow Index_study"]="Money Flow Index",e.exports["Moving Average Double_study"]="Moving Average Double",e.exports["Moving Average Triple_study"]="Moving Average Triple",e.exports["Moving Average Adaptive_study"]="Moving Average Adaptive",e.exports["Moving Average Hamming_study"]="Moving Average Hamming",e.exports["Moving Average Modified_study"]="Moving Average Modified",e.exports["Moving Average Multiple_study"]="Moving Average Multiple",e.exports["Linear Regression Slope_study"]="Linear Regression Slope",e.exports["Standard Error_study"]="Standard Error",e.exports["Standard Error Bands_study"]="Standard Error Bands",e.exports["Correlation - Log_study"]="Correlation - Log",e.exports["Standard Deviation_study"]="Standard Deviation",e.exports["Chaikin Volatility_study"]="Chaikin Volatility",e.exports["Volatility Close-to-Close_study"]="Volatility Close-to-Close",e.exports["Volatility Zero Trend Close-to-Close_study"]="Volatility Zero Trend Close-to-Close",e.exports["Volatility O-H-L-C_study"]="Volatility O-H-L-C",e.exports["Volatility Index_study"]="Volatility Index",e.exports["Trend Strength Index_study"]="Trend Strength Index",e.exports["Majority Rule_study"]="Majority Rule",e.exports["Advance Decline Line_study"]="Advance Decline Line",e.exports["Advance Decline Ratio_study"]="Advance Decline Ratio",e.exports["Advance/Decline Ratio (Bars)_study"]=["Advance/Decline Ratio (Balken)"],e.exports["BarUpDn Strategy_study"]=["BarUpDn Strategie"],e.exports["Bollinger Bands Strategy directed_study"]="Bollinger Bands Strategy directed",e.exports["Bollinger Bands Strategy_study"]="Bollinger Bands Strategy",e.exports.ChannelBreakOutStrategy_study="ChannelBreakOutStrategy",e.exports.Compare_study=["Vergleichen"],e.exports["Conditional Expressions_study"]=["bedingter Ausdruck"],e.exports.ConnorsRSI_study="ConnorsRSI",e.exports["Consecutive Up/Down Strategy_study"]=["Consecutive Up-/Down-Strategy"],e.exports["Cumulative Volume Index_study"]="Cumulative Volume Index",e.exports["Divergence Indicator_study"]="Divergence Indicator",e.exports["Greedy Strategy_study"]=["Greedy-Strategie"],e.exports["InSide Bar Strategy_study"]=["InSide-Bar Strategie"],e.exports["Keltner Channel Strategy_study"]=["Keltner Channel Strategie"],e.exports["Linear Regression_study"]=["Lineare Regression"],e.exports["MACD Strategy_study"]=["MACD Strategie"],e.exports["Momentum Strategy_study"]=["Momentum-Strategie"],e.exports["Moon Phases_study"]="Moon Phases",e.exports["Moving Average Convergence/Divergence_study"]=["Moving Average Konvergenz/Divergenz"],e.exports["MovingAvg Cross_study"]="MovingAvg Cross",e.exports["MovingAvg2Line Cross_study"]="MovingAvg2Line Cross",e.exports["OutSide Bar Strategy_study"]=["OutSide-Bar-Strategie"],e.exports.Overlay_study=["Überlagerung"],e.exports["Parabolic SAR Strategy_study"]=["Parabolic-SAR Strategie"],
e.exports["Pivot Extension Strategy_study"]=["Pivot-Extension-Strategie"],e.exports["Pivot Points High Low_study"]="Pivot Points High Low",e.exports["Pivot Reversal Strategy_study"]=["Pivot Reversal-Strategie"],e.exports["Price Channel Strategy_study"]=["Price Channel-Strategie"],e.exports["RSI Strategy_study"]=["RSI-Strategie"],e.exports["SMI Ergodic Indicator_study"]=["SMI Ergodic Indikator"],e.exports["SMI Ergodic Oscillator_study"]=["SMI Ergodic Oszillator"],e.exports["Stochastic Slow Strategy_study"]=["Stochastic Slow-Strategie"],e.exports["Volatility Stop_study"]=["Volatilitäts-Stopp"],e.exports["Volty Expan Close Strategy_study"]=["Volty-Expan-Close Strategie"],e.exports["Woodies CCI_study"]="Woodies CCI"},59791:e=>{e.exports="Anchored Volume Profile"},40434:e=>{e.exports="Fixed Range Volume Profile"},32819:e=>{e.exports="Vol"},66051:e=>{e.exports="Minor"},86054:e=>{e.exports="Minute"},20936:e=>{e.exports="Text"},98478:e=>{e.exports=["Konnte nicht kopiert werden"]},34004:e=>{e.exports=["Ausschneiden fehlgeschlagen"]},96260:e=>{e.exports=["Einfügen fehlgeschlagen"]},94370:e=>{e.exports=["Countdown zum nächsten Balken"]},15168:e=>{e.exports="Colombo"},36018:e=>{e.exports=["Säulen"]},19372:e=>{e.exports=["Kommentar"]},20229:e=>{e.exports=["Symbol hinzufügen oder vergleichen"]},46689:e=>{e.exports=["Eingabe bestätigen"]},43432:e=>{e.exports="Copenhagen"},35216:e=>{e.exports=["Kopieren"]},87898:e=>{e.exports=["Chart Layout kopieren"]},28851:e=>{e.exports=["Preis kopieren"]},94099:e=>{e.exports=["Kairo"]},64149:e=>{e.exports="Callout"},63528:e=>{e.exports=["Kerzen"]},46837:e=>{e.exports="Caracas"},53705:e=>{e.exports="Casablanca"},49329:e=>{e.exports=["Veränderung"]},28089:e=>{e.exports=["Symbol ändern"]},13737:e=>{e.exports="Change alerts color"},99374:e=>{e.exports=["Intervall ändern"]},14412:e=>{e.exports=["Chart Einstellungen"]},26619:e=>{e.exports=["Chart von TradingView"]},12011:e=>{e.exports=["Chart-Bild in Zwischenablage kopiert {emoji}"]},79393:e=>{e.exports=["Der Einbettungscode des Chart-Bilds wurde in die Zwischenablage kopiert {emoji}"]},59884:e=>{e.exports=["Chatham Inseln"]},28244:e=>{e.exports="Chicago"},49648:e=>{e.exports="Chongqing"},90068:e=>{e.exports=["Kreis"]},32234:e=>{e.exports=["Klicken Sie, um einen Punkt zu setzen"]},52977:e=>{e.exports=["Duplizieren"]},31691:e=>{e.exports="Close"},50493:e=>{e.exports=["Order erstellen"]},52302:e=>{e.exports=["Limit-Order erstellen"]},29908:e=>{e.exports=["Fadenkreuz"]},60997:e=>{e.exports=["Fadenkreuz"]},81520:e=>{e.exports=["Devisen"]},98486:e=>{e.exports=["Aktueller Intervall und darüber"]},73106:e=>{e.exports=["Aktueller Intervall und darunter"]},85964:e=>{e.exports=["Nur aktueller Intervall"]},17206:e=>{e.exports=["Kurve"]},95176:e=>{e.exports="Cycle"},87761:e=>{e.exports=["Zyklische Linien"]},27891:e=>{e.exports="Cypher Pattern"},56996:e=>{e.exports=["Ein Layout mit diesem Namen existiert bereits"]},30192:e=>{e.exports=["Ein Layout mit diesem Namen existiert bereits. Möchten Sie es überschreiben?"]},32852:e=>{e.exports=["ABCD Muster"]},88010:e=>{
e.exports="Amsterdam"},37422:e=>{e.exports=["Trade Setup analysieren"]},99873:e=>{e.exports=["Verankerung"]},66828:e=>{e.exports=["Verankerte Anmerkung"]},94782:e=>{e.exports=["Verankerter Text"]},61704:e=>{e.exports=["Verankerter VWAP"]},63597:e=>{e.exports=["Horizontale Linie hinzufügen"]},45743:e=>{e.exports=["Symbol hinzufügen"]},8700:e=>{e.exports=["Alarm hinzufügen"]},64615:e=>{e.exports=["Alarm für {title} hinzufügen"]},7005:e=>{e.exports=["Alarm für {title} zu {price} hinzufügen"]},3612:e=>{e.exports=["Finanz-Metrik hinzufügen für {instrumentName}"]},92206:e=>{e.exports=["Indikator / Strategie zu {studyTitle} hinzufügen"]},34810:e=>{e.exports=["Textnotiz zu {symbol} hinzufügen"]},75669:e=>{e.exports=["Diese Finanzkennzahl zum gesamten Layout hinzufügen"]},64288:e=>{e.exports=["Den Indikator dem gesamten Layout hinzufügen"]},77920:e=>{e.exports=["Die Strategie dem gesamten Layout hinzufügen"]},34059:e=>{e.exports=["Das Symbol dem gesamten Layout hinzufügen"]},17365:e=>{e.exports="Adelaide"},9408:e=>{e.exports=["Immer verborgen"]},71997:e=>{e.exports=["Immer sichtbar"]},97305:e=>{e.exports=["Alle Indikatoren und Zeichen-Tools"]},59192:e=>{e.exports=["Alle Intervalle"]},14452:e=>{e.exports="Almaty"},5716:e=>{e.exports=["Elliot Welle anwenden"]},19263:e=>{e.exports=["Haupt- Elliot Wellen anwenden"]},15818:e=>{e.exports=["Minimale Elliot Wellen anwenden"]},50352:e=>{e.exports=["Intermediate Elliot Wellen anwenden"]},66631:e=>{e.exports=["Manuellen Entscheidungspunkt verwenden"]},15682:e=>{e.exports=["Manuelles Chancen-Risiko verwenden"]},15644:e=>{e.exports=["WPT Down Wave anwenden"]},5897:e=>{e.exports=["WPT Up Wave anwenden"]},13345:e=>{e.exports=["Standard anwenden"]},95910:e=>{e.exports=["Diese Indikatoren dem gesamten Layout hinzufügen"]},42762:e=>{e.exports="Apr"},45104:e=>{e.exports=["Bogen"]},42097:e=>{e.exports=["Fläche"]},96237:e=>{e.exports=["Pfeil"]},48732:e=>{e.exports=["Pfeil Abwärts"]},82473:e=>{e.exports=["Pfeil-Markierung"]},8738:e=>{e.exports=["Pfeil nach unten"]},35062:e=>{e.exports=["Pfeil nach links"]},92163:e=>{e.exports=["Pfeil nach rechts"]},33196:e=>{e.exports=["Pfeil nach oben"]},10650:e=>{e.exports=["Pfeil Aufwärts"]},59340:e=>{e.exports=["Ashkhabad"]},13468:e=>{e.exports=["bei Schluß"]},21983:e=>{e.exports=["Athen"]},86951:e=>{e.exports="Auto"},50834:e=>{e.exports=["Auto (an Bildschirm anpassen)"]},38465:e=>{e.exports="Aug"},8975:e=>{e.exports=["Label für den durchschnittlichen Schlusskurs"]},87899:e=>{e.exports=["Durchschnittlicher Schlusskurs Linie"]},22554:e=>{e.exports=["Durchschn."]},54173:e=>{e.exports="Bogota"},53260:e=>{e.exports="Bahrain"},40664:e=>{e.exports=["Sprechblase"]},32376:e=>{e.exports="Bangkok"},19149:e=>{e.exports=["Bar Replay ist für diesen Charttyp nicht verfügbar. Möchten Sie Bar Replay beenden?"]},38660:e=>{e.exports=["Die Balkenwiedergabe ist für dieses Zeitintervall nicht erhältlich. Möchten Sie die Balkenwiedergabe verlassen?"]},16812:e=>{e.exports=["Balken"]},98838:e=>{e.exports=["Balkenmuster"]},17712:e=>{e.exports=["Grundlinie"]},54861:e=>{
e.exports=["Belgrad"]},26825:e=>{e.exports="Berlin"},30251:e=>{e.exports=["Pinsel"]},90204:e=>{e.exports=["Brüssel"]},5262:e=>{e.exports="Bratislava"},59901:e=>{e.exports=["Nach vorne bringen"]},26354:e=>{e.exports=["Ganz nach vorne bringen"]},11741:e=>{e.exports="Brisbane"},37728:e=>{e.exports=["Bukarest"]},87143:e=>{e.exports="Budapest"},82446:e=>{e.exports="Buenos Aires"},82128:e=>{e.exports=["Von TradingView"]},75190:e=>{e.exports=["Gehe zu Datum"]},38342:e=>{e.exports=["Gehe zu {lineToolName}"]},75139:e=>{e.exports=["Ich akzeptiere"]},81180:e=>{e.exports=["Gann-Box"]},68102:e=>{e.exports=["Gann Fächer"]},66321:e=>{e.exports=["Gann Square (Quadrat)"]},87107:e=>{e.exports=["Gann-Square fixiert"]},7914:e=>{e.exports=["Ghost-Feed"]},18367:e=>{e.exports=["Grand Supercycle"]},97065:e=>{e.exports=["Möchten Sie die Studienvorlage '{name}' wirklich löschen?"]},59368:e=>{e.exports=["Doppelkurve"]},35273:e=>{e.exports=["Doppelklicken Sie auf eine beliebige Kante, um das Layout-Raster zurückzusetzen."]},5828:e=>{e.exports=["Doppelklicken um Pfad zu beenden"]},63898:e=>{e.exports=["Doppelklicken um Linienzug zu beenden"]},42660:e=>{e.exports=["Abwärtsbewegung 1 oder A"]},44788:e=>{e.exports=["Abwärtsbewegung 2 oder B"]},71263:e=>{e.exports=["Abwärtsbewegung 3"]},70573:e=>{e.exports=["Abwärtsbewegung 4"]},59560:e=>{e.exports=["Abwärtsbewegung 5"]},70437:e=>{e.exports=["Abwärtsbewegung C"]},53831:e=>{e.exports=["Datenfenster öffnen"]},93345:e=>{e.exports=["Daten werden bereitgestellt von"]},76912:e=>{e.exports=["Datum"]},60222:e=>{e.exports=["Datumsbereich"]},79859:e=>{e.exports=["Daten- und Preisbereich"]},92203:e=>{e.exports=["Dez"]},69479:e=>{e.exports=["Grad"]},57701:e=>{e.exports="Denver"},24477:e=>{e.exports="Dhaka"},73720:e=>{e.exports=["Diamant"]},3556:e=>{e.exports=["Entkoppelter Kanal"]},62764:e=>{e.exports=["Verschiebung"]},22903:e=>{e.exports=["Zeichen-Werkzeugleiste"]},8338:e=>{e.exports=["Horizontale Linie zeichnen auf"]},22429:e=>{e.exports="Dubai"},9497:e=>{e.exports="Dublin"},85223:e=>{e.exports="Emoji"},24435:e=>{e.exports=["Chart Layout neu benennen"]},93512:e=>{e.exports=["Nachricht {title} bearbeiten"]},91215:e=>{e.exports=["Elliott Korrektur Welle (ABC)"]},80983:e=>{e.exports=["Elliot Doppel Combo Welle (WXY)"]},74118:e=>{e.exports=["Elliot Impuls Welle (12345)"]},95840:e=>{e.exports=["Elliot Dreiecks-Welle (ABCDE)"]},66637:e=>{e.exports=["Elliot Dreifach-Combo-Welle (WXYXZ)"]},69418:e=>{e.exports="Ellipse"},27558:e=>{e.exports=["Alarmlinien erweitern"]},2578:e=>{e.exports=["Verlängerte Linie"]},77295:e=>{e.exports=["Börse"]},2899:e=>{e.exports=["Vorhandener Bereich darüber"]},53387:e=>{e.exports=["Vorhandener Bereich unterhalb"]},36972:e=>{e.exports=["Prognose"]},17994:e=>{e.exports=["Bibliothek konnte nicht gespeichert werden"]},87375:e=>{e.exports=["Skript konnte nicht gespeichert werden"]},35050:e=>{e.exports="Feb"},82719:e=>{e.exports=["Fib Kanal"]},64192:e=>{e.exports=["Fib Kreise"]},63835:e=>{e.exports="Fib Retracement"},18072:e=>{e.exports=["Fib Speed Resistance Arcs (Bögen)"]},20877:e=>{
e.exports=["Fib Speed Resistance Fan (Fächer)"]},76783:e=>{e.exports=["Fib Spirale"]},89037:e=>{e.exports=["Fib Zeitzonen"]},72489:e=>{e.exports=["Fib Keil"]},21524:e=>{e.exports=["Flagge"]},55678:e=>{e.exports=["Flagge"]},29230:e=>{e.exports="Flat Top/Bottom"},92754:e=>{e.exports=["Umgedreht"]},42015:e=>{e.exports=["Dieser Teil ist ungültig"]},47542:e=>{e.exports=["Grundlegende Studien sind auf Charts nicht länger verfügbar"]},16245:e=>{e.exports=["Kalkutta"]},3155:e=>{e.exports=["Katmandu"]},92901:e=>{e.exports="Kagi"},2693:e=>{e.exports="Karachi"},72374:e=>{e.exports="Kuwait"},34911:e=>{e.exports=["HLC-Bereich"]},87338:e=>{e.exports="Ho Chi Minh"},61582:e=>{e.exports=["Hollow Candles"]},32918:e=>{e.exports=["Hongkong"]},61351:e=>{e.exports="Honolulu"},60049:e=>{e.exports=["Horizontale Linie"]},76604:e=>{e.exports=["Unterstützung-/Widerstandslinie"]},42616:e=>{e.exports=["Kopf und Schultern"]},40530:e=>{e.exports=["HeikinAshi"]},99820:e=>{e.exports="Helsinki"},31971:e=>{e.exports=["Verbergen"]},33911:e=>{e.exports=["Alles ausblenden"]},95551:e=>{e.exports=["Alle Zeichentools verbergen"]},44312:e=>{e.exports=["Alle Zeichnungen und Indikatoren verbergen"]},67927:e=>{e.exports=["Alle Zeichnungen, Indikatoren, Positionen und Orders verbergen"]},86306:e=>{e.exports=["Alle Indikatoren verbergen"]},70803:e=>{e.exports=["Alle Positionen und Orders ausblenden"]},13277:e=>{e.exports=["Zeichnungen verbergen"]},8251:e=>{e.exports=["Ereignisse im Chart ausblenden"]},44177:e=>{e.exports=["Indikatoren verbergen"]},2441:e=>{e.exports=["Markierungen auf Balken verbergen"]},90540:e=>{e.exports=["Positionen und Orders ausblenden"]},30777:e=>{e.exports=["Hoch"]},31994:e=>{e.exports=["High-Low"]},60259:e=>{e.exports=["Preislabel | Hoch/Tief"]},21803:e=>{e.exports=["Preislinien | Hoch/Tief"]},31895:e=>{e.exports=["Text Marker"]},69085:e=>{e.exports=['Das Histogramm ist zu groß, bitte erhöhen Sie die Einstellung "Zeilengröße".']},8122:e=>{e.exports=["Das Histogramm ist zu groß, bitte reduzieren Sie die Einstellung 'Zeilengröße'."]},23450:e=>{e.exports=["Bild"]},71778:e=>{e.exports="Intermediate"},14177:e=>{e.exports=["Ungültiges Symbol"]},53239:e=>{e.exports=["Skala invertieren"]},20062:e=>{e.exports=["Auf 100 indexiert"]},81584:e=>{e.exports=["Label der Indikatorenwerte"]},31485:e=>{e.exports=["Namenslabel des Indikators"]},27677:e=>{e.exports=["Info Linie"]},98767:e=>{e.exports=["Indikator einfügen"]},9114:e=>{e.exports=["Mittig"]},12354:e=>{e.exports=["Innerhalb der Pitchfork"]},26579:e=>{e.exports=["Symbol"]},37885:e=>{e.exports="Istanbul"},87469:e=>{e.exports="Johannesburg"},52707:e=>{e.exports="Jakarta"},95425:e=>{e.exports="Jan"},42890:e=>{e.exports="Jerusalem"},6215:e=>{e.exports="Jul"},15224:e=>{e.exports="Jun"},36253:e=>{e.exports="Juneau"},15241:e=>{e.exports=["Auf der Linken Seite"]},29404:e=>{e.exports=["Auf der Rechten Seite"]},850:e=>{e.exports=["Huch!"]},675:e=>{e.exports=["Objektbaum"]},73546:e=>{e.exports=["Okt"]},39280:e=>{e.exports=["Eröffnung"]},25595:e=>{e.exports="Original"},82906:e=>{e.exports="Oslo"},8136:e=>{
e.exports=["Tief"]},42284:e=>{e.exports=["Fixieren"]},1441:e=>{e.exports=["Fixieren / Lösen"]},82232:e=>{e.exports=["Vertikale Cursorlinie auf Zeitachse fixieren"]},18219:e=>{e.exports=["Kurs zu Balken Verhältnis fixieren"]},12285:e=>{e.exports=["Logarithmisch"]},50286:e=>{e.exports="London"},44604:e=>{e.exports=["Long-Position"]},87604:e=>{e.exports="Los Angeles"},18528:e=>{e.exports=["Label Abwärts"]},13046:e=>{e.exports=["Label Aufwärts"]},94420:e=>{e.exports=["Beschriftungen"]},89155:e=>{e.exports="Lagos"},25846:e=>{e.exports="Lima"},1277:e=>{e.exports=["Linie"]},38397:e=>{e.exports=["Linie mit Markierungen"]},63492:e=>{e.exports=["Line Break"]},83182:e=>{e.exports=["Linien"]},78104:e=>{e.exports=["Link zu dem in die Zwischenablage kopierten Chart-Bild {emoji}"]},50091:e=>{e.exports=["Lissabon"]},64352:e=>{e.exports=["Luxemburg"]},11156:e=>{e.exports="MTPredictor"},67861:e=>{e.exports=["Bewegen Sie den Punkt, um den Anker zu positionieren, und tappen Sie dann um ihn zu platzieren."]},45828:e=>{e.exports=["Bewegen nach"]},44302:e=>{e.exports=["Skala nach links bewegen"]},94338:e=>{e.exports=["Skala nach rechts bewegen"]},66276:e=>{e.exports=["Schiff modifiziert"]},18559:e=>{e.exports=["Modifizierte Schiff-Pitchfork"]},18665:e=>{e.exports=["Moskau"]},58038:e=>{e.exports="Madrid"},34190:e=>{e.exports="Malta"},90271:e=>{e.exports="Manila"},51369:e=>{e.exports=["Mrz"]},85095:e=>{e.exports=["Mexiko City"]},75633:e=>{e.exports=["Alle Skalen zu einer einzigen zusammenfügen"]},95093:e=>{e.exports=["Gemischt"]},10931:e=>{e.exports="Micro"},58397:e=>{e.exports="Millennium"},85884:e=>{e.exports="Minuette"},9632:e=>{e.exports="Minuscule"},63158:e=>{e.exports=["Gespiegelt"]},42769:e=>{e.exports=["Muskat"]},43088:e=>{e.exports=["Nicht verfügbar"]},3485:e=>{e.exports=["Keine Skalierung (Fullscreen)"]},8886:e=>{e.exports=["Keine Synchronisation"]},16971:e=>{e.exports=["Keine Volumendaten"]},75549:e=>{e.exports=["Anmerkung"]},71230:e=>{e.exports="Nov"},99203:e=>{e.exports="Norfolk Island"},79023:e=>{e.exports="Nairobi"},91203:e=>{e.exports="New York"},24143:e=>{e.exports=["Neuseeland"]},40887:e=>{e.exports=["Neuer Bereich oberhalb"]},96712:e=>{e.exports=["Neuer Bereich unterhalb"]},33566:e=>{e.exports=["Nikosia"]},56670:e=>{e.exports=["Etwas ist schiefgelaufen"]},64968:e=>{e.exports=["Es ist etwas schief gelaufen. Bitte versuchen Sie es erneut"]},10520:e=>{e.exports=["Neues Chart-Layout speichern"]},9908:e=>{e.exports=["Speichern unter"]},68553:e=>{e.exports="San Salvador"},65412:e=>{e.exports="Santiago"},13538:e=>{e.exports="Sao Paulo"},37207:e=>{e.exports=["Nur den Preis-Chart vergrößern"]},51464:e=>{e.exports="Schiff"},98114:e=>{e.exports=["Schiff-Pitchfork"]},1535:e=>{e.exports=["Das Skript wird möglicherweise nicht aktualisiert, wenn Sie die Seite verlassen."]},89517:e=>{e.exports=["Einstellungen"]},43247:e=>{e.exports=["Zweiter Bruchteil ungültig"]},19796:e=>{e.exports=["Ganz nach hinten verschieben"]},23221:e=>{e.exports=["Eins nach hinten verschieben"]},5961:e=>{e.exports="Seoul"},57902:e=>{e.exports="Sep"},25866:e=>{
e.exports=["Sitzung"]},59827:e=>{e.exports=["Session-Wechsel"]},69240:e=>{e.exports="Shanghai"},37819:e=>{e.exports=["Short-Position"]},81428:e=>{e.exports=["Anzeigen"]},98116:e=>{e.exports=["Alle Zeichnungen anzeigen"]},39046:e=>{e.exports=["Alle Zeichnungen und Indikatoren anzeigen"]},38293:e=>{e.exports=["Alle Zeichnungen, Indikatoren, Positionen und Orders anzeigen"]},49982:e=>{e.exports=["Alle Indikatoren anzeigen"]},48284:e=>{e.exports=["Alle Ideen anzeigen"]},62632:e=>{e.exports=["Alle Positionen und Orders anzeigen"]},24620:e=>{e.exports=["Kontinuierlichen Kontraktwechsel anzeigen"]},84813:e=>{e.exports=["Kontraktablauf anzeigen"]},66263:e=>{e.exports=["Dividenden anzeigen"]},46771:e=>{e.exports=["Earnings anzeigen"]},87933:e=>{e.exports=["Ideen von Usern denen ich folge anzeigen"]},72973:e=>{e.exports=["Die neuesten Updates anzeigen"]},58669:e=>{e.exports=["Nur meine Ideen anzeigen"]},30816:e=>{e.exports=["Splits anzeigen"]},68161:e=>{e.exports="Signpost"},56683:e=>{e.exports=["Singapur"]},69502:e=>{e.exports=["Sinuslinie"]},44904:e=>{e.exports=["Rechteck"]},70213:e=>{e.exports=["Studien Limit erreicht: {number} Studien pro Layout.\nBitte entfernen Sie einige Studien."]},32733:e=>{e.exports=["Stil"]},65323:e=>{e.exports=["Links sammeln"]},14113:e=>{e.exports=["Rechts sammeln"]},93161:e=>{e.exports=["Im Zeichenmodus bleiben"]},79511:e=>{e.exports=["Step-Line"]},84573:e=>{e.exports="Sticker"},48767:e=>{e.exports="Stockholm"},29662:e=>{e.exports="Submicro"},9753:e=>{e.exports="Submillennium"},71722:e=>{e.exports="Subminuette"},91889:e=>{e.exports="Supercycle"},33820:e=>{e.exports="Supermillennium"},11020:e=>{e.exports="Sydney"},89659:e=>{e.exports=["Symbol Fehler"]},90932:e=>{e.exports=["Name des Symbols"]},65986:e=>{e.exports=["Symbolinfo"]},52054:e=>{e.exports=["Letzter Wert des Symbols"]},33606:e=>{e.exports=["Global synchronisieren"]},18008:e=>{e.exports=["Auf alle Charts anwenden"]},99969:e=>{e.exports=["Point & Figure"]},53047:e=>{e.exports=["Linienzug"]},34402:e=>{e.exports=["Pfad"]},70394:e=>{e.exports=["Paralleler Kanal"]},95995:e=>{e.exports="Paris"},29682:e=>{e.exports=["Einfügen"]},51102:e=>{e.exports=["Prozent"]},35590:e=>{e.exports="Perth"},19093:e=>{e.exports="Phoenix"},22293:e=>{e.exports="Pitchfan"},43852:e=>{e.exports="Pitchfork"},37680:e=>{e.exports=["An neue linke Skala anheften"]},43707:e=>{e.exports=["An neue rechte Skala anheften"]},91130:e=>{e.exports=["An linke Skala anheften"]},61201:e=>{e.exports=["An linke Skala anheften (verborgen)"]},764:e=>{e.exports=["An rechte Skala anheften"]},20207:e=>{e.exports=["An rechte Skala anheften (verborgen)"]},66156:e=>{e.exports=["An Skala anheften (Jetzt links)"]},54727:e=>{e.exports=["An Skala anheften (Jetzt keine Skala)"]},76598:e=>{e.exports=["An Skala anheften (Jetzt rechts)"]},39065:e=>{e.exports=["An Skala anheften (Jetzt {label})"]},97324:e=>{e.exports=["An Skala angeheftet {label}"]},56948:e=>{e.exports=["An Skala anheften {label} (verborgen)"]},32156:e=>{e.exports=["An linke Skala angeheftet"]},8128:e=>{
e.exports=["An linke Skala angeheftet (verborgen)"]},3822:e=>{e.exports=["An rechte Skala angeheftet"]},44538:e=>{e.exports=["An rechte Skala anheften (verborgen)"]},65810:e=>{e.exports=["An Skala angeheftet {label}"]},14125:e=>{e.exports=["An Skala angeheftet {label} (verborgen)"]},97378:e=>{e.exports=["Skala + Plustaste für Ordermanagement"]},46669:e=>{e.exports=["Bitte erteilen Sie uns eine Schreibberechtigung für die Zwischenablage in Ihrem Browser oder drücken Sie {keystroke}"]},35963:e=>{e.exports=["Drücken und halten Sie {key} beim Zoomen, um die Chartposition bei zu behalten"]},95921:e=>{e.exports=["Preis-Label"]},28625:e=>{e.exports=["Kurs Notiz"]},2032:e=>{e.exports=["Preisspanne"]},32061:e=>{e.exports=["Preisformat ist ungültig."]},91492:e=>{e.exports=["Preislinie"]},48404:e=>{e.exports="Primary"},87086:e=>{e.exports=["Projektion"]},10160:e=>{e.exports=["Veröffentlicht von {customer},{date}"]},19056:e=>{e.exports=["Katar"]},9998:e=>{e.exports=["Drehbares Rechteck"]},74214:e=>{e.exports=["Rom"]},50470:e=>{e.exports=["Strahl"]},90357:e=>{e.exports="Range"},26833:e=>{e.exports="Reykjavik"},328:e=>{e.exports=["Rechteck"]},41615:e=>{e.exports=["Wiederherstellen"]},35001:e=>{e.exports=["Regressionstrend"]},34596:e=>{e.exports=["Entfernen"]},1434:e=>{e.exports=["Zeichnungen entfernen"]},13951:e=>{e.exports=["Indikatoren entfernen"]},4142:e=>{e.exports=["Chart-Layout umbenennen"]},20801:e=>{e.exports="Renko"},34301:e=>{e.exports=["Chartansicht zurücksetzen"]},18001:e=>{e.exports=["Punkte zurücksetzen"]},17258:e=>{e.exports=["Kursskala zurücksetzen"]},25333:e=>{e.exports=["Zeitachse zurücksetzen"]},52588:e=>{e.exports=["Riad"]},5871:e=>{e.exports="Riga"},33603:e=>{e.exports=["Warnung"]},48474:e=>{e.exports=["Warschau"]},20466:e=>{e.exports="Tokelau"},94284:e=>{e.exports=["Tokio"]},83836:e=>{e.exports="Toronto"},38788:e=>{e.exports="Taipei"},39108:e=>{e.exports="Tallinn"},37229:e=>{e.exports="Text"},16267:e=>{e.exports="Tehran"},19611:e=>{e.exports=["Vorlage"]},29198:e=>{e.exports=["Der Datenlieferant stellt keine Volumendaten für dieses Symbol zur Verfügung."]},8162:e=>{e.exports=["Die Veröffentlichungsvorschau konnte nicht geladen werden. Bitte deaktivieren Sie Ihre Browser-Erweiterungen und versuchen Sie es erneut."]},65943:e=>{e.exports=["Dieser Indikator kann nicht auf einen anderen Indikator angewandt werden"]},74986:e=>{e.exports=["Dieses Skript ist nur auf Einladung verfügbar. Um Zugang zu erhalten, wenden Sie sich bitte an den jeweiligen Autor."]},58018:e=>{e.exports=["Das, nur auf {linkStart}Tradingview{linkEnd} verfügbare, Symbol."]},98538:e=>{e.exports=["Three-Drives-Muster"]},30973:e=>{e.exports="Ticks"},31976:e=>{e.exports=["Zeit"]},64375:e=>{e.exports=["Zeitzone"]},95005:e=>{e.exports=["Zeitzyklen"]},87085:e=>{e.exports="Trade"},94770:e=>{e.exports=["Trendwinkel"]},23104:e=>{e.exports=["Trendlinie"]},15501:e=>{e.exports=["Trendbasierte Fib-Extension"]},31196:e=>{e.exports=["Trendbasierte Fib-Zeit"]},29245:e=>{e.exports=["Dreieck"]},83356:e=>{e.exports=["Dreieck Abwärts"]},12390:e=>{
e.exports=["Dreiecksmuster"]},28340:e=>{e.exports=["Dreieck Aufwärts"]},93855:e=>{e.exports="Tunis"},50406:e=>{e.exports="UTC"},81320:e=>{e.exports=["Rückgängig"]},25933:e=>{e.exports=["Einheiten"]},15101:e=>{e.exports=["Entsperren"]},34150:e=>{e.exports=["Aufwärtswelle 4"]},83927:e=>{e.exports=["Aufwärtswelle 5"]},58976:e=>{e.exports=["Aufwärtswelle 1 oder A"]},11661:e=>{e.exports=["Aufwärtswelle 2 oder B"]},53958:e=>{e.exports=["Aufwärtswelle 3"]},66560:e=>{e.exports=["Aufwärtswelle C"]},18426:e=>{e.exports="Volume Profile Fixed Range"},61022:e=>{e.exports=["Der Volumenprofil-Indikator ist nur in unseren leistungsstarken Abonnements verfügbar."]},15771:e=>{e.exports="Vancouver"},56211:e=>{e.exports=["Vertikale Linie"]},75354:e=>{e.exports="Vilnius"},21852:e=>{e.exports=["Sichtbarkeit"]},27557:e=>{e.exports=["Sichtbarkeit der Intervalle"]},89960:e=>{e.exports=["Sichtbar, wenn der Mauszeiger darüber bewegt wird"]},22198:e=>{e.exports=["Visuelle Reihenfolge"]},7050:e=>{e.exports=["X Kreuz"]},66527:e=>{e.exports=["XABCD-Muster"]},17126:e=>{e.exports=["Sie können dieses Pivot-Zeitfenster nicht mit dieser Auflösung sehen."]},69293:e=>{e.exports=["Rangun"]},84301:e=>{e.exports=["Zürich"]},76020:e=>{e.exports=["Elliott-Grad ändern"]},83935:e=>{e.exports=["überlappenden Labels anpassen"]},39402:e=>{e.exports=["Sichtbarkeit des Labels für den durchschnittlichen Schlusskurs ändern"]},98866:e=>{e.exports=["Sichtbarkeit der Linie für den durchschnittlichen Schlusskurs ändern"]},5100:e=>{e.exports=["Sichtbarkeit der Bid- und Ask-Labels ändern"]},32311:e=>{e.exports=["Sichtbarkeit der Bid- und Ask-Linien ändern"]},22641:e=>{e.exports=["Währung ändern"]},30501:e=>{e.exports=["Chart-Layout ändern zu {title}"]},7017:e=>{e.exports=["Änderung des kontinuierlichen Kontrakts unterbricht die Sichtbarkeit"]},58108:e=>{e.exports=["Sichtbarkeit des Countdowns bei Balkenschluss ändern"]},7151:e=>{e.exports=["Datumsbereich ändern"]},84944:e=>{e.exports=["Sichtbarkeit der Dividenden ändern"]},79574:e=>{e.exports=["Sichtbarkeit von Events auf dem Chart ändern"]},88217:e=>{e.exports=["Sichtbarkeit der Earnings ändern"]},28288:e=>{e.exports=["Die Ablaufsichtbarkeit des Terminkontrakts ändern"]},66805:e=>{e.exports=["Sichtbarkeit von Hoch und Tief Preislabels ändern"]},92556:e=>{e.exports=["Sichtbarkeit von Hoch und Tief Preislinien ändern"]},87027:e=>{e.exports=["ändert die Sichtbarkeit des Namenslabels der ​Indikatoren"]},14922:e=>{e.exports=["Ändert die Sichtbarkeit des Labels für Indikatorenwerte"]},19839:e=>{e.exports=["Sichtbarkeit der neuesten Updates ändern"]},23783:e=>{e.exports=["Verknüpfungsgruppe ändern"]},87510:e=>{e.exports=["Höhe des Fensters ändern"]},50190:e=>{e.exports=["Sichtbarkeit der Plustaste ändern"]},49889:e=>{e.exports=["Sichtbarkeit des Preislabels vorbörslich/nachbörslich ändern"]},16750:e=>{e.exports=["Sichtbarkeit der Preislinie vorbörslich/nachbörslich ändern"]},59883:e=>{e.exports=["Sichtbarkeit der Linie des vorherigen Schlusskurses ändern"]},67761:e=>{e.exports=["Kurslinie ändern"]},69510:e=>{
e.exports=["Verhältnis von Preis zu Balken ändern"]},32303:e=>{e.exports=["Auflösung ändern"]},526:e=>{e.exports=["Symbol ändern"]},9402:e=>{e.exports=["Sichtbarkeit der Symbolbeschriftungen ändern"]},53150:e=>{e.exports=["Sichtbarkeit des letzten Wertes des Symbols ändern"]},12707:e=>{e.exports=["Sichtbarkeit des Symbols für den vorherigen Schlusswert ändern"]},65303:e=>{e.exports=["Session ändern"]},15403:e=>{e.exports=["Sichtbarkeit der Session-Übergänge ändern"]},53438:e=>{e.exports=["Reihen-Stil ändern"]},74488:e=>{e.exports=["Sichtbarkeit der Splits ändern"]},20505:e=>{e.exports=["Zeitzone ändern"]},39028:e=>{e.exports=["Einheit ändern"]},21511:e=>{e.exports=["Sichtbarkeit ändern"]},16698:e=>{e.exports=["Sichtbarkeit im aktuellen Intervall ändern"]},78422:e=>{e.exports=["Sichtbarkeit im aktuellen Intervall und darüber ändern"]},49529:e=>{e.exports=["Sichtbarkeit im aktuellen Intervall und darunter ändern"]},66927:e=>{e.exports=["Sichtbarkeit in allen Intervallen ändern"]},74428:e=>{e.exports=["Stil von {title} ändern"]},72032:e=>{e.exports=["{pointIndex} Punkt ändern"]},65911:e=>{e.exports=["Charts von TradingView"]},5179:e=>{e.exports=["Linien-Klon Werkzeug"]},3195:e=>{e.exports=["Linien-Tool-Gruppe erstellen"]},92659:e=>{e.exports=["Linien-Tool-Gruppe aus Auswahl erstellen"]},81791:e=>{e.exports=["{tool} erstellen"]},63649:e=>{e.exports=["Quellen ausschneiden"]},78755:e=>{e.exports=["{title} ausschneiden"]},99113:e=>{e.exports=["Linien Tool {lineTool} zu Gruppe {name} hinzufügen"]},40242:e=>{e.exports=["Linienwerkzeug(e) zur Gruppe {group} hinzufügen"]},22856:e=>{e.exports=["Diese Finanzkennzahl zum gesamten Layout hinzufügen"]},82388:e=>{e.exports=["Den Indikator dem gesamten Layout hinzufügen"]},94292:e=>{e.exports=["Die Strategie dem gesamten Layout hinzufügen"]},27982:e=>{e.exports=["Das Symbol dem gesamten Layout hinzufügen"]},66568:e=>{e.exports=["Chart-Thema anwenden"]},64034:e=>{e.exports=["alle Chart-Eigenschaften anwenden"]},49037:e=>{e.exports=["Zeichenvorlage verwenden"]},96996:e=>{e.exports=["Werkseinstellungen auf ausgewählte Quellen anwenden"]},44547:e=>{e.exports=["Indikatoren auf das gesamte Layout anwenden"]},26065:e=>{e.exports=["Studienvorlage anwenden {template}"]},58570:e=>{e.exports=["Thema auf Symbolleisten anwenden"]},27195:e=>{e.exports=["Gruppe {title} nach vorne bringen"]},78246:e=>{e.exports=["{title} nach vorne bringen"]},56763:e=>{e.exports=["{title} nach vorne bewegen"]},5607:e=>{e.exports=["von TradingView"]},90621:e=>{e.exports=["Sperre für Datumsbereich"]},12962:e=>{e.exports=["Linie der Ebene löschen"]},63391:e=>{e.exports=["Linien-Tools aus Gruppe {group} ausschliessen"]},59942:e=>{e.exports=["Balkenmuster umdrehen"]},70301:e=>{e.exports=["Verberge {title}"]},91842:e=>{e.exports=["Alarm-Linien mit Label verbergen"]},54781:e=>{e.exports=["alle Zeichenwerkzeuge ausblenden"]},44974:e=>{e.exports=["Markierungen auf Balken verbergen"]},28916:e=>{e.exports=["Intervall verriegeln"]},94245:e=>{e.exports=["Skala invertieren"]},90743:e=>{e.exports=["{title} einfügen"]},53146:e=>{
e.exports=["{title} nach {targetTitle} einfügen"]},74055:e=>{e.exports=["{title} einfügen nach {target}"]},11231:e=>{e.exports=["{title} vor {target} einfügen"]},67176:e=>{e.exports=["{title} vor {targetTitle} einfügen"]},54597:e=>{e.exports=["Standard-Zeichnungsvorlage laden"]},30295:e=>{e.exports=["lade..."]},50193:e=>{e.exports=["Fixiere {title}"]},4963:e=>{e.exports=["Gruppe {group} fixieren"]},68163:e=>{e.exports=["Objekte verriegeln"]},47107:e=>{e.exports=["bewegen"]},11303:e=>{e.exports=["{title} zu neuer Kursskala, links, bewegen"]},45544:e=>{e.exports=["bewege {title} zu neuer rechter Skala"]},81898:e=>{e.exports=["Alle Skalen nach links bewegen"]},22863:e=>{e.exports=["Alle Skalen nach rechts bewegen"]},45356:e=>{e.exports=["Zeichnung(en) bewegen"]},15086:e=>{e.exports=["nach links bewegen"]},61711:e=>{e.exports=["nach rechts bewegen"]},4184:e=>{e.exports=["Skala bewegen"]},74642:e=>{e.exports=["Skalierung für {title} aufheben (Vollbild)"]},45223:e=>{e.exports=["Gruppe {group} unsichtbar machen"]},87927:e=>{e.exports=["Gruppe {group} sichtbar machen"]},62153:e=>{e.exports=["nach unten zusammenführen"]},70746:e=>{e.exports=["zum Fensterbereich zusammenfassen"]},66143:e=>{e.exports=["nach oben zusammenführen"]},81870:e=>{e.exports=["Balkenmuster spiegeln"]},16542:e=>{e.exports=["keine Angabe"]},47222:e=>{e.exports=["Preisskala"]},99042:e=>{e.exports=["Nur den Preis-Chart vergrößern"]},35962:e=>{e.exports=["Skalenzeit"]},68193:e=>{e.exports=["scrollen"]},70009:e=>{e.exports=["Zeitachse scrollen"]},69485:e=>{e.exports=["Auswahlstrategie der Preisskala auf {title} setzen"]},16259:e=>{e.exports=["{title} nach hinten bewegen"]},66781:e=>{e.exports=["{title} nach hinten verschieben"]},4998:e=>{e.exports=["Gruppe {title} nach hinten verschieben"]},64704:e=>{e.exports=["Linientools global teilen"]},77554:e=>{e.exports=["Linienwerkzeuge im Layout freigeben"]},16237:e=>{e.exports=["Alarm-Linien mit Label anzeigen"]},13622:e=>{e.exports=["alle Ideen anzeigen"]},26267:e=>{e.exports=["Ideen von Usern denen ich folge anzeigen"]},40061:e=>{e.exports=["nur meine Ideen anzeigen"]},52010:e=>{e.exports=["im Zeichenmodus bleiben"]},98784:e=>{e.exports=["Zeichnung nicht mehr synchronisieren"]},57011:e=>{e.exports=["Synchronisierung der Linienwerkzeuge beenden"]},92831:e=>{e.exports=["Symbol verriegeln"]},60635:e=>{e.exports=["Uhrzeit synchronisieren"]},99769:e=>{e.exports=["unterstützt von"]},68111:e=>{e.exports=["unterstützt von TradingView"]},96916:e=>{e.exports=["Zeichnung einfügen"]},80611:e=>{e.exports=["Indikator einfügen"]},41601:e=>{e.exports=["{title} einfügen"]},84018:e=>{e.exports=["an linke Skala anheften"]},22615:e=>{e.exports=["An rechte Skala anheften"]},56015:e=>{e.exports=["an Skala anheften {label}"]},33348:e=>{e.exports=["Ebenen neu anordnen"]},15516:e=>{e.exports=["Alle Studien entfernen"]},80171:e=>{e.exports=["Alle Studien und Zeichentools entfernen"]},59211:e=>{e.exports=["Entfernung deaktivierter Leerzeilen-Werkzeuge"]},44656:e=>{e.exports=["Zeichnungen entfernen"]},70653:e=>{
e.exports=["Zeichnungen der Gruppe entfernen"]},66414:e=>{e.exports=["Datenquellen entfernen"]},47637:e=>{e.exports=["Ebene entfernen"]},39859:e=>{e.exports=["{title} entfernen"]},78811:e=>{e.exports=["Linienwerkzeuge der Gruppe {name} entfernen"]},16338:e=>{e.exports=["Gruppe {group} zu {newName} umbenennen"]},30910:e=>{e.exports=["Layoutgröße zurücksetzen"]},21948:e=>{e.exports=["Skalen zurücksetzen"]},55064:e=>{e.exports=["Zeitachse zurücksetzen"]},13034:e=>{e.exports=["Größe des Layouts ändern"]},9608:e=>{e.exports=["Standardeinstellungen wiederherstellen"]},63060:e=>{e.exports=["auf automatische Skalierung umschalten"]},98860:e=>{e.exports=["auf 100 indexiert"]},21203:e=>{e.exports=["Skala verriegeln"]},60166:e=>{e.exports=["auf logarithmische Skalierung umschalten"]},68642:e=>{e.exports=["Prozentskala umschalten"]},33714:e=>{e.exports=["reguläre Skala"]},47122:e=>{e.exports=["Uhrzeit verfolgen"]},28068:e=>{e.exports=["Freigabe von Linientools ausschalten"]},66824:e=>{e.exports=["Objekte entriegeln"]},51114:e=>{e.exports=["Gruppe {group} Fixierung aufheben"]},92421:e=>{e.exports=["{title} entriegeln"]},20057:e=>{e.exports=["zum neuen unteren Fenster anheften"]},52540:e=>{e.exports=["Obere Zusammenführung lösen"]},86949:e=>{e.exports=["Untere Zusammenführung lösen"]},50728:e=>{e.exports=["Update {title} Skript"]},33355:e=>{e.exports=["{count} Balken"]},88841:e=>{e.exports=["{symbol} Finanzdaten von TradingView"]},38641:e=>{e.exports=["{userName} freigegeben für {customer}, {date}"]},59833:e=>{e.exports=["vergrößern"]},19813:e=>{e.exports=["Vergrößern"]},9645:e=>{e.exports=["Verkleinern"]},30572:e=>{e.exports=["Tag","Tage"]},52254:e=>{e.exports=["Stunde","Stunden"]},99062:e=>{e.exports=["Monat","Monate"]},69143:e=>{e.exports=["Minute","Minuten"]},71787:e=>{e.exports=["Sekunde","Sekunden"]},82797:e=>{e.exports=["Bereich","Bereiche"]},47966:e=>{e.exports=["Woche","Wochen"]},99136:e=>{e.exports=["Tick","Ticks"]},18562:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]="Apple Inc",e.exports["#AUDCAD-symbol-description"]=["Australischer Dollar/Kanadischer Dollar"],e.exports["#AUDCHF-symbol-description"]=["Australischer Dollar/Schweizer Franken"],e.exports["#AUDJPY-symbol-description"]=["Australischer Dollar/Japanischer Yen"],e.exports["#AUDNZD-symbol-description"]=["Australischer Dollar/Neuseeländischer Dollar"],e.exports["#AUDRUB-symbol-description"]=["Australischer Dollar/Russischer Rubel"],e.exports["#AUDUSD-symbol-description"]=["Australischer Dollar/U.S. Dollar"],e.exports["#BRLJPY-symbol-description"]=["Brasilianischer Real / Japanischer Yen"],e.exports["#BTCCAD-symbol-description"]=["Bitcoin / Kanadischer Dollar"],e.exports["#BTCCNY-symbol-description"]=["Bitcoin / Chinesischer Yuan"],e.exports["#BTCEUR-symbol-description"]="Bitcoin / Euro",e.exports["#BTCKRW-symbol-description"]=["Bitcoin / Südkoreanischer Won"],e.exports["#BTCRUR-symbol-description"]=["Bitcoin / Rubel"],e.exports["#BTCUSD-symbol-description"]=["Bitcoin / Dollar"],
e.exports["#BVSP-symbol-description"]="Brazil Bovespa Index",e.exports["#CADJPY-symbol-description"]=["Kanadischer Dollar/Japanischer Yen"],e.exports["#CB1!-symbol-description"]="Brent Crude Oil",e.exports["#CHFJPY-symbol-description"]=["Schweizer Franken/Japanischer Yen"],e.exports["#COPPER-symbol-description"]=["Kupfer CFD's"],e.exports["#ES1-symbol-description"]="S&P 500 E-Mini Futures",e.exports["#ESP35-symbol-description"]="IBEX 35 Index",e.exports["#EUBUND-symbol-description"]="Euro Bund",e.exports["#EURAUD-symbol-description"]=["Euro / Australischer Dollar"],e.exports["#EURBRL-symbol-description"]=["Euro / Brasilianischer Real"],e.exports["#EURCAD-symbol-description"]=["Euro / Kanadischer Dollar"],e.exports["#EURCHF-symbol-description"]=["Euro / Schweizer Franken"],e.exports["#EURGBP-symbol-description"]=["Euro / Britisches Pfund"],e.exports["#EURJPY-symbol-description"]=["Euro / Japanischer Yen"],e.exports["#EURNZD-symbol-description"]=["Euro / Neuseeländischer Dollar"],e.exports["#EURRUB-symbol-description"]=["EURO / RUSSISCHER RUBEL"],e.exports["#EURRUB_TOM-symbol-description"]=["EUR/RUB TOM"],e.exports["#EURSEK-symbol-description"]=["Euro / Schwedische Krone"],e.exports["#EURTRY-symbol-description"]=["Euro  / Neue Türkische Lira"],e.exports["#EURUSD-symbol-description"]=["Euro / US-Dollar"],e.exports["#EUSTX50-symbol-description"]="Euro Stoxx 50 Index",e.exports["#FRA40-symbol-description"]="CAC 40 Index",e.exports["#GB10-symbol-description"]=["Britische Staatsanleihe über 10 Jahre"],e.exports["#GBPAUD-symbol-description"]=["Britisches Pfund / Australischer Dollar"],e.exports["#GBPCAD-symbol-description"]=["Britisches Pfund / Kanadischer Dollar"],e.exports["#GBPCHF-symbol-description"]=["Britisches Pfund / Schweizer Franken"],e.exports["#GBPEUR-symbol-description"]=["PFUND STERLING / EURO"],e.exports["#GBPJPY-symbol-description"]=["Britisches Pfund / Japanischer Yen"],e.exports["#GBPNZD-symbol-description"]=["Britisches Pfund / Neuseeländischer Dollar"],e.exports["#GBPRUB-symbol-description"]=["Pfund Sterling / Russischer Rubel"],e.exports["#GBPUSD-symbol-description"]=["Britisches Pfund / US-Dollar"],e.exports["#GER30-symbol-description"]="DAX Index",e.exports["#GOOGL-symbol-description"]="Alphabet Inc (Google) Class A",e.exports["#ITA40-symbol-description"]="FTSE MIB Index",e.exports["#JPN225-symbol-description"]="Nikkei 225 Index",e.exports["#JPYKRW-symbol-description"]=["Japanischer Yen / Südkoreanischer Won"],e.exports["#JPYRUB-symbol-description"]=["Japanischer Yen / Russischer Rubel"],e.exports["#KA1-symbol-description"]=["Zucker #11 Futures"],e.exports["#KG1-symbol-description"]=["Baumwolle Futures"],e.exports["#KT1-symbol-description"]="Key Tronic Corр.",e.exports["#LKOH-symbol-description"]="LUKOIL",e.exports["#LTCBTC-symbol-description"]="Litecoin / Bitcoin",e.exports["#MGNT-symbol-description"]="Magnit",e.exports["#MICEX-symbol-description"]=["MICEX INDEX"],e.exports["#MNOD_ME.EQRP-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",
e.exports["#MSFT-symbol-description"]="Microsoft Corp.",e.exports["#NAS100-symbol-description"]="US 100 Cash CFD",e.exports["#NGAS-symbol-description"]=["Erdgas (Henry Hub)"],e.exports["#NKY-symbol-description"]="Nikkei 225 Index",e.exports["#NZDJPY-symbol-description"]=["Neuseeländischer Dollar / Japanischer Yen"],e.exports["#NZDUSD-symbol-description"]=["Neuseeländischer Dollar / US-Dollar"],e.exports["#RB1-symbol-description"]="RBOB Gasoline Futures",e.exports["#RTS-symbol-description"]=["Russischer RTS Index"],e.exports["#SBER-symbol-description"]="SBERBANK",e.exports["#SPX500-symbol-description"]="S&P 500 Index",e.exports["#TWTR-symbol-description"]=["TWITTER INC"],e.exports["#UK100-symbol-description"]=["FTSE 100 Englischer Leitindex"],e.exports["#USDBRL-symbol-description"]=["US Dollar / Brasilianischer Real"],e.exports["#USDCAD-symbol-description"]=["US-Dollar / Kanadischer Dollar"],e.exports["#USDCHF-symbol-description"]=["US-Dollar / Schweizer Franken"],e.exports["#USDCNY-symbol-description"]=["US DOLLAR / YUAN RENMINBI"],e.exports["#USDDKK-symbol-description"]=["US DOLLAR / DÄNISCHE KRONE"],e.exports["#USDHKD-symbol-description"]=["U.S.-Dollar/Hongkong-Dollar"],e.exports["#USDIDR-symbol-description"]=["US Dollar / Rupie"],e.exports["#USDINR-symbol-description"]=["US-Dollar / Indische Rupie"],e.exports["#USDJPY-symbol-description"]=["US-Dollar / Japanischer Yen"],e.exports["#USDKRW-symbol-description"]=["U.S. Dollar / Südkorea"],e.exports["#USDMXN-symbol-description"]=["U.S. Dollar/Mexikanischer Peso"],e.exports["#USDPHP-symbol-description"]=["US Dollar / Philippinischer Peso"],e.exports["#USDRUB-symbol-description"]=["U.S. Dollar / Russischer Rubel"],e.exports["#USDRUB_TOM-symbol-description"]=["USD/RUB TOM"],e.exports["#USDSEK-symbol-description"]=["US-Dollar / Schwedische Krone"],e.exports["#USDSGD-symbol-description"]=["US-DOLLAR / SINGAPUR-DOLLAR"],e.exports["#USDTRY-symbol-description"]=["US-Dollar / Türkische Neue Lira"],e.exports["#VTBR-symbol-description"]="VTB",e.exports["#XAGUSD-symbol-description"]=["Silber / US-Dollar"],e.exports["#XAUUSD-symbol-description"]="Gold Spot / U.S. Dollar",e.exports["#XPDUSD-symbol-description"]=["Palladium CFD's"],e.exports["#XPTUSD-symbol-description"]=["Platinum / US-Dollar"],e.exports["#ZS1-symbol-description"]=["Sojabohnen Futures - ECBT"],e.exports["#ZW1-symbol-description"]=["Weizen Futures - ECBT"],e.exports["#BTCGBP-symbol-description"]=["Bitcoin / Britisches Pfund"],e.exports["#MICEXINDEXCF-symbol-description"]=["MICEX Russia Index"],e.exports["#BTCAUD-symbol-description"]=["Bitcoin / Australischer Dollar"],e.exports["#BTCJPY-symbol-description"]=["Bitcoin / Japanischer Yen"],e.exports["#BTCBRL-symbol-description"]=["Bitcoin / Brasilianischer Real"],e.exports["#PT10-symbol-description"]=["Portugiesische Staatsanleihe 10-Jahre"],e.exports["#TXSX-symbol-description"]="TSX 60 Index",e.exports["#VIXC-symbol-description"]="TSX 60 VIX Index",e.exports["#USDPLN-symbol-description"]=["USD/PLN"],e.exports["#EURPLN-symbol-description"]=["EUR/PLN"],
e.exports["#BTCPLN-symbol-description"]=["Bitcoin / Polnischer Zloty"],e.exports["#CAC40-symbol-description"]="CAC 40 Index",e.exports["#XBTCAD-symbol-description"]=["Bitcoin / Kanadischer Dollar"],e.exports["#ITI2!-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIF2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIF2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIF2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIG2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIG2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIG2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIH2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIH2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIH2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIJ2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIJ2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIJ2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIK2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIK2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIK2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIM2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIM2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIM2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIM2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIN2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIN2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIN2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIN2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIQ2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIQ2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIQ2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIQ2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIU2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIU2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIU2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIU2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIV2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIV2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIV2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIV2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIX2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIX2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIX2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIX2020-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIZ2017-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIZ2018-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIZ2019-symbol-description"]=["Eisenerz Futures"],e.exports["#ITIZ2020-symbol-description"]=["Eisenerz Futures"],e.exports["#AMEX:GXF-symbol-description"]="Global x FTSE Nordic Region ETF",
e.exports["#ASX:XAF-symbol-description"]="S&P/ASX All Australian 50 Index",e.exports["#ASX:XAT-symbol-description"]="S&P/ASX All Australian 200 Index",e.exports["#BIST:XU100-symbol-description"]="BIST 100 Index",e.exports["#GPW:WIG20-symbol-description"]="WIG20 Index",e.exports["#INDEX:JKSE-symbol-description"]="Jakarta Composite Index",e.exports["#INDEX:KLSE-symbol-description"]="Bursa Malaysia KLCI Index",e.exports["#INDEX:NZD-symbol-description"]="NZX 50 Index",e.exports["#INDEX:STI-symbol-description"]="STI Index",e.exports["#INDEX:XLY0-symbol-description"]="Shanghai Composite Index",e.exports["#MOEX:MICEXINDEXCF-symbol-description"]="MOEX Russia Index",e.exports["#NYMEX:KT1!-symbol-description"]=["Kaffee Futures"],e.exports["#OANDA:NATGASUSD-symbol-description"]=["Erdgas CFD's"],e.exports["#OANDA:USDPLN-symbol-description"]=["USD/PLN"],e.exports["#TSX:TX60-symbol-description"]="S&P/TSX 60 Index",e.exports["#TSX:VBU-symbol-description"]=["Vanguard US Aggregate Bond Index ETF (CAD-hedged) UN"],e.exports["#TSX:VIXC-symbol-description"]="S&P/TSX 60 VIX Index",e.exports["#TVC:CAC40-symbol-description"]="CAC 40 Index",e.exports["#TVC:ES10-symbol-description"]=["Spanische Staatsanleihe 10-Jahre"],e.exports["#TVC:EUBUND-symbol-description"]="Euro Bund",e.exports["#TVC:GB02-symbol-description"]=["UK Staatsanleihe 2-Jahre"],e.exports["#TVC:GB10-symbol-description"]=["UK Staatsanleihe 10-Jahre"],e.exports["#TVC:GOLD-symbol-description"]=["GOLD CFD's (US$ / OZ)"],e.exports["#TVC:ID03-symbol-description"]=["Indonesische Staatsanleihe 3-Jahre"],e.exports["#TVC:ID10-symbol-description"]=["Indonesische Staatsanleihe 10-Jahre"],e.exports["#TVC:PALLADIUM-symbol-description"]=["Palladium CFD's (US$ / OZ)"],e.exports["#TVC:PT10-symbol-description"]=["Portugiesische Staatsanleihe 10-Jahre"],e.exports["#TVC:SILVER-symbol-description"]=["Silber CFD's (US$ / OZ)"],e.exports["#TVC:RUT-symbol-description"]="Russell 2000 Index",e.exports["#TSX:TSX-symbol-description"]="S&P/TSX Composite Index",e.exports["#OANDA:CH20CHF-symbol-description"]="Swiss 20 Index",e.exports["#TVC:SHCOMP-symbol-description"]="Shanghai Composite Index",e.exports["#NZX:ALLC-symbol-description"]=["S&P/NZX ALL Index ( Capital Index )"],e.exports["#AMEX:SHYG-symbol-description"]="Shares 0-5 YEAR High Yield Corporate Bond ETF",e.exports["#TVC:AU10-symbol-description"]=["Australische Staatsanleihe 10-Jahre"],e.exports["#TVC:CN10-symbol-description"]=["Chinesische Staatsanleihe 10-Jahre"],e.exports["#TVC:KR10-symbol-description"]=["Koreanische Staatsanleihe 10-Jahre"],e.exports["#NYMEX:RB1!-symbol-description"]="RBOB Gasoline Futures",e.exports["#NYMEX:HO1!-symbol-description"]="NY Harbor ULSD Futures",e.exports["#NYMEX:AEZ1!-symbol-description"]="NY Ethanol Futures",e.exports["#OANDA:XCUUSD-symbol-description"]=["Kupfer CFD's (US$ / lb)"],e.exports["#COMEX:ZA1!-symbol-description"]=["Zink Futures"],e.exports["#CBOT:ZW1!-symbol-description"]=["Weizen Futures"],e.exports["#NYMEX:KA1!-symbol-description"]=["Zucker #11 Futures"],
e.exports["#CBOT:QBC1!-symbol-description"]=["Mais Futures"],e.exports["#CME:E61!-symbol-description"]="Euro Futures",e.exports["#CME:B61!-symbol-description"]=["Britische Pfund Futures"],e.exports["#CME:QJY1!-symbol-description"]=["Japanische Yen Futures"],e.exports["#CME:A61!-symbol-description"]=["Australische Dollar Futures"],e.exports["#CME:D61!-symbol-description"]=["Kanadische Dollar Futures"],e.exports["#CME:SP1!-symbol-description"]="S&P 500 Futures",e.exports["#CME_MINI:NQ1!-symbol-description"]=["NASDAQ 100 E-MINI Futures"],e.exports["#CBOT_MINI:YM1!-symbol-description"]=["E-MINI DOW JONES ($5) Futures"],e.exports["#CME:NY1!-symbol-description"]="NIKKEI 225 Futures",e.exports["#EUREX:DY1!-symbol-description"]="DAX Index",e.exports["#CME:IF1!-symbol-description"]="IBOVESPA Index Futures-US$",e.exports["#CBOT:TY1!-symbol-description"]="10 Year T-Note Futures",e.exports["#CBOT:FV1!-symbol-description"]="5 Year T-Note Futures",e.exports["#CBOT:ZE1!-symbol-description"]="Treasury Notes - 3 Year Futures",e.exports["#CBOT:TU1!-symbol-description"]="2 Year T-Note Futures",e.exports["#CBOT:FF1!-symbol-description"]="30-Day FED Funds Interest Rate Futures",e.exports["#CBOT:US1!-symbol-description"]="T-Bond Futures",e.exports["#TVC:EXY-symbol-description"]=["Euro Währungsindex"],e.exports["#TVC:JXY-symbol-description"]=["Japanischer-Yen Währungsindex"],e.exports["#TVC:BXY-symbol-description"]=["Britischer-Pfund Währungsindex"],e.exports["#TVC:AXY-symbol-description"]=["Australischer-Dollar Währungsindex"],e.exports["#TVC:CXY-symbol-description"]=["Kanadischer-Dollar Währungsindex"],e.exports["#FRED:GDP-symbol-description"]=["Bruttoinlandsprodukt, 1-Dezimal"],e.exports["#FRED:UNRATE-symbol-description"]=["Arbeitslosenquote Zivil"],e.exports["#FRED:POP-symbol-description"]=["Gesamtbevölkerung: alle Altersgruppen einschließlich der Streitkräfte im Ausland"],e.exports["#ETHUSD-symbol-description"]=["Ethereum / Dollar"],e.exports["#BMFBOVESPA:IBOV-symbol-description"]="IBovespa Index",e.exports["#BMFBOVESPA:IBRA-symbol-description"]="IBrasil Index",e.exports["#BMFBOVESPA:IBXL-symbol-description"]="IBRX 50 Index",e.exports["#COMEX:HG1!-symbol-description"]=["Kupfer Futures"],e.exports["#INDEX:HSCE-symbol-description"]=["Hang Seng Chinenischer Leitindex"],e.exports["#NYMEX:CL1!-symbol-description"]="Light Crude Oil Futures",e.exports["#OTC:IHRMF-symbol-description"]="Ishares MSCI Japan SHS",e.exports["#TVC:DAX-symbol-description"]=["Index der 30-größten börsennotierten deutschen Unternehmen"],e.exports["#TVC:DE10-symbol-description"]=["Deutsche Staatsanleihen 10-Jahre"],e.exports["#TVC:DJI-symbol-description"]="Dow Jones Industrial Average Index",e.exports["#TVC:DXY-symbol-description"]=["US-Dollar Währungsindex"],e.exports["#TVC:FR10-symbol-description"]=["Französiche Staatsanleihe 10-Jahre"],e.exports["#TVC:HSI-symbol-description"]="Hang Seng Index",e.exports["#TVC:IBEX35-symbol-description"]="IBEX 35 Index",e.exports["#FX:AUS200-symbol-description"]="S&P/ASX Index",
e.exports["#AMEX:SHY-symbol-description"]="Ishares 1-3 Year Treasury Bond ETF",e.exports["#ASX:XJO-symbol-description"]="S&P/ASX 200 Index",e.exports["#BSE:SENSEX-symbol-description"]="S&P BSE Sensex Index",e.exports["#INDEX:MIB-symbol-description"]="MIB Index",e.exports["#INDEX:MOY0-symbol-description"]="Euro Stoxx 50 Index",e.exports["#MOEX:RTSI-symbol-description"]="RTS Index",e.exports["#NSE:NIFTY-symbol-description"]="Nifty 50 Index",e.exports["#NYMEX:NG1!-symbol-description"]="Natural Gas Futures",e.exports["#NYMEX:ZC1!-symbol-description"]=["Mais Futures"],e.exports["#TVC:IN10-symbol-description"]=["India Staatsanleihe 10-Jahre"],e.exports["#TVC:IT10-symbol-description"]=["Italienische Staatsanleihe 10-Jahre"],e.exports["#TVC:JP10-symbol-description"]=["Japanische Staatsanleihe 10-Jahre"],e.exports["#TVC:NDX-symbol-description"]="US 100 Index",e.exports["#TVC:NI225-symbol-description"]=["NIKKEI 225"],e.exports["#TVC:SPX-symbol-description"]=["S&P 500"],e.exports["#TVC:SX5E-symbol-description"]="Euro Stoxx 50 Index",e.exports["#TVC:TR10-symbol-description"]=["Türkei Staatsanleihe 10-Jahre"],e.exports["#TVC:UKOIL-symbol-description"]=["Brent Rohöl CFD's"],e.exports["#TVC:UKX-symbol-description"]="UK 100 Index",e.exports["#TVC:US02-symbol-description"]=["US Staatsanleihe 2-Jahre"],e.exports["#TVC:US05-symbol-description"]=["US Staatsanleihe 5-Jahre"],e.exports["#TVC:US10-symbol-description"]=["US Staatsanleihe 10-Jahre"],e.exports["#TVC:USOIL-symbol-description"]=["WTI Rohöl CFD's"],e.exports["#NYMEX:ITI1!-symbol-description"]="Iron Ore Futures",e.exports["#NASDAQ:SHY-symbol-description"]="Ishares 1-3 Year Treasury Bond ETF",e.exports["#AMEX:ALD-symbol-description"]="WisdomTree Asia Local Debt ETF",e.exports["#NASDAQ:AMD-symbol-description"]="Advanced Micro Devices Inc",e.exports["#NYSE:BABA-symbol-description"]="Alibaba Group Holdings Ltd.",e.exports["#ICEEUR:CB-symbol-description"]=["Rohöl Brent"],e.exports["#ICEEUR:CB1!-symbol-description"]=["Brent Rohöl"],e.exports["#ICEUSA:CC-symbol-description"]=["Kakao"],e.exports["#NYMEX:CL-symbol-description"]=["Rohöl WTI"],e.exports["#ICEUSA:CT-symbol-description"]=["Baumwolle #2"],e.exports["#NASDAQ:CTRV-symbol-description"]=["CONTRAVIR PHARMACEUTICALS INC"],e.exports["#CME:DL-symbol-description"]=["Klasse III Milch"],e.exports["#NYSE:F-symbol-description"]="FORD MTR CO DEL",e.exports["#MOEX:GAZP-symbol-description"]="GAZPROM",e.exports["#COMEX:GC-symbol-description"]="Gold",e.exports["#CME:GF-symbol-description"]=["Mastrinder"],e.exports["#CME:HE-symbol-description"]=["Schweine (mager)"],e.exports["#NASDAQ:IEF-symbol-description"]=["Ishares 7-10 Year Treasury BondETF"],e.exports["#NASDAQ:IEI-symbol-description"]="Ishares 3-7 Year Treasury Bond ETF",e.exports["#NYMEX:KA1-symbol-description"]=["Zucker #11 Futures"],e.exports["#ICEUSA:KC-symbol-description"]=["Kaffee"],e.exports["#NYMEX:KG1-symbol-description"]=["Baumwolle Futures"],e.exports["#FWB:KT1-symbol-description"]="Key Tronic Corр.",e.exports["#CME:LE-symbol-description"]=["Lebendvieh"],
e.exports["#ICEEUR:LO-symbol-description"]=["ICE-Heizöl"],e.exports["#CME:LS-symbol-description"]=["Bauholz"],e.exports["#MOEX:MGNT-symbol-description"]="MAGNIT",e.exports["#LSIN:MNOD-symbol-description"]="ADR GMK NORILSKIYNIKEL ORD SHS [REPO]",e.exports["#NYMEX:NG-symbol-description"]=["Erdgas"],e.exports["#ICEUSA:OJ-symbol-description"]=["Orangensaft"],e.exports["#NYMEX:PA-symbol-description"]="Palladium",e.exports["#NYSE:PBR-symbol-description"]="PETROLEO BRASILEIRO SA PETROBR",e.exports["#NYMEX:PL-symbol-description"]="Platinum",e.exports["#COMEX_MINI:QC-symbol-description"]="E-Mini Copper",e.exports["#NYMEX:RB-symbol-description"]="Gasoline RBOB",e.exports["#NYMEX:RB1-symbol-description"]="RBOB Gasoline Futures",e.exports["#MOEX:SBER-symbol-description"]="SBERBANK",e.exports["#AMEX:SCHO-symbol-description"]="Schwab Short-Term U.S. Treasury ETF",e.exports["#COMEX:SI-symbol-description"]=["Silber"],e.exports["#NASDAQ:TLT-symbol-description"]="Ishares 20+ Year Treasury Bond ETF",e.exports["#TVC:VIX-symbol-description"]=["Volatilitätsindex S&P 500"],e.exports["#MOEX:VTBR-symbol-description"]="VTB",e.exports["#COMEX:ZA-symbol-description"]=["Zink"],e.exports["#CBOT:ZC-symbol-description"]=["Mais"],e.exports["#CBOT:ZK-symbol-description"]="Ethanol Futures",e.exports["#CBOT:ZL-symbol-description"]=["Sojaöl"],e.exports["#CBOT:ZO-symbol-description"]=["Hafer"],e.exports["#CBOT:ZR-symbol-description"]=["Grober Reis"],e.exports["#CBOT:ZS-symbol-description"]=["Sojabohnen"],e.exports["#CBOT:ZS1-symbol-description"]=["Sojabohnen Futures"],e.exports["#CBOT:ZW-symbol-description"]=["Weizen"],e.exports["#CBOT:ZW1-symbol-description"]=["Weizen Futures - ECBT"],e.exports["#NASDAQ:ITI-symbol-description"]="Iteris Inc",e.exports["#NYMEX:ITI2!-symbol-description"]=["Eisenerz Futures"],e.exports["#CADUSD-symbol-description"]=["Kanadischer Dollar / US-Dollar"],e.exports["#CHFUSD-symbol-description"]=["Schweizer Franken / US-Dollar"],e.exports["#GPW:ACG-symbol-description"]="Acautogaz",e.exports["#JPYUSD-symbol-description"]=["Japanischer Yen / US-Dollar"],e.exports["#USDAUD-symbol-description"]=["US-Dollar / Australischer Dollar"],e.exports["#USDEUR-symbol-description"]=["US-Dollar / Euro"],e.exports["#USDGBP-symbol-description"]=["US-Dollar / Pfund Sterling"],e.exports["#USDNZD-symbol-description"]=["US Dollar / Neuseeland Dollar"],e.exports["#UKOIL-symbol-description"]=["Rohöl CFD's (Brent)"],e.exports["#USOIL-symbol-description"]=["Rohöl CFD's (WTI)"],e.exports["#US30-symbol-description"]="Dow Jones Industrial Average Index",e.exports["#BCHUSD-symbol-description"]=["Bitcoin Cash / Dollar"],e.exports["#ETCUSD-symbol-description"]=["Ethereum Classic / Dollar"],e.exports["#GOOG-symbol-description"]="Alphabet Inc (Google) Class C",e.exports["#LTCUSD-symbol-description"]=["Litecoin / Dollar"],e.exports["#XRPUSD-symbol-description"]=["XRP / US Dollar"],e.exports["#SP:SPX-symbol-description"]="S&P 500 Index",e.exports["#ETCBTC-symbol-description"]="Ethereum Classic / Bitcoin",e.exports["#ETHBTC-symbol-description"]="Ethereum / Bitcoin",
e.exports["#XRPBTC-symbol-description"]="XRP / Bitcoin",e.exports["#TVC:US30-symbol-description"]=["US-Staatsanleihen 30 Jahre"],e.exports["#COMEX:SI1!-symbol-description"]=["Silber Futures"],e.exports["#BTGUSD-symbol-description"]="Bitcoin Gold / U.S. Dollar",e.exports["#IOTUSD-symbol-description"]="IOTA / U.S. Dollar",e.exports["#CME:BTC1!-symbol-description"]="Bitcoin CME Futures",e.exports["#COMEX:GC1!-symbol-description"]="Gold Futures",e.exports["#CORNUSD-symbol-description"]=["CFDs auf Mais"],e.exports["#COTUSD-symbol-description"]=["CFDs auf Baumwolle"],e.exports["#DJ:DJA-symbol-description"]="Dow Jones Composite Average Index",e.exports["#DJ:DJI-symbol-description"]=["Dow Jones Industrial Average"],e.exports["#ETHEUR-symbol-description"]="Ethereum / Euro",e.exports["#ETHGBP-symbol-description"]=["Ethereum / Britisches Pfund"],e.exports["#ETHJPY-symbol-description"]=["Ethereum / Japanischer Jen"],e.exports["#EURNOK-symbol-description"]=["Euro / Norwegische Krone"],e.exports["#GBPPLN-symbol-description"]=["Britisches Pfund / Polnischer Zloty"],e.exports["#MOEX:BR1!-symbol-description"]="Brent Oil Futures",e.exports["#NYMEX:KG1!-symbol-description"]=["Baumwoll-Futures"],e.exports["#NYMEX:PL1!-symbol-description"]=["Platin Futures"],e.exports["#SOYBNUSD-symbol-description"]=["CFDs auf Sojabohnen"],e.exports["#SUGARUSD-symbol-description"]=["CFDs auf Zucker"],e.exports["#TVC:IXIC-symbol-description"]="US Composite Index",e.exports["#TVC:RU-symbol-description"]="Russell 1000 Index",e.exports["#USDZAR-symbol-description"]=["U.S. Dollar / Süd Afrikanischer Rand"],e.exports["#WHEATUSD-symbol-description"]=["CFDs auf Weizen"],e.exports["#XRPEUR-symbol-description"]="XRP / Euro",e.exports["#CBOT:S1!-symbol-description"]=["Sojabohnen Futures"],e.exports["#SP:MID-symbol-description"]="S&P 400 Index",e.exports["#TSX:XCUUSD-symbol-description"]=["CFDs auf Kupfer"],e.exports["#TVC:NYA-symbol-description"]="NYSE Composite Index",e.exports["#TVC:PLATINUM-symbol-description"]=["CFDs auf Platin (US$ / OZ)"],e.exports["#TVC:SSMI-symbol-description"]=["Schweizer Marktindex"],e.exports["#TVC:SXY-symbol-description"]=["Schweizer Franken Währungsindex"],e.exports["#TVC:RUI-symbol-description"]="Russell 1000 Index",e.exports["#MOEX:RI1!-symbol-description"]="RTS Index Futures",e.exports["#MOEX:MX1!-symbol-description"]="MICEX Index Futures",e.exports["#CBOE:BG1!-symbol-description"]="Bitcoin CBOE Futures",e.exports["#TVC:MY10-symbol-description"]=["Malaysische Staatsanleihen 10 YR"],e.exports["#CME:S61!-symbol-description"]=["Schweizer Franken Futures"],e.exports["#TVC:DEU30-symbol-description"]="DAX Index",e.exports["#BCHEUR-symbol-description"]="Bitcoin Cash / Euro",e.exports["#TVC:ZXY-symbol-description"]="New Zealand Dollar Currency Index",e.exports["#MIL:FTSEMIB-symbol-description"]="FTSE MIB Index",e.exports["#XETR:DAX-symbol-description"]="DAX Index",e.exports["#MOEX:IMOEX-symbol-description"]="MOEX Russia Index",e.exports["#FX:US30-symbol-description"]="Dow Jones Industrial Average Index",
e.exports["#MOEX:RUAL-symbol-description"]="United Company RUSAL PLC",e.exports["#MOEX:MX2!-symbol-description"]="MICEX Index Futures",e.exports["#NEOUSD-symbol-description"]="NEO / U.S. Dollar",e.exports["#XMRUSD-symbol-description"]="Monero / U.S. Dollar",e.exports["#ZECUSD-symbol-description"]=["Zach / U.S. Dollar"],e.exports["#TVC:CAC-symbol-description"]="CAC 40 Index",e.exports["#NASDAQ:ZS-symbol-description"]="Zscaler Inc",e.exports["#TVC:GB10Y-symbol-description"]=["Britische Staatsanleihen 10-Jahre"],e.exports["#TVC:AU10Y-symbol-description"]=["Australische Staatsanleihen 10-Jahre"],e.exports["#TVC:CN10Y-symbol-description"]=["Chinesische Staatsanleihen 10-Jahre"],e.exports["#TVC:DE10Y-symbol-description"]=["Deutsche Staatsanleihen 10-Jahre"],e.exports["#TVC:ES10Y-symbol-description"]=["Spanische Staatsanleihen 10-Jahre"],e.exports["#TVC:FR10Y-symbol-description"]=["Französiche Staatsanleihen 10-Jahre"],e.exports["#TVC:IN10Y-symbol-description"]=["Indische Staatsanleihen 10 Jahre"],e.exports["#TVC:IT10Y-symbol-description"]=["Italienische Staatsanleihen 10 Jahre"],e.exports["#TVC:JP10Y-symbol-description"]=["Japanische Staatsanleihen 10 Jahre"],e.exports["#TVC:KR10Y-symbol-description"]=["Koreanische Staatsanleihen 10-Jahre"],e.exports["#TVC:MY10Y-symbol-description"]=["Malaysische Staatsanleihen 10 Jahre"],e.exports["#TVC:PT10Y-symbol-description"]=["Portugiesische Staatsanleihen 10-Jahre"],e.exports["#TVC:TR10Y-symbol-description"]=["Türkische Staatsanleihen 10 Jahre"],e.exports["#TVC:US02Y-symbol-description"]=["US Staatsanleihen 2 Jahre"],e.exports["#TVC:US05Y-symbol-description"]=["US Staatsanleihen 5 Jahre"],e.exports["#TVC:US10Y-symbol-description"]=["US Staatsanleihen 10 Jahre"],e.exports["#INDEX:TWII-symbol-description"]="Taiwan Weighted Index",e.exports["#CME:J61!-symbol-description"]=["Japanischer Jen Futures"],e.exports["#CME_MINI:J71!-symbol-description"]=["Japanischer Jen E-mini Futures"],e.exports["#CME_MINI:WM1!-symbol-description"]=["E-Micro Japanischer Jen / U.S. Dollar Futures"],e.exports["#CME:M61!-symbol-description"]=["Mexikanischer Peso Futures"],e.exports["#CME:T61!-symbol-description"]=["Südafrikanischer Rand Futures"],e.exports["#CME:SK1!-symbol-description"]=["Schwedische Krone Futures"],e.exports["#CME:QT1!-symbol-description"]=["Chinesischer Chinese Renminbi / U.S. Dollar Futures"],e.exports["#COMEX:AUP1!-symbol-description"]="Aluminum MW U.S. Transaction Premium Platts (25MT) Futures",e.exports["#CME:L61!-symbol-description"]=["Brasilianischer Real Futures"],e.exports["#CME:WP1!-symbol-description"]=["Polnischer Zloty Futures"],e.exports["#CME:N61!-symbol-description"]=["Neuseeland Dollar Futures"],e.exports["#CME_MINI:MG1!-symbol-description"]=["E-micro Australischer Dollar / U.S. Dollar Futures"],e.exports["#CME_MINI:WN1!-symbol-description"]=["E-micro Schweizer Franken / U.S. Dollar Futures"],e.exports["#CME_MINI:MF1!-symbol-description"]=["E-Micro Euro / U.S. Dollar Futures"],e.exports["#CME_MINI:E71!-symbol-description"]="Euro E-mini Futures",
e.exports["#CBOT:ZK1!-symbol-description"]=["Denaturierter Ethanol Kraftstoff Futures"],e.exports["#CME_MINI:MB1!-symbol-description"]=["E-micro Britisches Pfund / U.S. Dollar Futures"],e.exports["#NYMEX_MINI:QU1!-symbol-description"]=["E-mini Benzin Futures"],e.exports["#NYMEX_MINI:QX1!-symbol-description"]=["E-mini Heizöl Futures"],e.exports["#COMEX_MINI:QC1!-symbol-description"]=["E-mini Kupfer Futures"],e.exports["#NYMEX_MINI:QG1!-symbol-description"]=["E-mini Erdgas Futures"],e.exports["#CME:E41!-symbol-description"]=["U.S. Dollar / Türkische Lira Futures"],e.exports["#COMEX_MINI:QI1!-symbol-description"]=["Silber (Mini) Futures"],e.exports["#CME:DL1!-symbol-description"]=["Milch, Klasse III Futures"],e.exports["#NYMEX:UX1!-symbol-description"]=["Uran Futures"],e.exports["#CBOT:BO1!-symbol-description"]=["Sojabohnen Öl Futures"],e.exports["#CME:HE1!-symbol-description"]=["Mager-Schwein Futures"],e.exports["#NYMEX:IAC1!-symbol-description"]=["Newcastle Kohle Futures"],e.exports["#NYMEX_MINI:QM1!-symbol-description"]=["E-mini  Leicht-Rohöl Futures"],e.exports["#NYMEX:JMJ1!-symbol-description"]=["Mini Brent Finanzmarkt Futures"],e.exports["#COMEX:AEP1!-symbol-description"]=["Europäische Premium Aluminium Futures"],e.exports["#CBOT:ZQ1!-symbol-description"]=["30 Tages Federal Funds Zinsraten Futures"],e.exports["#CME:LE1!-symbol-description"]=["Lebendvieh Futures"],e.exports["#CME:UP1!-symbol-description"]=["Schweizer Franken / Japanischer Jen Futures"],e.exports["#CBOT:ZN1!-symbol-description"]=["10 Jahres T-Note Futures"],e.exports["#CBOT:ZB1!-symbol-description"]="T-Bond Futures",e.exports["#CME:GF1!-symbol-description"]=["Mastrind Futures"],e.exports["#CBOT:UD1!-symbol-description"]="Ultra T-Bond Futures",e.exports["#CME:I91!-symbol-description"]="CME Housing Futures — Washington DC",e.exports["#CBOT:ZO1!-symbol-description"]=["Hafer Futures"],e.exports["#CBOT:ZM1!-symbol-description"]=["Sojabohnen Mehl Futures"],e.exports["#CBOT_MINI:XN1!-symbol-description"]=["Mais Mini Futures"],e.exports["#CBOT:ZC1!-symbol-description"]=["Mais Futures"],e.exports["#CME:LS1!-symbol-description"]=["Bauholz Futures"],e.exports["#CBOT_MINI:XW1!-symbol-description"]=["Weizen Mini Futures"],e.exports["#CBOT_MINI:XK1!-symbol-description"]=["Sojabohnen Mini Futures"],e.exports["#CBOT:ZS1!-symbol-description"]=["Sojabohnen Futures"],e.exports["#NYMEX:PA1!-symbol-description"]="Palladium Futures",e.exports["#CME:FTU1!-symbol-description"]="E-mini FTSE 100 Index USD Futures",e.exports["#CBOT:ZR1!-symbol-description"]=["Reis Futures"],e.exports["#COMEX_MINI:GR1!-symbol-description"]="Gold (E-micro) Futures",e.exports["#COMEX_MINI:QO1!-symbol-description"]="Gold (Mini) Futures",e.exports["#CME_MINI:RL1!-symbol-description"]=["E-mini Russel 1000 Futures"],e.exports["#CME_MINI:EW1!-symbol-description"]="S&P 400 Midcap E-mini Futures",e.exports["#COMEX:LD1!-symbol-description"]=["Blei Futures"],e.exports["#CME_MINI:ES1!-symbol-description"]="S&P 500 E-mini Futures",e.exports["#TVC:SA40-symbol-description"]="South Africa Top 40 Index",
e.exports["#BMV:ME-symbol-description"]="IPC Mexico Index",e.exports["#BCBA:IMV-symbol-description"]="MERVAL Index",e.exports["#HSI:HSI-symbol-description"]="Hang Seng Index",e.exports["#BVL:SPBLPGPT-symbol-description"]="S&P / BVL Peru General Index (PEN)",e.exports["#EGX:EGX30-symbol-description"]="EGX 30 Price Return Index",e.exports["#BVC:IGBC-symbol-description"]="Indice General de la Bolsa de Valores de Colombia",e.exports["#TWSE:TAIEX-symbol-description"]="Taiwan Capitalization Weighted Stock Index",e.exports["#QSE:GNRI-symbol-description"]="QE Index",e.exports["#BME:IBC-symbol-description"]="IBEX 35 Index",e.exports["#NZX:NZ50G-symbol-description"]=["S&P / NZX 50 Index"],e.exports["#SIX:SMI-symbol-description"]="Swiss Market Index",e.exports["#SZSE:399001-symbol-description"]="SZSE Component Index",e.exports["#TADAWUL:TASI-symbol-description"]=["Tadawul Aktienindex"],e.exports["#IDX:COMPOSITE-symbol-description"]="IDX Composite Index",e.exports["#EURONEXT:PX1-symbol-description"]="CAC 40 Index",e.exports["#OMXHEX:OMXH25-symbol-description"]="OMX Helsinki 25 Index",e.exports["#EURONEXT:BEL20-symbol-description"]="BEL 20 Index",e.exports["#TVC:STI-symbol-description"]="Straits Times Index",e.exports["#DFM:DFMGI-symbol-description"]="DFM Index",e.exports["#TVC:KOSPI-symbol-description"]="Korea Composite Stock Price Index",e.exports["#FTSEMYX:FBMKLCI-symbol-description"]="FTSE Bursa Malaysia KLCI Index",e.exports["#TASE:TA35-symbol-description"]=["TA -35 Index"],e.exports["#OMXSTO:OMXS30-symbol-description"]="OMX Stockholm 30 Index",e.exports["#OMXICE:OMXI8-symbol-description"]="OMX Iceland 8 Index",e.exports["#NSENG:NSE30-symbol-description"]="NSE 30 Index",e.exports["#BAHRAIN:BSEX-symbol-description"]="Bahrain All Share Index",e.exports["#OMXTSE:OMXTGI-symbol-description"]="OMX Tallinn GI",e.exports["#OMXCOP:OMXC25-symbol-description"]="OMX Copenhagen 25 Index",e.exports["#OMXRSE:OMXRGI-symbol-description"]="OMX Riga GI",e.exports["#BELEX:BELEX15-symbol-description"]="BELEX 15 Index",e.exports["#OMXVSE:OMXVGI-symbol-description"]="OMX Vilnius GI",e.exports["#EURONEXT:AEX-symbol-description"]="AEX Index",e.exports["#CBOE:VIX-symbol-description"]="Volatility S&P 500 Index",e.exports["#NASDAQ:XAU-symbol-description"]="PHLX Gold and Silver Sector Index",e.exports["#DJ:DJUSCL-symbol-description"]="Dow Jones U.S. Coal Index",e.exports["#DJ:DJCIKC-symbol-description"]="Dow Jones Commodity Index Coffee",e.exports["#DJ:DJCIEN-symbol-description"]="Dow Jones Commodity Index Energy",e.exports["#NASDAQ:OSX-symbol-description"]="PHLX Oil Service Sector Index",e.exports["#DJ:DJCISB-symbol-description"]="Dow Jones Commodity Index Sugar",e.exports["#DJ:DJCICC-symbol-description"]="Dow Jones Commodity Index Cocoa",e.exports["#DJ:DJCIGR-symbol-description"]="Dow Jones Commodity Index Grains",e.exports["#DJ:DJCIAGC-symbol-description"]="Dow Jones Commodity Index Agriculture Capped Component",e.exports["#DJ:DJCISI-symbol-description"]="Dow Jones Commodity Index Silver",
e.exports["#DJ:DJCIIK-symbol-description"]="Dow Jones Commodity Index Nickel",e.exports["#NASDAQ:HGX-symbol-description"]="PHLX Housing Sector Index",e.exports["#DJ:DJCIGC-symbol-description"]="Dow Jones Commodity Index Gold",e.exports["#SP:SPGSCI-symbol-description"]="S&P Goldman Sachs Commodity Index",e.exports["#NASDAQ:UTY-symbol-description"]="PHLX Utility Sector Index",e.exports["#DJ:DJU-symbol-description"]="Dow Jones Utility Average Index",e.exports["#SP:SVX-symbol-description"]="S&P 500 Value Index",e.exports["#SP:OEX-symbol-description"]="S&P 100 Index",e.exports["#CBOE:OEX-symbol-description"]="S&P 100 Index",e.exports["#NASDAQ:SOX-symbol-description"]="Philadelphia Semiconductor Index",e.exports["#RUSSELL:RUI-symbol-description"]="Russell 1000 Index",e.exports["#RUSSELL:RUA-symbol-description"]="Russell 3000 Index",e.exports["#RUSSELL:RUT-symbol-description"]="Russell 2000 Index",e.exports["#NYSE:XMI-symbol-description"]="NYSE ARCA Major Market Index",e.exports["#NYSE:XAX-symbol-description"]="AMEX Composite Index",e.exports["#NASDAQ:NDX-symbol-description"]="Nasdaq 100 Index",e.exports["#NASDAQ:IXIC-symbol-description"]="Nasdaq Composite Index",e.exports["#DJ:DJT-symbol-description"]="Dow Jones Transportation Average Index",e.exports["#NYSE:NYA-symbol-description"]="NYSE Composite Index",e.exports["#NYMEX:CJ1!-symbol-description"]="Cocoa Futures",e.exports["#USDILS-symbol-description"]=["U.S. Dollar / Israelischer Shekel"],e.exports["#TSXV:F-symbol-description"]="Fiore Gold Inc",e.exports["#SIX:F-symbol-description"]="Ford Motor Company",e.exports["#BMV:F-symbol-description"]="Ford Motor Company",e.exports["#TWII-symbol-description"]="Taiwan Weighted Index",e.exports["#TVC:PL10Y-symbol-description"]=["Polnische Staatsanleihen 10 Jahre"],e.exports["#TVC:PL05Y-symbol-description"]=["Polnische Staatsanleihen 5 Jahre"],e.exports["#SET:GC-symbol-description"]="Global Connections Public Company",e.exports["#TSX:GC-symbol-description"]="Great Canadian Gaming Corporation",e.exports["#TVC:FTMIB-symbol-description"]="Milano Italia Borsa Index",e.exports["#OANDA:SPX500USD-symbol-description"]="S&P 500 Index",e.exports["#BMV:CT-symbol-description"]="China SX20 RT",e.exports["#TSXV:CT-symbol-description"]="Centenera Mining Corporation",e.exports["#BYBIT:ETHUSD-symbol-description"]=["ETHUSD endloser Kontrakt"],e.exports["#BYBIT:XRPUSD-symbol-description"]=["XRPUSD endloser Kontrakt"],e.exports["#BYBIT:BTCUSD-symbol-description"]=["BTCUSD endloser Kontrakt"],e.exports["#BITMEX:ETHUSD-symbol-description"]=["ETHUSD endloser Futures-Kontrakt"],e.exports["#DERIBIT:BTCUSD-symbol-description"]=["BTCUSD endloser Futures-Kontrakt"],e.exports["#DERIBIT:ETHUSD-symbol-description"]=["ETHUSD endloser Futures-Kontrakt"],e.exports["#USDHUF-symbol-description"]=["U.S. Dollar / Ungarischer Forint"],e.exports["#USDTHB-symbol-description"]="U.S. Dollar / Thai Baht",e.exports["#FOREXCOM:US2000-symbol-description"]="US Small Cap 2000",e.exports["#TSXV:PBR-symbol-description"]="Para Resources Inc",
e.exports["#NYSE:SI-symbol-description"]="Silvergate Capital Corporation",e.exports["#NASDAQ:LE-symbol-description"]="Lands' End Inc",e.exports["#CME:CB1!-symbol-description"]=["Butter Futures-Cash (Continuous: aktueller vorne)"],e.exports["#LSE:SCHO-symbol-description"]="Scholium Group Plc Ord 1P",e.exports["#NEO:HE-symbol-description"]=["#NEO:HE-Symbol-Beschreibung"],e.exports["#NYSE:HE-symbol-description"]="Hawaiian Electric Industries",e.exports["#OMXCOP:SCHO-symbol-description"]="Schouw & Co A/S",e.exports["#TSX:HE-symbol-description"]=["#TSX:HE-Symbol-Beschreibung"],e.exports["#BSE:ITI-symbol-description"]="ITI Ltd",e.exports["#NSE:ITI-symbol-description"]="Indian Telephone Industries Limited",e.exports["#TSX:LS-symbol-description"]="Middlefield Healthcare & Life Sciences Dividend Fund",e.exports["#BITMEX:XBT-symbol-description"]="Bitcoin / U.S. Dollar Index",e.exports["#CME_MINI:RTY1!-symbol-description"]="E-Mini Russell 2000 Index Futures",e.exports["#CRYPTOCAP:TOTAL-symbol-description"]=["Total Krypto Marktkapitalisierung, $"],e.exports["#ICEUS:DX1!-symbol-description"]="U.S. Dollar Index Futures",e.exports["#NYMEX:TT1!-symbol-description"]=["Baumwoll-Futures"],e.exports["#PHEMEX:BTCUSD-symbol-description"]=["BTC endloser Futures Kontrakt"],e.exports["#PHEMEX:ETHUSD-symbol-description"]=["ETH endloser Futures Kontrakt"],e.exports["#PHEMEX:XRPUSD-symbol-description"]=["XRP endloser Futures Kontrakt"],e.exports["#PHEMEX:LTCUSD-symbol-description"]=["LTC endloser Futures Kontrakt"],e.exports["#BITCOKE:BCHUSD-symbol-description"]="BCH Quanto Swap",e.exports["#BITCOKE:BTCUSD-symbol-description"]="BTC Quanto Swap",e.exports["#BITCOKE:ETHUSD-symbol-description"]="ETH Quanto Swap",e.exports["#BITCOKE:LTCUSD-symbol-description"]="LTC Quanto Swap",e.exports["#TVC:CA10-symbol-description"]=["Kanadische Staatsanleihen, 10 YR"],e.exports["#TVC:CA10Y-symbol-description"]=["Kanadische Staatsanleihen 10 YR Rendite"],e.exports["#TVC:ID10Y-symbol-description"]=["Indonesien Staatsanleihen 10 YR Rendite"],e.exports["#TVC:NL10-symbol-description"]=["Niederländische Staatsanleihen, 10 YR"],e.exports["#TVC:NL10Y-symbol-description"]=["Niederländische Staatsanleihen 10 YR Rendite"],e.exports["#TVC:NZ10-symbol-description"]=["Neuseeländische Staatsanleihen, 10 YR"],e.exports["#TVC:NZ10Y-symbol-description"]=["Neuseeländische Staatsanleihen 10 YR Rendite"],e.exports["#SOLUSD-symbol-description"]="Solana / U.S. Dollar",e.exports["#LUNAUSD-symbol-description"]="Luna / U.S. Dollar",e.exports["#UNIUSD-symbol-description"]="Uniswap / U.S. Dollar",e.exports["#LTCBRL-symbol-description"]=["Litecoin / Brasilianischer Real"],e.exports["#ETCEUR-symbol-description"]="Ethereum Classic / Euro",e.exports["#ETHKRW-symbol-description"]=["Ethereum / Südkoreanischer Won"],e.exports["#BTCRUB-symbol-description"]=["Bitcoin / Russischer Rubel"],e.exports["#BTCTHB-symbol-description"]=["Bitcoin / Thailändischer Baht"],e.exports["#ETHTHB-symbol-description"]=["Ethereum / Thailändischer Baht"],
e.exports["#TVC:EU10YY-symbol-description"]=["Euro Staatsanleihen 10 YR Yield"]}}]);