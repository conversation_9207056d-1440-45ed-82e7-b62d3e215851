"""
Test AI Pattern Recognition Fix
Verify that AI patterns are now being detected properly
"""

import sys
import os
import pandas as pd
import numpy as np

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def create_test_data():
    """Create test OHLCV data for pattern testing"""
    
    # Create 100 bars of test data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    
    # Generate realistic price data with some patterns
    base_price = 80000
    prices = []
    
    for i in range(100):
        # Add some trend and volatility
        trend = i * 50  # Upward trend
        volatility = np.random.normal(0, 500)
        price = base_price + trend + volatility
        prices.append(max(price, 1000))  # Ensure positive prices
    
    # Create OHLCV data
    data = []
    for i, price in enumerate(prices):
        high = price * (1 + np.random.uniform(0, 0.02))
        low = price * (1 - np.random.uniform(0, 0.02))
        open_price = prices[i-1] if i > 0 else price
        close = price
        volume = np.random.uniform(100000, 500000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

def create_test_smc_results():
    """Create test SMC results for pattern testing"""
    
    return {
        'order_blocks': [],
        'fvgs': [],
        'liquidity_zones': [],
        'bos_events': [],
        'liquidity_sweeps': [],
        'confluence': {'total_score': 0.5},
        'market_structure': {'trend': 'bullish'},
        'premium_discount': None
    }

def test_ai_pattern_detection():
    """Test AI pattern detection with real data"""
    print("🤖 Testing AI Pattern Detection Fix")
    print("=" * 50)
    
    try:
        # Import the AI pattern recognizer
        from app.components.ai_pattern_recognition import AIPatternRecognizer
        
        # Create test data
        print("📊 Creating test data...")
        df = create_test_data()
        smc_results = create_test_smc_results()
        
        print(f"✅ Test data created: {len(df)} bars")
        print(f"   Price range: {df['close'].min():.0f} - {df['close'].max():.0f}")
        print(f"   Trend: {((df['close'].iloc[-1] - df['close'].iloc[0]) / df['close'].iloc[0] * 100):.1f}%")
        
        # Initialize pattern recognizer
        print("\n🧠 Initializing AI Pattern Recognizer...")
        recognizer = AIPatternRecognizer()
        print("✅ Pattern recognizer initialized")
        
        # Detect patterns
        print("\n🔍 Detecting patterns...")
        patterns = recognizer.detect_patterns(df, smc_results)
        
        print(f"✅ Pattern detection completed")
        print(f"📈 Patterns detected: {len(patterns)}")
        
        # Display detected patterns
        if patterns:
            print("\n🎯 Detected Patterns:")
            print("-" * 30)
            
            for i, pattern in enumerate(patterns[:5]):  # Show top 5
                print(f"{i+1}. {pattern.pattern_name.replace('_', ' ').title()}")
                print(f"   Type: {pattern.pattern_type.upper()}")
                print(f"   Confidence: {pattern.confidence:.1%}")
                print(f"   Success Rate: {pattern.success_probability:.1%}")
                print(f"   Expected Move: {pattern.expected_move:.1%}")
                print(f"   Time Horizon: {pattern.time_horizon} bars")
                print(f"   R/R Ratio: {pattern.risk_reward_ratio:.1f}")
                print()
        else:
            print("\n⚠️ No patterns detected")
            
            # Debug information
            print("\n🔧 Debug Information:")
            features = recognizer._extract_features(df, smc_results)
            print(f"   Price trend: {features.get('price_trend', 0):.3f}")
            print(f"   Momentum: {features.get('momentum', 0):.3f}")
            print(f"   Volatility: {features.get('price_volatility', 0):.3f}")
            print(f"   Confluence: {features.get('confluence_score', 0):.3f}")
        
        return len(patterns) > 0
        
    except Exception as e:
        print(f"❌ Error in pattern detection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_pattern_features():
    """Test pattern feature extraction"""
    print("\n🔧 Testing Pattern Features:")
    print("-" * 35)
    
    try:
        from app.components.ai_pattern_recognition import AIPatternRecognizer
        
        df = create_test_data()
        smc_results = create_test_smc_results()
        
        recognizer = AIPatternRecognizer()
        features = recognizer._extract_features(df, smc_results)
        
        print("✅ Feature extraction successful")
        print(f"   Features extracted: {len(features)}")
        
        # Show key features
        key_features = ['price_trend', 'momentum', 'price_volatility', 'confluence_score']
        for feature in key_features:
            value = features.get(feature, 0)
            print(f"   {feature}: {value:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in feature extraction: {str(e)}")
        return False

def main():
    """Run the AI pattern detection fix test"""
    print("🧠 AI Pattern Recognition Fix Test")
    print("=" * 60)
    
    success1 = test_ai_pattern_detection()
    success2 = test_pattern_features()
    
    if success1 and success2:
        print("\n🎉 AI PATTERN DETECTION FIX SUCCESSFUL!")
        print("\n✅ Key Improvements Made:")
        print("   • Added missing pattern detection methods")
        print("   • Implemented fallback momentum patterns")
        print("   • Implemented fallback trend patterns")
        print("   • Lowered confidence threshold (60% → 40%)")
        print("   • Enhanced feature extraction")
        print("   • Added comprehensive pattern database")
        
        print("\n🚀 AI Pattern Recognition Now Includes:")
        print("   📊 SMC Patterns:")
        print("      • Order block retest patterns")
        print("      • FVG fill reversal patterns")
        print("      • Liquidity sweep patterns")
        print("      • BOS continuation patterns")
        print("      • Premium/discount patterns")
        
        print("\n   📈 Classic Patterns:")
        print("      • Double top/bottom patterns")
        print("      • Head and shoulders patterns")
        print("      • Triangle patterns")
        print("      • Volume breakout patterns")
        
        print("\n   🎯 Fallback Patterns:")
        print("      • Momentum patterns")
        print("      • Price trend patterns")
        print("      • High confluence setups")
        
        print("\n✨ The AI Pattern Recognition should now work properly!")
        print("Patterns will be detected and displayed in the SMC interface.")
        
    else:
        print("\n❌ AI Pattern Detection Fix FAILED!")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
