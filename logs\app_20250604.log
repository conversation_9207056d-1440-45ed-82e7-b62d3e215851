2025-06-04 12:33:15,657 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-04 12:33:19,923 - app - INFO - Memory management utilities loaded
2025-06-04 12:33:19,939 - app - INFO - Error handling utilities loaded
2025-06-04 12:33:19,961 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 12:33:19,975 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 12:33:19,976 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 12:33:19,976 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 12:33:19,976 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 12:33:19,976 - app - INFO - Applied NumPy fix
2025-06-04 12:33:19,985 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 12:33:19,986 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 12:33:19,986 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 12:33:19,988 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 12:33:19,988 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 12:33:36,147 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 12:33:36,149 - app - INFO - Applied TensorFlow fix
2025-06-04 12:33:36,171 - app.config - INFO - Configuration initialized
2025-06-04 12:33:36,197 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 12:33:36,214 - models.train - INFO - TensorFlow test successful
2025-06-04 12:33:37,544 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 12:33:37,544 - models.train - INFO - Transformer model is available
2025-06-04 12:33:37,546 - models.train - INFO - Using TensorFlow-based models
2025-06-04 12:33:37,560 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 12:33:37,561 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 12:33:37,585 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 12:33:38,067 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 12:33:38,067 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 12:33:38,225 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 12:33:38,240 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:33:38,879 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 12:33:39,820 - app.utils.session_state - INFO - Initializing session state
2025-06-04 12:33:39,821 - app.utils.session_state - INFO - Session state initialized
2025-06-04 12:33:41,264 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 12:33:41,282 - app.utils.memory_management - INFO - Memory before cleanup: 425.18 MB
2025-06-04 12:33:41,467 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 12:33:41,471 - app.utils.memory_management - INFO - Memory after cleanup: 425.56 MB (freed -0.38 MB)
2025-06-04 12:34:00,363 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:00,555 - app.utils.memory_management - INFO - Memory before cleanup: 429.08 MB
2025-06-04 12:34:00,962 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 12:34:00,964 - app.utils.memory_management - INFO - Memory after cleanup: 429.12 MB (freed -0.04 MB)
2025-06-04 12:34:06,444 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:06,835 - app.utils.memory_management - INFO - Memory before cleanup: 430.14 MB
2025-06-04 12:34:07,137 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 12:34:07,139 - app.utils.memory_management - INFO - Memory after cleanup: 430.18 MB (freed -0.04 MB)
2025-06-04 12:34:07,953 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:20,581 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 12:34:20,582 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 12:34:20,586 - app.utils.memory_management - INFO - Memory before cleanup: 431.47 MB
2025-06-04 12:34:20,879 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 12:34:20,881 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed 0.00 MB)
2025-06-04 12:34:20,883 - app.utils.memory_management - INFO - Memory before cleanup: 431.47 MB
2025-06-04 12:34:21,068 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 12:34:21,068 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed 0.00 MB)
2025-06-04 12:34:56,063 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:56,216 - app.utils.memory_management - INFO - Memory before cleanup: 432.13 MB
2025-06-04 12:34:56,534 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 12:34:56,536 - app.utils.memory_management - INFO - Memory after cleanup: 432.13 MB (freed 0.00 MB)
2025-06-04 12:35:00,872 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:35:08,662 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:35:08,662 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:35:09,554 - app.utils.memory_management - INFO - Memory before cleanup: 448.73 MB
2025-06-04 12:35:09,775 - app.utils.memory_management - INFO - Garbage collection: collected 1130 objects
2025-06-04 12:35:09,777 - app.utils.memory_management - INFO - Memory after cleanup: 448.80 MB (freed -0.06 MB)
2025-06-04 12:36:16,932 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:16,988 - app.utils.memory_management - INFO - Memory before cleanup: 450.92 MB
2025-06-04 12:36:17,230 - app.utils.memory_management - INFO - Garbage collection: collected 271 objects
2025-06-04 12:36:17,232 - app.utils.memory_management - INFO - Memory after cleanup: 450.92 MB (freed 0.00 MB)
2025-06-04 12:36:20,439 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:20,469 - app.utils.memory_management - INFO - Memory before cleanup: 450.92 MB
2025-06-04 12:36:20,691 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 12:36:20,691 - app.utils.memory_management - INFO - Memory after cleanup: 450.92 MB (freed 0.00 MB)
2025-06-04 12:36:21,917 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:30,471 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 12:36:30,472 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 12:36:30,473 - app.utils.memory_management - INFO - Memory before cleanup: 450.93 MB
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Memory after cleanup: 450.93 MB (freed 0.00 MB)
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Memory before cleanup: 450.93 MB
2025-06-04 12:36:30,921 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 12:36:30,923 - app.utils.memory_management - INFO - Memory after cleanup: 450.93 MB (freed 0.00 MB)
2025-06-04 12:44:06,456 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:44:06,545 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 12:44:06,571 - app.utils.memory_management - INFO - Memory before cleanup: 451.14 MB
2025-06-04 12:44:06,810 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 12:44:06,810 - app.utils.memory_management - INFO - Memory after cleanup: 451.14 MB (freed 0.00 MB)
2025-06-04 12:44:08,668 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:44:16,960 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:44:16,962 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:44:17,159 - app.utils.memory_management - INFO - Memory before cleanup: 451.19 MB
2025-06-04 12:44:17,355 - app.utils.memory_management - INFO - Garbage collection: collected 1435 objects
2025-06-04 12:44:17,356 - app.utils.memory_management - INFO - Memory after cleanup: 451.19 MB (freed 0.00 MB)
2025-06-04 13:04:25,458 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:04:26,902 - app - INFO - Memory management utilities loaded
2025-06-04 13:04:26,904 - app - INFO - Error handling utilities loaded
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:04:26,908 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:04:26,908 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 13:04:26,910 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 13:04:26,920 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 13:04:26,924 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 13:04:26,924 - app - INFO - Applied NumPy fix
2025-06-04 13:04:26,925 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 13:04:26,927 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:04:26,930 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:04:26,937 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:04:26,939 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 13:04:30,536 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 13:04:30,539 - app - INFO - Applied TensorFlow fix
2025-06-04 13:04:30,989 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:31,037 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:31,037 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:31,041 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:31,057 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:31,058 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:31,070 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:04:33,436 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:33,436 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:33,438 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:33,452 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:33,452 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:33,460 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:04:35,027 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:35,027 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:35,038 - app - INFO - Cleaning up resources...
2025-06-04 13:04:35,043 - app.utils.memory_management - INFO - Memory before cleanup: 366.84 MB
2025-06-04 13:04:35,189 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-06-04 13:04:35,189 - app.utils.memory_management - INFO - Memory after cleanup: 366.85 MB (freed -0.00 MB)
2025-06-04 13:04:35,189 - app - INFO - Application shutdown complete
2025-06-04 13:05:01,865 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:05:01,886 - app - INFO - Memory management utilities loaded
2025-06-04 13:05:01,892 - app - INFO - Error handling utilities loaded
2025-06-04 13:05:01,897 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:05:01,904 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:05:01,907 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:05:01,910 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:05:49,280 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:05:50,477 - app - INFO - Memory management utilities loaded
2025-06-04 13:05:50,479 - app - INFO - Error handling utilities loaded
2025-06-04 13:05:50,480 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:05:50,482 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:05:50,482 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:05:50,483 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:05:50,483 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 13:05:50,484 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 13:05:50,485 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 13:05:50,485 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 13:05:50,485 - app - INFO - Applied NumPy fix
2025-06-04 13:05:50,486 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:05:50,487 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:05:50,489 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:05:50,489 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:05:50,489 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 13:05:55,464 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 13:05:55,465 - app - INFO - Applied TensorFlow fix
2025-06-04 13:05:55,467 - app.config - INFO - Configuration initialized
2025-06-04 13:05:55,470 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 13:05:55,480 - models.train - INFO - TensorFlow test successful
2025-06-04 13:05:56,079 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 13:05:56,079 - models.train - INFO - Transformer model is available
2025-06-04 13:05:56,080 - models.train - INFO - Using TensorFlow-based models
2025-06-04 13:05:56,081 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 13:05:56,081 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 13:05:56,085 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 13:05:56,451 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 13:05:56,451 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 13:05:56,451 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:05:56,451 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:05:56,553 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 13:05:56,557 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:05:56,900 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 13:05:57,474 - app.utils.session_state - INFO - Initializing session state
2025-06-04 13:05:57,476 - app.utils.session_state - INFO - Session state initialized
2025-06-04 13:05:58,830 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 13:05:58,850 - app.utils.memory_management - INFO - Memory before cleanup: 425.21 MB
2025-06-04 13:05:59,128 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 13:05:59,129 - app.utils.memory_management - INFO - Memory after cleanup: 425.61 MB (freed -0.39 MB)
2025-06-04 13:06:07,380 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:06:07,540 - app.utils.memory_management - INFO - Memory before cleanup: 429.07 MB
2025-06-04 13:06:07,728 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 13:06:07,729 - app.utils.memory_management - INFO - Memory after cleanup: 429.07 MB (freed -0.00 MB)
2025-06-04 13:06:12,355 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:06:12,399 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:06:12,420 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:06:12,421 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:06:12,423 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:06:14,082 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:06:14,082 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:06:14,843 - app.utils.memory_management - INFO - Memory before cleanup: 448.17 MB
2025-06-04 13:06:15,080 - app.utils.memory_management - INFO - Garbage collection: collected 1193 objects
2025-06-04 13:06:15,081 - app.utils.memory_management - INFO - Memory after cleanup: 448.27 MB (freed -0.10 MB)
2025-06-04 15:14:32,179 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:14:39,510 - app - INFO - Memory management utilities loaded
2025-06-04 15:14:39,519 - app - INFO - Error handling utilities loaded
2025-06-04 15:14:39,525 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:14:39,529 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:14:39,532 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:14:39,533 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:14:39,534 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:14:39,535 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:14:39,535 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:14:39,536 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:14:39,536 - app - INFO - Applied NumPy fix
2025-06-04 15:14:39,548 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:14:39,550 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:14:39,551 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:14:39,551 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:14:39,552 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:15:06,586 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:15:06,590 - app - INFO - Applied TensorFlow fix
2025-06-04 15:15:06,609 - app.config - INFO - Configuration initialized
2025-06-04 15:15:06,692 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 15:15:06,709 - models.train - INFO - TensorFlow test successful
2025-06-04 15:15:12,094 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 15:15:12,094 - models.train - INFO - Transformer model is available
2025-06-04 15:15:12,094 - models.train - INFO - Using TensorFlow-based models
2025-06-04 15:15:12,105 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 15:15:12,105 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 15:15:12,139 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 15:15:13,996 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:15:13,996 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:15:14,445 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 15:15:14,460 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:15:15,348 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 15:15:18,539 - app.utils.session_state - INFO - Initializing session state
2025-06-04 15:15:18,540 - app.utils.session_state - INFO - Session state initialized
2025-06-04 15:15:19,963 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:15:19,981 - app.utils.memory_management - INFO - Memory before cleanup: 425.26 MB
2025-06-04 15:15:20,162 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:15:20,163 - app.utils.memory_management - INFO - Memory after cleanup: 425.65 MB (freed -0.39 MB)
2025-06-04 15:27:47,940 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:27:47,968 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:27:47,988 - app.utils.memory_management - INFO - Memory before cleanup: 424.50 MB
2025-06-04 15:27:48,156 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:27:48,160 - app.utils.memory_management - INFO - Memory after cleanup: 424.51 MB (freed -0.00 MB)
2025-06-04 15:27:51,978 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:27:52,009 - app.utils.memory_management - INFO - Memory before cleanup: 425.38 MB
2025-06-04 15:27:52,291 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-04 15:27:52,292 - app.utils.memory_management - INFO - Memory after cleanup: 425.42 MB (freed -0.04 MB)
2025-06-04 15:27:53,752 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:28:09,581 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 15:28:09,582 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 15:28:09,583 - app.utils.memory_management - INFO - Memory before cleanup: 426.79 MB
2025-06-04 15:28:09,769 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 15:28:09,769 - app.utils.memory_management - INFO - Memory after cleanup: 426.79 MB (freed 0.00 MB)
2025-06-04 15:28:09,769 - app.utils.memory_management - INFO - Memory before cleanup: 426.79 MB
2025-06-04 15:28:09,907 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 15:28:09,907 - app.utils.memory_management - INFO - Memory after cleanup: 426.79 MB (freed 0.00 MB)
2025-06-04 15:28:47,673 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:28:50,863 - app.utils.memory_management - INFO - Memory before cleanup: 427.47 MB
2025-06-04 15:28:51,031 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 15:28:51,031 - app.utils.memory_management - INFO - Memory after cleanup: 427.47 MB (freed 0.00 MB)
2025-06-04 15:28:54,287 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:28:54,333 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:28:54,401 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 15:28:54,403 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:28:54,407 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:28:55,990 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:28:55,990 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:28:56,961 - app.utils.memory_management - INFO - Memory before cleanup: 443.82 MB
2025-06-04 15:28:57,155 - app.utils.memory_management - INFO - Garbage collection: collected 1109 objects
2025-06-04 15:28:57,161 - app.utils.memory_management - INFO - Memory after cleanup: 443.82 MB (freed 0.00 MB)
2025-06-04 15:29:28,090 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:29:28,152 - app.utils.memory_management - INFO - Memory before cleanup: 445.87 MB
2025-06-04 15:29:28,363 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-06-04 15:29:28,365 - app.utils.memory_management - INFO - Memory after cleanup: 445.87 MB (freed 0.00 MB)
2025-06-04 15:34:10,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:10,807 - app.utils.session_state - INFO - Initializing session state
2025-06-04 15:34:10,808 - app.utils.session_state - INFO - Session state initialized
2025-06-04 15:34:10,819 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:34:10,829 - app.utils.memory_management - INFO - Memory before cleanup: 445.90 MB
2025-06-04 15:34:11,077 - app.utils.memory_management - INFO - Garbage collection: collected 181 objects
2025-06-04 15:34:11,079 - app.utils.memory_management - INFO - Memory after cleanup: 445.90 MB (freed 0.00 MB)
2025-06-04 15:34:17,486 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:17,509 - app.utils.memory_management - INFO - Memory before cleanup: 399.76 MB
2025-06-04 15:34:17,691 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-06-04 15:34:17,691 - app.utils.memory_management - INFO - Memory after cleanup: 399.76 MB (freed 0.00 MB)
2025-06-04 15:34:24,948 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:25,114 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:25,150 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:25,150 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:25,154 - app.utils.memory_management - INFO - Memory before cleanup: 400.09 MB
2025-06-04 15:34:25,413 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-04 15:34:25,413 - app.utils.memory_management - INFO - Memory after cleanup: 400.09 MB (freed 0.00 MB)
2025-06-04 15:34:35,744 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:35,812 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:35,819 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:35,820 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:35,822 - app.utils.memory_management - INFO - Memory before cleanup: 400.26 MB
2025-06-04 15:34:36,056 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-04 15:34:36,056 - app.utils.memory_management - INFO - Memory after cleanup: 400.26 MB (freed 0.00 MB)
2025-06-04 15:34:38,696 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:38,789 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:38,800 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:38,806 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:38,810 - app.utils.memory_management - INFO - Memory before cleanup: 400.34 MB
2025-06-04 15:34:39,003 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-04 15:34:39,004 - app.utils.memory_management - INFO - Memory after cleanup: 400.34 MB (freed 0.00 MB)
2025-06-04 15:34:39,778 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:39,851 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:39,858 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:39,858 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:39,861 - app.utils.memory_management - INFO - Memory before cleanup: 400.40 MB
2025-06-04 15:34:40,051 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-04 15:34:40,054 - app.utils.memory_management - INFO - Memory after cleanup: 400.40 MB (freed 0.00 MB)
2025-06-04 15:34:42,121 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:42,261 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:42,303 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:42,304 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:42,306 - app.utils.memory_management - INFO - Memory before cleanup: 400.83 MB
2025-06-04 15:34:42,548 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-04 15:34:42,548 - app.utils.memory_management - INFO - Memory after cleanup: 399.83 MB (freed 1.00 MB)
2025-06-04 15:34:46,888 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:47,028 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:47,056 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:47,057 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:47,059 - app.utils.memory_management - INFO - Memory before cleanup: 401.19 MB
2025-06-04 15:34:47,246 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-04 15:34:47,247 - app.utils.memory_management - INFO - Memory after cleanup: 401.19 MB (freed 0.00 MB)
2025-06-04 15:34:53,652 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:53,699 - app.utils.memory_management - INFO - Memory before cleanup: 401.15 MB
2025-06-04 15:34:54,039 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-06-04 15:34:54,042 - app.utils.memory_management - INFO - Memory after cleanup: 401.15 MB (freed 0.00 MB)
2025-06-04 15:34:56,812 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:56,860 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:34:56,878 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:34:56,879 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:34:56,882 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:34:58,967 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:34:58,967 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:34:59,229 - app.utils.memory_management - INFO - Memory before cleanup: 402.59 MB
2025-06-04 15:34:59,415 - app.utils.memory_management - INFO - Garbage collection: collected 1756 objects
2025-06-04 15:34:59,415 - app.utils.memory_management - INFO - Memory after cleanup: 402.59 MB (freed 0.00 MB)
2025-06-04 15:46:11,733 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:46:11,764 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:46:11,800 - app.utils.memory_management - INFO - Memory before cleanup: 393.98 MB
2025-06-04 15:46:12,049 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-04 15:46:12,049 - app.utils.memory_management - INFO - Memory after cleanup: 393.98 MB (freed 0.00 MB)
2025-06-04 15:46:15,683 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:46:15,719 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:46:15,735 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:46:15,736 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:46:15,739 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:46:17,366 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:46:17,366 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:46:17,642 - app.utils.memory_management - INFO - Memory before cleanup: 395.21 MB
2025-06-04 15:46:17,825 - app.utils.memory_management - INFO - Garbage collection: collected 1700 objects
2025-06-04 15:46:17,826 - app.utils.memory_management - INFO - Memory after cleanup: 395.21 MB (freed 0.00 MB)
2025-06-04 15:48:00,687 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:48:00,743 - app.utils.memory_management - INFO - Memory before cleanup: 395.30 MB
2025-06-04 15:48:01,017 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-04 15:48:01,019 - app.utils.memory_management - INFO - Memory after cleanup: 395.30 MB (freed 0.00 MB)
2025-06-04 15:48:03,004 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:48:03,035 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:48:03,050 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:48:03,051 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:48:03,053 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:48:04,622 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:48:04,624 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:48:04,849 - app.utils.memory_management - INFO - Memory before cleanup: 395.33 MB
2025-06-04 15:48:04,999 - app.utils.memory_management - INFO - Garbage collection: collected 2051 objects
2025-06-04 15:48:04,999 - app.utils.memory_management - INFO - Memory after cleanup: 395.33 MB (freed 0.00 MB)
2025-06-04 15:55:06,397 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:55:07,678 - app - INFO - Memory management utilities loaded
2025-06-04 15:55:07,679 - app - INFO - Error handling utilities loaded
2025-06-04 15:55:07,680 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:55:07,682 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:55:07,682 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:55:07,682 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:55:07,683 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:55:07,684 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:55:07,684 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:55:07,687 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:55:07,688 - app - INFO - Applied NumPy fix
2025-06-04 15:55:07,692 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:55:07,692 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:55:07,693 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:55:07,693 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:55:07,693 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:55:07,694 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:55:07,694 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:55:07,694 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:55:26,819 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:55:26,820 - app - INFO - Applied TensorFlow fix
2025-06-04 15:55:26,910 - app.components.advanced_smc_features - INFO - Found 7 high swing points with lookback 5
2025-06-04 15:55:26,947 - app.components.advanced_smc_features - INFO - Found 6 low swing points with lookback 5
2025-06-04 15:55:26,947 - app.components.advanced_smc_features - INFO - Found 7 swing highs and 6 swing lows
2025-06-04 15:55:26,951 - app.components.advanced_smc_features - INFO - Detected 5 BOS events
2025-06-04 15:55:27,024 - app.components.advanced_smc_features - INFO - Found 7 high swing points with lookback 5
2025-06-04 15:55:27,056 - app.components.advanced_smc_features - INFO - Found 6 low swing points with lookback 5
2025-06-04 15:55:27,059 - app.components.advanced_smc_features - INFO - Found 7 swing highs and 6 swing lows
2025-06-04 15:55:27,059 - app.components.advanced_smc_features - INFO - Detected 5 BOS events
2025-06-04 15:55:27,694 - app - INFO - Cleaning up resources...
2025-06-04 15:55:27,703 - app.utils.memory_management - INFO - Memory before cleanup: 322.03 MB
2025-06-04 15:55:27,816 - app.utils.memory_management - INFO - Garbage collection: collected 411 objects
2025-06-04 15:55:27,816 - app.utils.memory_management - INFO - Memory after cleanup: 322.32 MB (freed -0.29 MB)
2025-06-04 15:55:27,817 - app - INFO - Application shutdown complete
2025-06-04 15:57:15,191 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:57:15,201 - app - INFO - Memory management utilities loaded
2025-06-04 15:57:15,203 - app - INFO - Error handling utilities loaded
2025-06-04 15:57:15,206 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:57:15,206 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:57:15,208 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:57:15,209 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:57:40,945 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:57:41,690 - app - INFO - Memory management utilities loaded
2025-06-04 15:57:41,690 - app - INFO - Error handling utilities loaded
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:57:41,699 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:57:41,699 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:57:41,699 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:57:41,699 - app - INFO - Applied NumPy fix
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:57:41,702 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:57:41,702 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:57:41,702 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:57:41,702 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:57:46,395 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:57:46,395 - app - INFO - Applied TensorFlow fix
2025-06-04 15:57:46,421 - app - INFO - Cleaning up resources...
2025-06-04 15:57:46,424 - app.utils.memory_management - INFO - Memory before cleanup: 309.56 MB
2025-06-04 15:57:46,532 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-04 15:57:46,532 - app.utils.memory_management - INFO - Memory after cleanup: 309.90 MB (freed -0.34 MB)
2025-06-04 15:57:46,533 - app - INFO - Application shutdown complete
