2025-06-04 12:33:15,657 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-04 12:33:19,923 - app - INFO - Memory management utilities loaded
2025-06-04 12:33:19,939 - app - INFO - Error handling utilities loaded
2025-06-04 12:33:19,961 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 12:33:19,975 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 12:33:19,976 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 12:33:19,976 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 12:33:19,976 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 12:33:19,976 - app - INFO - Applied NumPy fix
2025-06-04 12:33:19,985 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 12:33:19,986 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 12:33:19,986 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 12:33:19,988 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 12:33:19,988 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 12:33:36,147 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 12:33:36,149 - app - INFO - Applied TensorFlow fix
2025-06-04 12:33:36,171 - app.config - INFO - Configuration initialized
2025-06-04 12:33:36,197 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 12:33:36,214 - models.train - INFO - TensorFlow test successful
2025-06-04 12:33:37,544 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 12:33:37,544 - models.train - INFO - Transformer model is available
2025-06-04 12:33:37,546 - models.train - INFO - Using TensorFlow-based models
2025-06-04 12:33:37,560 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 12:33:37,561 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 12:33:37,585 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 12:33:38,067 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 12:33:38,067 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 12:33:38,225 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 12:33:38,240 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:33:38,879 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 12:33:39,820 - app.utils.session_state - INFO - Initializing session state
2025-06-04 12:33:39,821 - app.utils.session_state - INFO - Session state initialized
2025-06-04 12:33:41,264 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 12:33:41,282 - app.utils.memory_management - INFO - Memory before cleanup: 425.18 MB
2025-06-04 12:33:41,467 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 12:33:41,471 - app.utils.memory_management - INFO - Memory after cleanup: 425.56 MB (freed -0.38 MB)
2025-06-04 12:34:00,363 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:00,555 - app.utils.memory_management - INFO - Memory before cleanup: 429.08 MB
2025-06-04 12:34:00,962 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 12:34:00,964 - app.utils.memory_management - INFO - Memory after cleanup: 429.12 MB (freed -0.04 MB)
2025-06-04 12:34:06,444 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:06,835 - app.utils.memory_management - INFO - Memory before cleanup: 430.14 MB
2025-06-04 12:34:07,137 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 12:34:07,139 - app.utils.memory_management - INFO - Memory after cleanup: 430.18 MB (freed -0.04 MB)
2025-06-04 12:34:07,953 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:20,581 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 12:34:20,582 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 12:34:20,586 - app.utils.memory_management - INFO - Memory before cleanup: 431.47 MB
2025-06-04 12:34:20,879 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 12:34:20,881 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed 0.00 MB)
2025-06-04 12:34:20,883 - app.utils.memory_management - INFO - Memory before cleanup: 431.47 MB
2025-06-04 12:34:21,068 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 12:34:21,068 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed 0.00 MB)
2025-06-04 12:34:56,063 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:56,216 - app.utils.memory_management - INFO - Memory before cleanup: 432.13 MB
2025-06-04 12:34:56,534 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 12:34:56,536 - app.utils.memory_management - INFO - Memory after cleanup: 432.13 MB (freed 0.00 MB)
2025-06-04 12:35:00,872 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:35:08,662 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:35:08,662 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:35:09,554 - app.utils.memory_management - INFO - Memory before cleanup: 448.73 MB
2025-06-04 12:35:09,775 - app.utils.memory_management - INFO - Garbage collection: collected 1130 objects
2025-06-04 12:35:09,777 - app.utils.memory_management - INFO - Memory after cleanup: 448.80 MB (freed -0.06 MB)
2025-06-04 12:36:16,932 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:16,988 - app.utils.memory_management - INFO - Memory before cleanup: 450.92 MB
2025-06-04 12:36:17,230 - app.utils.memory_management - INFO - Garbage collection: collected 271 objects
2025-06-04 12:36:17,232 - app.utils.memory_management - INFO - Memory after cleanup: 450.92 MB (freed 0.00 MB)
2025-06-04 12:36:20,439 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:20,469 - app.utils.memory_management - INFO - Memory before cleanup: 450.92 MB
2025-06-04 12:36:20,691 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 12:36:20,691 - app.utils.memory_management - INFO - Memory after cleanup: 450.92 MB (freed 0.00 MB)
2025-06-04 12:36:21,917 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:30,471 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 12:36:30,472 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 12:36:30,473 - app.utils.memory_management - INFO - Memory before cleanup: 450.93 MB
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Memory after cleanup: 450.93 MB (freed 0.00 MB)
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Memory before cleanup: 450.93 MB
2025-06-04 12:36:30,921 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 12:36:30,923 - app.utils.memory_management - INFO - Memory after cleanup: 450.93 MB (freed 0.00 MB)
2025-06-04 12:44:06,456 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:44:06,545 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 12:44:06,571 - app.utils.memory_management - INFO - Memory before cleanup: 451.14 MB
2025-06-04 12:44:06,810 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 12:44:06,810 - app.utils.memory_management - INFO - Memory after cleanup: 451.14 MB (freed 0.00 MB)
2025-06-04 12:44:08,668 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:44:16,960 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:44:16,962 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:44:17,159 - app.utils.memory_management - INFO - Memory before cleanup: 451.19 MB
2025-06-04 12:44:17,355 - app.utils.memory_management - INFO - Garbage collection: collected 1435 objects
2025-06-04 12:44:17,356 - app.utils.memory_management - INFO - Memory after cleanup: 451.19 MB (freed 0.00 MB)
2025-06-04 13:04:25,458 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:04:26,902 - app - INFO - Memory management utilities loaded
2025-06-04 13:04:26,904 - app - INFO - Error handling utilities loaded
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:04:26,908 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:04:26,908 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 13:04:26,910 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 13:04:26,920 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 13:04:26,924 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 13:04:26,924 - app - INFO - Applied NumPy fix
2025-06-04 13:04:26,925 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 13:04:26,927 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:04:26,930 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:04:26,937 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:04:26,939 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 13:04:30,536 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 13:04:30,539 - app - INFO - Applied TensorFlow fix
2025-06-04 13:04:30,989 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:31,037 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:31,037 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:31,041 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:31,057 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:31,058 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:31,070 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:04:33,436 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:33,436 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:33,438 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:33,452 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:33,452 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:33,460 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:04:35,027 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:35,027 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:35,038 - app - INFO - Cleaning up resources...
2025-06-04 13:04:35,043 - app.utils.memory_management - INFO - Memory before cleanup: 366.84 MB
2025-06-04 13:04:35,189 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-06-04 13:04:35,189 - app.utils.memory_management - INFO - Memory after cleanup: 366.85 MB (freed -0.00 MB)
2025-06-04 13:04:35,189 - app - INFO - Application shutdown complete
2025-06-04 13:05:01,865 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:05:01,886 - app - INFO - Memory management utilities loaded
2025-06-04 13:05:01,892 - app - INFO - Error handling utilities loaded
2025-06-04 13:05:01,897 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:05:01,904 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:05:01,907 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:05:01,910 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:05:49,280 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:05:50,477 - app - INFO - Memory management utilities loaded
2025-06-04 13:05:50,479 - app - INFO - Error handling utilities loaded
2025-06-04 13:05:50,480 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:05:50,482 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:05:50,482 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:05:50,483 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:05:50,483 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 13:05:50,484 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 13:05:50,485 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 13:05:50,485 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 13:05:50,485 - app - INFO - Applied NumPy fix
2025-06-04 13:05:50,486 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:05:50,487 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:05:50,489 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:05:50,489 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:05:50,489 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 13:05:55,464 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 13:05:55,465 - app - INFO - Applied TensorFlow fix
2025-06-04 13:05:55,467 - app.config - INFO - Configuration initialized
2025-06-04 13:05:55,470 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 13:05:55,480 - models.train - INFO - TensorFlow test successful
2025-06-04 13:05:56,079 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 13:05:56,079 - models.train - INFO - Transformer model is available
2025-06-04 13:05:56,080 - models.train - INFO - Using TensorFlow-based models
2025-06-04 13:05:56,081 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 13:05:56,081 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 13:05:56,085 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 13:05:56,451 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 13:05:56,451 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 13:05:56,451 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:05:56,451 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:05:56,553 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 13:05:56,557 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:05:56,900 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 13:05:57,474 - app.utils.session_state - INFO - Initializing session state
2025-06-04 13:05:57,476 - app.utils.session_state - INFO - Session state initialized
2025-06-04 13:05:58,830 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 13:05:58,850 - app.utils.memory_management - INFO - Memory before cleanup: 425.21 MB
2025-06-04 13:05:59,128 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 13:05:59,129 - app.utils.memory_management - INFO - Memory after cleanup: 425.61 MB (freed -0.39 MB)
2025-06-04 13:06:07,380 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:06:07,540 - app.utils.memory_management - INFO - Memory before cleanup: 429.07 MB
2025-06-04 13:06:07,728 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 13:06:07,729 - app.utils.memory_management - INFO - Memory after cleanup: 429.07 MB (freed -0.00 MB)
2025-06-04 13:06:12,355 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:06:12,399 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:06:12,420 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:06:12,421 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:06:12,423 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:06:14,082 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:06:14,082 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:06:14,843 - app.utils.memory_management - INFO - Memory before cleanup: 448.17 MB
2025-06-04 13:06:15,080 - app.utils.memory_management - INFO - Garbage collection: collected 1193 objects
2025-06-04 13:06:15,081 - app.utils.memory_management - INFO - Memory after cleanup: 448.27 MB (freed -0.10 MB)
2025-06-04 15:14:32,179 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:14:39,510 - app - INFO - Memory management utilities loaded
2025-06-04 15:14:39,519 - app - INFO - Error handling utilities loaded
2025-06-04 15:14:39,525 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:14:39,529 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:14:39,532 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:14:39,533 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:14:39,534 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:14:39,535 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:14:39,535 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:14:39,536 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:14:39,536 - app - INFO - Applied NumPy fix
2025-06-04 15:14:39,548 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:14:39,550 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:14:39,551 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:14:39,551 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:14:39,552 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:15:06,586 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:15:06,590 - app - INFO - Applied TensorFlow fix
2025-06-04 15:15:06,609 - app.config - INFO - Configuration initialized
2025-06-04 15:15:06,692 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 15:15:06,709 - models.train - INFO - TensorFlow test successful
2025-06-04 15:15:12,094 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 15:15:12,094 - models.train - INFO - Transformer model is available
2025-06-04 15:15:12,094 - models.train - INFO - Using TensorFlow-based models
2025-06-04 15:15:12,105 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 15:15:12,105 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 15:15:12,139 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 15:15:13,996 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:15:13,996 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:15:14,445 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 15:15:14,460 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:15:15,348 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 15:15:18,539 - app.utils.session_state - INFO - Initializing session state
2025-06-04 15:15:18,540 - app.utils.session_state - INFO - Session state initialized
2025-06-04 15:15:19,963 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:15:19,981 - app.utils.memory_management - INFO - Memory before cleanup: 425.26 MB
2025-06-04 15:15:20,162 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:15:20,163 - app.utils.memory_management - INFO - Memory after cleanup: 425.65 MB (freed -0.39 MB)
