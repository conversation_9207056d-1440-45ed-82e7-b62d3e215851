# TradingView Charting Library


This repository contains Charting Library package. If you use Git in your project, please feel free to use this repo as a submodule to your one. 

`master` branch contains current stable version. 

`unstable` branch contains the most recent features and fixes, but it can be less stable (actually, it is beta which is already thourghly tested).

### Documentation
See our docs @ this repository's [wiki](https://github.com/tradingview/charting_library/wiki). Please read it (at least, [Overview](https://github.com/tradingview/charting_library/wiki) and [Best Practices](https://github.com/tradingview/charting_library/wiki/Best-practices)) before running the Library.

### Issues Tracking
We use GitHub [Issues tracker](https://github.com/tradingview/charting_library/issues) for our project. Feel free to create bug reports and features requests. Please try reading the doc before asking questions because we will have more time for development then. 

### Stay Tuned
[Follow us](https://twitter.com/intent/follow?screen_name=tv_charts) on Twitter to receive the updates.
