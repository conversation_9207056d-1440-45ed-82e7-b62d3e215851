(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4717],{1414:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC","icon-only":"icon-only-D4RPB3ZC",link:"link-D4RPB3ZC","color-brand":"color-brand-D4RPB3ZC","variant-primary":"variant-primary-D4RPB3ZC","variant-secondary":"variant-secondary-D4RPB3ZC","color-gray":"color-gray-D4RPB3ZC","color-green":"color-green-D4RPB3ZC","color-red":"color-red-D4RPB3ZC","color-black":"color-black-D4RPB3ZC","size-xsmall":"size-xsmall-D4RPB3ZC","start-icon-wrap":"start-icon-wrap-D4RPB3ZC","end-icon-wrap":"end-icon-wrap-D4RPB3ZC","with-start-icon":"with-start-icon-D4RPB3ZC","with-end-icon":"with-end-icon-D4RPB3ZC","size-small":"size-small-D4RPB3ZC","size-medium":"size-medium-D4RPB3ZC","size-large":"size-large-D4RPB3ZC","size-xlarge":"size-xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC","adjust-position":"adjust-position-D4RPB3ZC","first-row":"first-row-D4RPB3ZC","first-col":"first-col-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC","text-wrap":"text-wrap-D4RPB3ZC","multiline-content":"multiline-content-D4RPB3ZC","secondary-text":"secondary-text-D4RPB3ZC","primary-text":"primary-text-D4RPB3ZC"}},81026:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},7236:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw",
"after-slot":"after-slot-W53jtLjw"}},30930:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},25650:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa","loader-initial":"loader-initial-UL6iwcBa","loader-appear":"loader-appear-UL6iwcBa"}},88803:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},20817:e=>{e.exports={autocomplete:"autocomplete-uszkUMOz",caret:"caret-uszkUMOz",icon:"icon-uszkUMOz",suggestions:"suggestions-uszkUMOz",suggestion:"suggestion-uszkUMOz",noResults:"noResults-uszkUMOz",selected:"selected-uszkUMOz",opened:"opened-uszkUMOz"}},34587:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},94720:(e,t,n)=>{"use strict";n.d(t,{Button:()=>_});var r=n(50959),o=n(97754),s=n(95604),i=n(9745),a=n(1414),l=n.n(a);function u(e){const{color:t="brand",size:n="medium",variant:r="primary",stretch:i=!1,icon:a,startIcon:u,endIcon:c,iconOnly:d=!1,className:h,isGrouped:p,cellState:f,disablePositionAdjustment:m=!1,primaryText:g,secondaryText:y,isAnchor:v=!1}=e,_=function(e){let t="";return 0!==e&&(1&e&&(t=o(t,l()["no-corner-top-left"])),2&e&&(t=o(t,l()["no-corner-top-right"])),4&e&&(t=o(t,l()["no-corner-bottom-right"])),8&e&&(t=o(t,l()["no-corner-bottom-left"]))),t}((0,s.getGroupCellRemoveRoundBorders)(f));return o(h,l().button,l()[`size-${n}`],l()[`color-${t}`],l()[`variant-${r}`],i&&l().stretch,(a||u)&&l()["with-start-icon"],c&&l()["with-end-icon"],d&&l()["icon-only"],_,p&&l().grouped,p&&!m&&l()["adjust-position"],p&&f.isTop&&l()["first-row"],p&&f.isLeft&&l()["first-col"],g&&y&&l()["multiline-content"],v&&l().link)}function c(e){const{startIcon:t,icon:n,iconOnly:o,children:s,endIcon:a,primaryText:u,secondaryText:c}=e,d=null!=t?t:n,h=!(t||n||a||o)&&!s&&u&&c;return r.createElement(r.Fragment,null,d&&r.createElement(i.Icon,{icon:d,className:l()["start-icon-wrap"]}),s&&r.createElement("span",{className:l().content},s),a&&!o&&r.createElement(i.Icon,{icon:a,className:l()["end-icon-wrap"]}),h&&function(e){return e.primaryText&&e.secondaryText&&r.createElement("div",{className:l()["text-wrap"]},r.createElement("span",{className:l()["primary-text"]}," ",e.primaryText," "),"string"==typeof e.secondaryText?r.createElement("span",{className:l()["secondary-text"]}," ",e.secondaryText," "):r.createElement("span",{className:l()["secondary-text"]},r.createElement("span",null,e.secondaryText.firstLine),r.createElement("span",null,e.secondaryText.secondLine)))}(e))}var d=n(86332),h=n(90186);function p(e){
const{className:t,color:n,variant:r,size:o,stretch:s,animated:i,icon:a,iconOnly:l,startIcon:u,endIcon:c,primaryText:d,secondaryText:p,...f}=e;return{...f,...(0,h.filterDataProps)(e),...(0,h.filterAriaProps)(e)}}function f(e){const{reference:t,...n}=e,{isGrouped:o,cellState:s,disablePositionAdjustment:i}=(0,r.useContext)(d.ControlGroupContext),a=u({...n,isGrouped:o,cellState:s,disablePositionAdjustment:i});return r.createElement("button",{...p(n),className:a,ref:t},r.createElement(c,{...n}))}function m(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function g(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function y(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}function v(e){const{intent:t,size:n,appearance:r,useFullWidth:o,icon:s,...i}=e;return{...i,color:g(t),size:y(n),variant:m(r),stretch:o,startIcon:s}}function _(e){return r.createElement(f,{...v(e)})}},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>r});const r=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function r(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>r})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>v,InputClasses:()=>m});var r=n(50959),o=n(97754),s=n(50151),i=n(38528),a=n(90186),l=n(86332),u=n(95604);var c=n(81026),d=n.n(c);function h(e){let t="";return 0!==e&&(1&e&&(t=o(t,d()["no-corner-top-left"])),2&e&&(t=o(t,d()["no-corner-top-right"])),4&e&&(t=o(t,d()["no-corner-bottom-right"])),8&e&&(t=o(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,r){const{removeRoundBorder:s,className:i,intent:a="default",borderStyle:l="thin",size:c,highlight:p,disabled:f,readonly:m,stretch:g,noReadonlyStyles:y,isFocused:v}=e,_=h(null!=s?s:(0,u.getGroupCellRemoveRoundBorders)(n));return o(d().container,d()[`container-${c}`],d()[`intent-${a}`],d()[`border-${l}`],c&&d()[`size-${c}`],_,p&&d()["with-highlight"],f&&d().disabled,m&&!y&&d().readonly,v&&d().focused,g&&d().stretch,t&&d().grouped,!r&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],i)}function f(e,t,n){const{highlight:r,highlightRemoveRoundBorder:s}=e;if(!r)return d().highlight;const i=h(null!=s?s:(0,u.getGroupCellRemoveRoundBorders)(t));return o(d().highlight,d().shown,d()[`size-${n}`],i)}const m={FontSizeMedium:(0,s.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,s.ensureDefined)(d()["font-size-large"])},g={passive:!1};function y(e,t){const{style:n,id:o,role:s,onFocus:u,onBlur:c,onMouseOver:d,onMouseOut:h,onMouseDown:m,onMouseUp:y,onKeyDown:v,onClick:_,tabIndex:w,startSlot:b,middleSlot:C,endSlot:x,onWheel:R,onWheelNoPassive:E=null,size:S}=e,{isGrouped:D,cellState:P,disablePositionAdjustment:k=!1}=(0,r.useContext)(l.ControlGroupContext),N=function(e,t=null,n){const o=(0,r.useRef)(null),s=(0,
r.useRef)(null),i=(0,r.useCallback)((()=>{if(null===o.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&o.current.addEventListener(e,t,n)}),[]),a=(0,r.useCallback)((()=>{if(null===o.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&o.current.removeEventListener(e,t,n)}),[]),l=(0,r.useCallback)((e=>{a(),o.current=e,i()}),[]);return(0,r.useEffect)((()=>(s.current=[e,t,n],i(),a)),[e,t,n]),l}("wheel",E,g);return r.createElement("span",{style:n,id:o,role:s,className:p(e,D,P,k),tabIndex:w,ref:(0,i.useMergedRefs)([t,N]),onFocus:u,onBlur:c,onMouseOver:d,onMouseOut:h,onMouseDown:m,onMouseUp:y,onKeyDown:v,onClick:_,onWheel:R,...(0,a.filterDataProps)(e),...(0,a.filterAriaProps)(e)},b,C,x,r.createElement("span",{className:f(e,P,S)}))}y.displayName="ControlSkeleton";const v=r.forwardRef(y)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>c,EndSlot:()=>u,MiddleSlot:()=>l,StartSlot:()=>a});var r=n(50959),o=n(97754),s=n(7236),i=n.n(s);function a(e){const{className:t,interactive:n=!0,icon:s=!1,children:a}=e;return r.createElement("span",{className:o(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},a)}function l(e){const{className:t,children:n}=e;return r.createElement("span",{className:o(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function u(e){const{className:t,interactive:n=!0,icon:s=!1,children:a}=e;return r.createElement("span",{className:o(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},a)}function c(e){const{className:t,children:n}=e;return r.createElement("span",{className:o(i()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>v});var r=n(50959),o=n(97754),s=n(90186),i=n(47201),a=n(48907),l=n(38528),u=n(48027),c=n(29202),d=n(45812),h=n(67029),p=n(78274),f=n(30930),m=n.n(f);function g(e){return!(0,s.isAriaAttribute)(e)&&!(0,s.isDataAttribute)(e)}function y(e){const{id:t,title:n,role:i,tabIndex:a,placeholder:l,name:u,type:c,value:d,defaultValue:f,draggable:y,autoComplete:v,autoFocus:_,maxLength:w,min:b,max:C,step:x,pattern:R,inputMode:E,onSelect:S,onFocus:D,onBlur:P,onKeyDown:k,onKeyUp:N,onKeyPress:O,onChange:z,onDragStart:B,size:I="small",className:L,inputClassName:W,disabled:M,readonly:Z,containerTabIndex:T,startSlot:U,endSlot:A,reference:F,containerReference:j,onContainerFocus:V,...$}=e,q=(0,s.filterProps)($,g),H={...(0,s.filterAriaProps)($),...(0,s.filterDataProps)($),id:t,title:n,role:i,tabIndex:a,placeholder:l,name:u,type:c,value:d,defaultValue:f,draggable:y,autoComplete:v,autoFocus:_,maxLength:w,min:b,max:C,step:x,pattern:R,inputMode:E,onSelect:S,onFocus:D,onBlur:P,onKeyDown:k,onKeyUp:N,onKeyPress:O,onChange:z,onDragStart:B};return r.createElement(h.ControlSkeleton,{...q,disabled:M,readonly:Z,tabIndex:T,className:o(m().container,L),size:I,ref:j,onFocus:V,startSlot:U,middleSlot:r.createElement(p.MiddleSlot,null,r.createElement("input",{...H,className:o(m().input,m()[`size-${I}`],W,U&&m()["with-start-slot"],A&&m()["with-end-slot"]),disabled:M,readOnly:Z,ref:F})),endSlot:A})}function v(e){e=(0,u.useControl)(e)
;const{disabled:t,autoSelectOnFocus:n,tabIndex:o=0,onFocus:s,onBlur:h,reference:p,containerReference:f=null}=e,m=(0,r.useRef)(null),g=(0,r.useRef)(null),[v,_]=(0,c.useFocus)(),w=t?void 0:v?-1:o,b=t?void 0:v?o:-1,{isMouseDown:C,handleMouseDown:x,handleMouseUp:R}=(0,d.useIsMouseDown)(),E=(0,i.createSafeMulticastEventHandler)(_.onFocus,(function(e){n&&!C.current&&(0,a.selectAllContent)(e.currentTarget)}),s),S=(0,i.createSafeMulticastEventHandler)(_.onBlur,h),D=(0,r.useCallback)((e=>{m.current=e,p&&("function"==typeof p&&p(e),"object"==typeof p&&(p.current=e))}),[m,p]);return r.createElement(y,{...e,isFocused:v,containerTabIndex:w,tabIndex:b,onContainerFocus:function(e){g.current===e.target&&null!==m.current&&m.current.focus()},onFocus:E,onBlur:S,reference:D,containerReference:(0,l.useMergedRefs)([g,f]),onMouseDown:x,onMouseUp:R})}},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>s});var r=n(47201),o=n(29202);function s(e){const{onFocus:t,onBlur:n,intent:s,highlight:i,disabled:a}=e,[l,u]=(0,o.useFocus)(void 0,a),c=(0,r.createSafeMulticastEventHandler)(a?void 0:u.onFocus,t),d=(0,r.createSafeMulticastEventHandler)(a?void 0:u.onBlur,n);return{...e,intent:s||(l?"primary":"default"),highlight:null!=i?i:l,onFocus:c,onBlur:d}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>o});var r=n(50959);function o(e,t){const[n,o]=(0,r.useState)(!1);(0,r.useEffect)((()=>{t&&n&&o(!1)}),[t,n]);const s={onFocus:(0,r.useCallback)((function(t){void 0!==e&&e.current!==t.target||o(!0)}),[e]),onBlur:(0,r.useCallback)((function(t){void 0!==e&&e.current!==t.target||o(!1)}),[e])};return[n,s]}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>o});var r=n(50959);function o(){const e=(0,r.useRef)(!1),t=(0,r.useCallback)((()=>{e.current=!0}),[e]),n=(0,r.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},38528:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>s});var r=n(50959),o=n(53017);function s(e){return(0,r.useCallback)((0,o.mergeRefs)(e),e)}},27267:(e,t,n)=>{"use strict";function r(e,t,n,r,o){function s(o){if(e>o.timeStamp)return;const s=o.target;void 0!==n&&null!==t&&null!==s&&s.ownerDocument===r&&(t.contains(s)||n(o))}return o.click&&r.addEventListener("click",s,!1),o.mouseDown&&r.addEventListener("mousedown",s,!1),o.touchEnd&&r.addEventListener("touchend",s,!1),o.touchStart&&r.addEventListener("touchstart",s,!1),()=>{r.removeEventListener("click",s,!1),r.removeEventListener("mousedown",s,!1),r.removeEventListener("touchend",s,!1),r.removeEventListener("touchstart",s,!1)}}n.d(t,{addOutsideEventListener:()=>r})},36383:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>s});var r=n(50959),o=n(27267);function s(e){const{click:t,mouseDown:n,touchEnd:s,touchStart:i,handler:a,reference:l,ownerDocument:u=document}=e,c=(0,r.useRef)(null),d=(0,r.useRef)(new CustomEvent("timestamp").timeStamp);return(0,r.useLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:s,touchStart:i},r=l?l.current:c.current;return(0,o.addOutsideEventListener)(d.current,r,a,u,e)}),[t,n,s,i,a]),l||c}},9745:(e,t,n)=>{
"use strict";n.d(t,{Icon:()=>o});var r=n(50959);const o=r.forwardRef(((e,t)=>{const{icon:n="",...o}=e;return r.createElement("span",{...o,ref:t,dangerouslySetInnerHTML:{__html:n}})}))},26996:(e,t,n)=>{"use strict";n.d(t,{Loader:()=>u});var r,o=n(50959),s=n(97754),i=n(74991),a=n(25650),l=n.n(a);!function(e){e[e.Initial=0]="Initial",e[e.Appear=1]="Appear",e[e.Active=2]="Active"}(r||(r={}));class u extends o.PureComponent{constructor(e){super(e),this._stateChangeTimeout=null,this.state={state:r.Initial}}render(){const{className:e,color:t="black",size:n="medium",staticPosition:r}=this.props,i=s(l().item,l()[t],l()[n]);return o.createElement("span",{className:s(l().loader,r&&l().static,this._getStateClass(),e)},o.createElement("span",{className:i}),o.createElement("span",{className:i}),o.createElement("span",{className:i}))}componentDidMount(){this.setState({state:r.Appear}),this._stateChangeTimeout=setTimeout((()=>{this.setState({state:r.Active})}),2*i.dur)}componentWillUnmount(){this._stateChangeTimeout&&(clearTimeout(this._stateChangeTimeout),this._stateChangeTimeout=null)}_getStateClass(){switch(this.state.state){case r.Initial:return l()["loader-initial"];case r.Appear:return l()["loader-appear"];default:return""}}}},90186:(e,t,n)=>{"use strict";function r(e){return s(e,i)}function o(e){return s(e,a)}function s(e,t){const n=Object.entries(e).filter(t),r={};for(const[e,t]of n)r[e]=t;return r}function i(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function a(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>o,filterDataProps:()=>r,filterProps:()=>s,isAriaAttribute:()=>a,isDataAttribute:()=>i})},48907:(e,t,n)=>{"use strict";function r(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>r})},53017:(e,t,n)=>{"use strict";function r(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){return r([e])}n.d(t,{isomorphicRef:()=>o,mergeRefs:()=>r})},67961:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>s,getRootOverlapManager:()=>a});var r=n(50151);class o{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class s{constructor(e=document){this._storage=new o,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const r=this._document.createElement("div");if(r.style.position=t.position,r.style.zIndex=this._index.toString(),r.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length
;if(t.index>=e)this._container.appendChild(r);else if(t.index<=0)this._container.insertBefore(r,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(r,e)}}else"reverse"===t.direction?this._container.insertBefore(r,this._container.firstChild):this._container.appendChild(r);return this._windows.set(e,r),++this._index,r}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){this.ensureWindow(e).style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const i=new WeakMap;function a(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,r.ensureDefined)(i.get(t));{const t=new s(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return i.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}},47201:(e,t,n)=>{"use strict";function r(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>r})},99054:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>u});const r=(()=>{let e;return()=>{var t;if(void 0===e){const n=document.createElement("div"),r=n.style;r.visibility="hidden",r.width="100px",r.msOverflowStyle="scrollbar",document.body.appendChild(n);const o=n.offsetWidth;n.style.overflow="scroll";const s=document.createElement("div");s.style.width="100%",n.appendChild(s);const i=s.offsetWidth;null===(t=n.parentNode)||void 0===t||t.removeChild(n),e=o-i}return e}})();function o(e,t,n){null!==e&&e.style.setProperty(t,n)}function s(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function i(e,t){return parseInt(s(e,t))}let a=0,l=!1;function u(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=s(t,"overflow"),a=i(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(o(n,"right",`${r()}px`),t.style.paddingRight=`${a+r()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),l)){o(n,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=r()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>o});var r=n(88803);const o={SmallHeight:r["small-height-breakpoint"],TabletSmall:r["tablet-small-breakpoint"],TabletNormal:r["tablet-normal-breakpoint"]}},21788:(e,t,n)=>{"use strict";n.d(t,{Autocomplete:()=>f});var r,o=n(44352),s=n(50959),i=n(97754),a=n(10381),l=n(78274),u=n(31261),c=n(76594);!function(e){e[e.Enter=13]="Enter",e[e.Space=32]="Space",e[e.Backspace=8]="Backspace",e[e.DownArrow=40]="DownArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.LeftArrow=37]="LeftArrow",e[e.Escape=27]="Escape",e[e.Tab=9]="Tab"}(r||(r={}));var d=n(65718),h=n(20817)
;function p(e,t){return""===e||-1!==t.toLowerCase().indexOf(e.toLowerCase())}class f extends s.PureComponent{constructor(e){if(super(e),this._containerInputElement=null,this._raf=null,this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleMeasure=()=>{if(this.state.isMeasureValid||!this.props.suggestionsInPortal||!this._containerInputElement)return;const{bottom:e,left:t,width:n}=this._containerInputElement.getBoundingClientRect();this.setState({appearingWidth:n,appearingPosition:{x:t,y:e},isMeasureValid:!0})},this._setInputRef=e=>{e&&(this._inputElement=e,this.props.setupHTMLInput&&this.props.setupHTMLInput(e),this._inputElement.addEventListener("keyup",this._handleKeyUpEnter))},this._setContainerInputRef=e=>{this._containerInputElement=e},this._handleCaretClick=()=>{this.state.isOpened?(this._close(),this.props.preventOnFocusOpen&&this._focus()):this.props.preventOnFocusOpen?this._open():this._focus()},this._handleOutsideClick=()=>{const{allowUserDefinedValues:e,value:t,onChange:n}=this.props,{queryValue:r}=this.state;e?n&&r!==t&&n(r):this.setState(this._valueToQuery(t)),this._close()},this._handleFocus=e=>{this.props.preventOnFocusOpen||this._open(),this.props.onFocus&&this.props.onFocus(e)},this._handleChange=e=>{const{preventSearchOnEmptyQuery:t,allowUserDefinedValues:n,onChange:r,onSuggestionsOpen:o,onSuggestionsClose:s}=this.props,i=e.currentTarget.value;if(t&&""===i)this.setState({queryValue:i,isOpened:!1,active:void 0}),s&&s();else{const e=this._suggestions(i),t=Object.keys(e).length>0;this.setState({queryValue:i,isOpened:t,active:n?void 0:this._getActiveKeyByValue(i)}),t&&o&&o()}n&&r&&r(i)},this._handleItemClick=e=>{const t=e.currentTarget.id;this.setState({queryValue:m(this.props.source)[t]}),this.props.onChange&&this.props.onChange(t),this._close()},this._handleKeyDown=e=>{if(-1===[r.DownArrow,r.UpArrow,r.Enter,r.Escape].indexOf(e.which))return;const{allowUserDefinedValues:t,value:n,onChange:o,onSuggestionsOpen:s}=this.props,{active:i,isOpened:a,queryValue:l}=this.state;a&&(e.preventDefault(),e.stopPropagation());const u=this._suggestions(l);switch(e.which){case r.DownArrow:case r.UpArrow:const c=Object.keys(u);if(!a&&c.length&&e.which===r.DownArrow){this.setState({isOpened:!0,active:c[0]}),s&&s();break}let d;if(void 0===i){if(e.which===r.UpArrow){this._close();break}d=0}else d=c.indexOf(i)+(e.which===r.UpArrow?-1:1);d<0&&(d=0),d>c.length-1&&(d=c.length-1);const h=c[d];this.setState({active:h});const p=document.getElementById(h);p&&this._scrollIfNotVisible(p,this._suggestionsElement);break;case r.Escape:this._close(),a||this._blur();break;case r.Enter:let f=i;t&&(a&&f?this.setState(this._valueToQuery(f)):f=l),void 0!==f&&(this._close(),a||this._blur(),f!==n?o&&o(f):this.setState(this._valueToQuery(f)))}},this._setSuggestionsRef=e=>{e&&(this._suggestionsElement=e)},this._scrollIfNotVisible=(e,t)=>{
const n=t.scrollTop,r=t.scrollTop+t.clientHeight,o=e.offsetTop,s=o+e.clientHeight;o<=n?e.scrollIntoView(!0):s>=r&&e.scrollIntoView(!1)},!(e=>Array.isArray(e.source)||!e.allowUserDefinedValues)(e))throw new Error("allowUserDefinedProps === true cay only be used if source is array");this.state={valueFromProps:e.value,isOpened:!1,active:e.value,queryValue:m(e.source)[e.value]||(e.allowUserDefinedValues?e.value:"")}}componentDidMount(){this.props.suggestionsInPortal&&window.addEventListener("resize",this._resize)}componentDidUpdate(){this.state.isOpened&&this._handleMeasure()}componentWillUnmount(){this._inputElement&&this._inputElement.removeEventListener("keyup",this._handleKeyUpEnter),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),window.removeEventListener("resize",this._resize)}render(){return s.createElement(c.OutsideEvent,{handler:this._handleOutsideClick,click:!0},(e=>s.createElement("div",{className:i(h.autocomplete,"js-dialog-skip-escape"),ref:e},s.createElement(u.InputControl,{id:this.props.id,name:this.props.name,endSlot:Object.keys(this._suggestions(this.state.queryValue)).length?s.createElement(l.EndSlot,null,s.createElement("span",{className:h.caret,onClick:this._handleCaretClick,tabIndex:-1},s.createElement(a.ToolWidgetCaret,{className:h.icon,dropped:this.state.isOpened}))):void 0,maxLength:this.props.maxLength,reference:this._setInputRef,containerReference:this._setContainerInputRef,stretch:!0,placeholder:this.props.placeholder,value:this.state.queryValue,intent:this.props.error?"danger":void 0,onChange:this._handleChange,onFocus:this._handleFocus,onBlur:this.props.onBlur,onMouseOver:this.props.onMouseOver,onMouseOut:this.props.onMouseOut,onKeyDown:this._handleKeyDown,autoComplete:"off",size:this.props.size}),this._renderSuggestions())))}static getDerivedStateFromProps(e,t){const{allowUserDefinedValues:n,value:r,source:o}=e;if(r===t.valueFromProps&&t.isOpened)return null;const s=n?r:""===r?"":m(o)[r]||t.queryValue;return{...t,valueFromProps:r,active:r,queryValue:s}}_renderSuggestions(){return this.props.suggestionsInPortal?this.state.isOpened?this._renderPortalSuggestions():null:this._renderSuggestionsItems()}_renderPortalSuggestions(){return s.createElement(d.Portal,null,this._renderSuggestionsItems())}_focus(){this._inputElement.focus()}_blur(){this._inputElement.blur()}_open(){const{onSuggestionsOpen:e}=this.props;this._focus(),this.setState({isOpened:!0,active:this.props.value}),e&&e()}_close(){const{onSuggestionsClose:e}=this.props;this.setState({isOpened:!1,active:void 0}),e&&e()}_suggestions(e){const{filter:t=p}=this.props,n=m(this.props.source),r={};return Object.keys(n).filter((r=>t(e,n[r]))).forEach((e=>r[e]=n[e])),r}_renderSuggestionsItems(){const e=this._suggestions(this.state.queryValue),t=Object.keys(e).map((t=>{const n=i(h.suggestion,this.state.active===t&&h.selected);return s.createElement("li",{id:t,key:t,className:n,onClick:this._handleItemClick},e[t])})),r=s.createElement("li",{className:h.noResults},o.t(null,void 0,n(56614)))
;if(!t.length&&this.props.noEmptyText)return null;const{appearingPosition:a,appearingWidth:l}=this.state;return s.createElement("ul",{className:i(h.suggestions,this.state.isOpened&&h.opened),ref:this._setSuggestionsRef,style:{left:a&&a.x,top:a&&a.y,width:l&&l}},t.length?t:r)}_handleKeyUpEnter(e){e.which===r.Enter&&e.stopImmediatePropagation()}_getActiveKeyByValue(e){const{filter:t=p}=this.props,n=this._suggestions(e),r=Object.keys(n);for(const o of r)if(t(e,n[o]))return o;return r[0]}_valueToQuery(e){return{queryValue:m(this.props.source)[e]||""}}}function m(e){let t={};return Array.isArray(e)?e.forEach((e=>{t[e]=e})):t=e,t}},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>o});var r=n(50959);class o extends r.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addListener(this._handleChange)}_unsubscribe(e){e.removeListener(this._handleChange)}}},65718:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>l,PortalContext:()=>u});var r=n(50959),o=n(962),s=n(36174),i=n(67961),a=n(60508);class l extends r.PureComponent{constructor(){super(...arguments),this._uuid=(0,s.guid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);return e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"",o.createPortal(r.createElement(u.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,i.getRootOverlapManager)():this.context}}l.contextType=a.SlotContext;const u=r.createContext(null)},60508:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>o,SlotContext:()=>s});var r=n(50959);class o extends r.Component{shouldComponentUpdate(){return!1}render(){return r.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const s=r.createContext(null)},10381:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>l});var r=n(50959),o=n(97754),s=n(9745),i=n(34587),a=n(578);function l(e){const{dropped:t,className:n}=e;return r.createElement(s.Icon,{className:o(n,i.icon,{[i.dropped]:t}),icon:a})}},95257:(e,t)=>{"use strict"
;var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.iterator;var f={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||f}function v(){}function _(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||f}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=_.prototype=new v;w.constructor=_,m(w,y.prototype),w.isPureReactComponent=!0;var b=Array.isArray,C=Object.prototype.hasOwnProperty,x={current:null},R={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var o,s={},i=null,a=null;if(null!=t)for(o in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(i=""+t.key),t)C.call(t,o)&&!R.hasOwnProperty(o)&&(s[o]=t[o]);var l=arguments.length-2;if(1===l)s.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===s[o]&&(s[o]=l[o]);return{$$typeof:n,type:e,key:i,ref:a,props:s,_owner:x.current}}function S(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var D=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function k(e,t,o,s,i){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var l=!1;if(null===e)l=!0;else switch(a){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return i=i(l=e),e=""===s?"."+P(l,0):s,b(i)?(o="",null!=e&&(o=e.replace(D,"$&/")+"/"),k(i,t,o,"",(function(e){return e}))):null!=i&&(S(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(D,"$&/")+"/")+e)),t.push(i)),1;if(l=0,s=""===s?".":s+":",b(e))for(var u=0;u<e.length;u++){var c=s+P(a=e[u],u);l+=k(a,t,o,c,i)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),u=0;!(a=e.next()).done;)l+=k(a=a.value,t,o,c=s+P(a,u++),i);else if("object"===a)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")
;return l}function N(e,t,n){if(null==e)return e;var r=[],o=0;return k(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var z={current:null},B={transition:null},I={ReactCurrentDispatcher:z,ReactCurrentBatchConfig:B,ReactCurrentOwner:x};t.Children={map:N,forEach:function(e,t,n){N(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return N(e,(function(){t++})),t},toArray:function(e){return N(e,(function(e){return e}))||[]},only:function(e){if(!S(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=i,t.PureComponent=_,t.StrictMode=s,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),s=e.key,i=e.ref,a=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,a=x.current),void 0!==t.key&&(s=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)C.call(t,u)&&!R.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];o.children=l}return{$$typeof:n,type:e.type,key:s,ref:i,props:o,_owner:a}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=S,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=B.transition;B.transition={};try{e()}finally{B.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return z.current.useCallback(e,t)},t.useContext=function(e){return z.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return z.current.useDeferredValue(e)},t.useEffect=function(e,t){return z.current.useEffect(e,t)},t.useId=function(){return z.current.useId()},t.useImperativeHandle=function(e,t,n){return z.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return z.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return z.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return z.current.useMemo(e,t)},t.useReducer=function(e,t,n){return z.current.useReducer(e,t,n)},t.useRef=function(e){
return z.current.useRef(e)},t.useState=function(e){return z.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return z.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return z.current.useTransition()},t.version="18.2.0"},50959:(e,t,n)=>{"use strict";e.exports=n(95257)},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},7720:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 17" width="17" height="17" fill="currentColor"><path d="m.58 1.42.82-.82 15 15-.82.82z"/><path d="m.58 15.58 15-15 .82.82-15 15z"/></svg>'},20036:e=>{e.exports={ar:["إلغاء"],ca_ES:["Cancel·la"],cs:["Zrušit"],de:["Abbrechen"],el:["Άκυρο"],en:"Cancel",es:["Cancelar"],fa:["لغو"],fr:["Annuler"],he_IL:["ביטול"],hu_HU:["Törlés"],id_ID:["Batal"],it:["Annulla"],ja:["キャンセル"],ko:["취소"],ms_MY:["Batal"],nl_NL:["Annuleren"],pl:["Anuluj"],pt:["Cancelar"],ro:"Cancel",ru:["Отмена"],sv:["Avbryt"],th:["ยกเลิก"],tr:["İptal"],vi:["Hủy bỏ"],zh:["取消"],zh_TW:["取消"]}},68988:e=>{e.exports={ar:["موافق"],ca_ES:["Acceptar"],cs:"Ok",de:"Ok",el:"Ok",en:"Ok",es:["Aceptar"],fa:"Ok",fr:["D'accord"],he_IL:["אוקיי"],hu_HU:["Oké"],id_ID:"Ok",it:"Ok",ja:["OK"],ko:["확인"],ms_MY:"Ok",nl_NL:"Ok",pl:"Ok",pt:"Ok",ro:"Ok",ru:["Ок"],sv:["OK"],th:["ตกลง"],tr:["Tamam"],vi:"Ok",zh:["确认"],zh_TW:["確認"]}},56614:e=>{e.exports={ar:["لا توجد نتائج"],ca_ES:["No s'han trobat resultats"],cs:"No results found",de:["Keine Ergebnisse"],el:"No results found",en:"No results found",es:["No se han encontrado resultados"],fa:"No results found",fr:["Pas de résultat trouvé"],he_IL:["לא נמצאו תוצאות"],hu_HU:"No results found",id_ID:["Hasil tidak ditemukan"],it:["Nessun risultato trovato"],ja:["該当なし"],ko:["결과를 찾을 수 없습니다"],ms_MY:["Tiada keputusan dijumpai"],nl_NL:"No results found",pl:["Brak wyników przeszukiwania"],pt:["Nenhum resultado encontrado"],ro:"No results found",ru:["Не найдено результатов"],sv:["Inga resultat hittades"],th:["ไม่พบข้อมูลใดๆ"],tr:["Hiç sonuç bulunamadı"],vi:["Không tìm thấy kết quả"],zh:["未搜寻结果"],zh_TW:["未找到結果"]}},85520:e=>{e.exports={ar:["حفظ"],ca_ES:["Desa"],cs:["Uložit"],de:["Speichern"],el:["Αποθήκευση"],en:"Save",es:["Guardar"],fa:["ذخیره"],fr:["Sauvegarder"],he_IL:["שמור"],hu_HU:["Mentés"],id_ID:["Simpan"],it:["Salva"],ja:["保存"],ko:["저장"],ms_MY:["Simpan"],nl_NL:["Opslaan"],pl:["Zapisz"],pt:["Salvar"],ro:"Save",ru:["Сохранить"],sv:["Spara"],th:["บันทึก"],tr:["Kaydet"],vi:["Lưu"],zh:["保存"],zh_TW:["儲存"]}}}]);