## General
* Please use English. We want to share experience between our customers to increase effectiveness of this bug tracker.

## Reporting a bug
* Please help us spend our time effectively and always update your Library's build to the latest `unstable` version to check if the issue still happens. You can also find the Charting Library built from `unstable` at https://demo_chart.tradingview.com
* Report a version of the library. You can check it with `TradingView.version()` from the browser console
* Report a device/browser where the issue can be replicated
* Provide step-by-step way to reproduce the issue
* Provide a code example
* Give a link to your Charting Library where we can reproduce it

## Asking a question
* We spent a lot of time creating those [docs](https://github.com/tradingview/charting_library/wiki) for you to make your life easier. Please give it a try. If you are a newcomer please make sure that you've read the [Best Practices](https://github.com/tradingview/charting_library/wiki/Best-practices) and [Frequently Asked Questions](https://github.com/tradingview/charting_library/wiki/Frequently-Asked-Questions).
* Describe what do you want to achieve
* Provide screenshots if possible
 
## Requesting a new feature
* While we would love to be able to implement everything, we simply don’t have the resources. At this time, please be aware we have long term plans and quick implementation of new feature requests should not be expected. However, we review everything and take your many great suggestions into account. 
* Thank you for playing an active part in the TradingView community!
