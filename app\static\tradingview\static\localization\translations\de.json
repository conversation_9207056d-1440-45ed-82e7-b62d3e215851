{"ticks_slippage ... ticks": "Ticks", "Months_interval": "Monate", "Realtime": "Echtzeit", "RSI Length_input": "RSI Länge", "Sync to all charts": "Alle Charts synchronisieren", "month": "<PERSON><PERSON>", "roclen1_input": "roclen1", "Unmerge Down": "Verschmelzung nach unten aufheben", "Percents": "Prozente", "Search Note": "Notiz suchen", "Do you really want to delete Chart Layout '{0}' ?": "Möchten Sie wirklich diese Chartskizze '{0}' löschen?", "Quotes are delayed by {0} min and updated every 30 seconds": "Kurse sind um {0} Min. verzögert und werden alle 30 Sekunden aktualisiert", "Magnet Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OSC_input": "OSC", "Hide alert label line": "", "Volume_study": "Volumen", "Lips_input": "Lips (Lippen)", "Show real prices on price scale (instead of Heikin-Ashi price)": "Reale Preise auf der Preisachse anzeigen (anstatt der Heikin-Ashi-Preise)", "Histogram": "Histogramm", "Base Line_input": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Step": "Stuf<PERSON>", "Insert Study Template": "Studienvorlage einfügen", "Fib Time Zone": "Fib Zeitzonen", "SMALen2_input": "SMALen2", "Bollinger Bands_study": "Bollinger Bands", "Show/Hide": "Anzeigen/Verbergen", "Upper_input": "Ober", "exponential_input": "exponentiell", "Move Up": "Aufwärts", "This indicator cannot be applied to another indicator": "Diese Anzeige kann nicht bei einer anderen Anzeige angewendet werden.", "Scales Properties...": "Skalierungseigenschaften...", "Count_input": "<PERSON><PERSON><PERSON>", "Full Circles": "Volle Kreise", "Industry": "Branche", "OnBalanceVolume_input": "OnBalanceVolumen", "Cross_chart_type": "Cross", "H_in_legend": "H", "a day": "ein <PERSON>", "R_data_mode_replay_letter": "R", "Accumulation/Distribution_study": "Akkumulation/Verteilung", "Rate Of Change_study": "Veränderungsrate", "in_dates": "in", "Clone": "Duplizieren", "Color 7_input": "Farbe 7", "Chop Zone_study": "Chop Zone", "Scales Properties": "Skalierungseigenschaften", "Trend-Based Fib Time": "Trendbasierte Fib-Zeit", "Remove All Indicators": "Alle Indikatoren entfernen", "Oscillator_input": "<PERSON><PERSON><PERSON><PERSON>", "Last Modified": "Zuletzt geändert", "yay Color 0_input": "yay Farbe 0", "Labels": "Beschriftungen", "Chande Kroll Stop_study": "Chande-Kroll-Stop", "Hours_interval": "Stunden", "Allow up to": "Ermöglicht bis zu", "Scale Right": "Skalierung rechts", "Money Flow_study": "Geldfluss", "siglen_input": "siglen", "Indicator Labels": "<PERSON><PERSON><PERSON><PERSON>", "__specialSymbolOpen__Tomorrow at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__morgen bei__specialSymbolClose____dayTime__", "Toggle Percentage": "Auf Prozent umschalten", "Remove All Drawing Tools": "Alle Zeichen-Tools entfernen", "Remove all line tools for ": "Alle Linien-Tools entfernen für", "Linear Regression Curve_study": "Linere Regressionskurve", "Symbol_input": "Symbol", "increment_input": "Schrittweite", "Compare or Add Symbol...": "Symbol hinzufügen oder vergleichen...", "__specialSymbolOpen__Last__specialSymbolClose__ __dayName__ __specialSymbolOpen__at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__letzter__specialSymbolClose____dayName____specialSymbolOpen__bei__specialSymbolClose____dayTime__", "Save Chart Layout": "Chart-Layout speichern", "Number Of Line": "<PERSON><PERSON><PERSON><PERSON>", "Label": "Beschriftung", "Post Market": "<PERSON><PERSON>", "second": "Sekunde", "Change Hours To": "Stunden ändern in", "smoothD_input": "smoothD", "Falling_input": "Fallend", "X_input": "X", "Risk/Reward short": "Risiko/<PERSON>ewinn Short", "UpperLimit_input": "OberesLimit", "Donchian Channels_study": "<PERSON><PERSON><PERSON>", "Entry price:": "Einstiegspreis:", "Circles": "Kreise", "Head": "<PERSON><PERSON>", "Stop: {0} ({1}) {2}, Amount: {3}": "Stop: {0}({1}){2}, Betrag: {3}", "Mirrored": "Gespiegelt", "Ichimoku Cloud_study": "Ichimoku Cloud", "Signal smoothing_input": "Signalglättung", "Toggle Log Scale": "Auf logarithmische Skalierung umschalten", "Toggle Auto Scale": "Auf automatische Skalierung umschalten", "Grid": "Gitter", "Triangle Down": "Dreieck Abwärts", "Apply Elliot Wave Minor": "Minimale Elliot <PERSON> an<PERSON>", "Rename...": "Umbenennen...", "Smoothing_input": "Glättung", "Color 3_input": "Farbe 3", "Jaw Length_input": "<PERSON><PERSON> (Kiefer) Länge", "Delete all drawing for this symbol": "Alle Zeichenwerkzeuge für dieses Symbol löschen", "Fundamentals": "Fundamentaldaten", "Keltner Channels_study": "<PERSON><PERSON><PERSON> Kanäle", "Long Position": "Long-Position", "Bands style_input": "Bänder-Stil", "Undo {0}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "With Markers": "<PERSON><PERSON>", "Momentum_study": "Momentum", "MF_input": "MF", "Gann Box": "Gann-Box", "Switch to the next chart": "Zum nächsten Chart wechseln", "charts by TradingView": "<PERSON> <PERSON>", "Fast length_input": "<PERSON><PERSON><PERSON>", "Apply Elliot Wave": "<PERSON> an<PERSON>", "Disjoint Angle": "<PERSON><PERSON><PERSON><PERSON>", "W_interval_short": "W", "Show Only Future Events": "Nur zukünftige Ereignisse anzeigen", "Log Scale": "Logarithmische Skalierung", "Line - High": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "Zurich": "Zürich", "Equality Line_input": "Linie der Gleichwertigkeit", "Short_input": "<PERSON><PERSON>", "Fib Wedge": "<PERSON><PERSON>", "Line": "<PERSON><PERSON>", "Session": "Sit<PERSON>ng", "Down fractals_input": "Down Bruchteile", "smalen2_input": "smalen2", "isCentered_input": "istZentriert", "Border": "<PERSON><PERSON><PERSON>", "Klinger Oscillator_study": "<PERSON><PERSON> Osz<PERSON>tor", "Absolute": "Absolut", "Tue": "Die", "Style": "Stil", "Show Left Scale": "Linke Preisachse anzeigen", "SMI Ergodic Indicator/Oscillator_study": "SMI Ergodic Indikator/Oszillator", "Last available bar": "Letzter vorhandener Balken", "Manage Drawings": "Zeichnungen verwalten", "Analyze Trade Setup": "Trade Setup analysieren", "No drawings yet": "<PERSON>ch keine Zei<PERSON>nungen", "SMI_input": "SMI", "Chande MO_input": "<PERSON><PERSON>", "jawLength_input": "<PERSON><PERSON><PERSON><PERSON>", "TRIX_study": "TRIX", "Show Bars Range": "<PERSON><PERSON><PERSON><PERSON> anzeigen", "RVGI_input": "RVGI", "Last edited ": "Zuletzt geändert ", "signalLength_input": "signalLänge", "%s ago_time_range": "vor %s", "Reset Settings": "Einstellungen zurücksetzen", "d_dates": "t", "Are you sure?": "Sind Sie sicher?", "Bar #": "Balken Nr.", "Recalculate After Order filled": "Nach Ausführen der Order neu berechnen", "Source_compare": "<PERSON><PERSON>", "Q_input": "Q", "Correlation Coefficient_study": "Korrelations-Koeffizient", "Delayed": "Verzögert", "Bottom Labels": "Beschriftungen", "Text color": "Textfarbe", "Levels": "Level", "Short Length_input": "<PERSON><PERSON><PERSON>", "teethLength_input": "teethLänge", "Visible Range_study": "Visible Range", "Hong Kong": "Hongkong", "Open {{symbol}} Text Note": "{{symbol}} <PERSON><PERSON><PERSON><PERSON>", "Lock All Drawing Tools": "Fixiere alle Zeichen-Tools", "Long_input": "<PERSON>", "Right End": "<PERSON><PERSON><PERSON> Ende", "Show Symbol Last Value": "Letzten Symbol-Wert anzeigen", "Head & Shoulders": "Kopf- & Schultern", "Do you really want to delete Study Template '{0}' ?": "Möchten Sie wirklich diese Studienvorlage '{0}' löschen?", "Favorite Drawings Toolbar": "Bevorzugte Zeichenwerkzeugleiste", "Properties...": "Eigenschaften...", "Reset Scale": "Skalierung zurücksetzen", "MA Cross_study": "MA Cross", "Trend Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Snapshot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Signal line period_input": "Singnallinienperiode", "Timezone/Sessions Properties...": "Zeitzonen- / Sitzungseinstellungen...", "Line Break": "Linienunterbrechung", "Quantity": "<PERSON><PERSON><PERSON>", "Price Volume Trend_study": "Preis-Volumen-Trend", "Auto Scale": "Autoskalierung", "hour": "Stunde", "Delete chart layout": "Chart Layout löschen", "F_data_mode_forbidden_letter": "F", "Risk/Reward long": "<PERSON>/<PERSON><PERSON><PERSON>", "Long RoC Length_input": "Lange Ro<PERSON>", "Length3_input": "Länge 3", "+DI_input": "+DI", "Length_input": "<PERSON><PERSON><PERSON>", "Use one color": "Eine Farbe verwenden", "Chart Properties": "Chart Einstellungen", "No Overlapping Labels_scale_menu": "<PERSON>ine <PERSON>den Label", "Exit Full Screen (ESC)": "<PERSON><PERSON><PERSON><PERSON> (ESC)", "MACD_study": "MACD", "Show Economic Events on Chart": "Wirtschaftliche Ereignisse auf dem Chart anzeigen", "Moving Average_study": "<PERSON><PERSON><PERSON><PERSON>", "Show Wave": "Welle anzeigen", "Failure back color": "Hintergrundfarbe Fehler", "Below Bar": "Unterhalb der Bars", "Time Scale": "Zeitachse", "<p>Only <b>D, W, M</b> intervals are supported for this symbol/exchange. You will be automatically switched to a D interval. Intraday intervals are not available because of exchange policies.</p>": "<p>Nur<b>D,W,M</b>Intervalle werden für dieses Symbol / diese Börse unterstützt. Das Chart wechselt automatisch zu einem D Intervall. Intraday Intervalle sind wegen Regulierungen der Börse nicht verfügbar.</p>", "Extend Left": "Nach links verlängern", "Date Range": "Datumsbereich", "Min Move": "<PERSON><PERSON>", "Price format is invalid.": "Preisformat ist ungültig.", "Show Price": "Preise anzeigen", "Level_input": "Niveau", "Commodity Channel Index_study": "Commodity Channel Index", "Elder's Force Index_input": "Elder's Force Index", "Gann Square": "Gann Square (Quadrat)", "Currency": "Währung", "Color bars based on previous close": "Balken gemäß vorherigem Schlußkurs färben.", "Change band background": "Bandhintergrund ändern", "Target: {0} ({1}) {2}, Amount: {3}": "Ziel: {0} ({1}) {2}, Betrag: {3}", "Zoom Out": "Verkleinern", "This chart layout has a lot of objects and can't be published! Please remove some drawings and/or studies from this chart layout and try to publish it again.": "Dieser Entwurf enthält zu viele Objekte und kann nicht veröffentlicht werden! Bitte entfernen Sie einige Zeichnungen und / oder Texte von diesem Entwurf und versuchen Sie es erneut.", "Anchored Text": "Verankerter Text", "Long length_input": "<PERSON>", "Edit {0} Alert...": "{0} <PERSON><PERSON><PERSON> bearbeiten ...", "Previous Close Price Line": "Vorherige Schlusskurslinie", "Up Wave 5": "Aufwärtswelle 5", "Qty: {0}": "Anz:{0}", "Heikin Ashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Aroon_study": "Aroon", "show MA_input": "MA anzeigen", "Lead 1_input": "Lead 1", "Short Position": "Short-Position", "SMALen1_input": "SMALen1", "P_input": "P", "Apply Default": "Standard anwenden", "SMALen3_input": "SMALen3", "Average Directional Index_study": "Average Directional Index", "Fr_day_of_week": "Fr", "Invite-only script. Contact the author for more information.": "Auf-Einladung- Skript. Kontaktieren sie den Autor für nähere Informationen.", "Curve": "<PERSON><PERSON>", "a year": "ein <PERSON>", "Target Color:": "<PERSON><PERSON><PERSON>l Farbe:", "Bars Pattern": "Balkenmuster", "D_input": "D", "Font Size": "Schriftgröße", "Create Vertical Line": "<PERSON><PERSON><PERSON><PERSON>", "p_input": "p", "Rotated Rectangle": "<PERSON><PERSON><PERSON><PERSON>", "Chart layout name": "Chart Layout Name", "Fib Circles": "Fib Kreise", "Apply Manual Decision Point": "Manuellen Entscheidungspunkt verwenden", "Dot": "<PERSON><PERSON>", "Target back color": "Hintergrundfarbe Kursziel", "All": "Alle", "orders_up to ... orders": "Order", "Dot_hotkey": "<PERSON><PERSON>", "Lead 2_input": "Lead 2", "Save image": "Bild speichern", "Move Down": "Abwärts", "Triangle Up": "<PERSON><PERSON><PERSON>", "Box Size": "Boxgröße", "Navigation Buttons": "Navigationstasten", "Apply": "<PERSON><PERSON><PERSON>", "Down Wave 3": "Abwärtsbewegung 3", "Plots Background_study": "<PERSON><PERSON><PERSON>", "Marketplace Add-ons": "Handelsplatz Add-Ons", "Sine Line": "Sinuslinie", "Fill": "<PERSON><PERSON><PERSON>", "%d day": "%d Tag", "Hide": "Verbergen", "Toggle Maximize Chart": "<PERSON><PERSON> maximierten Chart umschalten", "Target text color": "Textfarbe Kursziel", "Scale Left": "Skalierung links", "Elliott Wave Subminuette": "<PERSON><PERSON><PERSON><PERSON>", "Down Wave C": "Abwärtsbewegung C", "UO_input": "UO", "Go to Date...": "<PERSON><PERSON><PERSON> zu <PERSON>tum...", "Text Alignment:": "Textausrichtung:", "R_data_mode_realtime_letter": "R", "Extend Lines": "<PERSON><PERSON>", "Conversion Line_input": "Umrechnungslinie", "March": "<PERSON><PERSON><PERSON>", "Su_day_of_week": "Su", "Exchange": "Börse", "Arcs": "Bögen", "Regression Trend": "Regressionstrend", "Fib Spiral": "<PERSON>b <PERSON>", "Double EMA_study": "Double EMA", "minute": "Minute", "All Indicators And Drawing Tools": "Alle Indikatoren und Zeichen-Tools", "Indicator Last Value": "Letzter Wert des Indikators", "Sync drawings to all charts": "Zeichen-Tools mit allen Charts synchronisieren", "Change Average HL value": "Durchschnittlichen HL-Wert ändern", "Stop Color:": "Stop Farbe:", "Stay in Drawing Mode": "<PERSON><PERSON> b<PERSON>n", "Bottom Margin": "unterer Abstand", "Save Chart Layout saves not just some particular chart, it saves all charts for all symbols and intervals which you are modifying while working with this Layout": "Chart-Layout speichern speichert nicht nur einen bestimmten Chart, sondern es speichert alle Charts für alle Symbole und Intervalle, welche Sie verändern, wä<PERSON><PERSON> Si<PERSON> in diesem Layout arbeiten.", "Average True Range_study": "Average True Range", "Max value_input": "Maximalwert", "MA Length_input": "MA Länge", "Invite-Only Scripts": "Auf-Einladung-Skripte", "in %s_time_range": "in %s", "Extend Bottom": "Nach unten verlängern", "sym_input": "sym", "DI Length_input": "DI-Länge", "Rome": "Rom", "Scale": "Skalierung", "Periods_input": "Zeiträume", "Arrow": "Pfeil", "Square": "<PERSON><PERSON><PERSON>", "Basis_input": "<PERSON><PERSON>", "Arrow Mark Down": "<PERSON><PERSON><PERSON> nach unten", "lengthStoch_input": "LängeStoch", "Objects Tree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remove from favorites": "Aus Favoriten entfernen", "Show Symbol Previous Close Value": "Vorigen Schlusskurs des Symbols anzeigen", "Scale Series Only": "Nur Datenreihe skalieren", "Source text color": "Textfarbe Ursprung", "Simple": "<PERSON><PERSON><PERSON>", "Report a data issue": "Ein Problem mit den Daten melden", "Arnaud Legoux Moving Average_study": "<PERSON><PERSON><PERSON>", "Smoothed Moving Average_study": "Geglättete Gleitende Durchschnitt", "Lower Band_input": "Unteres Band", "Verify Price for Limit Orders": "Preis für Limit-Orders überprüfen", "VI +_input": "VI +", "Line Width": "Linienbreite", "Contracts": "Verträge", "Always Show Stats": "Statistiken immer anzeigen", "Down Wave 4": "Abwärtsbewegung 4", "ROCLen2_input": "ROCLen2", "Simple ma(signal line)_input": "Simple ma(Signallinie)", "Change Interval...": "Intervall ändern", "Public Library": "Öffentliche Bibliothek", " Do you really want to delete Drawing Template '{0}' ?": " Möchten Sie die Zeichenvorlage '{0}' wirklich löschen?", "Sat": "Sa", "Left Shoulder": "<PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "CRSI_study": "CRSI", "Close message": "Nachricht schließen", "Base currency": "Basis-Währung", "Show Drawings Toolbar": "Zeichnung Toolbar anzeigen", "Chaikin Oscillator_study": "Chaikin-Oszillator", "Price Source": "<PERSON><PERSON><PERSON><PERSON>", "Market Open": "Börse g<PERSON>öffnet", "Color Theme": "Farbthema", "Awesome Oscillator_study": "Awesome Oscillator", "Bollinger Bands Width_input": "Bollinger-Bänder-Breite", "long_input": "lang", "Error occured while publishing": "Während der Veröffentlichung ist ein Fehler aufgetreten", "Fisher_input": "<PERSON>", "Color 1_input": "Farbe 1", "Moving Average Weighted_study": "Gewichteter Gleitender Durchschnitt", "Save": "Speichern", "Type": "<PERSON><PERSON>", "Accumulative Swing Index_study": "Accumulative Swing Index", "Load Chart Layout": "Chart-Layout laden", "Show Values": "Werte anzeigen", "Fib Speed Resistance Fan": "Fib Speed Resistance Fan (Fächer)", "Bollinger Bands Width_study": "Bollinger Bands Weite", "This chart layout has more than 1000 drawings, which is a lot! This may negatively affect performance, storage and publishing. We recommend to remove some drawings to avoid potential performance issues.": "Dieses Chart-Layout verfügt über 1000 Zeichnungselemente. Das ist eine Menge! Dies kann die Leistung, das Speichern und die Veröffentlichung negativ beeinflussen. Wir empfehlen einige Zeichnungselemente zu entfernen, um eventuelle Leistungsprobleme zu verhindern.", "Left End": "link<PERSON>", "%d year": "%d <PERSON><PERSON>r", "Always Visible": "<PERSON>mmer sichtbar", "S_data_mode_snapshot_letter": "S", "Flag": "Flagge", "Elliott Wave Circle": "<PERSON>", "Earnings breaks": "Earnings-Breaks", "Change Minutes From": "<PERSON><PERSON><PERSON> Minuten von", "Do not ask again": "Nicht erneut fragen", "Displacement_input": "Verschiebung", "smalen4_input": "smalen4", "CCI_input": "CCI", "Upper Deviation_input": "Obere Abweichung", "(H + L)/2": "", "XABCD Pattern": "XABCD-<PERSON><PERSON>", "Schiff Pitchfork": "Schiff-Pitchfork", "Copied to clipboard": "In die Zwischenablage kopiert", "Flipped": "Umgedreht", "DEMA_input": "DEMA", "Move_input": "Bewegung", "NV_input": "NV", "Choppiness Index_study": "Choppiness Index", "Study Template '{0}' already exists. Do you really want to replace it?": "Studienvorlage '{0}' gibt es bereits. Möchten Sie es wirklich ersetzen?", "Merge Down": "Nach unten zusammenführen", " per contract": " pro Kontrakt", "Overlay the main chart": "<PERSON><PERSON><PERSON><PERSON>", "Delete": "Löschen", "Save Indicator Template As": "Indikatorvorlage speichern als", "Length MA_input": "Länge MA", "percent_input": "Prozent", "{0} copy": "{0} kop<PERSON>en", "Avg HL in minticks": "Durchschnittliche HL in Minticks", "Accumulation/Distribution_input": "Akkumulation/Distribution", "C_in_legend": "C", "Weeks_interval": "<PERSON><PERSON><PERSON>", "smoothK_input": "smoothK", "Percentage_scale_menu": "Prozentsatz", "Change Extended Hours": "Erweiterte Stunden ändern", "MOM_input": "MOM", "h_interval_short": "Std.", "Change Interval": "Intervall ändern", "Change area background": "Bereichshintergrund ändern", "Modified Schiff": "Modifizierte Schiff", "top": "Oberseite", "Custom color...": "Benutzerdefinierte Farbe...", "Send Backward": "Eins nach hinten verschieben", "Mexico City": "Mexiko City", "TRIX_input": "TRIX", "Show Price Range": "Preisspanne anzeigen", "Elliott Major Retracement": "Major <PERSON> Retracement", "ASI_study": "ASI", "Notification": "Benachrichtigung", "Fri": "Fr", "just now": "gerade", "Forecast": "Prognose", "Fraction part is invalid.": "Dieser Teil ist ungültig", "Connecting": "Es wird verbunden", "Ghost Feed": "", "Signal_input": "Signal", "Histogram_input": "Histogramm", "The Extended Trading Hours feature is available only for intraday charts": "Die Funktion \"Erweiterte Handelszeiten\" ist nur für Intraday-Charts verfügbar", "Stop syncing": "Synchronisierung stoppen", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StdDev_input": "StdAbw", "EMA Cross_study": "EMA Cross", "Conversion Line Periods_input": "Perioden der Umrechnungslinie", "Diamond": "<PERSON><PERSON><PERSON>", "My Scripts": "<PERSON><PERSON>", "Monday": "Montag", "Add Symbol_compare_or_add_symbol_dialog": "Symbol hinzufügen", "Williams %R_study": "Williams %R", "Symbol": "Symbol / Währungspaar", "a month": "ein <PERSON>", "Precision": "Präzision", "depth_input": "<PERSON><PERSON><PERSON>", "Go to": "<PERSON><PERSON><PERSON> zu", "Please enter chart layout name": "<PERSON>te geben Si<PERSON> einen Namen für das Chart-Layout ein.", "Mar": "Mrz", "VWAP_study": "VWAP", "Offset": "Ausgleich", "Date": "Datum", "Apply WPT Up Wave": "WPT Up Wave anwenden", "__dayName__ __specialSymbolOpen__at__specialSymbolClose__ __dayTime__": "__dayName____specialSymbolOpen__bei__specialSymbolClose____dayTime__", "Toggle Maximize Pane": "<PERSON><PERSON> maximierten Ausschnitt umschalten", "Search": "<PERSON><PERSON>", "Zig Zag_study": "Zig Zag", "Actual": "Ist", "SUCCESS": "ERFOLG", "Long period_input": "<PERSON><PERSON>", "length_input": "<PERSON><PERSON><PERSON>", "roclen4_input": "roclen4", "Price Line": "Preislinie", "Area With Breaks": "Fläche mit Lücken", "Median_input": "Median", "Stop Level. Ticks:": "Stop Wert. Ticks:", "Economy & Symbols": "Wirtschaft und Symbole", "Circle Lines": "Kreislinien", "Visual Order": "Visuelle Reihenfolge", "__specialSymbolOpen__Yesterday at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__gestern bei__specialSymbolClose____dayTime__", "Stop Background Color": "Hintergrundfarbe beenden", "Slow length_input": "Lang<PERSON>", "Sector": "Se<PERSON><PERSON>", "powered by TradingView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "Marker Color": "Stiftfarbe", "TEMA_input": "TEMA", "Format...": "Formatierung...", "Min Move 2": "Min Bewegung 2", "Extend Left End": "Linkes Ende verlängern", "Advance/Decline_study": "Anstieg/Rückgang", "Any Number": "Jegliche <PERSON>ummer", "Flag Mark": "Flagge", "Drawings": "Z<PERSON><PERSON>nungen", "Cancel": "Abbrechen", "Compare or Add Symbol": "Symbol hinzufügen oder vergleichen", "Redo": "Wiederherstellen", "Hide Drawings Toolbar": "Zeichnen-Werkzeugleiste ausblenden", "Ultimate Oscillator_study": "<PERSON>lt<PERSON><PERSON><PERSON>", "Vert Grid Lines": "<PERSON><PERSON><PERSON><PERSON>", "Growing_input": "Wachsend", "Angle": "<PERSON><PERSON>", "Plot_input": "<PERSON><PERSON><PERSON><PERSON>", "Color 8_input": "Farbe 8", "Indicators, Fundamentals, Economy and Add-ons": "Indikatoren, Unternehmens- und Wirtschaftsdaten und Add-ons", "h_dates": "h", "ROC Length_input": "ROC Periodenlänge", "roclen3_input": "roclen3", "Overbought_input": "Überkauft", "Extend Top": "Nach oben verlängern", "Change Minutes To": "Wechsle Minuten zu", "No study templates saved": "<PERSON><PERSON>udienvorlagen gespeichert", "Trend Line": "Trendlinie", "TimeZone": "Zeitzone", "Your chart is being saved, please wait a moment before you leave this page.": "Ihre Chart wurde g<PERSON>, bitte warten Si<PERSON> einen Moment bevor Si<PERSON> diese Seite verlassen.", "Percentage": "Prozentsatz", "Tu_day_of_week": "Tu", "Extended Hours": "Verlängerte Handelszeit", "Triangle": "<PERSON><PERSON><PERSON>", "Line With Breaks": "<PERSON>ie mit Unterbrechungen", "Period_input": "Zeitraum", "Watermark": "Wasserzeichen", "Trigger_input": "Auslöser", "SigLen_input": "SigLen", "Extend Right": "Nach rechts verlängern", "Color 2_input": "Farbe 2", "Show Prices": "Preise anzeigen", "Unlock": "Entsperren", "Copy": "<PERSON><PERSON><PERSON>", "high": "High", "Arc": "Bogen", "Edit Order": "Auftrag bearbeiten", "January": "<PERSON><PERSON><PERSON>", "Arrow Mark Right": "<PERSON><PERSON><PERSON> nach rechts", "Extend Alert Line": "Alarmlinie erweitern", "Background color 1": "Hintergrundfarbe 1", "RSI Source_input": "RSI Quelle", "Close Position": "Posi<PERSON> s<PERSON><PERSON><PERSON>", "Stop syncing drawing": "Zeichentool-Synchronisierung beenden", "Visible on Mouse Over": "<PERSON><PERSON><PERSON>, wenn der Mauszeiger darüber bewegt wird", "MA/EMA Cross_study": "MA/EMA Cross", "Thu": "Do", "Vortex Indicator_study": "Vortex-Indikator", "view-only chart by {user}": "schreibgeschützter Chart von {user}", "ROCLen1_input": "ROCLen1", "M_interval_short": "M", "Chaikin Oscillator_input": "Chaikin-Oszillator", "Price Levels": "Preisniveaus", "Show Splits": "<PERSON>s anzeigen", "Zero Line_input": "Null<PERSON>e", "Replay Mode": "Wiedeholungs-Modus", "__specialSymbolOpen__Today at__specialSymbolClose__ __dayTime__": "__specialSymbolOpen__heute bei__specialSymbolClose____dayTime__", "Increment_input": "Schrittweite", "Days_interval": "Tage", "Show Right Scale": "Rechte Preisachse anzeigen", "Show Alert Labels": "Alarm-Labels anzeigen", "Historical Volatility_study": "Historische Volatilität", "Lock": "Fixieren", "length14_input": "Länge14", "ext": "verl.", "Date and Price Range": "Daten- und Preisbereich", "Polyline": "Linienzug", "Reconnect": "Neu verbinden", "Lock/Unlock": "Fixieren / Lösen", "Base Level": "Grundwert", "Label Down": "Beschriftung Abwärts", "Saturday": "Samstag", "Symbol Last Value": "Letzter Symbolwert", "Above Bar": "Oberhalb des Balkens", "Studies": "St<PERSON><PERSON>", "Color 0_input": "Farbe 0", "Add Symbol": "Symbol hinzufügen", "maximum_input": "Maximum", "Wed": "<PERSON>", "D_data_mode_delayed_letter": "D", "Sigma_input": "Sigma", "VWMA_study": "VWMA", "fastLength_input": "schnelleLänge", "Time Levels": "Zeitebenen", "Width": "Breite", "Loading": "Wird geladen", "Template": "Vorlage", "Use Lower Deviation_input": "<PERSON>utze untere Abweichung", "Up Wave 3": "Aufwärtswelle 3", "Parallel Channel": "<PERSON><PERSON><PERSON>", "Time Cycles": "Zeitzyklen", "Second fraction part is invalid.": "Zweiter Bruchteil ungültig", "Divisor_input": "Divisor", "Baseline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Down Wave 1 or A": "Abwärtsbewegung 1 oder A", "ROC_input": "ROC", "Dec": "<PERSON>z", "Ray": "<PERSON><PERSON><PERSON>", "Extend": "Verlängern", "length7_input": "Länge7", "Bring Forward": "Nach vorne bringen", "Bottom": "Boden", "Apply Elliot Wave Major": "Haupt- <PERSON>", "Undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Window Size_input": "Fenstergröße", "useTrueRange_input": "useTrueRange", "Right Labels": "Beschriftung Rechts", "Long Length_input": "<PERSON>", "True Strength Indicator_study": "Wahre-Stärke-Indikator", "%R_input": "%R", "There are no saved charts": "<PERSON>s gibt keine gespeicherten Charts.", "Instrument is not allowed": "Dieses Instrument ist nicht erlaubt.", "bars_margin": "<PERSON><PERSON><PERSON>", "Decimal Places": "Dezimalstellen", "Show Indicator Last Value": "Letzten Indikatorwert anzeigen", "Initial capital": "Anfangskapital", "Show Angle": "<PERSON><PERSON> anzeigen", "Mass Index_study": "Mass-Index", "More features on tradingview.com": "Mehr Funktionen auf tradingview.com", "Objects Tree...": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "Remove Drawing Tools & Indicators": "Zeichenwerkzeuge und Indikatoren entfernen", "Length1_input": "Länge 1", "Always Invisible": "<PERSON>mmer verborgen", "Circle": "Kreis", "Days": "Tage", "x_input": "x", "Save As...": "Speichern unter...", "Elliott Double Combo Wave (WXY)": "<PERSON> (WXY)", "Parabolic SAR_study": "Parabolic SAR", "Any Symbol": "Jegliches Symbol", "Variance": "<PERSON>bwei<PERSON><PERSON>", "Stats Text Color": "Statistiken Textfarbe", "Minutes": "Minuten", "Short RoC Length_input": "Kurze RoC <PERSON>", "Projection": "Projektion", "Jaw_input": "<PERSON><PERSON> (Kiefer)", "Right": "<PERSON><PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON>", "Coppock Curve_study": "Coppock-<PERSON>rve", "Reversal Amount": "Umkehrbetrag", "Reset Chart": "Chart zurücksetzen", "Sunday": "Sonntag", "Left Axis": "Linke Achse", "YES": "JA", "longlen_input": "longlen", "Moving Average Exponential_study": "Exponentiell Gleitender Durchsch<PERSON>tt", "Source border color": "Randfarbe Ursprung", "Redo {0}": "<PERSON>iederherstellen {0}", "Cypher Pattern": "Zahlenmuster", "s_dates": "s", "Area": "Fläche", "Triangle Pattern": "Dreiecksmuster", "Balance of Power_study": "Balance of Power", "EOM_input": "EOM", "Shapes_input": "Formen", "Oversold_input": "Überverkauft", "Apply Manual Risk/Reward": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> ver<PERSON>", "Market Closed": "<PERSON><PERSON> geschlossen", "Indicators": "Indikatoren", "close": "Close", "q_input": "q", "You are notified": "<PERSON>e wurden benachritigt", "Font Icons": "Schriftartsymbole", "%D_input": "%D", "Border Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Offset_input": "Offset", "Risk": "<PERSON><PERSON><PERSON>", "Price Scale": "Preisskala", "HV_input": "HV", "Seconds": "Sekunden", "Settings": "Einstellungen", "Start_input": "Start", "Elliott Impulse Wave (12345)": "<PERSON> (12345)", "Hours": "Stunden", "Send to Back": "Ganz nach hinten verschieben", "Color 4_input": "Farbe 4", "Angles": "<PERSON><PERSON>", "Prices": "<PERSON><PERSON>", "July": "<PERSON><PERSON>", "Create Horizontal Line": "Horizontale <PERSON>", "ADX Smoothing_input": "ADX Glättung", "One color for all lines": "Eine Farbe für alle Linien", "m_dates": "m", "(H + L + C)/3": "(H + L + C)/3\n(H + L + C)/3", "Candles": "<PERSON><PERSON><PERSON>", "We_day_of_week": "We", "Width (% of the Box)": "Breite (% der Box)", "%d minute": "%d Minute", "Go to...": "<PERSON><PERSON>e zu...", "Pip Size": "Pip-Größe", "Wednesday": "Mittwoch", "This drawing is used in alert. If you remove the drawing, the alert will be also removed. Do you want to remove the drawing anyway?": "Dieses <PERSON>ei<PERSON>nungselemente wird für einem Alarm benutzt. Wenn Si<PERSON> dieses entfernen, wird der Alarm ebenfalls gelöscht. Möchten Sie dieses Zeichnungselement trotzdem löschen?", "Show Countdown": "Countdown anzeigen", "Show alert label line": "Alarm-Label-Line anzeigen", "MA_input": "MA", "Length2_input": "Länge 2", "not authorized": "nicht autorisiert", "Session Volume_study": "Session Volume", "Image URL": "Bild URL", "SMI Ergodic Oscillator_input": "SMI Ergodic Oszillator", "Show Objects Tree": "Objektbaum anzeigen", "Price:": "Preis:", "Bring to Front": "Ganz nach vorne bringen", "Brush": "<PERSON><PERSON>l", "Not Now": "<PERSON><PERSON>t nicht", "Yes": "<PERSON>a", "C_data_mode_connecting_letter": "C", "SMALen4_input": "SMALen4", "Apply Default Drawing Template": "Voreingestellte Vorlage anwenden", "Compact": "Kompakt", "Save As Default": "Als Standard speichern", "Target border color": "Randfarbe Kursziel", "Invalid Symbol": "Ungültiges Symbol", "yay Color 1_input": "yay Farbe 1", "Quandl is a huge financial database that we have connected to TradingView. Most of its data is EOD and is not updated in real-time, however the information may be extremely useful for fundamental analysis.": "Quandl ist eine riesige Finanzdatenbank, welche wir an TradingView angeschlossen haben. Die meisten Daten sind EOD und werden nicht Echtzeit aktualisiert, dennoch könnte die Information für die Fundamentalanalyse sehr nützlich sein.", "Hide Marks On Bars": "", "Cancel Order": "Auftrag abbrechen", "Hide All Drawing Tools": "Alle Zeichen-Tools verbergen", "WMA Length_input": "WMA Länge", "Show Dividends on Chart": "Dividenden auf dem Chart anzeigen", "Show Executions": "Ausführungen anzeigen", "Borders": "<PERSON><PERSON><PERSON>", "Remove Indicators": "Indikatoren entfernen", "loading...": "lade...", "Closed_line_tool_position": "geschlossen", "Rectangle": "<PERSON><PERSON><PERSON>", "Change Resolution": "Auflösung ändern", "Indicator Arguments": "Funktionsargument des Indikators", "Symbol Description": "Symbolbeschreibung", "Chande Momentum Oscillator_study": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Oszillator", "Degree": "Grad", " per order": " pro Order", "Line - HL/2": "Linie - HT/2", "Up Wave 4": "Aufwärtswelle 4", "Least Squares Moving Average_study": "Least-Squares gleitender Durchschnitt", "Change Variance value": "Varianzenwert ändern", "powered by ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von ", "Source_input": "<PERSON><PERSON>", "Change Seconds To": "Wechsle Sekunden zu", "%K_input": "%K", "Scales Text": "Skalentext", "Please enter template name": "<PERSON>te geben Si<PERSON> einen Namen für die Vorlage ein.", "Tokyo": "<PERSON><PERSON><PERSON>", "Events Breaks": "Ereignisabbruch", "Study Templates": "<PERSON><PERSON><PERSON>", "Months": "Monate", "Symbol Info...": "Symbol-Info...", "Elliott Wave Minor": "<PERSON> <PERSON>", "Read our blog for more info!": "<PERSON>en Si<PERSON> unseren Blog, um mehr zu erfahren!", "Measure (Shift + Click on the chart)": "Maß (<PERSON><PERSON> + Klick auf den Chart)", "Override Min Tick": "<PERSON> ü<PERSON>chreiben", "Show Positions": "Positionen anzeigen", "Add To Text Notes": "<PERSON><PERSON><PERSON> zu Textnotizen hinzu", "Elliott Triple Combo Wave (WXYXZ)": "<PERSON>-Combo-Welle (WXYXZ)", "Multiplier_input": "Multiplikator", "Risk/Reward": "<PERSON><PERSON>ko/Chance", "Base Line Periods_input": "Perioden der Grundlinien", "Show Dividends": "Dividenden anzeigen", "Relative Strength Index_study": "Relative-Stärke-Index", "Modified Schiff Pitchfork": "Modifizierte Schiff-Pitchfork", "Top Labels": "Markierungen an Oberseite", "Show Earnings": "<PERSON>arning<PERSON> zeigen", "Line - Open": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Elliott Triangle Wave (ABCDE)": "<PERSON><PERSON><PERSON> (ABCDE)", "Text Wrap": "Zeilenumbruch", "Reverse Position": "Position Umkehren", "Elliott Minor Retracement": "Minor <PERSON> Retracement", "DPO_input": "DPO", "Th_day_of_week": "Th", "Slash_hotkey": "Schrägstrich", "No symbols matched your criteria": "Kein passendes Symbol für Ihr Kriterium gefunden", "Icon": "Symbol", "lengthRSI_input": "LängeRSI", "Tuesday": "Dienstag", "Teeth Length_input": "Teeth (Zähne) Länge", "Indicator_input": "<PERSON><PERSON><PERSON><PERSON>", "Box size assignment method": "Zuweisungsmethode der Boxgrösse", "Open Interval Dialog": "Intervall-<PERSON><PERSON>", "Athens": "<PERSON><PERSON>", "Fib Speed Resistance Arcs": "Fib Speed Resistance Arcs (Bögen)", "Content": "Inhalt", "middle": "<PERSON><PERSON>", "Lock Cursor In Time": "Sperre den Zeiger in Zeit", "Eraser": "<PERSON><PERSON><PERSON>", "Relative Vigor Index_study": "Relative-Vitalität-Index", "Envelope_study": "Umschlag", "Pre Market": "<PERSON><PERSON>", "Horizontal Line": "Horizontale <PERSON>", "O_in_legend": "O", "Confirmation": "Bestätigung", "HL Bars": "High-Low Bars", "Lines:": "<PERSON><PERSON>:", "Hide Favorite Drawings Toolbar": "Favorisierte Zeichen-Werkzeugleiste ausblenden", "X Cross": "X-Kreuz", "Profit Level. Ticks:": "Gewinnspanne. Ticks:", "Show Date/Time Range": "Datums-/Zeitspanne anzeigen", "Level {0}": "", "Favorites": "<PERSON><PERSON>", "Horz Grid Lines": "<PERSON><PERSON>", "-DI_input": "-DI", "Price Range": "Preisspanne", "day": "Tag", "deviation_input": "<PERSON>bwei<PERSON><PERSON>", "Account Size": "Kontogröße", "Value_input": "Wert", "Time Interval": "Zeitinterval", "Success text color": "Textfarbe Erfolg", "ADX smoothing_input": "ADX Glättung", "%d hour": "%d Stunde", "Order size": "Umfang der Order", "Drawing Tools": "Zeichen-Tools", "Save Drawing Template As": "Zeichenvorlage speichern als", "Traditional": "<PERSON><PERSON>", "Chaikin Money Flow_study": "<PERSON><PERSON><PERSON>", "Ease Of Movement_study": "Ease Of Movement", "Defaults": "Standardeinstellungen", "Percent_input": "Prozent", "Interval is not applicable": "Intervall ist nicht anwendbar", "short_input": "kurz", "Visual settings...": "Visuelle Einstellungen...", "RSI_input": "RSI", "Chatham Islands": "Chatham Inseln", "Detrended Price Oscillator_input": "Trendbereinig<PERSON>-Oszillator", "Mo_day_of_week": "Mo", "center": "zentrieren", "Vertical Line": "<PERSON><PERSON><PERSON><PERSON>", "Show Splits on Chart": "Splits auf dem <PERSON> anzeigen", "Sorry, the Copy Link button doesn't work in your browser. Please select the link and copy it manually.": "Entschuldigung, <PERSON> K<PERSON><PERSON> \"Link-Adresse kopieren\" funktioniert nicht in Ihrem Browser. Bitte markieren Sie das Link und kopieren Sie es manuell.", "Levels Line": "", "Events & Alerts": "Ereignisse & Alarme", "May": "<PERSON>", "ROCLen4_input": "ROCLen4", "Aroon Down_input": "<PERSON><PERSON>", "Add To Watchlist": "Zur Watchlist <PERSON>nzufügen", "Total": "Gesamt", "Price": "Pre<PERSON>", "left": "links", "Lock scale": "Skalierung feststellen", "Limit_input": "Limit", "Change Days To": "Tage ändern in", "Price Oscillator_study": "Preis-Oszillator", "smalen1_input": "smalen1", "Drawing Template '{0}' already exists. Do you really want to replace it?": "Zeichenvorlage '{0}' existiert bereits. Möchten Sie sie wirklich ersetzen?", "Show Middle Point": "Zeige Mittelpunkt", "KST_input": "KST", "Extend Right End": "Rechtes Ende verlängern", "Fans": "<PERSON><PERSON><PERSON>", "Color based on previous close_input": "<PERSON><PERSON> basiert auf vorherigem Schlusskurs", "Price_input": "Pre<PERSON>", "Gann Fan": "<PERSON><PERSON>", "Weeks": "<PERSON><PERSON><PERSON>", "McGinley Dynamic_study": "McGinley-Dynamik", "Relative Volatility Index_study": "Relative-Volatilität-Index", "Source Code...": "Quellcode...", "PVT_input": "PVT", "Show Hidden Tools": "Versteckte Tools anzeigen", "Hull Moving Average_study": "Hull gleitenden Durchschnitt", "Symbol Prev. Close Value": "<PERSON><PERSON><PERSON>hlusskurs des Symbols", "{0} chart by TradingView": "{0} <PERSON> <PERSON>", "Right Shoulder": "<PERSON><PERSON><PERSON>", "Remove Drawing Tools": "Zeichenwerkzeuge entfernen", "Friday": "Freitag", "Zero_input": "<PERSON><PERSON>", "Company Comparison": "Unternehmen vergleichen", "Stochastic Length_input": "Stochastische Länge", "mult_input": "mult", "URL cannot be received": "URL kann nicht empfangen werden", "Success back color": "Hintergrundfarbe Erfolg", "E_data_mode_end_of_day_letter": "E", "Trend-Based Fib Extension": "Trendbasierte Fib-Extension", "Top": "<PERSON><PERSON>", "Double Curve": "Doppel-<PERSON><PERSON>", "Stochastic RSI_study": "Stochastischer RSI", "Oops!": "Huch!", "Horizontal Ray": "Unterstützung-/Widerstandslinie", "smalen3_input": "smalen3", "Symbol Labels": "Symbol-Label", "Script Editor...": "<PERSON><PERSON><PERSON><PERSON>-Editor", "Trades on Chart": "Trades auf Charts", "Listed Exchange": "Gelistete Börse", "Error:": "<PERSON><PERSON>:", "Fullscreen mode": "Vollbildmodus", "Add Text Note For {0}": "Textnotiz für {0} hinzufügen", "K_input": "K", "Do you really want to delete Drawing Template '{0}' ?": "Möchten Sie wirklich die Zeichenvorlage '{0}' löschen?", "ROCLen3_input": "ROCLen3", "Restore Size": "Größe Wiederherstellen", "Text Color": "Textfarbe", "Rename Chart Layout": "Chart-Layout umben<PERSON>n", "Background color 2": "Hintergrundfarbe 2", "Drawings Toolbar": "Zeichen-Werkzeugleiste", "New Zealand": "Neuseeland", "CHOP_input": "CHOP", "Apply Defaults": "Voreinstellungen anwenden", "Screen (No Scale)": "Bildschirm (keine Skalierung)", "Extended Alert Line": "Alarmlinie erweitern", "Note": "Anmerkung", "Moving Average Channel_study": "Kanal der gleitenden Durchschnitte", "like": "positive Bewertung", "Show": "Anzeigen", "{0} bars": "{0} <PERSON><PERSON><PERSON>", "Lower_input": "Unter", "Created ": "<PERSON><PERSON><PERSON><PERSON> ", "Warning": "<PERSON><PERSON><PERSON>", "Elder's Force Index_study": "Elder's Force Index", "Show Earnings on Chart": "Earnings im Chart zeigen", "ATR_input": "ATR", "Stochastic_study": "Stochastik", "Bollinger Bands %B_study": "Bollinger Bands %B", "Time Zone": "Zeitzone", "right": "rechts", "%d month": "%d <PERSON><PERSON>", "Wrong value": "Falscher Wert", "Upper Band_input": "Oberes Band", "Sun": "Son", "start_input": "Start", "No indicators matched your criteria.": "<PERSON><PERSON> passenden Indikatoren zu Ihren Kriterien gefunden", "Commission": "Kommission", "Short length_input": "<PERSON><PERSON><PERSON>", "Kolkata": "<PERSON><PERSON><PERSON><PERSON>", "Triple EMA_study": "Dreifache EMA", "Technical Analysis": "Technische Analyse", "Show Text": "Text anzeigen", "Channel": "<PERSON><PERSON>", "FXCM CFD data is available only to FXCM account holders": "FXCM CFD Informationen sind nur für Inhaber eines FXCM Kontos verfügbar.", "Lagging Span 2 Periods_input": "Lagging Span 2 Perioden", "Connecting Line": "Verbindungsleitung", "bottom": "Unterseite", "Teeth_input": "Teeth (Zähne)", "Sig_input": "Sig", "Open Manage Drawings": "Zeichnungen verwalten öffnen", "Save New Chart Layout": "Neues Chart-Layout speichern", "Fib Channel": "<PERSON><PERSON>", "Save Drawing Template As...": "Zeichenvorlage speichern als...", "Minutes_interval": "Minuten", "Up Wave 2 or B": "Aufwärtswelle 2 oder B", "Columns": "Spalten", "Directional Movement_study": "Richtungsbewegung", "roclen2_input": "roclen2", "Apply WPT Down Wave": "WPT Down Wave anwenden", "Not applicable": "<PERSON><PERSON> anwen<PERSON>bar", "Bollinger Bands %B_input": "Bollinger-Bänder %B", "Default": "Standard", "Template name": "Vorlage Name", "Indicator Values": "Werte des Indikators", "Lips Length_input": "Länge der Lips (Lippen)", "Use Upper Deviation_input": "Benutze obere Abweichung", "L_in_legend": "Tief", "Remove custom interval": "Benutzerdefiniertes Intervall entfernen", "shortlen_input": "shortlen", "Quotes are delayed by {0} min": "Ku<PERSON> sind um {0} Minuten verzögert", "Hide Events on Chart": "Ereignisse im Chart ausblenden", "Williams Alligator_study": "Williams Alligator", "Profit Background Color": "Gewinn Hintergrund Farbe", "Bar's Style": "Balken Darstellung", "Exponential_input": "Exponentiell", "Down Wave 5": "Abwärtsbewegung 5", "Previous": "<PERSON><PERSON><PERSON><PERSON>", "Stay In Drawing Mode": "<PERSON><PERSON> b<PERSON>n", "Comment": "Kommentar", "Connors RSI_study": "Connors RSI", "Bars": "<PERSON><PERSON><PERSON>", "Show Labels": "Markierungen anzeigen", "Symbol Type": "Symboltyp", "December": "Dezember", "Lock drawings": "Zeichnungen fixieren", "Border color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Change Seconds From": "Wechsle Sekunden von", "Left Labels": "Beschriftungen links", "Insert Indicator...": "Indikator einfügen...", "ADR_B_input": "ADR_B", "Paste %s": "Einfügen %s", "Change Symbol...": "Symbol ändern...", "Timezone": "Zeitzone", "Invite-only script. You have been granted access.": "Auf-Einladung-Skript. Sie haben Zugang erhalten.", "Color 6_input": "Farbe 6", "Oct": "Okt", "ATR Length": "ATR-Länge", "{0} financials by TradingView": "{0} <PERSON><PERSON><PERSON><PERSON>", "Extend Lines Left": "<PERSON><PERSON> nach links erweitern", "Source back color": "Hintergrundfarbe Ursprung", "Transparency": "Transparenz", "No": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "Cyclic Lines": "Zyklische Linien", "length28_input": "Länge28", "ABCD Pattern": "<PERSON><PERSON>", "When selecting this checkbox the study template will set \"__interval__\" interval on a chart": "<PERSON>n diese Checkbox ausgewählt wird, stellt die Indikatorvorlage das __interval__-Intervall auf dem Chart ein.", "Add": "Hinzufügen", "Line - Low": "<PERSON><PERSON> <PERSON><PERSON>", "On Balance Volume_study": "On Balance Volume", "Apply Indicator on {0} ...": "<PERSON><PERSON><PERSON><PERSON> anwenden auf {0}...", "NEW": "NEU", "Wick": "<PERSON><PERSON>", "Hull MA_input": "Hull MA", "Lock Scale": "Skalierung fixieren", "distance: {0}": "Abstand: {0}", "Extended": "verl<PERSON><PERSON><PERSON>", "Three Drives Pattern": "Three-Drives-Muster", "NO": "NEIN", "Top Margin": "<PERSON>berer Rand", "Up fractals_input": "Up Brüche", "Insert Drawing Tool": "Zeichenwerkzeug einfügen", "OHLC Values": "OHLC Werte", "Correlation_input": "Korrelation", "Session Breaks": "Sitzungspausen", "Add {0} To Watchlist": "{0} zur Watchlist hinzufügen", "Anchored Note": "Verankerter Hinweis", "lipsLength_input": "lips<PERSON><PERSON><PERSON>", "low": "Low", "Apply Indicator on {0}": "<PERSON><PERSON><PERSON><PERSON> anwen<PERSON> auf {0}", "UpDown Length_input": "UpDown Periodenlänge", "Price Label": "Preis-Label", "Balloon": "<PERSON><PERSON>", "Track time": "Zeit verfolgen", "Background Color": "Hintergrundfarbe", "an hour": "eine Stunde", "Right Axis": "Rechte Achse", "D_data_mode_delayed_streaming_letter": "D", "VI -_input": "VI -", "slowLength_input": "<PERSON><PERSON><PERSON><PERSON>", "Click to set a point": "<PERSON><PERSON><PERSON>, um einen Punkt zu setzen", "Save Indicator Template As...": "Indikatorvorlage speichern als...", "Arrow Up": "<PERSON><PERSON><PERSON>", "Indicator Titles": "Titel des Indikators", "Failure text color": "Textfarbe <PERSON>hler", "Sa_day_of_week": "Sa", "Net Volume_study": "Nettovolumen", "Error": "<PERSON><PERSON>", "Edit Position": "Position bearbeiten", "RVI_input": "RVI", "Centered_input": "<PERSON><PERSON><PERSON>", "Recalculate On Every Tick": "Bei Jedem Anklicken Neu Berechnen", "Left": "Links", "Simple ma(oscillator)_input": "Simple ma(Oszillator)", "Compare": "Vergleichen", "Fisher Transform_study": "Fisher Transform", "Show Orders": "Orders anzeigen", "Zoom In": "Vergrößern", "Length EMA_input": "Länge EMA", "Enter a new chart layout name": "Chart Layout neu benennen", "Signal Length_input": "Signallänge", "FAILURE": "FEHLER", "Point Value": "Punktwert", "D_interval_short": "D", "MA with EMA Cross_study": "MA mit EMA Cross", "Label Up": "Beschriftung Aufwärts", "Price Channel_study": "Preiskanal", "Close": "Schließen", "ParabolicSAR_input": "ParabolicSAR", "Log Scale_scale_menu": "Logarithmische Skalierung", "MACD_input": "MACD", "Do not show this message again": "<PERSON><PERSON><PERSON>t nicht erneut anzeigen", "No Overlapping Labels": "<PERSON>ine <PERSON>den Label", "Arrow Mark Left": "Pfeil nach links", "Down Wave 2 or B": "Abwärtsbewegung 2 oder B", "Line - Close": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "(O + H + L + C)/4": "", "Confirm Inputs": "Eingabe bestätigen", "Open_line_tool_position": "<PERSON><PERSON><PERSON>", "Lagging Span_input": "Lagging Span", "Thursday": "Don<PERSON><PERSON>", "Arrow Down": "Pfeil Abwärts", "Elliott Correction Wave (ABC)": "<PERSON> (ABC)", "Error while trying to create snapshot.": "Fehler während der Erstellung eines Schnappschusses", "Label Background": "Beschriftung-Hintergrund", "Templates": "Vorlagen", "Please report the issue or click Reconnect.": "<PERSON><PERSON>, melden Sie das Problem und klicken Neu verbinden", "1. Slide your finger to select location for first anchor<br>2. Tap anywhere to place the first anchor": "1. Bewegen Sie Ihren Finger zur Position des ersten Ankerpunkts.<br/>2. Durch Antippen einer beliebigen Stelle wird der erste Ankerpunkt gesetzt.", "Signal Labels": "Kennzeichnung Signal", "Delete Text Note": "Textnotiz <PERSON>", "compiling...": "wird zusammengestellt...", "Detrended Price Oscillator_study": "Trendbereinigter Kursoszillator", "Color 5_input": "Farbe 5", "Fixed Range_study": "Fixed Range", "Up Wave 1 or A": "Aufwärtswelle 1 oder A", "Scale Price Chart Only": "Nur den Preis-Chart vergrößern", "Unmerge Up": "Verschmelzung nach oben aufheben", "auto_scale": "Automatisch", "Short period_input": "<PERSON><PERSON><PERSON>", "Background": "Hi<PERSON>grund", "% of equity": "% Aktien", "Apply Elliot Wave Intermediate": "Intermediate Elliot <PERSON> anwen<PERSON>", "VWMA_input": "VWMA", "Lower Deviation_input": "Untere Abweichung", "Save Interval": "Intervall speichern", "February": "<PERSON><PERSON><PERSON>", "Reverse": "INVERT", "Oops, something went wrong": "<PERSON><PERSON>, etwas ging schief", "Add to favorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "Median": "Medianer Wert", "ADX_input": "ADX", "Remove": "Entfernen", "len_input": "len", "Arrow Mark Up": "<PERSON><PERSON><PERSON> nach oben", "Active Symbol": "Aktives Symbol", "Crosses_input": "<PERSON><PERSON><PERSON>", "Middle_input": "<PERSON><PERSON>", "Sync drawing to all charts": "Zeichen-Tools mit allen Charts synchronisieren", "LowerLimit_input": "UntereBegrenzung", "Know Sure Thing_study": "Know-Sure-Thing", "Copy Chart Layout": "Chart Layout kopieren", "Compare...": "Vergleichen...", "1. Slide your finger to select location for next anchor<br>2. Tap anywhere to place the next anchor": "1. Bewegen Sie Ihren Finger zur Position des nächsten Ankerpunkts.<br/>2. Durch Antippen einer beliebigen Stelle wird der nächste Ankerpunkt gesetzt.", "Text Notes are available only on chart page. Please <a href=\"/chart/\">open a chart</a> and then try again.": "Textnotizen sind verfügbar nur auf der Chartseite. Bitte <a href=\"/chart/\">öffne einen Chart</a> und versuche Sie es erneut.", "Color": "Farbe", "Aroon Up_input": "Aroon Tief", "Singapore": "Singapur", "Scales Lines": "Skalenlinien", "Type the interval number for munute charts (i.e. 5 if it is going to be a five minute chart). Or number plus letter for H (Hourly), D (Daily), W (Weekly), M (Monthly) intervals (i.e. D or 2H)": "Geben Sie die Intervallänge für Minuten-Charts ein (z.B. 5, wenn es sich um ein Fünf-Minuten-Diagramm handeln soll). Oder Zahl plus Buchstabe für die Intervalle H (Std.), <PERSON> (Täglich), <PERSON> (Wöchentlich), <PERSON> (Monatlich) (z.B. D oder 2H).", "HLC Bars": "HLC-<PERSON><PERSON><PERSON>", "Up Wave C": "Aufwärtswelle C", "Show Distance": "Abstand anzeigen", "Risk/Reward Ratio: {0}": "Chance/Risiko <PERSON>: {0}", "Volume Oscillator_study": "Volume Oscillator", "Williams Fractal_study": "<PERSON>", "Merge Up": "Nach oben zusammenführen", "Right Margin": "<PERSON><PERSON><PERSON>", "Moscow": "<PERSON><PERSON><PERSON>", "Warsaw": "Warschau"}