"""
Advanced Chart Tools Page
Implements Smart Money Concepts (SMC) and other advanced technical indicators
"""

import streamlit as st
import streamlit.components.v1 as components
import pandas as pd
import plotly.graph_objects as go
import logging
from typing import List, Dict, Tuple
from dataclasses import dataclass

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class OrderBlock:
    """Order Block data structure"""
    start_time: int
    end_time: int
    top: float
    bottom: float
    is_high_block: bool
    strength: int = 1
    is_active: bool = True

@dataclass
class FairValueGap:
    """Fair Value Gap data structure"""
    start_time: int
    end_time: int
    top: float
    bottom: float
    direction: int  # 1 for bullish, -1 for bearish
    is_active: bool = True

@dataclass
class StructurePoint:
    """Market Structure Point data structure"""
    time: int
    price: float
    is_high: bool
    structure_type: str  # "BoS", "CHoCH", "I-BoS", "I-CHoCH"
    direction: int  # 1 for bullish, -1 for bearish

class SMCIndicator:
    """Smart Money Concepts Indicator Implementation"""

    def __init__(self, sensitivity: int = 5, internal_sensitivity: int = 3, show_last: int = 10):
        self.sensitivity = sensitivity
        self.internal_sensitivity = internal_sensitivity
        self.show_last = show_last
        self.order_blocks = []
        self.fair_value_gaps = []
        self.structure_points = []
        self.session_data = {}

    def calculate_pivots(self, df: pd.DataFrame, length: int) -> Tuple[pd.Series, pd.Series]:
        """Calculate pivot highs and lows"""
        try:
            high_series = df['High'].rolling(window=length*2+1, center=True).max()
            low_series = df['Low'].rolling(window=length*2+1, center=True).min()

            pivot_highs = pd.Series(index=df.index, dtype=float)
            pivot_lows = pd.Series(index=df.index, dtype=float)

            for i in range(length, len(df) - length):
                # Check for pivot high
                if df['High'].iloc[i] == high_series.iloc[i]:
                    # Verify it's actually a pivot (higher than surrounding bars)
                    is_pivot_high = True
                    for j in range(i - length, i + length + 1):
                        if j != i and df['High'].iloc[j] >= df['High'].iloc[i]:
                            is_pivot_high = False
                            break
                    if is_pivot_high:
                        pivot_highs.iloc[i] = df['High'].iloc[i]

                # Check for pivot low
                if df['Low'].iloc[i] == low_series.iloc[i]:
                    # Verify it's actually a pivot (lower than surrounding bars)
                    is_pivot_low = True
                    for j in range(i - length, i + length + 1):
                        if j != i and df['Low'].iloc[j] <= df['Low'].iloc[i]:
                            is_pivot_low = False
                            break
                    if is_pivot_low:
                        pivot_lows.iloc[i] = df['Low'].iloc[i]

            return pivot_highs, pivot_lows

        except Exception as e:
            logger.error(f"Error calculating pivots: {str(e)}")
            return pd.Series(index=df.index, dtype=float), pd.Series(index=df.index, dtype=float)

    def detect_order_blocks(self, df: pd.DataFrame) -> List[OrderBlock]:
        """Detect institutional order blocks based on pivot points"""
        try:
            order_blocks = []
            pivot_highs, pivot_lows = self.calculate_pivots(df, self.sensitivity)

            # Detect high order blocks (resistance zones)
            for i in range(len(df)):
                if not pd.isna(pivot_highs.iloc[i]):
                    # Look for the candle that created this high
                    high_price = pivot_highs.iloc[i]

                    # Find the order block zone (the candle before the move)
                    for j in range(max(0, i - self.sensitivity), i):
                        if (df['Close'].iloc[j] < df['Open'].iloc[j] and  # Bearish candle
                            df['High'].iloc[j] < high_price):  # Below the high

                            order_block = OrderBlock(
                                start_time=j,
                                end_time=len(df),  # Extend to current time
                                top=df['High'].iloc[j],
                                bottom=df['Low'].iloc[j],
                                is_high_block=True,
                                strength=1
                            )
                            order_blocks.append(order_block)
                            break

            # Detect low order blocks (support zones)
            for i in range(len(df)):
                if not pd.isna(pivot_lows.iloc[i]):
                    # Look for the candle that created this low
                    low_price = pivot_lows.iloc[i]

                    # Find the order block zone (the candle before the move)
                    for j in range(max(0, i - self.sensitivity), i):
                        if (df['Close'].iloc[j] > df['Open'].iloc[j] and  # Bullish candle
                            df['Low'].iloc[j] > low_price):  # Above the low

                            order_block = OrderBlock(
                                start_time=j,
                                end_time=len(df),  # Extend to current time
                                top=df['High'].iloc[j],
                                bottom=df['Low'].iloc[j],
                                is_high_block=False,
                                strength=1
                            )
                            order_blocks.append(order_block)
                            break

            # Clean invalid order blocks (price has moved through them)
            valid_blocks = []
            current_price = df['Close'].iloc[-1]

            for block in order_blocks:
                if block.is_high_block:
                    # High block is invalid if price closed above it
                    if current_price < block.top:
                        valid_blocks.append(block)
                else:
                    # Low block is invalid if price closed below it
                    if current_price > block.bottom:
                        valid_blocks.append(block)

            # Limit to show_last number of blocks
            if len(valid_blocks) > self.show_last:
                valid_blocks = valid_blocks[-self.show_last:]

            self.order_blocks = valid_blocks
            return valid_blocks

        except Exception as e:
            logger.error(f"Error detecting order blocks: {str(e)}")
            return []

    def detect_fair_value_gaps(self, df: pd.DataFrame) -> List[FairValueGap]:
        """Detect 3-candle Fair Value Gaps"""
        try:
            fair_value_gaps = []

            for i in range(2, len(df)):
                # Get 3 consecutive candles
                candle1 = df.iloc[i-2]  # First candle
                candle2 = df.iloc[i-1]  # Middle candle
                candle3 = df.iloc[i]    # Third candle

                # Check for bullish FVG (gap up)
                if (candle1['Close'] < candle1['Open'] and  # First candle bearish
                    candle2['Close'] > candle2['Open'] and  # Middle candle bullish
                    candle3['Close'] > candle3['Open'] and  # Third candle bullish
                    candle3['Low'] > candle1['High']):      # Gap exists

                    fvg = FairValueGap(
                        start_time=i-1,
                        end_time=len(df),
                        top=candle3['Low'],
                        bottom=candle1['High'],
                        direction=1,  # Bullish
                        is_active=True
                    )
                    fair_value_gaps.append(fvg)

                # Check for bearish FVG (gap down)
                elif (candle1['Close'] > candle1['Open'] and  # First candle bullish
                      candle2['Close'] < candle2['Open'] and  # Middle candle bearish
                      candle3['Close'] < candle3['Open'] and  # Third candle bearish
                      candle3['High'] < candle1['Low']):      # Gap exists

                    fvg = FairValueGap(
                        start_time=i-1,
                        end_time=len(df),
                        top=candle1['Low'],
                        bottom=candle3['High'],
                        direction=-1,  # Bearish
                        is_active=True
                    )
                    fair_value_gaps.append(fvg)

            # Remove filled gaps
            active_gaps = []
            current_high = df['High'].iloc[-1]
            current_low = df['Low'].iloc[-1]

            for gap in fair_value_gaps:
                if gap.direction == 1:  # Bullish gap
                    # Gap is filled if price went back down into the gap
                    if current_low > gap.bottom:
                        active_gaps.append(gap)
                else:  # Bearish gap
                    # Gap is filled if price went back up into the gap
                    if current_high < gap.top:
                        active_gaps.append(gap)

            self.fair_value_gaps = active_gaps
            return active_gaps

        except Exception as e:
            logger.error(f"Error detecting fair value gaps: {str(e)}")
            return []

    def analyze_market_structure(self, df: pd.DataFrame) -> List[StructurePoint]:
        """Analyze market structure for BoS and CHoCH patterns"""
        try:
            structure_points = []
            pivot_highs, pivot_lows = self.calculate_pivots(df, self.sensitivity)

            # Get valid pivot points
            high_pivots = [(i, price) for i, price in enumerate(pivot_highs) if not pd.isna(price)]
            low_pivots = [(i, price) for i, price in enumerate(pivot_lows) if not pd.isna(price)]

            # Combine and sort all pivots by time
            all_pivots = [(i, price, True) for i, price in high_pivots] + [(i, price, False) for i, price in low_pivots]
            all_pivots.sort(key=lambda x: x[0])

            if len(all_pivots) < 3:
                return structure_points

            # Track market direction
            current_trend = None  # 1 for bullish, -1 for bearish
            last_high = None
            last_low = None

            for i in range(len(all_pivots)):
                time_idx, price, is_high = all_pivots[i]

                if is_high:  # High pivot
                    if last_high is not None:
                        if price > last_high[1]:  # Higher high
                            if current_trend == -1:  # Was bearish, now bullish
                                # Change of Character (CHoCH)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=True,
                                    structure_type="CHoCH",
                                    direction=1
                                )
                                structure_points.append(structure_point)
                                current_trend = 1
                            elif current_trend == 1:  # Continuing bullish
                                # Break of Structure (BoS)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=True,
                                    structure_type="BoS",
                                    direction=1
                                )
                                structure_points.append(structure_point)
                        else:  # Lower high
                            if current_trend == 1:  # Was bullish, potential bearish
                                current_trend = -1

                    last_high = (time_idx, price)

                else:  # Low pivot
                    if last_low is not None:
                        if price < last_low[1]:  # Lower low
                            if current_trend == 1:  # Was bullish, now bearish
                                # Change of Character (CHoCH)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=False,
                                    structure_type="CHoCH",
                                    direction=-1
                                )
                                structure_points.append(structure_point)
                                current_trend = -1
                            elif current_trend == -1:  # Continuing bearish
                                # Break of Structure (BoS)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=False,
                                    structure_type="BoS",
                                    direction=-1
                                )
                                structure_points.append(structure_point)
                        else:  # Higher low
                            if current_trend == -1:  # Was bearish, potential bullish
                                current_trend = 1

                    last_low = (time_idx, price)

            # Limit to recent structure points
            if len(structure_points) > self.show_last:
                structure_points = structure_points[-self.show_last:]

            self.structure_points = structure_points
            return structure_points

        except Exception as e:
            logger.error(f"Error analyzing market structure: {str(e)}")
            return []

    def calculate_session_data(self, df: pd.DataFrame) -> Dict:
        """Calculate session-based analysis (NY, London, Asia)"""
        try:
            session_data = {}

            # Add hour column if not exists
            if 'Hour' not in df.columns:
                df['Hour'] = pd.to_datetime(df['Date']).dt.hour

            # Define session times (assuming UTC)
            sessions = {
                'Asian': (20, 2),    # 8 PM to 2 AM UTC
                'London': (7, 15),   # 7 AM to 3 PM UTC
                'New_York': (13, 21) # 1 PM to 9 PM UTC
            }

            for session_name, (start_hour, end_hour) in sessions.items():
                if start_hour > end_hour:  # Session crosses midnight
                    session_mask = (df['Hour'] >= start_hour) | (df['Hour'] <= end_hour)
                else:
                    session_mask = (df['Hour'] >= start_hour) & (df['Hour'] <= end_hour)

                session_df = df[session_mask]

                if not session_df.empty:
                    session_data[session_name] = {
                        'high': session_df['High'].max(),
                        'low': session_df['Low'].min(),
                        'volume': session_df['Volume'].mean(),
                        'volatility': (session_df['High'] - session_df['Low']).mean(),
                        'candle_count': len(session_df)
                    }

            self.session_data = session_data
            return session_data

        except Exception as e:
            logger.error(f"Error calculating session data: {str(e)}")
            return {}

def show_chart_tools():
    """Main function to display the Chart Tools page"""
    st.title("🔧 Advanced Chart Tools")
    st.markdown("Professional TradingView charts with institutional Smart Money Concepts analysis")

    # Check if historical data is available
    if 'historical_data' not in st.session_state or st.session_state.historical_data is None:
        st.warning("⚠️ No historical data available. Please load stock data first from the Stock Management page.")
        return

    df = st.session_state.historical_data
    symbol = st.session_state.get('symbol', 'UNKNOWN')

    if df.empty:
        st.warning("⚠️ Historical data is empty. Please load valid stock data.")
        return

    # Sidebar configuration
    st.sidebar.header("🎛️ Chart Configuration")

    # Chart Type Selection
    chart_type = st.sidebar.radio(
        "📊 Chart Type",
        ["TradingView Professional", "TradingView Full Screen", "Custom SMC Analysis", "Combined View"],
        index=0,
        help="Choose your preferred chart interface"
    )

    # Chart Data Settings
    st.sidebar.subheader("📅 Chart Data Range")

    # Time range selection
    time_range_options = {
        "1 Week (7 days)": 7,
        "2 Weeks (14 days)": 14,
        "1 Month (30 days)": 30,
        "3 Months (90 days)": 90,
        "6 Months (180 days)": 180,
        "1 Year (365 days)": 365,
        "All Data": None
    }

    selected_range = st.sidebar.selectbox(
        "📊 Data Range",
        list(time_range_options.keys()),
        index=0,  # Default to 1 Week
        help="Choose how many days of data to display for clearer chart view"
    )

    days_to_show = time_range_options[selected_range]

    # Custom days option
    if st.sidebar.checkbox("🎛️ Custom Days", help="Set a custom number of days"):
        days_to_show = st.sidebar.number_input(
            "Number of Days",
            min_value=1,
            max_value=len(df),
            value=7,
            help="Enter custom number of days to display"
        )

    # SMC Settings
    st.sidebar.subheader("🎯 SMC Settings")
    sensitivity = st.sidebar.slider("Pivot Sensitivity", min_value=3, max_value=15, value=5,
                                   help="Higher values = fewer, stronger pivots")
    show_last = st.sidebar.slider("Show Last N Levels", min_value=5, max_value=20, value=10,
                                 help="Number of recent levels to display")

    # TradingView Settings
    st.sidebar.subheader("📊 TradingView Settings")

    # Interval selection for TradingView
    interval_options = {
        "1 minute": "1",
        "5 minutes": "5",
        "15 minutes": "15",
        "30 minutes": "30",
        "1 hour": "60",
        "4 hours": "240",
        "1 day": "1D",
        "1 week": "1W"
    }

    selected_interval = st.sidebar.selectbox(
        "⏱️ Chart Interval",
        list(interval_options.keys()),
        index=2,  # Default to 15 minutes
        help="Choose the timeframe for TradingView chart"
    )

    chart_interval = interval_options[selected_interval]

    # Analysis toggles
    st.sidebar.subheader("📈 Analysis Layers")
    analysis_options = {
        "order_blocks": st.sidebar.checkbox("🔴 Order Blocks", value=True),
        "fair_value_gaps": st.sidebar.checkbox("⚡ Fair Value Gaps", value=True),
        "market_structure": st.sidebar.checkbox("📊 Market Structure", value=True),
        "session_analysis": st.sidebar.checkbox("🌍 Session Analysis", value=False)
    }

    # Filter data based on selected time range
    if days_to_show is not None:
        df_filtered = df.tail(days_to_show).copy()
        st.sidebar.info(f"📊 Showing last {days_to_show} days ({len(df_filtered)} data points)")
    else:
        df_filtered = df.copy()
        st.sidebar.info(f"📊 Showing all data ({len(df_filtered)} data points)")

    # Display date range info
    if not df_filtered.empty:
        try:
            start_date = df_filtered.index[0].strftime("%Y-%m-%d")
            end_date = df_filtered.index[-1].strftime("%Y-%m-%d")
            st.sidebar.caption(f"📅 From: {start_date}")
            st.sidebar.caption(f"📅 To: {end_date}")
        except AttributeError:
            # Handle case where index is not datetime
            st.sidebar.caption(f"📅 Data points: {len(df_filtered)}")

    # Initialize SMC indicator
    smc = SMCIndicator(sensitivity=sensitivity, show_last=show_last)

    # Generate SMC analysis on filtered data
    with st.spinner("🔍 Analyzing Smart Money Concepts..."):
        order_blocks = smc.detect_order_blocks(df_filtered) if analysis_options["order_blocks"] else []
        fair_value_gaps = smc.detect_fair_value_gaps(df_filtered) if analysis_options["fair_value_gaps"] else []
        structure_points = smc.analyze_market_structure(df_filtered) if analysis_options["market_structure"] else []
        session_data = smc.calculate_session_data(df_filtered) if analysis_options["session_analysis"] else {}

    # Main content based on chart type selection
    if chart_type == "TradingView Professional":
        show_tradingview_with_smc(symbol, df_filtered, order_blocks, fair_value_gaps, structure_points, session_data, analysis_options, chart_interval)
    elif chart_type == "TradingView Full Screen":
        show_tradingview_fullscreen(symbol, order_blocks, fair_value_gaps, structure_points, chart_interval)
    elif chart_type == "Custom SMC Analysis":
        show_custom_smc_analysis(df_filtered, order_blocks, fair_value_gaps, structure_points, analysis_options)
    else:  # Combined View
        show_combined_analysis(symbol, df_filtered, order_blocks, fair_value_gaps, structure_points, analysis_options, chart_interval)

def show_tradingview_with_smc(symbol, df, order_blocks, fair_value_gaps, structure_points, session_data, analysis_options, chart_interval="15"):
    """Show TradingView chart with SMC analysis panels below"""

    # Main TradingView Chart
    st.subheader("📈 Professional TradingView Chart")
    st.markdown(f"**Symbol:** {symbol} | **Market:** Egyptian Exchange (EGX) | **Data Points:** {len(df)}")

    # Create TradingView widget with proper spacing
    tradingview_widget = create_tradingview_widget(symbol, chart_interval)

    # Add some spacing before the chart
    st.markdown("<br>", unsafe_allow_html=True)

    # Render the chart with increased height
    components.html(tradingview_widget, height=700, scrolling=False)

    # SMC Analysis Panels
    st.markdown("---")
    st.subheader("🔍 Smart Money Concepts Analysis")

    # Create tabs for different SMC components
    smc_tabs = []
    if analysis_options["order_blocks"]:
        smc_tabs.append("🔴 Order Blocks")
    if analysis_options["fair_value_gaps"]:
        smc_tabs.append("⚡ Fair Value Gaps")
    if analysis_options["market_structure"]:
        smc_tabs.append("📊 Market Structure")
    if analysis_options["session_analysis"]:
        smc_tabs.append("🌍 Session Analysis")

    if smc_tabs:
        tabs = st.tabs(smc_tabs)
        tab_index = 0

        if analysis_options["order_blocks"]:
            with tabs[tab_index]:
                show_order_blocks_analysis(df, order_blocks)
            tab_index += 1

        if analysis_options["fair_value_gaps"]:
            with tabs[tab_index]:
                show_fvg_analysis(df, fair_value_gaps)
            tab_index += 1

        if analysis_options["market_structure"]:
            with tabs[tab_index]:
                show_market_structure_analysis(df, structure_points)
            tab_index += 1

        if analysis_options["session_analysis"]:
            with tabs[tab_index]:
                show_session_analysis(session_data)

    # Summary sidebar
    with st.sidebar:
        st.markdown("---")
        show_smc_summary_compact(order_blocks, fair_value_gaps, structure_points, df)

def show_tradingview_fullscreen(symbol, order_blocks, fair_value_gaps, structure_points, chart_interval="15"):
    """Show TradingView chart in full screen mode"""

    # Full screen TradingView Chart
    st.subheader("📈 TradingView Full Screen Chart")
    st.markdown(f"**Symbol:** {symbol} | **Market:** Egyptian Exchange (EGX)")

    # Create TradingView widget with maximum height
    tradingview_widget = create_tradingview_widget(symbol, chart_interval)

    # Add some spacing before the chart
    st.markdown("<br>", unsafe_allow_html=True)

    # Render the chart with maximum height for full screen experience
    components.html(tradingview_widget, height=800, scrolling=False)

    # Compact summary below
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        high_blocks = [b for b in order_blocks if b.is_high_block]
        low_blocks = [b for b in order_blocks if not b.is_high_block]
        st.metric("🔴 Resistance Blocks", len(high_blocks))
        st.metric("🟢 Support Blocks", len(low_blocks))

    with col2:
        bullish_gaps = [g for g in fair_value_gaps if g.direction == 1]
        bearish_gaps = [g for g in fair_value_gaps if g.direction == -1]
        st.metric("⚡ Bullish FVGs", len(bullish_gaps))
        st.metric("⚡ Bearish FVGs", len(bearish_gaps))

    with col3:
        bos_points = [s for s in structure_points if s.structure_type == "BoS"]
        choch_points = [s for s in structure_points if s.structure_type == "CHoCH"]
        st.metric("📊 BoS Points", len(bos_points))
        st.metric("📊 CHoCH Points", len(choch_points))

    with col4:
        if structure_points:
            latest = structure_points[-1]
            direction = "🟢 Bullish" if latest.direction == 1 else "🔴 Bearish"
            st.metric("Latest Structure", latest.structure_type)
            st.caption(direction)
        else:
            st.metric("Latest Structure", "None")
            st.caption("No structure detected")

def create_tradingview_widget(symbol, interval="15"):
    """Create TradingView widget HTML"""
    # Map common Egyptian symbols to TradingView format
    symbol_mapping = {
        'COMI': 'EGX:COMI',
        'ABUK': 'EGX:ABUK',
        'SWDY': 'EGX:SWDY',
        'TMGH': 'EGX:TMGH',
        'ISPH': 'EGX:ISPH',
        'JUFO': 'EGX:JUFO',
        'SKPC': 'EGX:SKPC'
    }

    tv_symbol = symbol_mapping.get(symbol, f'EGX:{symbol}')

    widget_html = f"""
    <div class="tradingview-widget-container" style="height:650px;width:100%;margin:0;padding:0;">
        <div class="tradingview-widget-container__widget" style="height:100%;width:100%;">
            <div id="tradingview_chart" style="height:100%;width:100%;"></div>
        </div>
        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js" async>
        {{
            "width": "100%",
            "height": "650",
            "symbol": "{tv_symbol}",
            "interval": "{interval}",
            "timezone": "Africa/Cairo",
            "theme": "dark",
            "style": "1",
            "locale": "en",
            "enable_publishing": false,
            "backgroundColor": "rgba(19, 23, 34, 1)",
            "gridColor": "rgba(42, 46, 57, 0.06)",
            "hide_top_toolbar": false,
            "hide_legend": false,
            "save_image": true,
            "calendar": false,
            "hide_volume": false,
            "allow_symbol_change": true,
            "container_id": "tradingview_chart",
            "support_host": "https://www.tradingview.com",
            "studies": [
                "Volume@tv-basicstudies"
            ]
        }}
        </script>
    </div>
    """
    return widget_html

def show_custom_smc_analysis(df, order_blocks, fair_value_gaps, structure_points, analysis_options):
    """Show custom SMC analysis with clean charts"""
    st.subheader("🔍 Custom SMC Analysis")

    # Create clean individual charts for each component
    if analysis_options["order_blocks"]:
        st.markdown("### 🔴 Order Blocks Analysis")
        fig_ob = create_clean_order_blocks_chart(df, order_blocks)
        st.plotly_chart(fig_ob, use_container_width=True)

    if analysis_options["fair_value_gaps"]:
        st.markdown("### ⚡ Fair Value Gaps Analysis")
        fig_fvg = create_clean_fvg_chart(df, fair_value_gaps)
        st.plotly_chart(fig_fvg, use_container_width=True)

    if analysis_options["market_structure"]:
        st.markdown("### 📊 Market Structure Analysis")
        fig_ms = create_clean_structure_chart(df, structure_points)
        st.plotly_chart(fig_ms, use_container_width=True)

def show_combined_analysis(symbol, df, order_blocks, fair_value_gaps, structure_points, analysis_options, chart_interval="15"):
    """Show combined TradingView + Custom analysis"""
    col1, col2 = st.columns([2, 1])

    with col1:
        # TradingView Chart
        st.subheader("📈 TradingView Chart")
        tradingview_widget = create_tradingview_widget(symbol, chart_interval)
        components.html(tradingview_widget, height=500)

    with col2:
        # Compact SMC Summary
        show_smc_summary_compact(order_blocks, fair_value_gaps, structure_points, df)

    # SMC Analysis below
    st.markdown("---")
    show_custom_smc_analysis(df, order_blocks, fair_value_gaps, structure_points, analysis_options)

def show_order_blocks_analysis(df, order_blocks):
    """Show detailed order blocks analysis"""
    if not order_blocks:
        st.info("No order blocks detected with current settings.")
        return

    # Create clean chart with only order blocks
    fig = create_clean_order_blocks_chart(df, order_blocks)
    st.plotly_chart(fig, use_container_width=True)

    # Order blocks table
    st.subheader("📋 Order Blocks Details")

    ob_data = []
    for i, block in enumerate(order_blocks):
        ob_data.append({
            "ID": i + 1,
            "Type": "Resistance" if block.is_high_block else "Support",
            "Top": f"{block.top:.2f}",
            "Bottom": f"{block.bottom:.2f}",
            "Size": f"{block.top - block.bottom:.2f}",
            "Strength": block.strength,
            "Status": "Active" if block.is_active else "Broken"
        })

    if ob_data:
        st.dataframe(pd.DataFrame(ob_data), use_container_width=True)

def show_fvg_analysis(df, fair_value_gaps):
    """Show detailed fair value gaps analysis"""
    if not fair_value_gaps:
        st.info("No fair value gaps detected with current settings.")
        return

    # Create clean chart with only FVGs
    fig = create_clean_fvg_chart(df, fair_value_gaps)
    st.plotly_chart(fig, use_container_width=True)

    # FVG table
    st.subheader("📋 Fair Value Gaps Details")

    fvg_data = []
    for i, gap in enumerate(fair_value_gaps):
        fvg_data.append({
            "ID": i + 1,
            "Direction": "Bullish" if gap.direction == 1 else "Bearish",
            "Top": f"{gap.top:.2f}",
            "Bottom": f"{gap.bottom:.2f}",
            "Size": f"{gap.top - gap.bottom:.2f}",
            "Status": "Active" if gap.is_active else "Filled"
        })

    if fvg_data:
        st.dataframe(pd.DataFrame(fvg_data), use_container_width=True)

def show_market_structure_analysis(df, structure_points):
    """Show detailed market structure analysis"""
    if not structure_points:
        st.info("No market structure changes detected with current settings.")
        return

    # Create clean chart with only structure points
    fig = create_clean_structure_chart(df, structure_points)
    st.plotly_chart(fig, use_container_width=True)

    # Structure points table
    st.subheader("📋 Market Structure Details")

    structure_data = []
    for i, point in enumerate(structure_points):
        structure_data.append({
            "ID": i + 1,
            "Type": point.structure_type,
            "Direction": "Bullish" if point.direction == 1 else "Bearish",
            "Price": f"{point.price:.2f}",
            "Level": "High" if point.is_high else "Low"
        })

    if structure_data:
        st.dataframe(pd.DataFrame(structure_data), use_container_width=True)

def show_session_analysis(session_data):
    """Show session analysis"""
    if not session_data:
        st.info("No session data available.")
        return

    st.subheader("🌍 Trading Sessions Analysis")

    cols = st.columns(len(session_data))
    for i, (session_name, data) in enumerate(session_data.items()):
        with cols[i]:
            st.metric(
                f"{session_name} Session",
                f"{data['high']:.2f}",
                delta=f"Range: {data['high'] - data['low']:.2f}"
            )
            st.caption(f"Low: {data['low']:.2f}")
            st.caption(f"Avg Volume: {data['volume']:,.0f}")
            st.caption(f"Volatility: {data['volatility']:.2f}")

def show_smc_summary_compact(order_blocks, fair_value_gaps, structure_points, df):
    """Show compact SMC summary for sidebar"""
    st.subheader("📊 SMC Summary")

    # Order Blocks
    high_blocks = [b for b in order_blocks if b.is_high_block]
    low_blocks = [b for b in order_blocks if not b.is_high_block]
    st.metric("🔴 Resistance Blocks", len(high_blocks))
    st.metric("🟢 Support Blocks", len(low_blocks))

    # Fair Value Gaps
    bullish_gaps = [g for g in fair_value_gaps if g.direction == 1]
    bearish_gaps = [g for g in fair_value_gaps if g.direction == -1]
    st.metric("⚡ Bullish FVGs", len(bullish_gaps))
    st.metric("⚡ Bearish FVGs", len(bearish_gaps))

    # Market Structure
    bos_points = [s for s in structure_points if s.structure_type == "BoS"]
    choch_points = [s for s in structure_points if s.structure_type == "CHoCH"]
    st.metric("📊 BoS Points", len(bos_points))
    st.metric("📊 CHoCH Points", len(choch_points))

    # Latest structure
    if structure_points:
        latest = structure_points[-1]
        direction = "🟢 Bullish" if latest.direction == 1 else "🔴 Bearish"
        st.info(f"Latest: {latest.structure_type} - {direction}")

    # Trading signals
    st.markdown("**🎯 Signals**")
    signals = generate_smc_signals(order_blocks, fair_value_gaps, structure_points, df)

    for signal in signals[:3]:  # Show only top 3 signals
        if signal['type'] == 'bullish':
            st.success(f"🟢 {signal['message'][:30]}...")
        elif signal['type'] == 'bearish':
            st.error(f"🔴 {signal['message'][:30]}...")
        else:
            st.info(f"ℹ️ {signal['message'][:30]}...")

def generate_smc_signals(order_blocks, fair_value_gaps, structure_points, df):
    """Generate trading signals based on SMC analysis"""
    signals = []
    current_price = df['Close'].iloc[-1]

    # Order Block signals
    for block in order_blocks:
        if not block.is_high_block and current_price <= block.top and current_price >= block.bottom:
            signals.append({
                'type': 'bullish',
                'message': f"Price in Support Order Block ({block.bottom:.2f} - {block.top:.2f})"
            })
        elif block.is_high_block and current_price <= block.top and current_price >= block.bottom:
            signals.append({
                'type': 'bearish',
                'message': f"Price in Resistance Order Block ({block.bottom:.2f} - {block.top:.2f})"
            })

    # Fair Value Gap signals
    for gap in fair_value_gaps:
        if current_price <= gap.top and current_price >= gap.bottom:
            gap_type = "Bullish" if gap.direction == 1 else "Bearish"
            signals.append({
                'type': 'neutral',
                'message': f"Price in {gap_type} FVG ({gap.bottom:.2f} - {gap.top:.2f})"
            })

    # Market Structure signals
    if structure_points:
        latest_structure = structure_points[-1]
        if latest_structure.structure_type == "CHoCH":
            direction = "Bullish" if latest_structure.direction == 1 else "Bearish"
            signals.append({
                'type': 'bullish' if latest_structure.direction == 1 else 'bearish',
                'message': f"Recent Change of Character - {direction} momentum"
            })

    if not signals:
        signals.append({
            'type': 'neutral',
            'message': "No active SMC signals detected"
        })

    return signals

def create_clean_order_blocks_chart(df: pd.DataFrame, order_blocks: List[OrderBlock]) -> go.Figure:
    """Create clean chart showing only order blocks"""
    try:
        fig = go.Figure()

        # Add candlestick data
        fig.add_trace(go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name="Price",
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350'
        ))

        # Add Order Blocks only
        for i, block in enumerate(order_blocks):
            color = "rgba(239, 83, 80, 0.2)" if block.is_high_block else "rgba(38, 166, 154, 0.2)"
            border_color = "#ef5350" if block.is_high_block else "#26a69a"
            block_type = "Resistance" if block.is_high_block else "Support"

            fig.add_shape(
                type="rect",
                x0=df.index[block.start_time],
                x1=df.index[-1],
                y0=block.bottom,
                y1=block.top,
                fillcolor=color,
                line=dict(color=border_color, width=2),
                name=f"{block_type} Block {i+1}"
            )

        fig.update_layout(
            title="Order Blocks Analysis",
            xaxis_title="Time",
            yaxis_title="Price",
            template="plotly_dark",
            height=400,
            showlegend=False,
            xaxis_rangeslider_visible=False
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating order blocks chart: {str(e)}")
        return go.Figure()

def create_clean_fvg_chart(df: pd.DataFrame, fair_value_gaps: List[FairValueGap]) -> go.Figure:
    """Create clean chart showing only fair value gaps"""
    try:
        fig = go.Figure()

        # Add candlestick data
        fig.add_trace(go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name="Price",
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350'
        ))

        # Add Fair Value Gaps only
        for i, gap in enumerate(fair_value_gaps):
            color = "rgba(255, 193, 7, 0.2)" if gap.direction == 1 else "rgba(156, 39, 176, 0.2)"
            border_color = "#ffc107" if gap.direction == 1 else "#9c27b0"
            gap_type = "Bullish" if gap.direction == 1 else "Bearish"

            fig.add_shape(
                type="rect",
                x0=df.index[gap.start_time],
                x1=df.index[-1],
                y0=gap.bottom,
                y1=gap.top,
                fillcolor=color,
                line=dict(color=border_color, width=2, dash="dash"),
                name=f"{gap_type} FVG {i+1}"
            )

        fig.update_layout(
            title="Fair Value Gaps Analysis",
            xaxis_title="Time",
            yaxis_title="Price",
            template="plotly_dark",
            height=400,
            showlegend=False,
            xaxis_rangeslider_visible=False
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating FVG chart: {str(e)}")
        return go.Figure()

def create_clean_structure_chart(df: pd.DataFrame, structure_points: List[StructurePoint]) -> go.Figure:
    """Create clean chart showing only market structure"""
    try:
        fig = go.Figure()

        # Add candlestick data
        fig.add_trace(go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name="Price",
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350'
        ))

        # Add Market Structure Points only
        for i, point in enumerate(structure_points):
            color = "#00ff00" if point.direction == 1 else "#ff0000"
            symbol = "triangle-up" if point.direction == 1 else "triangle-down"

            fig.add_trace(go.Scatter(
                x=[df.index[point.time]],
                y=[point.price],
                mode='markers',
                name=f"{point.structure_type} {i+1}",
                marker=dict(
                    color=color,
                    size=20,
                    symbol=symbol,
                    line=dict(color="white", width=2)
                ),
                showlegend=False
            ))

            # Add structure label
            fig.add_annotation(
                x=df.index[point.time],
                y=point.price,
                text=point.structure_type,
                showarrow=True,
                arrowhead=2,
                arrowcolor=color,
                bgcolor=color,
                bordercolor=color,
                font=dict(color="white", size=12),
                yshift=30 if point.is_high else -30
            )

        fig.update_layout(
            title="Market Structure Analysis",
            xaxis_title="Time",
            yaxis_title="Price",
            template="plotly_dark",
            height=400,
            showlegend=False,
            xaxis_rangeslider_visible=False
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating structure chart: {str(e)}")
        return go.Figure()

def create_smc_chart(df: pd.DataFrame, order_blocks: List[OrderBlock], fair_value_gaps: List[FairValueGap], structure_points: List[StructurePoint] = None) -> go.Figure:
    """Create SMC analysis chart with Plotly"""
    try:
        # Create candlestick chart
        fig = go.Figure()

        # Add candlestick data
        fig.add_trace(go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name="Price",
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350'
        ))

        # Add Order Blocks
        for i, block in enumerate(order_blocks):
            color = "rgba(239, 83, 80, 0.3)" if block.is_high_block else "rgba(38, 166, 154, 0.3)"
            border_color = "#ef5350" if block.is_high_block else "#26a69a"
            block_type = "Resistance" if block.is_high_block else "Support"

            fig.add_shape(
                type="rect",
                x0=df.index[block.start_time],
                x1=df.index[-1],
                y0=block.bottom,
                y1=block.top,
                fillcolor=color,
                line=dict(color=border_color, width=1),
                name=f"{block_type} Block {i+1}"
            )

            # Add label
            fig.add_annotation(
                x=df.index[block.start_time],
                y=block.top if block.is_high_block else block.bottom,
                text=f"OB-{block_type[0]}",
                showarrow=True,
                arrowhead=2,
                arrowcolor=border_color,
                bgcolor=border_color,
                bordercolor=border_color,
                font=dict(color="white", size=10)
            )

        # Add Fair Value Gaps
        for i, gap in enumerate(fair_value_gaps):
            color = "rgba(255, 193, 7, 0.3)" if gap.direction == 1 else "rgba(156, 39, 176, 0.3)"
            border_color = "#ffc107" if gap.direction == 1 else "#9c27b0"
            gap_type = "Bullish" if gap.direction == 1 else "Bearish"

            fig.add_shape(
                type="rect",
                x0=df.index[gap.start_time],
                x1=df.index[-1],
                y0=gap.bottom,
                y1=gap.top,
                fillcolor=color,
                line=dict(color=border_color, width=1, dash="dash"),
                name=f"{gap_type} FVG {i+1}"
            )

            # Add label
            fig.add_annotation(
                x=df.index[gap.start_time],
                y=(gap.top + gap.bottom) / 2,
                text=f"FVG-{gap_type[0]}",
                showarrow=True,
                arrowhead=2,
                arrowcolor=border_color,
                bgcolor=border_color,
                bordercolor=border_color,
                font=dict(color="white", size=10)
            )

        # Add Market Structure Points
        if structure_points:
            for i, point in enumerate(structure_points):
                color = "#00ff00" if point.direction == 1 else "#ff0000"
                symbol = "triangle-up" if point.direction == 1 else "triangle-down"

                fig.add_trace(go.Scatter(
                    x=[df.index[point.time]],
                    y=[point.price],
                    mode='markers',
                    name=f"{point.structure_type} {i+1}",
                    marker=dict(
                        color=color,
                        size=15,
                        symbol=symbol,
                        line=dict(color="white", width=2)
                    ),
                    showlegend=False
                ))

                # Add structure label
                fig.add_annotation(
                    x=df.index[point.time],
                    y=point.price,
                    text=point.structure_type,
                    showarrow=True,
                    arrowhead=2,
                    arrowcolor=color,
                    bgcolor=color,
                    bordercolor=color,
                    font=dict(color="white", size=9),
                    yshift=20 if point.is_high else -20
                )

        # Update layout
        fig.update_layout(
            title="Smart Money Concepts Analysis",
            xaxis_title="Time",
            yaxis_title="Price",
            template="plotly_dark",
            height=600,
            showlegend=False,
            xaxis_rangeslider_visible=False
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating SMC chart: {str(e)}")
        # Return empty chart on error
        fig = go.Figure()
        fig.add_annotation(text=f"Error creating chart: {str(e)}",
                          xref="paper", yref="paper", x=0.5, y=0.5)
        return fig

if __name__ == "__main__":
    show_chart_tools()
