"""
Advanced Chart Tools Page
Implements Smart Money Concepts (SMC) and other advanced technical indicators
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class OrderBlock:
    """Order Block data structure"""
    start_time: int
    end_time: int
    top: float
    bottom: float
    is_high_block: bool
    strength: int = 1
    is_active: bool = True

@dataclass
class FairValueGap:
    """Fair Value Gap data structure"""
    start_time: int
    end_time: int
    top: float
    bottom: float
    direction: int  # 1 for bullish, -1 for bearish
    is_active: bool = True

@dataclass
class StructurePoint:
    """Market Structure Point data structure"""
    time: int
    price: float
    is_high: bool
    structure_type: str  # "BoS", "CHoCH", "I-BoS", "I-CHoCH"
    direction: int  # 1 for bullish, -1 for bearish

class SMCIndicator:
    """Smart Money Concepts Indicator Implementation"""

    def __init__(self, sensitivity: int = 5, internal_sensitivity: int = 3, show_last: int = 10):
        self.sensitivity = sensitivity
        self.internal_sensitivity = internal_sensitivity
        self.show_last = show_last
        self.order_blocks = []
        self.fair_value_gaps = []
        self.structure_points = []
        self.session_data = {}

    def calculate_pivots(self, df: pd.DataFrame, length: int) -> Tuple[pd.Series, pd.Series]:
        """Calculate pivot highs and lows"""
        try:
            high_series = df['High'].rolling(window=length*2+1, center=True).max()
            low_series = df['Low'].rolling(window=length*2+1, center=True).min()

            pivot_highs = pd.Series(index=df.index, dtype=float)
            pivot_lows = pd.Series(index=df.index, dtype=float)

            for i in range(length, len(df) - length):
                # Check for pivot high
                if df['High'].iloc[i] == high_series.iloc[i]:
                    # Verify it's actually a pivot (higher than surrounding bars)
                    is_pivot_high = True
                    for j in range(i - length, i + length + 1):
                        if j != i and df['High'].iloc[j] >= df['High'].iloc[i]:
                            is_pivot_high = False
                            break
                    if is_pivot_high:
                        pivot_highs.iloc[i] = df['High'].iloc[i]

                # Check for pivot low
                if df['Low'].iloc[i] == low_series.iloc[i]:
                    # Verify it's actually a pivot (lower than surrounding bars)
                    is_pivot_low = True
                    for j in range(i - length, i + length + 1):
                        if j != i and df['Low'].iloc[j] <= df['Low'].iloc[i]:
                            is_pivot_low = False
                            break
                    if is_pivot_low:
                        pivot_lows.iloc[i] = df['Low'].iloc[i]

            return pivot_highs, pivot_lows

        except Exception as e:
            logger.error(f"Error calculating pivots: {str(e)}")
            return pd.Series(index=df.index, dtype=float), pd.Series(index=df.index, dtype=float)

    def detect_order_blocks(self, df: pd.DataFrame) -> List[OrderBlock]:
        """Detect institutional order blocks based on pivot points"""
        try:
            order_blocks = []
            pivot_highs, pivot_lows = self.calculate_pivots(df, self.sensitivity)

            # Detect high order blocks (resistance zones)
            for i in range(len(df)):
                if not pd.isna(pivot_highs.iloc[i]):
                    # Look for the candle that created this high
                    high_price = pivot_highs.iloc[i]

                    # Find the order block zone (the candle before the move)
                    for j in range(max(0, i - self.sensitivity), i):
                        if (df['Close'].iloc[j] < df['Open'].iloc[j] and  # Bearish candle
                            df['High'].iloc[j] < high_price):  # Below the high

                            order_block = OrderBlock(
                                start_time=j,
                                end_time=len(df),  # Extend to current time
                                top=df['High'].iloc[j],
                                bottom=df['Low'].iloc[j],
                                is_high_block=True,
                                strength=1
                            )
                            order_blocks.append(order_block)
                            break

            # Detect low order blocks (support zones)
            for i in range(len(df)):
                if not pd.isna(pivot_lows.iloc[i]):
                    # Look for the candle that created this low
                    low_price = pivot_lows.iloc[i]

                    # Find the order block zone (the candle before the move)
                    for j in range(max(0, i - self.sensitivity), i):
                        if (df['Close'].iloc[j] > df['Open'].iloc[j] and  # Bullish candle
                            df['Low'].iloc[j] > low_price):  # Above the low

                            order_block = OrderBlock(
                                start_time=j,
                                end_time=len(df),  # Extend to current time
                                top=df['High'].iloc[j],
                                bottom=df['Low'].iloc[j],
                                is_high_block=False,
                                strength=1
                            )
                            order_blocks.append(order_block)
                            break

            # Clean invalid order blocks (price has moved through them)
            valid_blocks = []
            current_price = df['Close'].iloc[-1]

            for block in order_blocks:
                if block.is_high_block:
                    # High block is invalid if price closed above it
                    if current_price < block.top:
                        valid_blocks.append(block)
                else:
                    # Low block is invalid if price closed below it
                    if current_price > block.bottom:
                        valid_blocks.append(block)

            # Limit to show_last number of blocks
            if len(valid_blocks) > self.show_last:
                valid_blocks = valid_blocks[-self.show_last:]

            self.order_blocks = valid_blocks
            return valid_blocks

        except Exception as e:
            logger.error(f"Error detecting order blocks: {str(e)}")
            return []

    def detect_fair_value_gaps(self, df: pd.DataFrame) -> List[FairValueGap]:
        """Detect 3-candle Fair Value Gaps"""
        try:
            fair_value_gaps = []

            for i in range(2, len(df)):
                # Get 3 consecutive candles
                candle1 = df.iloc[i-2]  # First candle
                candle2 = df.iloc[i-1]  # Middle candle
                candle3 = df.iloc[i]    # Third candle

                # Check for bullish FVG (gap up)
                if (candle1['Close'] < candle1['Open'] and  # First candle bearish
                    candle2['Close'] > candle2['Open'] and  # Middle candle bullish
                    candle3['Close'] > candle3['Open'] and  # Third candle bullish
                    candle3['Low'] > candle1['High']):      # Gap exists

                    fvg = FairValueGap(
                        start_time=i-1,
                        end_time=len(df),
                        top=candle3['Low'],
                        bottom=candle1['High'],
                        direction=1,  # Bullish
                        is_active=True
                    )
                    fair_value_gaps.append(fvg)

                # Check for bearish FVG (gap down)
                elif (candle1['Close'] > candle1['Open'] and  # First candle bullish
                      candle2['Close'] < candle2['Open'] and  # Middle candle bearish
                      candle3['Close'] < candle3['Open'] and  # Third candle bearish
                      candle3['High'] < candle1['Low']):      # Gap exists

                    fvg = FairValueGap(
                        start_time=i-1,
                        end_time=len(df),
                        top=candle1['Low'],
                        bottom=candle3['High'],
                        direction=-1,  # Bearish
                        is_active=True
                    )
                    fair_value_gaps.append(fvg)

            # Remove filled gaps
            active_gaps = []
            current_high = df['High'].iloc[-1]
            current_low = df['Low'].iloc[-1]

            for gap in fair_value_gaps:
                if gap.direction == 1:  # Bullish gap
                    # Gap is filled if price went back down into the gap
                    if current_low > gap.bottom:
                        active_gaps.append(gap)
                else:  # Bearish gap
                    # Gap is filled if price went back up into the gap
                    if current_high < gap.top:
                        active_gaps.append(gap)

            self.fair_value_gaps = active_gaps
            return active_gaps

        except Exception as e:
            logger.error(f"Error detecting fair value gaps: {str(e)}")
            return []

    def analyze_market_structure(self, df: pd.DataFrame) -> List[StructurePoint]:
        """Analyze market structure for BoS and CHoCH patterns"""
        try:
            structure_points = []
            pivot_highs, pivot_lows = self.calculate_pivots(df, self.sensitivity)

            # Get valid pivot points
            high_pivots = [(i, price) for i, price in enumerate(pivot_highs) if not pd.isna(price)]
            low_pivots = [(i, price) for i, price in enumerate(pivot_lows) if not pd.isna(price)]

            # Combine and sort all pivots by time
            all_pivots = [(i, price, True) for i, price in high_pivots] + [(i, price, False) for i, price in low_pivots]
            all_pivots.sort(key=lambda x: x[0])

            if len(all_pivots) < 3:
                return structure_points

            # Track market direction
            current_trend = None  # 1 for bullish, -1 for bearish
            last_high = None
            last_low = None

            for i in range(len(all_pivots)):
                time_idx, price, is_high = all_pivots[i]

                if is_high:  # High pivot
                    if last_high is not None:
                        if price > last_high[1]:  # Higher high
                            if current_trend == -1:  # Was bearish, now bullish
                                # Change of Character (CHoCH)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=True,
                                    structure_type="CHoCH",
                                    direction=1
                                )
                                structure_points.append(structure_point)
                                current_trend = 1
                            elif current_trend == 1:  # Continuing bullish
                                # Break of Structure (BoS)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=True,
                                    structure_type="BoS",
                                    direction=1
                                )
                                structure_points.append(structure_point)
                        else:  # Lower high
                            if current_trend == 1:  # Was bullish, potential bearish
                                current_trend = -1

                    last_high = (time_idx, price)

                else:  # Low pivot
                    if last_low is not None:
                        if price < last_low[1]:  # Lower low
                            if current_trend == 1:  # Was bullish, now bearish
                                # Change of Character (CHoCH)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=False,
                                    structure_type="CHoCH",
                                    direction=-1
                                )
                                structure_points.append(structure_point)
                                current_trend = -1
                            elif current_trend == -1:  # Continuing bearish
                                # Break of Structure (BoS)
                                structure_point = StructurePoint(
                                    time=time_idx,
                                    price=price,
                                    is_high=False,
                                    structure_type="BoS",
                                    direction=-1
                                )
                                structure_points.append(structure_point)
                        else:  # Higher low
                            if current_trend == -1:  # Was bearish, potential bullish
                                current_trend = 1

                    last_low = (time_idx, price)

            # Limit to recent structure points
            if len(structure_points) > self.show_last:
                structure_points = structure_points[-self.show_last:]

            self.structure_points = structure_points
            return structure_points

        except Exception as e:
            logger.error(f"Error analyzing market structure: {str(e)}")
            return []

    def calculate_session_data(self, df: pd.DataFrame) -> Dict:
        """Calculate session-based analysis (NY, London, Asia)"""
        try:
            session_data = {}

            # Add hour column if not exists
            if 'Hour' not in df.columns:
                df['Hour'] = pd.to_datetime(df['Date']).dt.hour

            # Define session times (assuming UTC)
            sessions = {
                'Asian': (20, 2),    # 8 PM to 2 AM UTC
                'London': (7, 15),   # 7 AM to 3 PM UTC
                'New_York': (13, 21) # 1 PM to 9 PM UTC
            }

            for session_name, (start_hour, end_hour) in sessions.items():
                if start_hour > end_hour:  # Session crosses midnight
                    session_mask = (df['Hour'] >= start_hour) | (df['Hour'] <= end_hour)
                else:
                    session_mask = (df['Hour'] >= start_hour) & (df['Hour'] <= end_hour)

                session_df = df[session_mask]

                if not session_df.empty:
                    session_data[session_name] = {
                        'high': session_df['High'].max(),
                        'low': session_df['Low'].min(),
                        'volume': session_df['Volume'].mean(),
                        'volatility': (session_df['High'] - session_df['Low']).mean(),
                        'candle_count': len(session_df)
                    }

            self.session_data = session_data
            return session_data

        except Exception as e:
            logger.error(f"Error calculating session data: {str(e)}")
            return {}

def show_chart_tools():
    """Main function to display the Chart Tools page"""
    st.title("🔧 Advanced Chart Tools")
    st.markdown("Professional-grade technical analysis tools with Smart Money Concepts")

    # Check if historical data is available
    if 'historical_data' not in st.session_state or st.session_state.historical_data is None:
        st.warning("⚠️ No historical data available. Please load stock data first from the Stock Management page.")
        return

    df = st.session_state.historical_data
    if df.empty:
        st.warning("⚠️ Historical data is empty. Please load valid stock data.")
        return

    # Sidebar configuration
    st.sidebar.header("🎛️ SMC Configuration")

    # SMC Settings
    sensitivity = st.sidebar.slider("Pivot Sensitivity", min_value=3, max_value=15, value=5,
                                   help="Higher values = fewer, stronger pivots")
    internal_sensitivity = st.sidebar.slider("Internal Structure Sensitivity", min_value=2, max_value=10, value=3,
                                            help="Sensitivity for internal market structure")
    show_last = st.sidebar.slider("Show Last N Levels", min_value=5, max_value=50, value=10,
                                 help="Number of recent levels to display")

    # Feature toggles
    st.sidebar.subheader("📊 Display Options")
    show_order_blocks = st.sidebar.checkbox("Order Blocks", value=True)
    show_fair_value_gaps = st.sidebar.checkbox("Fair Value Gaps", value=True)
    show_market_structure = st.sidebar.checkbox("Market Structure", value=True)
    show_session_analysis = st.sidebar.checkbox("Session Analysis", value=False)

    # Initialize SMC indicator
    smc = SMCIndicator(sensitivity=sensitivity, internal_sensitivity=internal_sensitivity, show_last=show_last)

    # Main content area
    col1, col2 = st.columns([3, 1])

    with col1:
        st.subheader("📈 SMC Analysis Chart")

        # Generate SMC analysis
        with st.spinner("Analyzing Smart Money Concepts..."):
            order_blocks = smc.detect_order_blocks(df) if show_order_blocks else []
            fair_value_gaps = smc.detect_fair_value_gaps(df) if show_fair_value_gaps else []
            structure_points = smc.analyze_market_structure(df) if show_market_structure else []
            session_data = smc.calculate_session_data(df) if show_session_analysis else {}

        # Create the chart
        fig = create_smc_chart(df, order_blocks, fair_value_gaps, structure_points)
        st.plotly_chart(fig, use_container_width=True)

        # Session Analysis
        if show_session_analysis and session_data:
            st.subheader("📊 Session Analysis")
            session_cols = st.columns(len(session_data))

            for i, (session_name, data) in enumerate(session_data.items()):
                with session_cols[i]:
                    st.metric(f"{session_name} Session", f"{data['high']:.2f}")
                    st.caption(f"Low: {data['low']:.2f}")
                    st.caption(f"Vol: {data['volume']:,.0f}")
                    st.caption(f"Volatility: {data['volatility']:.2f}")

    with col2:
        st.subheader("📊 SMC Summary")

        # Order Blocks Summary
        if show_order_blocks:
            st.markdown("**🔴 Order Blocks**")
            high_blocks = [b for b in order_blocks if b.is_high_block]
            low_blocks = [b for b in order_blocks if not b.is_high_block]

            st.metric("Resistance Blocks", len(high_blocks))
            st.metric("Support Blocks", len(low_blocks))

        # Fair Value Gaps Summary
        if show_fair_value_gaps:
            st.markdown("**⚡ Fair Value Gaps**")
            bullish_gaps = [g for g in fair_value_gaps if g.direction == 1]
            bearish_gaps = [g for g in fair_value_gaps if g.direction == -1]

            st.metric("Bullish FVGs", len(bullish_gaps))
            st.metric("Bearish FVGs", len(bearish_gaps))

        # Market Structure Summary
        if show_market_structure:
            st.markdown("**📈 Market Structure**")
            bos_points = [s for s in structure_points if s.structure_type == "BoS"]
            choch_points = [s for s in structure_points if s.structure_type == "CHoCH"]

            st.metric("Break of Structure", len(bos_points))
            st.metric("Change of Character", len(choch_points))

            # Show latest structure
            if structure_points:
                latest = structure_points[-1]
                direction = "🟢 Bullish" if latest.direction == 1 else "🔴 Bearish"
                st.info(f"Latest: {latest.structure_type} - {direction}")

        # Trading Signals
        st.markdown("**🎯 Trading Signals**")
        signals = generate_smc_signals(order_blocks, fair_value_gaps, structure_points, df)

        for signal in signals:
            if signal['type'] == 'bullish':
                st.success(f"🟢 {signal['message']}")
            elif signal['type'] == 'bearish':
                st.error(f"🔴 {signal['message']}")
            else:
                st.info(f"ℹ️ {signal['message']}")

def generate_smc_signals(order_blocks, fair_value_gaps, structure_points, df):
    """Generate trading signals based on SMC analysis"""
    signals = []
    current_price = df['Close'].iloc[-1]

    # Order Block signals
    for block in order_blocks:
        if not block.is_high_block and current_price <= block.top and current_price >= block.bottom:
            signals.append({
                'type': 'bullish',
                'message': f"Price in Support Order Block ({block.bottom:.2f} - {block.top:.2f})"
            })
        elif block.is_high_block and current_price <= block.top and current_price >= block.bottom:
            signals.append({
                'type': 'bearish',
                'message': f"Price in Resistance Order Block ({block.bottom:.2f} - {block.top:.2f})"
            })

    # Fair Value Gap signals
    for gap in fair_value_gaps:
        if current_price <= gap.top and current_price >= gap.bottom:
            gap_type = "Bullish" if gap.direction == 1 else "Bearish"
            signals.append({
                'type': 'neutral',
                'message': f"Price in {gap_type} FVG ({gap.bottom:.2f} - {gap.top:.2f})"
            })

    # Market Structure signals
    if structure_points:
        latest_structure = structure_points[-1]
        if latest_structure.structure_type == "CHoCH":
            direction = "Bullish" if latest_structure.direction == 1 else "Bearish"
            signals.append({
                'type': 'bullish' if latest_structure.direction == 1 else 'bearish',
                'message': f"Recent Change of Character - {direction} momentum"
            })

    if not signals:
        signals.append({
            'type': 'neutral',
            'message': "No active SMC signals detected"
        })

    return signals

def create_smc_chart(df: pd.DataFrame, order_blocks: List[OrderBlock], fair_value_gaps: List[FairValueGap], structure_points: List[StructurePoint] = None) -> go.Figure:
    """Create SMC analysis chart with Plotly"""
    try:
        # Create candlestick chart
        fig = go.Figure()

        # Add candlestick data
        fig.add_trace(go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name="Price",
            increasing_line_color='#26a69a',
            decreasing_line_color='#ef5350'
        ))

        # Add Order Blocks
        for i, block in enumerate(order_blocks):
            color = "rgba(239, 83, 80, 0.3)" if block.is_high_block else "rgba(38, 166, 154, 0.3)"
            border_color = "#ef5350" if block.is_high_block else "#26a69a"
            block_type = "Resistance" if block.is_high_block else "Support"

            fig.add_shape(
                type="rect",
                x0=df.index[block.start_time],
                x1=df.index[-1],
                y0=block.bottom,
                y1=block.top,
                fillcolor=color,
                line=dict(color=border_color, width=1),
                name=f"{block_type} Block {i+1}"
            )

            # Add label
            fig.add_annotation(
                x=df.index[block.start_time],
                y=block.top if block.is_high_block else block.bottom,
                text=f"OB-{block_type[0]}",
                showarrow=True,
                arrowhead=2,
                arrowcolor=border_color,
                bgcolor=border_color,
                bordercolor=border_color,
                font=dict(color="white", size=10)
            )

        # Add Fair Value Gaps
        for i, gap in enumerate(fair_value_gaps):
            color = "rgba(255, 193, 7, 0.3)" if gap.direction == 1 else "rgba(156, 39, 176, 0.3)"
            border_color = "#ffc107" if gap.direction == 1 else "#9c27b0"
            gap_type = "Bullish" if gap.direction == 1 else "Bearish"

            fig.add_shape(
                type="rect",
                x0=df.index[gap.start_time],
                x1=df.index[-1],
                y0=gap.bottom,
                y1=gap.top,
                fillcolor=color,
                line=dict(color=border_color, width=1, dash="dash"),
                name=f"{gap_type} FVG {i+1}"
            )

            # Add label
            fig.add_annotation(
                x=df.index[gap.start_time],
                y=(gap.top + gap.bottom) / 2,
                text=f"FVG-{gap_type[0]}",
                showarrow=True,
                arrowhead=2,
                arrowcolor=border_color,
                bgcolor=border_color,
                bordercolor=border_color,
                font=dict(color="white", size=10)
            )

        # Add Market Structure Points
        if structure_points:
            for i, point in enumerate(structure_points):
                color = "#00ff00" if point.direction == 1 else "#ff0000"
                symbol = "triangle-up" if point.direction == 1 else "triangle-down"

                fig.add_trace(go.Scatter(
                    x=[df.index[point.time]],
                    y=[point.price],
                    mode='markers',
                    name=f"{point.structure_type} {i+1}",
                    marker=dict(
                        color=color,
                        size=15,
                        symbol=symbol,
                        line=dict(color="white", width=2)
                    ),
                    showlegend=False
                ))

                # Add structure label
                fig.add_annotation(
                    x=df.index[point.time],
                    y=point.price,
                    text=point.structure_type,
                    showarrow=True,
                    arrowhead=2,
                    arrowcolor=color,
                    bgcolor=color,
                    bordercolor=color,
                    font=dict(color="white", size=9),
                    yshift=20 if point.is_high else -20
                )

        # Update layout
        fig.update_layout(
            title="Smart Money Concepts Analysis",
            xaxis_title="Time",
            yaxis_title="Price",
            template="plotly_dark",
            height=600,
            showlegend=False,
            xaxis_rangeslider_visible=False
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating SMC chart: {str(e)}")
        # Return empty chart on error
        fig = go.Figure()
        fig.add_annotation(text=f"Error creating chart: {str(e)}",
                          xref="paper", yref="paper", x=0.5, y=0.5)
        return fig

if __name__ == "__main__":
    show_chart_tools()
