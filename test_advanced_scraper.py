"""
Test script for the advanced TradingView scraper
Tests both the direct scraper and the PriceScraper wrapper
"""

import asyncio
import logging
from scrapers.price_scraper import PriceScraper
from scrapers.advanced_scraper import scrape_multiple_pairs, PLAYWRIGHT_AVAILABLE

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_direct_scraper():
    """Test the direct advanced scraper"""
    print("\n🧪 Testing Direct Advanced Scraper")
    print("=" * 50)
    
    if not PLAYWRIGHT_AVAILABLE:
        print("❌ Playwright not available. Skipping direct scraper test.")
        return
    
    # Test with EGX symbols
    symbols = ["EGX-COMI", "EGX-CIB"]
    intervals = ["1D"]
    
    try:
        print(f"📊 Scraping symbols: {symbols}")
        print(f"⏰ Intervals: {intervals}")
        
        result = await scrape_multiple_pairs(symbols, intervals)
        
        if result:
            print("✅ Direct scraper test successful!")
            for symbol, data_list in result.items():
                print(f"\n📈 {symbol}:")
                for data in data_list:
                    print(f"  💰 Price: {data.price}")
                    print(f"  📊 Oscillators: {len(data.oscillators)}")
                    print(f"  📈 Moving Averages: {len(data.moving_averages)}")
                    print(f"  🎯 Pivots: {len(data.pivots)}")
        else:
            print("❌ No data returned from direct scraper")
            
    except Exception as e:
        print(f"❌ Direct scraper test failed: {str(e)}")

def test_price_scraper():
    """Test the PriceScraper wrapper"""
    print("\n🧪 Testing PriceScraper Wrapper")
    print("=" * 50)
    
    # Initialize scraper
    scraper = PriceScraper(source="tradingview")
    
    # Test symbols
    symbols = ["COMI", "CIB", "ETEL"]
    
    for symbol in symbols:
        try:
            print(f"\n📊 Getting price for {symbol}...")
            price_data = scraper.get_price(symbol)
            
            if price_data:
                print(f"✅ {symbol}: {price_data['price']} {price_data['currency']}")
                print(f"   📊 Source: {price_data['source']}")
                print(f"   ⏰ Real-time: {price_data['real_time']}")
                
                if 'technical_analysis' in price_data:
                    ta = price_data['technical_analysis']
                    print(f"   📈 Buy signals: {ta['buy_signals']}")
                    print(f"   📉 Sell signals: {ta['sell_signals']}")
                    print(f"   ⚖️ Neutral signals: {ta['neutral_signals']}")
            else:
                print(f"❌ No data for {symbol}")
                
        except Exception as e:
            print(f"❌ Error getting price for {symbol}: {str(e)}")

def test_advanced_data():
    """Test the advanced data functionality"""
    print("\n🧪 Testing Advanced Data Functionality")
    print("=" * 50)
    
    if not PLAYWRIGHT_AVAILABLE:
        print("❌ Playwright not available. Using sample data.")
        
    scraper = PriceScraper(source="tradingview")
    
    # Test multiple symbols with multiple intervals
    symbols = ["COMI", "CIB"]
    intervals = ["1D", "1W"]
    
    try:
        print(f"📊 Getting advanced data for: {symbols}")
        print(f"⏰ Intervals: {intervals}")
        
        data = scraper.get_advanced_data_sync(symbols, intervals)
        
        if data:
            print("✅ Advanced data test successful!")
            for symbol, financial_data_list in data.items():
                print(f"\n📈 {symbol}:")
                for i, financial_data in enumerate(financial_data_list):
                    print(f"  📊 Interval {i+1}:")
                    print(f"    💰 Price: {financial_data.price}")
                    print(f"    📊 Oscillators: {len(financial_data.oscillators)}")
                    print(f"    📈 Moving Averages: {len(financial_data.moving_averages)}")
                    print(f"    🎯 Pivots: {len(financial_data.pivots)}")
                    
                    # Show some indicator details
                    if financial_data.oscillators:
                        print(f"    🔍 Sample Oscillator: {financial_data.oscillators[0].name} = {financial_data.oscillators[0].action}")
                    if financial_data.moving_averages:
                        print(f"    🔍 Sample MA: {financial_data.moving_averages[0].name} = {financial_data.moving_averages[0].action}")
        else:
            print("❌ No advanced data returned")
            
    except Exception as e:
        print(f"❌ Advanced data test failed: {str(e)}")

def test_legacy_methods():
    """Test legacy method compatibility"""
    print("\n🧪 Testing Legacy Method Compatibility")
    print("=" * 50)
    
    scraper = PriceScraper(source="tradingview")
    
    # Test legacy methods
    legacy_methods = [
        ("get_tradingview_price", "COMI"),
        ("get_mubasher_price", "CIB"),
        ("login_to_tradingview", None),
        ("is_logged_in_to_tradingview", None)
    ]
    
    for method_name, symbol in legacy_methods:
        try:
            method = getattr(scraper, method_name)
            if symbol:
                result = method(symbol)
                print(f"✅ {method_name}({symbol}): Success")
                if isinstance(result, dict) and 'price' in result:
                    print(f"   💰 Price: {result['price']}")
            else:
                result = method()
                print(f"✅ {method_name}(): {result}")
                
        except Exception as e:
            print(f"❌ {method_name}: {str(e)}")

async def main():
    """Run all tests"""
    print("🚀 Advanced TradingView Scraper Test Suite")
    print("=" * 60)
    
    # Check Playwright availability
    if PLAYWRIGHT_AVAILABLE:
        print("✅ Playwright is available")
    else:
        print("⚠️ Playwright not available - some tests will use sample data")
        print("   Install with: pip install playwright && playwright install chromium")
    
    # Run tests
    await test_direct_scraper()
    test_price_scraper()
    test_advanced_data()
    test_legacy_methods()
    
    print("\n🎉 Test suite completed!")
    print("\n📝 Next Steps:")
    print("1. Start the API server: python scrapers/api_server.py")
    print("2. Test the API at: http://127.0.0.1:8000/docs")
    print("3. Use the new scraper in your stock prediction app")

if __name__ == "__main__":
    asyncio.run(main())
