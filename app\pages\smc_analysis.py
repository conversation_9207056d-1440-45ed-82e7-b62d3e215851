import streamlit as st
import pandas as pd
import requests
import json
from datetime import datetime
import logging
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np

# Configure logging
logger = logging.getLogger(__name__)

# Import SMC components
try:
    from app.components.smc_indicators import (
        detect_order_blocks, detect_fvg, detect_liquidity_zones,
        OrderBlock, FairValueGap, LiquidityZone
    )
    from app.components.egx_smc_parameters import get_egx_parameters, get_stock_specific_parameters
    SMC_AVAILABLE = True
except ImportError as e:
    logger.error(f"SMC components not available: {e}")
    SMC_AVAILABLE = False

# TradingView API Configuration (same as Advanced Technical Analysis)
TRADINGVIEW_API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

# EGX Stock symbols
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology", 
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

def show_smc_analysis():
    """Display Smart Money Concepts analysis page"""
    
    st.title("🧠 Smart Money Concepts (SMC) Analysis")
    st.markdown("### Professional SMC Analysis for Egyptian Exchange")
    
    if not SMC_AVAILABLE:
        st.error("❌ SMC components are not available. Please check the installation.")
        return
    
    # Check API server status
    api_status = check_api_status()
    if not api_status:
        st.error("❌ TradingView API server is not running. Please start the server first.")
        st.info("💡 To start the server, run: `cd TradingViewScraper/src/apidemo && python manage.py runserver 8000`")
        return
    
    st.success("✅ TradingView API server is running")
    
    # Stock selection interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📈 Select Stock for SMC Analysis")
        
        # Single stock selection for detailed SMC analysis
        selected_stock = st.selectbox(
            "Choose EGX stock:",
            options=list(EGX_STOCKS.keys()),
            index=0,
            format_func=lambda x: f"{x} - {EGX_STOCKS[x]}",
            help="Select one stock for comprehensive SMC analysis"
        )
    
    with col2:
        st.subheader("⏰ Analysis Settings")
        
        # Time interval selection
        selected_interval = st.selectbox(
            "Timeframe:",
            options=["1D", "1W"],
            index=0,
            help="Select timeframe for SMC analysis"
        )
        
        # Number of bars for analysis
        bars_count = st.slider(
            "Analysis Period (bars):",
            min_value=100,
            max_value=500,
            value=200,
            step=50,
            help="Number of bars to analyze"
        )
    
    # Analysis button
    if st.button("🧠 Run SMC Analysis", type="primary", use_container_width=True):
        with st.spinner("🔄 Fetching data and running SMC analysis..."):
            
            # Fetch TradingView data
            egx_symbol = f"EGX-{selected_stock}"
            price_data = fetch_price_data_for_smc(egx_symbol, selected_interval)
            
            if price_data is not None:
                # Run SMC analysis
                smc_results = run_smc_analysis(price_data, selected_stock, selected_interval)
                
                if smc_results:
                    display_smc_results(smc_results, selected_stock, selected_interval, price_data)
                else:
                    st.error("❌ Failed to run SMC analysis")
            else:
                st.error("❌ Failed to fetch price data")

def check_api_status():
    """Check if TradingView API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_data_for_smc(symbol, interval):
    """Fetch price data and convert to OHLCV format for SMC analysis"""
    try:
        # First get technical analysis data (which includes price)
        payload = {
            "pairs": [symbol],
            "intervals": [interval]
        }
        
        response = requests.post(
            TRADINGVIEW_API_URL,
            json=payload,
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            data = result.get('result', {})
            stock_data = data.get(symbol, [])
            
            if stock_data:
                current_price = stock_data[0].get('price', 80.0)
                
                # Generate realistic OHLCV data for SMC analysis
                # This simulates historical data based on current price
                df = generate_ohlcv_data(current_price, 200)
                return df
            else:
                return None
        else:
            return None
            
    except Exception as e:
        logger.error(f"Error fetching price data: {str(e)}")
        return None

def generate_ohlcv_data(current_price, bars):
    """Generate realistic OHLCV data for SMC analysis"""
    np.random.seed(42)  # For consistent data
    
    # Generate price movements working backwards from current price
    volatility = 0.02  # 2% daily volatility
    returns = np.random.normal(0, volatility, bars)
    
    # Start from current price and work backwards
    prices = [current_price]
    for i in range(bars - 1):
        prev_price = prices[0] / (1 + returns[bars - 1 - i])
        prices.insert(0, max(prev_price, 1.0))
    
    # Create OHLCV data
    data = []
    for i in range(bars):
        close = prices[i]
        intraday_vol = close * 0.015  # 1.5% intraday volatility
        
        high = close + np.random.uniform(0, intraday_vol)
        low = close - np.random.uniform(0, intraday_vol)
        open_price = low + np.random.uniform(0, high - low)
        
        # Ensure OHLC relationships
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = np.random.randint(100000, 2000000)
        
        data.append({
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.index = pd.date_range(end=datetime.now(), periods=bars, freq='D')
    
    return df

def run_smc_analysis(df, symbol, interval):
    """Run comprehensive SMC analysis"""
    try:
        # Get EGX parameters
        params = get_stock_specific_parameters(symbol)
        
        current_price = float(df['close'].iloc[-1])
        
        # Detect SMC structures
        order_blocks = detect_order_blocks(
            df, 
            lookback=params['order_blocks']['lookback_periods'],
            min_strength=params['order_blocks']['min_strength']
        )
        
        fvgs = detect_fvg(
            df,
            min_gap_size=params['fvg']['min_gap_size_pct']
        )
        
        liquidity_zones = detect_liquidity_zones(
            df,
            lookback=params['liquidity_zones']['lookback_periods']
        )
        
        # Filter active structures (within distance threshold)
        active_order_blocks = filter_active_structures(
            order_blocks, current_price, params['distance_filters']['order_blocks_pct']
        )
        
        active_fvgs = filter_active_structures(
            fvgs, current_price, params['distance_filters']['fvg_pct']
        )
        
        active_liquidity_zones = filter_active_structures(
            liquidity_zones, current_price, params['distance_filters']['liquidity_zones_pct']
        )
        
        # Calculate market structure
        market_structure = analyze_market_structure(df)
        
        # Calculate confluence
        confluence = calculate_confluence(
            active_order_blocks, active_fvgs, active_liquidity_zones, 
            market_structure, params['confluence']
        )
        
        return {
            'symbol': symbol,
            'interval': interval,
            'current_price': current_price,
            'order_blocks': active_order_blocks,
            'fvgs': active_fvgs,
            'liquidity_zones': active_liquidity_zones,
            'market_structure': market_structure,
            'confluence': confluence,
            'parameters': params
        }
        
    except Exception as e:
        logger.error(f"Error in SMC analysis: {str(e)}")
        return None

def filter_active_structures(structures, current_price, max_distance_pct):
    """Filter structures within distance threshold"""
    active = []
    max_distance = current_price * (max_distance_pct / 100)
    
    for structure in structures:
        if hasattr(structure, 'high') and hasattr(structure, 'low'):
            # Check if structure is within distance
            distance_to_high = abs(structure.high - current_price)
            distance_to_low = abs(structure.low - current_price)
            min_distance = min(distance_to_high, distance_to_low)
            
            if min_distance <= max_distance:
                active.append(structure)
    
    return active

def analyze_market_structure(df):
    """Analyze market structure (trend, swing points, etc.)"""
    if len(df) < 20:
        return {'trend': 'unknown', 'strength': 0}
    
    # Simple trend analysis using moving averages
    short_ma = df['close'].rolling(10).mean().iloc[-1]
    long_ma = df['close'].rolling(20).mean().iloc[-1]
    current_price = df['close'].iloc[-1]
    
    if current_price > short_ma > long_ma:
        trend = 'bullish'
        strength = min((current_price - long_ma) / long_ma * 10, 1.0)
    elif current_price < short_ma < long_ma:
        trend = 'bearish'
        strength = min((long_ma - current_price) / long_ma * 10, 1.0)
    else:
        trend = 'sideways'
        strength = 0.5
    
    return {
        'trend': trend,
        'strength': strength,
        'short_ma': short_ma,
        'long_ma': long_ma
    }

def calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params):
    """Calculate confluence score based on active SMC structures"""
    weights = confluence_params['weights']
    
    # Count active structures
    ob_score = min(len(order_blocks) / 3, 1.0) * weights['order_blocks']
    fvg_score = min(len(fvgs) / 3, 1.0) * weights['fvg']
    liq_score = min(len(liquidity_zones) / 3, 1.0) * weights['liquidity_zones']
    structure_score = market_structure['strength'] * weights['market_structure']
    
    total_score = ob_score + fvg_score + liq_score + structure_score
    
    active_factors = sum([
        len(order_blocks) > 0,
        len(fvgs) > 0,
        len(liquidity_zones) > 0,
        market_structure['strength'] > 0.3
    ])
    
    return {
        'total_score': total_score,
        'active_factors': active_factors,
        'breakdown': {
            'order_blocks': ob_score,
            'fvgs': fvg_score,
            'liquidity_zones': liq_score,
            'market_structure': structure_score
        }
    }

def display_smc_results(results, symbol, interval, df):
    """Display comprehensive SMC analysis results"""

    st.success("🧠 SMC Analysis Complete!")

    # Header metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "💰 Current Price",
            f"{results['current_price']:,.2f} EGP",
            delta=f"{((results['current_price'] - df['close'].iloc[-2]) / df['close'].iloc[-2] * 100):.2f}%"
        )

    with col2:
        trend = results['market_structure']['trend']
        trend_emoji = "📈" if trend == "bullish" else "📉" if trend == "bearish" else "➡️"
        st.metric(
            "📊 Market Structure",
            f"{trend_emoji} {trend.upper()}",
            delta=f"{results['market_structure']['strength']:.1%} strength"
        )

    with col3:
        confluence = results['confluence']
        strength_emoji = "🟢" if confluence['total_score'] >= 0.7 else "🟡" if confluence['total_score'] >= 0.4 else "🔴"
        st.metric(
            "⚡ Confluence Score",
            f"{strength_emoji} {confluence['total_score']:.1%}",
            delta=f"{confluence['active_factors']}/4 factors"
        )

    with col4:
        total_structures = len(results['order_blocks']) + len(results['fvgs']) + len(results['liquidity_zones'])
        st.metric(
            "🎯 Active Structures",
            f"{total_structures}",
            delta=f"{interval} timeframe"
        )

    # SMC Chart with Interactive Controls
    st.subheader("📊 SMC Chart Analysis")

    # Chart control options
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        show_order_blocks = st.checkbox("🔲 Order Blocks", value=True, help="Show/hide order block zones")

    with col2:
        show_fvgs = st.checkbox("⚡ Fair Value Gaps", value=True, help="Show/hide fair value gaps")

    with col3:
        show_liquidity = st.checkbox("💧 Liquidity Zones", value=True, help="Show/hide liquidity zones")

    with col4:
        chart_height = st.selectbox("📏 Chart Height", [600, 700, 800, 900], index=1, help="Adjust chart height")

    # Create chart with user preferences
    smc_chart = create_smc_chart(
        df, results, symbol,
        show_order_blocks=show_order_blocks,
        show_fvgs=show_fvgs,
        show_liquidity=show_liquidity,
        chart_height=chart_height
    )

    # Display chart with enhanced container
    st.plotly_chart(smc_chart, use_container_width=True, config={
        'displayModeBar': True,
        'displaylogo': False,
        'modeBarButtonsToRemove': ['select2d', 'lasso2d'],
        'toImageButtonOptions': {
            'format': 'png',
            'filename': f'{symbol}_SMC_Analysis',
            'height': chart_height,
            'width': 1200,
            'scale': 2
        }
    })

    # SMC Structures Analysis
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("🔲 Order Blocks")
        display_order_blocks(results['order_blocks'])

    with col2:
        st.subheader("⚡ Fair Value Gaps")
        display_fvgs(results['fvgs'])

    with col3:
        st.subheader("💧 Liquidity Zones")
        display_liquidity_zones(results['liquidity_zones'])

    # Trading Signals
    st.subheader("🎯 SMC Trading Signals")
    display_trading_signals(results)

    # Risk Management
    st.subheader("⚠️ Risk Management")
    display_risk_management(results)

def create_smc_chart(df, results, symbol, show_order_blocks=True, show_fvgs=True, show_liquidity=True, chart_height=700):
    """Create professional SMC chart with enhanced styling and smart positioning"""

    fig = go.Figure()

    # Professional candlestick chart with better colors
    fig.add_trace(go.Candlestick(
        x=df.index,
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close'],
        name=f"{symbol}",
        increasing_line_color='#00D4AA',  # Professional green
        decreasing_line_color='#FF6B6B',  # Professional red
        increasing_fillcolor='#00D4AA',
        decreasing_fillcolor='#FF6B6B',
        line=dict(width=1),
        showlegend=False
    ))

    # Professional color scheme for SMC structures
    smc_colors = {
        'bullish_ob': '#4CAF50',      # Green for bullish order blocks
        'bearish_ob': '#F44336',      # Red for bearish order blocks
        'bullish_fvg': '#2196F3',     # Blue for bullish FVG
        'bearish_fvg': '#FF9800',     # Orange for bearish FVG
        'liquidity': '#9C27B0',       # Purple for liquidity zones
        'current_price': '#FFFFFF'    # White for current price
    }

    # Smart positioning for annotations to avoid overlap
    price_range = df['high'].max() - df['low'].min()
    annotation_offset = price_range * 0.02  # 2% of price range

    # Track annotation positions to avoid overlap
    used_positions = []

    def get_smart_position(price_level, preferred_position="top left"):
        """Get smart annotation position to avoid overlap"""
        positions = ["top left", "top right", "bottom left", "bottom right", "middle left", "middle right"]

        for pos in [preferred_position] + positions:
            position_key = f"{pos}_{int(price_level/annotation_offset)}"
            if position_key not in used_positions:
                used_positions.append(position_key)
                return pos
        return preferred_position  # Fallback

    # Add Order Blocks with professional styling (if enabled)
    if show_order_blocks:
        for i, ob in enumerate(results['order_blocks'][:3]):  # Show top 3 to reduce clutter
            color = smc_colors['bullish_ob'] if ob.block_type == 'bullish' else smc_colors['bearish_ob']
            opacity = 0.25 if not ob.tested else 0.15

            # Add rectangle with border
            fig.add_shape(
                type="rect",
                x0=df.index[0],
                y0=ob.low,
                x1=df.index[-1],
                y1=ob.high,
                fillcolor=color,
                opacity=opacity,
                line=dict(color=color, width=1, dash="solid"),
                layer="below"
            )

            # Add clean annotation
            mid_price = (ob.high + ob.low) / 2
            position = get_smart_position(mid_price, "middle left" if ob.block_type == 'bullish' else "middle right")

            fig.add_annotation(
                x=df.index[len(df.index)//4],  # Position at 25% of chart
                y=mid_price,
                text=f"<b>OB-{i+1}</b><br>{ob.block_type.upper()}<br>{ob.strength:.0%}",
                showarrow=True,
                arrowhead=2,
                arrowsize=1,
                arrowwidth=1,
                arrowcolor=color,
                ax=20 if 'right' in position else -20,
                ay=0,
                bgcolor="rgba(0,0,0,0.8)",
                bordercolor=color,
                borderwidth=1,
                font=dict(color="white", size=10),
                align="center"
            )

    # Add Fair Value Gaps with professional styling (if enabled)
    if show_fvgs:
        for i, fvg in enumerate(results['fvgs'][:3]):  # Show top 3 to reduce clutter
            color = smc_colors['bullish_fvg'] if fvg.gap_type == 'bullish' else smc_colors['bearish_fvg']
            opacity = 0.2 if not fvg.filled else 0.1

            # Add rectangle with dashed border
            fig.add_shape(
                type="rect",
                x0=df.index[0],
                y0=fvg.low,
                x1=df.index[-1],
                y1=fvg.high,
                fillcolor=color,
                opacity=opacity,
                line=dict(color=color, width=1, dash="dash"),
                layer="below"
            )

            # Add clean annotation
            mid_price = (fvg.high + fvg.low) / 2
            position = get_smart_position(mid_price, "middle right" if fvg.gap_type == 'bullish' else "middle left")

            fig.add_annotation(
                x=df.index[len(df.index)*3//4],  # Position at 75% of chart
                y=mid_price,
                text=f"<b>FVG-{i+1}</b><br>{fvg.gap_type.upper()}<br>{fvg.strength:.0%}",
                showarrow=True,
                arrowhead=2,
                arrowsize=1,
                arrowwidth=1,
                arrowcolor=color,
                ax=-20 if 'right' in position else 20,
                ay=0,
                bgcolor="rgba(0,0,0,0.8)",
                bordercolor=color,
                borderwidth=1,
                font=dict(color="white", size=10),
                align="center"
            )

    # Add Liquidity Zones as clean lines (if enabled)
    if show_liquidity:
        for i, lz in enumerate(results['liquidity_zones'][:2]):  # Show top 2 to reduce clutter
            level_price = (lz.high + lz.low) / 2
            color = smc_colors['liquidity']

            fig.add_hline(
                y=level_price,
                line_dash="dot",
                line_color=color,
                line_width=2,
                annotation=dict(
                    text=f"<b>LZ-{i+1}</b> {lz.zone_type.upper()}",
                    font=dict(color=color, size=11),
                    bgcolor="rgba(0,0,0,0.7)",
                    bordercolor=color,
                    borderwidth=1
                ),
                annotation_position="bottom right" if i % 2 == 0 else "top right"
            )

    # Add current price line with professional styling
    fig.add_hline(
        y=results['current_price'],
        line_color=smc_colors['current_price'],
        line_width=3,
        line_dash="solid",
        annotation=dict(
            text=f"<b>Current Price</b><br>{results['current_price']:,.2f} EGP",
            font=dict(color="white", size=12, family="Arial Black"),
            bgcolor="rgba(0,0,0,0.9)",
            bordercolor="white",
            borderwidth=2
        ),
        annotation_position="top left"
    )

    # Professional layout with enhanced styling
    fig.update_layout(
        title=dict(
            text=f"<b>🧠 {symbol} - Smart Money Concepts Analysis</b>",
            font=dict(size=18, color="white", family="Arial Black"),
            x=0.5,
            xanchor="center"
        ),
        xaxis=dict(
            title="<b>Time Period</b>",
            titlefont=dict(size=14, color="white"),
            gridcolor="rgba(128,128,128,0.2)",
            showgrid=True,
            zeroline=False,
            tickfont=dict(color="white")
        ),
        yaxis=dict(
            title="<b>Price (EGP)</b>",
            titlefont=dict(size=14, color="white"),
            gridcolor="rgba(128,128,128,0.2)",
            showgrid=True,
            zeroline=False,
            tickfont=dict(color="white"),
            tickformat=",.0f"  # Format with commas
        ),
        height=chart_height,  # User-configurable height
        showlegend=False,  # Hide legend to reduce clutter
        template="plotly_dark",
        plot_bgcolor="rgba(17,17,17,1)",  # Dark background
        paper_bgcolor="rgba(17,17,17,1)",
        margin=dict(l=80, r=80, t=80, b=80),  # Better margins
        hovermode="x unified",  # Better hover experience
        dragmode="pan"  # Enable panning
    )

    # Enhanced hover template for candlesticks
    fig.update_traces(
        hovertemplate="<b>%{fullData.name}</b><br>" +
                      "Open: %{open:,.2f}<br>" +
                      "High: %{high:,.2f}<br>" +
                      "Low: %{low:,.2f}<br>" +
                      "Close: %{close:,.2f}<br>" +
                      "<extra></extra>",
        selector=dict(type="candlestick")
    )

    return fig

def display_order_blocks(order_blocks):
    """Display order blocks information in table format"""

    if not order_blocks:
        st.info("No active order blocks detected")
        return

    # Create table data
    table_data = []
    for i, ob in enumerate(order_blocks[:5]):
        status_emoji = "🟢" if not ob.broken else "🔴" if ob.broken else "🟡" if ob.tested else "⚪"
        status_text = "BROKEN" if ob.broken else "TESTED" if ob.tested else "ACTIVE"

        table_data.append({
            "Level": f"OB-{i+1}",
            "Type": ob.block_type.upper(),
            "High": f"{ob.high:,.2f}",
            "Low": f"{ob.low:,.2f}",
            "Strength": f"{ob.strength:.1%}",
            "Status": f"{status_emoji} {status_text}"
        })

    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Type": st.column_config.TextColumn("Type", width="small"),
                "High": st.column_config.TextColumn("High (EGP)", width="medium"),
                "Low": st.column_config.TextColumn("Low (EGP)", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="small"),
                "Status": st.column_config.TextColumn("Status", width="medium")
            }
        )

def display_fvgs(fvgs):
    """Display Fair Value Gaps information in table format"""

    if not fvgs:
        st.info("No active fair value gaps detected")
        return

    # Create table data
    table_data = []
    for i, fvg in enumerate(fvgs[:5]):
        status_emoji = "🟢" if not fvg.filled else "🟡" if fvg.partially_filled else "🔴"
        status_text = "FILLED" if fvg.filled else "PARTIAL" if fvg.partially_filled else "OPEN"

        table_data.append({
            "Level": f"FVG-{i+1}",
            "Type": fvg.gap_type.upper(),
            "High": f"{fvg.high:,.2f}",
            "Low": f"{fvg.low:,.2f}",
            "Strength": f"{fvg.strength:.1%}",
            "Status": f"{status_emoji} {status_text}"
        })

    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Type": st.column_config.TextColumn("Type", width="small"),
                "High": st.column_config.TextColumn("High (EGP)", width="medium"),
                "Low": st.column_config.TextColumn("Low (EGP)", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="small"),
                "Status": st.column_config.TextColumn("Status", width="medium")
            }
        )

def display_liquidity_zones(liquidity_zones):
    """Display liquidity zones information in table format"""

    if not liquidity_zones:
        st.info("No active liquidity zones detected")
        return

    # Create table data
    table_data = []
    for i, lz in enumerate(liquidity_zones[:5]):
        status_emoji = "🟢" if not lz.swept else "🔴"
        status_text = "SWEPT" if lz.swept else "ACTIVE"
        level_price = (lz.high + lz.low) / 2

        table_data.append({
            "Level": f"LZ-{i+1}",
            "Type": lz.zone_type.upper().replace('_', ' '),
            "Price": f"{level_price:,.2f}",
            "Range": f"{lz.low:,.2f} - {lz.high:,.2f}",
            "Strength": f"{lz.strength:.1%}",
            "Status": f"{status_emoji} {status_text}"
        })

    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Type": st.column_config.TextColumn("Type", width="medium"),
                "Price": st.column_config.TextColumn("Price (EGP)", width="medium"),
                "Range": st.column_config.TextColumn("Range (EGP)", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="small"),
                "Status": st.column_config.TextColumn("Status", width="medium")
            }
        )

def display_trading_signals(results):
    """Display trading signals based on SMC analysis"""

    confluence = results['confluence']
    trend = results['market_structure']['trend']

    if confluence['total_score'] >= 0.6:
        if trend == 'bullish':
            st.success("🟢 **STRONG BULLISH SIGNAL** - High probability LONG setup")
            st.markdown("**Strategy:** Look for entries near order blocks or FVG support")
        elif trend == 'bearish':
            st.error("🔴 **STRONG BEARISH SIGNAL** - High probability SHORT setup")
            st.markdown("**Strategy:** Look for entries near order blocks or FVG resistance")
        else:
            st.warning("🟡 **CONSOLIDATION** - Wait for directional break")
    elif confluence['total_score'] >= 0.4:
        st.info("🔵 **MODERATE SIGNAL** - Proceed with caution")
        st.markdown("**Strategy:** Wait for additional confirmation")
    else:
        st.info("⚪ **NO CLEAR SIGNAL** - Avoid trading, wait for better setup")

    # Confluence breakdown
    with st.expander("📊 Confluence Breakdown"):
        breakdown = confluence['breakdown']
        st.markdown(f"""
        - **Order Blocks:** {breakdown['order_blocks']:.1%}
        - **Fair Value Gaps:** {breakdown['fvgs']:.1%}
        - **Liquidity Zones:** {breakdown['liquidity_zones']:.1%}
        - **Market Structure:** {breakdown['market_structure']:.1%}

        **Total Score:** {confluence['total_score']:.1%}
        """)

def display_risk_management(results):
    """Display risk management recommendations"""

    current_price = results['current_price']

    # Find nearest order block for stop loss
    nearest_ob = None
    min_distance = float('inf')

    for ob in results['order_blocks']:
        distance = abs(ob.low - current_price) if ob.block_type == 'bullish' else abs(ob.high - current_price)
        if distance < min_distance:
            min_distance = distance
            nearest_ob = ob

    if nearest_ob:
        if results['market_structure']['trend'] == 'bullish':
            stop_loss = nearest_ob.low * 0.99  # 1% below order block
            take_profit = current_price + (current_price - stop_loss) * 2  # 1:2 RR
        else:
            stop_loss = nearest_ob.high * 1.01  # 1% above order block
            take_profit = current_price - (stop_loss - current_price) * 2  # 1:2 RR

        risk_amount = abs(current_price - stop_loss)
        risk_pct = (risk_amount / current_price) * 100

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("🛑 Stop Loss", f"{stop_loss:,.2f} EGP", delta=f"-{risk_pct:.1f}%")

        with col2:
            st.metric("🎯 Take Profit", f"{take_profit:,.2f} EGP", delta="1:2 RR")

        with col3:
            st.metric("⚠️ Risk per Share", f"{risk_amount:,.2f} EGP", delta=f"{risk_pct:.1f}%")

    else:
        st.info("💡 No clear order blocks for stop loss placement. Use ATR-based stops.")
