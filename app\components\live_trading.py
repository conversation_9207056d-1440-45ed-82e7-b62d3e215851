import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go
import os
import sys
import logging

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import application configuration
from app.config import (
    STOCK_DATA_DIR, MODELS_DIR, DEFAULT_PREDICTION_HORIZONS,
    DEFAULT_SEQUENCE_LENGTH, MODEL_DISPLAY_NAMES
)

# Import utility functions
from app.utils.common import (
    load_stock_data, get_available_stocks,
    format_prediction_result, get_model_display_name
)
from app.utils.error_handling import handle_errors, log_execution_time

# Import necessary functions
from scrapers.price_scraper import PriceScraper
from models.train import train_from_csv, USING_TENSORFLOW
from models.predict import predict_future_prices, predict_from_live_data
from models.ensemble_predict import get_ensemble_predictions
from models.enhanced_ensemble_predict import get_enhanced_ensemble_predictions
from app.utils.data_processing import is_model_trained, save_scaler, load_scaler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@handle_errors(fallback_return=None)
def make_prediction_with_error_handling(historical_data, live_data, symbol, horizons, model_type,
                                      use_enhanced=False, status_placeholder=None, info_placeholder=None):
    """
    Make predictions with standardized error handling.

    Args:
        historical_data (pd.DataFrame): Historical stock data
        live_data (pd.DataFrame): Live stock data (can be None)
        symbol (str): Stock symbol
        horizons (list): List of prediction horizons
        model_type (str): Type of model to use
        use_enhanced (bool): Whether to use enhanced ensemble model
        status_placeholder: Streamlit placeholder for status updates
        info_placeholder: Streamlit placeholder for info messages

    Returns:
        dict: Dictionary with predictions for each horizon
    """
    try:
        # Update status
        if status_placeholder:
            status_placeholder.info(f"Generating predictions using {get_model_display_name(model_type)}...")

        # Check if we're using ensemble model
        is_ensemble = model_type.lower() == 'ensemble'

        # Make predictions based on model type
        if is_ensemble:
            if use_enhanced:
                # Use enhanced ensemble model
                if status_placeholder:
                    status_placeholder.info("Using enhanced ensemble model with adaptive weighting...")

                predictions = get_enhanced_ensemble_predictions(
                    historical_data,
                    live_data,
                    symbol,
                    horizons=horizons,
                    models_path=MODELS_DIR
                )
            else:
                # Use standard ensemble model
                if status_placeholder:
                    status_placeholder.info("Using standard ensemble model...")

                predictions = get_ensemble_predictions(
                    historical_data,
                    live_data,
                    symbol,
                    horizons=horizons,
                    models_path=MODELS_DIR
                )
        else:
            # Use standard prediction methods
            if live_data is not None and not live_data.empty:
                # Make predictions using live data
                predictions = predict_from_live_data(
                    live_data,
                    historical_data,
                    symbol,
                    horizons=horizons,
                    model_type=model_type,
                    models_path=MODELS_DIR
                )
            else:
                # Make predictions using only historical data
                predictions = predict_future_prices(
                    historical_data,
                    symbol,
                    horizons=horizons,
                    model_type=model_type,
                    models_path=MODELS_DIR
                )

        # Update status
        if status_placeholder:
            status_placeholder.success("Predictions generated successfully")

        return predictions

    except Exception as e:
        # Log the error
        logger.error(f"Error making predictions for {symbol}: {str(e)}")

        # Update the status placeholders
        if status_placeholder:
            status_placeholder.error(f"Error making predictions: {str(e)}")

        if info_placeholder:
            info_placeholder.info("Try training the model again or selecting a different model type.")

        return None

@handle_errors(fallback_return=None)
def train_model_with_error_handling(file_path, symbol, horizon, model_type, epochs, batch_size, status_placeholder):
    """
    Train a model with standardized error handling.

    Args:
        file_path (str): Path to the CSV file with stock data
        symbol (str): Stock symbol
        horizon (int): Prediction horizon
        model_type (str): Type of model to train
        epochs (int): Number of training epochs
        batch_size (int): Batch size for training
        status_placeholder: Streamlit placeholder for status updates

    Returns:
        bool: True if training was successful, False otherwise
    """
    try:
        # Train the model
        train_from_csv(
            file_path,
            symbol,
            horizons=[horizon],  # Train one horizon at a time
            model_type=model_type,
            epochs=epochs,
            batch_size=batch_size,
            save_path=MODELS_DIR
        )
        return True
    except Exception as e:
        # Log the error
        logger.error(f"Error training model for {symbol} with horizon {horizon}: {str(e)}")

        # Update the status placeholder
        if status_placeholder:
            status_placeholder.error(f"Error training model for horizon {horizon}: {str(e)}")

        return False

@log_execution_time
def live_trading_component():
    """
    Integrated component that combines live data fetching, model training, and prediction
    """
    st.header("Live Trading Dashboard")

    # Check if we have historical data and symbol
    if 'historical_data' not in st.session_state or st.session_state.historical_data is None:
        # Try to load default or sample stock data if available
        try:
            available_stocks = get_available_stocks()
            if available_stocks:
                sample_symbol = available_stocks[0]
                sample_data = load_stock_data(sample_symbol)
                if sample_data is not None:
                    st.session_state.symbol = sample_symbol
                    st.session_state.historical_data = sample_data
                    st.success(f"Automatically loaded sample data for {sample_symbol}")
                else:
                    st.warning("Please upload historical data first")
                    st.info("Go to the 'Upload Data' page to upload stock data, or select a stock from the sidebar.")
                    return
            else:
                st.warning("Please upload historical data first")
                st.info("Go to the 'Upload Data' page to upload stock data, or select a stock from the sidebar.")
                return
        except Exception as e:
            logger.error(f"Error loading sample data: {str(e)}")
            st.warning("Please upload historical data first")
            st.info("Go to the 'Upload Data' page to upload stock data, or select a stock from the sidebar.")
            return

    if 'symbol' not in st.session_state or st.session_state.symbol is None:
        st.warning("Please select a stock symbol first")
        st.info("Go to the 'Select Stock' page to choose a stock, or select one from the sidebar.")
        return

    # Get the current stock and data
    symbol = st.session_state.symbol
    historical_data = st.session_state.historical_data

    # Create tabs for different sections
    tab1, tab2, tab3 = st.tabs(["Live Data", "Train Model", "Predictions"])

    # Tab 1: Live Data
    with tab1:
        st.subheader("Live Price Data")
        st.info(f"Selected stock: {symbol}")

        # Display date range of historical data
        st.write(f"Historical data range: {historical_data['Date'].min().strftime('%Y-%m-%d')} to {historical_data['Date'].max().strftime('%Y-%m-%d')}")

        # Scraper source selection
        source = st.selectbox(
            "Select data source",
            options=["TradingView", "Mubasher"],
            index=0,
            key="live_trading_source"
        )

        # Real-time data option
        use_real_time = st.checkbox("Use real-time data (requires TradingView paid account)",
                                   value=False,
                                   key="live_trading_real_time")

        # If real-time is selected, show credential inputs
        username = None
        password = None
        is_gmail = False
        if use_real_time:
            st.markdown("### TradingView Authentication")
            st.markdown("""
            ℹ️ **Real-time data requires TradingView credentials**

            **Important Notes:**
            - For standard TradingView accounts, enter your **username** (not email)
            - TradingView occasionally requires verification steps. If prompted, complete these steps in the browser window
            - The browser window must remain open during the entire session
            """)

            # Default to standard TradingView account (not Google)
            account_type = st.radio(
                "TradingView Account Type",
                ["Standard TradingView Account", "Google Account"],
                index=0,  # Make Standard the default
                help="Select your TradingView account type. Standard accounts use email/password login directly with TradingView."
            )
            is_gmail = account_type == "Google Account"

            if is_gmail:
                st.warning("⚠️ Google authentication often requires additional security verification steps and may be less reliable.")
                st.info("Consider using a standard TradingView account for more reliable access.")
            else:
                st.success("✅ Standard TradingView accounts typically provide more reliable automated login.")

            username = st.text_input(
                "TradingView Username" if not is_gmail else "Google Email",
                help="Enter your TradingView username (not email) for standard accounts, or email for Google accounts."
            )

            password = st.text_input(
                "TradingView Password",
                type="password",
                help="Your password will not be stored or logged."
            )

            # Add more detailed notes about the login process
            st.info("""
            ℹ️ **Login Process Information:**

            1. A browser window will open to log you in automatically
            2. If automatic login fails, you'll see instructions for manual login
            3. You may need to complete CAPTCHA or verification steps if prompted
            4. The app will continue once login is successful

            The browser window must remain open during the entire session to maintain your login.
            """)

        # Create a placeholder for the scraper to avoid reinitializing it every time
        if 'scraper' not in st.session_state:
            st.session_state.scraper = PriceScraper(
                source=source.lower(),
                use_real_time=use_real_time and username and password,
                username=username,
                password=password,
                is_gmail=is_gmail
            )
        elif (st.session_state.scraper.source.lower() != source.lower() or
               st.session_state.scraper.use_real_time != (use_real_time and username and password) or
               st.session_state.scraper.username != username or
               st.session_state.scraper.password != password or
               st.session_state.scraper.is_gmail != is_gmail):
            # Only reinitialize if settings have changed
            # First, clean up the old scraper if it exists
            try:
                # Use the new close_driver method for proper cleanup
                st.session_state.scraper.close_driver()
                logger.info("Successfully closed previous WebDriver")
            except Exception as e:
                logger.error(f"Error closing previous WebDriver: {str(e)}")

            # Create a new scraper
            st.session_state.scraper = PriceScraper(
                source=source.lower(),
                use_real_time=use_real_time and username and password,
                username=username,
                password=password,
                is_gmail=is_gmail
            )

        scraper = st.session_state.scraper

        # Make sure driver is initialized if real-time is requested
        if use_real_time and username and password and (scraper.driver is None):
            try:
                scraper.initialize_driver()
                if scraper.driver is not None:
                    if is_gmail:
                        scraper.login_to_tradingview_via_google()
                    else:
                        scraper.login_to_tradingview()
            except Exception as e:
                st.error(f"Error initializing browser: {str(e)}")
                st.info("Falling back to delayed/sample data.")

        # Create placeholders for live data display
        live_data_status = st.empty()
        live_data_info = st.empty()
        live_data_display = st.empty()

        # Get live data
        if st.button("Fetch Live Price", key="live_trading_fetch"):
            try:
                with st.spinner("Fetching live price..."):
                    # Use cache by default to improve performance
                    price_data = scraper.get_price(symbol, use_cache=True)

                    if price_data:
                        # Convert scraper data to prediction-compatible format
                        current_time = datetime.now()
                        price = price_data.get('price', 0)

                        # Create DataFrame with required columns for predictions
                        live_df = pd.DataFrame([{
                            'Date': current_time,
                            'Open': price,
                            'High': price * 1.001,  # Simulate small high variation
                            'Low': price * 0.999,   # Simulate small low variation
                            'Close': price,
                            'Volume': 1000000,      # Default volume
                            'symbol': price_data.get('symbol', symbol),
                            'currency': price_data.get('currency', 'EGP'),
                            'timestamp': price_data.get('timestamp', current_time.isoformat()),
                            'source': price_data.get('source', 'Unknown'),
                            'real_time': price_data.get('real_time', False)
                        }])

                        # Check if this is sample data
                        is_sample = price_data.get('source', '').lower() == 'sample data'
                        is_real_time = price_data.get('real_time', False)

                        if is_sample:
                            live_data_status.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                            live_data_info.info("The sample price is based on historical data with a small random variation to simulate live data.")
                        elif is_real_time:
                            # Create a more prominent real-time indicator
                            live_data_status.markdown("""
                            <div style="background-color: #0f5132; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                                <h3 style="color: white; margin: 0;">🔴 REAL-TIME DATA ACTIVE</h3>
                            </div>
                            """, unsafe_allow_html=True)
                            live_data_info.success(f"✅ Real-time price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                            live_data_info.info("This is real-time data from your TradingView paid account.")
                        else:
                            # Create a delayed data indicator
                            live_data_status.markdown("""
                            <div style="background-color: #084298; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                                <h3 style="color: white; margin: 0;">⏱️ DELAYED DATA (15-minute delay)</h3>
                            </div>
                            """, unsafe_allow_html=True)
                            live_data_info.success(f"Live price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                            live_data_info.info("This is delayed data with a 15-minute delay from the market.")

                        # Display live data
                        live_data_display.dataframe(live_df)

                        # Store in session state
                        if 'live_data' not in st.session_state:
                            st.session_state.live_data = live_df
                        else:
                            st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)
                    else:
                        live_data_status.error(f"Failed to fetch live price for {symbol}")
                        live_data_info.info("This could be due to network issues or the stock symbol not being available on the selected source.")

            except Exception as e:
                live_data_status.error(f"Error fetching live price: {str(e)}")
                logger.error(f"Error fetching live price: {str(e)}")
                live_data_info.info("Trying to generate sample price data based on historical data...")

                # Try to generate sample data as a fallback
                try:
                    sample_data = scraper._generate_sample_price(symbol)
                    if sample_data:
                        # Convert sample data to prediction-compatible format
                        current_time = datetime.now()
                        price = sample_data.get('price', 0)

                        live_df = pd.DataFrame([{
                            'Date': current_time,
                            'Open': price,
                            'High': price * 1.001,  # Simulate small high variation
                            'Low': price * 0.999,   # Simulate small low variation
                            'Close': price,
                            'Volume': 1000000,      # Default volume
                            'symbol': sample_data.get('symbol', symbol),
                            'currency': sample_data.get('currency', 'EGP'),
                            'timestamp': sample_data.get('timestamp', current_time.isoformat()),
                            'source': sample_data.get('source', 'Sample Data'),
                            'real_time': sample_data.get('real_time', False)
                        }])

                        live_data_status.warning("⚠️ Using sample price data due to error fetching live data.")
                        live_data_display.dataframe(live_df)

                        # Store in session state
                        if 'live_data' not in st.session_state:
                            st.session_state.live_data = live_df
                        else:
                            st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)
                except Exception as e2:
                    logger.error(f"Error generating sample data: {str(e2)}")

        # Display existing live data if available
        if 'live_data' in st.session_state and st.session_state.live_data is not None:
            st.subheader("Current Live Data")
            st.dataframe(st.session_state.live_data)

    # Tab 2: Train Model
    with tab2:
        st.subheader("Train AI Model")

        # Add a note about the importance of training before prediction
        st.info("⚠️ **Important**: You must train models before making predictions. Each prediction horizon requires a separate trained model.")

        # Create two columns for model selection
        col1, col2 = st.columns([1, 2])

        with col1:
            # Model category selection
            model_category = st.radio(
                "Select model category",
                options=["Machine Learning", "Deep Learning", "Hybrid/Ensemble"],
                index=0,
                key="live_trading_model_category"
            )

        with col2:
            # Model type selection based on category
            if model_category == "Machine Learning":
                model_options = ["rf", "gb", "lr", "svr"]

                # Check if XGBoost is available
                try:
                    # Just check if we can import it
                    __import__("xgboost")
                    model_options.insert(2, "xgb")
                except ImportError:
                    # XGBoost not available, continue without it
                    pass

                # Check if Prophet is available
                try:
                    __import__("prophet")
                    model_options.append("prophet")
                except ImportError:
                    # Prophet not available, continue without it
                    pass

                model_type = st.selectbox(
                    "Select model type",
                    options=model_options,
                    index=0,
                    key="live_trading_model_type_ml"
                )

                # No epochs or batch size for scikit-learn models
                epochs = 1  # Not used for scikit-learn
                batch_size = 32  # Not used for scikit-learn

                # Show model descriptions
                if model_type == "rf":
                    st.info("Random Forest: An ensemble learning method that builds multiple decision trees and merges their predictions.")
                elif model_type == "gb":
                    st.info("Gradient Boosting: A technique that builds models sequentially, with each new model correcting errors from previous ones.")
                elif model_type == "xgb":
                    st.info("XGBoost: An optimized gradient boosting library designed for speed and performance.")
                elif model_type == "lr":
                    st.info("Linear Regression: A simple approach that models the relationship between variables using a linear equation.")
                elif model_type == "svr":
                    st.info("Support Vector Regression: Uses support vector machines to predict continuous variables.")
                elif model_type == "prophet":
                    st.info("Prophet: Facebook's time series forecasting tool designed for business forecasting with strong seasonality patterns.")

            elif model_category == "Deep Learning":
                if USING_TENSORFLOW:
                    model_options = ["lstm", "bilstm"]

                    # Add transformer if available
                    try:
                        from models.transformer_model import StockTransformerModel
                        model_options.append("transformer")
                    except ImportError:
                        # Transformer not available
                        pass

                    model_type = st.selectbox(
                        "Select model type",
                        options=model_options,
                        index=0,
                        key="live_trading_model_type_dl"
                    )

                    # Training parameters specific to TensorFlow
                    epochs = st.slider("Number of epochs", min_value=10, max_value=200, value=50, step=10, key="live_trading_epochs")
                    batch_size = st.slider("Batch size", min_value=16, max_value=128, value=32, step=16, key="live_trading_batch_size")

                    # Show model descriptions
                    if model_type == "lstm":
                        st.info("LSTM (Long Short-Term Memory): A type of recurrent neural network capable of learning long-term dependencies in time series data.")
                    elif model_type == "bilstm":
                        st.info("BiLSTM (Bidirectional LSTM): Processes data in both forward and backward directions to capture more context.")
                    elif model_type == "transformer":
                        st.info("Transformer: Uses attention mechanisms to capture long-range dependencies in time series data. Effective for complex patterns but requires more training data.")
                else:
                    st.warning("TensorFlow is not available. Deep Learning models cannot be used.")
                    st.info("Falling back to Random Forest model.")
                    model_type = "rf"
                    epochs = 1
                    batch_size = 32
            else:  # Hybrid/Ensemble
                model_options = ["hybrid", "ensemble"]

                # Check if enhanced ensemble is available
                try:
                    from models.enhanced_ensemble_predict import get_enhanced_ensemble_predictions
                    model_options.append("enhanced_ensemble")
                except ImportError:
                    # Enhanced ensemble not available
                    pass

                model_type = st.selectbox(
                    "Select model type",
                    options=model_options,
                    index=0,
                    key="live_trading_model_type_hybrid"
                )

                # No epochs or batch size for hybrid/ensemble models
                epochs = 1
                batch_size = 32

                # Show model descriptions
                if model_type == "hybrid":
                    st.info("Hybrid Model: Combines ARIMA for time series patterns with machine learning for feature-based prediction.")
                elif model_type == "ensemble":
                    st.info("Ensemble Model: Combines predictions from multiple models (RF, GB, LR, and XGBoost if available).")
                elif model_type == "enhanced_ensemble":
                    st.info("Enhanced Ensemble: An advanced ensemble that uses adaptive weighting based on recent performance of each model.")

            st.markdown("""
            **Model types:**
            - rf: Random Forest Regressor
            - gb: Gradient Boosting Regressor
            - lr: Linear Regression
            - prophet: Facebook Prophet Time Series
            """)

            if "xgb" in model_options:
                st.markdown("- xgb: XGBoost Regressor")

            st.markdown("""
            **Hybrid models:**
            - hybrid: ARIMA + ML Hybrid Model
            - ensemble: Ensemble of multiple models
            """)

            # Show additional options for hybrid models
            if model_type == "hybrid":
                st.info("The hybrid model combines ARIMA for time series patterns with machine learning for feature-based prediction.")
            elif model_type == "ensemble":
                st.info("The ensemble model combines predictions from multiple models (RF, GB, LR, and XGBoost if available).")
            elif model_type == "prophet":
                st.info("The Prophet model is designed for time series forecasting with strong seasonality patterns and is developed by Facebook.")

            # No epochs or batch size for scikit-learn models
            epochs = 1  # Not used for scikit-learn
            batch_size = 32  # Not used for scikit-learn

        # Prediction horizons
        st.subheader("Select Prediction Horizons")

        # Offer different horizon options
        horizon_options = [
            "Short-term (minutes)",
            "Medium-term (days)",
            "Long-term (weeks)"
        ]
        horizon_type = st.radio("Select prediction timeframe", horizon_options, key="live_trading_horizon_type")

        if horizon_type == "Short-term (minutes)":
            horizons = [4, 25, 30, 69]
            horizon_unit = "minutes"
            # For short-term, we'll use the existing single-point prediction
            num_points = 1
        elif horizon_type == "Medium-term (days)":
            horizons = [1, 2, 3, 5, 7]
            horizon_unit = "days"
            # For medium-term, we'll predict multiple points (one per day)
            num_points = st.slider("Number of prediction points", min_value=1, max_value=7, value=5, key="live_trading_num_points_days")
        else:  # Long-term
            horizons = [1, 2, 4, 8, 12]
            horizon_unit = "weeks"
            # For long-term, we'll predict multiple points (one per week)
            num_points = st.slider("Number of prediction points", min_value=1, max_value=12, value=4, key="live_trading_num_points_weeks")

        selected_horizons = st.multiselect(
            f"Select prediction horizons ({horizon_unit})",
            options=horizons,
            default=horizons,
            key="live_trading_horizons"
        )

        # Store the horizon unit and number of points in session state
        st.session_state.horizon_unit = horizon_unit
        st.session_state.num_prediction_points = num_points

        # Create placeholders for training status
        train_status = st.empty()
        train_info = st.empty()

        # Check if models are already trained
        models_already_trained = []
        for horizon in selected_horizons:
            if is_model_trained(symbol, horizon, model_type.lower(), 'saved_models'):
                models_already_trained.append(horizon)

        if models_already_trained:
            train_info.info(f"Models already trained for horizons: {', '.join(map(str, models_already_trained))}")

        # Train model
        if st.button("Train Model", key="live_trading_train"):
            try:
                # Create a progress bar
                progress_bar = st.progress(0)

                # Save data to disk if needed
                file_path = os.path.join(STOCK_DATA_DIR, f"{symbol}.csv")

                if os.path.exists(file_path):
                    # Show training in progress
                    train_status.info("Training models... This may take a few minutes.")

                    # Train models one by one with progress updates
                    for i, horizon in enumerate(selected_horizons):
                        # Update progress
                        progress = (i / len(selected_horizons)) * 100
                        progress_bar.progress(int(progress))
                        train_status.info(f"Training model for {horizon} {st.session_state.horizon_unit} horizon ({i+1}/{len(selected_horizons)})...")

                        # Convert horizon based on unit
                        model_horizon = horizon
                        if horizon_unit == "days":
                            # For daily data, use the number of days directly
                            # This assumes each row is one day
                            model_horizon = horizon
                        elif horizon_unit == "weeks":
                            # For weekly horizons, convert to days
                            model_horizon = horizon * 7

                        # Check if this is a machine learning model
                        is_ml_model = model_type.lower() in ['rf', 'gb', 'lr', 'svr', 'xgb', 'prophet', 'ensemble', 'hybrid']

                        if is_ml_model:
                            train_status.info(f"Training machine learning model ({model_type}) for horizon {horizon}. This may be quick as ML models don't use epochs like neural networks.")

                        # Train this specific model
                        success = train_model_with_error_handling(
                            file_path,
                            symbol,
                            model_horizon,
                            model_type.lower(),
                            epochs,
                            batch_size,
                            train_status
                        )

                        if success:
                            if is_ml_model:
                                train_status.success(f"Machine learning model ({model_type}) trained successfully for horizon {horizon}")
                            else:
                                train_status.success(f"Deep learning model ({model_type}) trained successfully for horizon {horizon}")

                    # Complete the progress bar
                    progress_bar.progress(100)

                    # Show success message
                    train_status.success("All models trained successfully!")

                    # Store trained horizons in session state for predictions
                    st.session_state.trained_horizons = selected_horizons
                    st.session_state.trained_model_type = model_type.lower()

                    # Add a button to go to the Predictions tab
                    if st.button("Go to Predictions tab", key="goto_predictions"):
                        # This will be handled by JavaScript to click the Predictions tab
                        st.markdown("""
                        <script>
                            // Wait for the DOM to be fully loaded
                            document.addEventListener('DOMContentLoaded', function() {
                                // Find the Predictions tab and click it
                                var tabs = document.querySelectorAll('button[role="tab"]');
                                for (var i = 0; i < tabs.length; i++) {
                                    if (tabs[i].textContent.includes('Predictions')) {
                                        tabs[i].click();
                                        break;
                                    }
                                }
                            });
                        </script>
                        """, unsafe_allow_html=True)
                else:
                    train_status.error(f"File not found: {file_path}")
                    train_info.info("Please make sure the stock data is saved correctly.")

            except Exception as e:
                train_status.error(f"Error training model: {str(e)}")
                logger.error(f"Error training model: {str(e)}")

    # Tab 3: Predictions
    with tab3:
        st.subheader("Price Predictions")

        # Check if we have live data
        has_live_data = 'live_data' in st.session_state and st.session_state.live_data is not None and not st.session_state.live_data.empty
        live_data = st.session_state.live_data if has_live_data else None

        # Get model type from session state or let user select
        if 'trained_model_type' in st.session_state:
            model_type = st.session_state.trained_model_type
            st.info(f"Using trained model type: {model_type}")
        else:
            # Model type selection
            if USING_TENSORFLOW:
                model_type = st.selectbox(
                    "Select model type",
                    options=["lstm", "bilstm", "transformer"],
                    index=0,
                    key="live_trading_pred_model_type_tf"
                )

                # Add info about Transformer model
                if model_type == "transformer":
                    st.info("The Transformer model uses attention mechanisms to capture long-range dependencies in time series data. It can be more effective for complex patterns but requires more training data.")
            else:
                model_options = ["rf", "gb", "lr", "prophet", "hybrid", "ensemble"]

                # Check if XGBoost is available
                try:
                    # Just check if we can import it
                    __import__("xgboost")
                    model_options.insert(4, "xgb")
                except ImportError:
                    # XGBoost not available, continue without it
                    pass

                model_type = st.selectbox(
                    "Select model type",
                    options=model_options,
                    index=0,
                    key="live_trading_pred_model_type_sk"
                )

        # Get horizon unit from session state or let user select
        if 'horizon_unit' in st.session_state:
            horizon_unit = st.session_state.horizon_unit
            st.info(f"Using prediction timeframe: {horizon_unit}")
        else:
            # Offer different horizon options
            horizon_options = [
                "Short-term (minutes)",
                "Medium-term (days)",
                "Long-term (weeks)"
            ]
            horizon_type = st.radio("Select prediction timeframe", horizon_options, key="live_trading_pred_horizon_type")

            if horizon_type == "Short-term (minutes)":
                horizon_unit = "minutes"
                num_points = 1
            elif horizon_type == "Medium-term (days)":
                horizon_unit = "days"
                num_points = st.slider("Number of prediction points", min_value=1, max_value=7, value=5, key="live_trading_pred_num_points_days")
            else:  # Long-term
                horizon_unit = "weeks"
                num_points = st.slider("Number of prediction points", min_value=1, max_value=12, value=4, key="live_trading_pred_num_points_weeks")

            # Store in session state
            st.session_state.horizon_unit = horizon_unit
            st.session_state.num_prediction_points = num_points

        # Get horizons from session state or let user select
        if 'trained_horizons' in st.session_state:
            available_horizons = st.session_state.trained_horizons
        else:
            # Set default horizons based on horizon unit
            if horizon_unit == "minutes":
                available_horizons = [4, 15, 25, 30, 69]
            elif horizon_unit == "days":
                available_horizons = [1, 2, 3, 5, 7]
            else:  # weeks
                available_horizons = [1, 2, 4, 8, 12]

        selected_horizons = st.multiselect(
            f"Select prediction horizons ({horizon_unit})",
            options=available_horizons,
            default=[available_horizons[0]] if available_horizons else [],
            key="live_trading_pred_horizons"
        )

        if not selected_horizons:
            st.warning("Please select at least one prediction horizon")
        else:
            # Check if any models are trained
            models_trained = []
            models_not_trained = []
            for horizon in selected_horizons:
                if is_model_trained(symbol, horizon, model_type, MODELS_DIR, horizon_unit):
                    models_trained.append(horizon)
                else:
                    models_not_trained.append(horizon)

            if not models_trained:
                st.warning("No trained models found for the selected horizons. Please train models first.")
                st.info("Go to the 'Train Model' tab to train models for the selected horizons.")
            elif models_not_trained:
                st.warning(f"Models not trained for horizons: {', '.join(map(str, models_not_trained))}")
                st.info("Only predictions for trained models will be generated.")

            # Create placeholders for prediction status and results
            pred_status = st.empty()
            pred_info = st.empty()
            pred_results = st.empty()

            # Check if we're using the ensemble model
            is_ensemble = model_type.lower() == 'ensemble'

            # For ensemble models, add the enhanced option
            use_enhanced = False
            if is_ensemble:
                use_enhanced = st.checkbox("Use Enhanced Ensemble Model", value=True,
                                         help="Uses an improved ensemble model with adaptive weighting")

            # Make predictions
            if st.button("Generate Predictions", key="live_trading_predict"):
                try:
                    # Create a progress bar
                    progress_bar = st.progress(0)
                    pred_status.info("Generating predictions... This may take a moment.")

                    # Use only horizons with trained models
                    trained_horizons = [h for h in selected_horizons if h in models_trained]

                    # Check if we have any trained models
                    if not trained_horizons:
                        pred_status.error("No trained models found for the selected horizons.")
                        pred_info.info("Please train models first before generating predictions.")
                        return

                    # Update progress
                    progress_bar.progress(25)

                    # Convert horizons to minutes for prediction functions
                    model_horizons = []
                    for h in trained_horizons:
                        if horizon_unit == "days":
                            model_horizons.append(h * 24 * 60)  # days to minutes
                        elif horizon_unit == "weeks":
                            model_horizons.append(h * 7 * 24 * 60)  # weeks to minutes
                        else:  # minutes
                            model_horizons.append(h)

                    # Make predictions using our helper function
                    result = make_prediction_with_error_handling(
                        historical_data=historical_data,
                        live_data=live_data if has_live_data else None,
                        symbol=symbol,
                        horizons=model_horizons,
                        model_type=model_type,
                        use_enhanced=use_enhanced if is_ensemble else False,
                        status_placeholder=pred_status,
                        info_placeholder=pred_info
                    )

                    # Map the predictions back to the original horizons
                    if result is not None and not is_ensemble:
                        horizon_predictions = {}
                        for i, h in enumerate(trained_horizons):
                            horizon_predictions[h] = result[model_horizons[i]]
                        result = horizon_predictions

                    # Update progress
                    progress_bar.progress(75)

                    # Check if prediction was successful
                    if result is None:
                        # Error already displayed by the helper function
                        return

                    # Store the ensemble results for later use with weights display
                    ensemble_results = {}

                    # For ensemble models, extract the ensemble and individual predictions
                    if is_ensemble:
                        # Check if there was an error in the ensemble predictions
                        has_error = any('error' in result[h] for h in trained_horizons)

                        if has_error:
                            # Display a warning instead of an error since we're using fallback predictions
                            pred_info.warning("Using fallback predictions based on current price. The ensemble model is working with realistic values.")

                            # Log the errors for debugging
                            for h in trained_horizons:
                                if 'error' in result[h]:
                                    logger.warning(f"Using fallback for horizon {h}: {result[h]['error']}")

                        # Extract ensemble predictions for compatibility with existing code
                        predictions = {h: result[h]['ensemble'] for h in trained_horizons}

                        # Store individual model predictions in session state for display
                        st.session_state.individual_predictions = {h: result[h]['individual'] for h in trained_horizons}

                        # Store the full ensemble results for later use
                        ensemble_results = result
                    else:
                        # For non-ensemble models, the result is already in the right format
                        predictions = result

                    # Complete the progress bar
                    progress_bar.progress(100)

                    # Display predictions
                    pred_status.success("Predictions generated successfully")

                    # Create a table of predictions
                    pred_data = []
                    current_time = datetime.now()

                    # Get the horizon unit and number of points from session state
                    num_points = st.session_state.num_prediction_points

                    # For medium and long-term predictions, we'll generate multiple points
                    if horizon_unit in ['days', 'weeks']:
                        # Generate multiple prediction points for each horizon
                        all_predictions = {}

                        for horizon in trained_horizons:
                            # Initialize dictionary to store multiple predictions for this horizon
                            horizon_pred_dict = {}

                            # Base price is the last known price
                            if has_live_data:
                                base_price = live_data['Close'].iloc[-1]
                            else:
                                base_price = historical_data['Close'].iloc[-1]

                            # Use the model's prediction as a trend indicator
                            trend_factor = predictions[horizon] / base_price

                            # Generate predictions for each point
                            for i in range(1, num_points + 1):
                                if horizon_unit == "days":
                                    point_time = current_time + timedelta(days=i * horizon)
                                    # Adjust trend based on the point number
                                    point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                    point_price = base_price * point_trend
                                else:  # weeks
                                    point_time = current_time + timedelta(weeks=i * horizon)
                                    # Adjust trend based on the point number
                                    point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                    point_price = base_price * point_trend

                                # Add to predictions data
                                pred_data.append({
                                    'Horizon': f'{horizon} {horizon_unit}',
                                    'Point': i,
                                    'Predicted Time': point_time.strftime('%Y-%m-%d %H:%M:%S'),
                                    'Predicted Price': round(point_price, 2)
                                })

                                # Store for plotting
                                horizon_pred_dict[point_time] = point_price

                            all_predictions[horizon] = horizon_pred_dict

                        # Create and display the predictions dataframe
                        pred_df = pd.DataFrame(pred_data)
                        st.dataframe(pred_df)

                        # Plot predictions with multiple points
                        plot_predictions_multi(historical_data, live_data, all_predictions, symbol, horizon_unit)
                    else:
                        # For short-term predictions (minutes), use the existing single-point prediction
                        for horizon in trained_horizons:
                            pred_time = current_time + timedelta(minutes=horizon)
                            horizon_label = f'{horizon} minutes'

                            pred_data.append({
                                'Horizon': horizon_label,
                                'Predicted Time': pred_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'Predicted Price': round(predictions[horizon], 2)
                            })

                        pred_df = pd.DataFrame(pred_data)
                        st.dataframe(pred_df)

                        # Plot predictions with single points
                        plot_predictions(historical_data, live_data, predictions, symbol)

                    # Display individual model predictions if using ensemble model
                    if model_type.lower() == 'ensemble' and 'individual_predictions' in st.session_state:
                        st.subheader("Individual Model Predictions")
                        st.info("The ensemble model combines predictions from multiple models. Here are the individual model predictions:")

                        # Create a table of individual model predictions
                        for horizon in trained_horizons:
                            st.write(f"### Horizon: {horizon} {horizon_unit}")

                            # Get individual predictions for this horizon
                            individual_preds = st.session_state.individual_predictions[horizon]

                            # Create DataFrame for display
                            models = list(individual_preds.keys())
                            prices = [round(individual_preds[model], 2) for model in models]

                            # Add ensemble prediction
                            models.append('Ensemble (Combined)')
                            prices.append(round(predictions[horizon], 2))

                            # Check if we have weights information
                            has_weights = 'weights' in ensemble_results[horizon]

                            if has_weights and ensemble_results[horizon]['weights']:
                                # Get weights
                                weights = ensemble_results[horizon]['weights']

                                # Make sure we have the right number of weights
                                if len(weights) < len(models) - 1:  # -1 for the ensemble model
                                    weights = weights + [0] * (len(models) - 1 - len(weights))
                                elif len(weights) > len(models) - 1:
                                    weights = weights[:len(models) - 1]

                                # Add ensemble weight (always 1.0)
                                weights.append(1.0)

                                # Create DataFrame with weights
                                model_df = pd.DataFrame({
                                    'Model': models,
                                    'Predicted Price': prices,
                                    'Weight': [f"{w:.2f}" if i < len(weights) else "N/A" for i, w in enumerate(weights)]
                                })
                            else:
                                # Create DataFrame without weights
                                model_df = pd.DataFrame({
                                    'Model': models,
                                    'Predicted Price': prices
                                })

                            # Display as table
                            st.dataframe(model_df)

                            # Create a bar chart to compare predictions
                            fig = go.Figure()

                            # Add individual model predictions
                            for i, model in enumerate(models[:-1]):  # Exclude ensemble
                                fig.add_trace(go.Bar(
                                    x=[model],
                                    y=[prices[i]],
                                    name=model
                                ))

                            # Add ensemble prediction with different color
                            fig.add_trace(go.Bar(
                                x=[models[-1]],  # Ensemble is last
                                y=[prices[-1]],
                                name=models[-1],
                                marker_color='red'
                            ))

                            # Update layout
                            fig.update_layout(
                                title=f'Model Comparison for {horizon} {horizon_unit} Horizon',
                                xaxis_title='Model',
                                yaxis_title='Predicted Price',
                                barmode='group'
                            )

                            # Show plot
                            st.plotly_chart(fig, use_container_width=True)

                except FileNotFoundError as e:
                    st.error(f"Error: {str(e)}")
                    st.info("This usually means the model or scaler file is missing. Please train the model first.")
                    logger.error(f"File not found error: {str(e)}")

                except ValueError as e:
                    st.error(f"Error: {str(e)}")
                    st.info("This might be due to incompatible data format or model configuration.")
                    logger.error(f"Value error: {str(e)}")

                except Exception as e:
                    st.error(f"Error generating predictions: {str(e)}")
                    st.info("An unexpected error occurred. Please check the logs for more details.")
                    logger.error(f"Error generating predictions: {str(e)}")

def plot_predictions(historical_data, live_data, predictions, symbol):
    """
    Plot historical data and predictions for single-point predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon
        symbol (str): Stock symbol
    """
    st.subheader("Price Forecast")

    # Create figure
    fig = go.Figure()

    # Add historical data
    fig.add_trace(go.Scatter(
        x=historical_data['Date'],
        y=historical_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    current_time = datetime.now()

    # Get the horizon unit from session state
    horizon_unit = st.session_state.get('horizon_unit', 'minutes')

    for horizon, price in predictions.items():
        # Calculate prediction time based on the horizon unit
        if horizon_unit == 'minutes':
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'
        elif horizon_unit == 'days':
            pred_time = current_time + timedelta(days=horizon)
            horizon_label = f'{horizon} days'
        elif horizon_unit == 'weeks':
            pred_time = current_time + timedelta(weeks=horizon)
            horizon_label = f'{horizon} weeks'
        else:
            # Default to minutes
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'

        fig.add_trace(go.Scatter(
            x=[pred_time],
            y=[price],
            mode='markers',
            name=horizon_label,
            marker=dict(color='red', size=10, symbol='star')
        ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)

    # Add trend line between last actual price and predictions
    if live_data is not None and not live_data.empty:
        last_price = live_data['Close'].iloc[-1]
        last_time = live_data['Date'].iloc[-1]
    else:
        last_price = historical_data['Close'].iloc[-1]
        last_time = historical_data['Date'].iloc[-1]

    # Create trend lines
    fig2 = go.Figure()

    # Add last actual price point
    fig2.add_trace(go.Scatter(
        x=[last_time],
        y=[last_price],
        mode='markers',
        name='Last Actual',
        marker=dict(color='blue', size=10)
    ))

    # Add prediction lines
    for horizon, price in predictions.items():
        # Calculate prediction time based on the horizon unit
        if horizon_unit == 'minutes':
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'
        elif horizon_unit == 'days':
            pred_time = current_time + timedelta(days=horizon)
            horizon_label = f'{horizon} days'
        elif horizon_unit == 'weeks':
            pred_time = current_time + timedelta(weeks=horizon)
            horizon_label = f'{horizon} weeks'
        else:
            # Default to minutes
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'

        fig2.add_trace(go.Scatter(
            x=[last_time, pred_time],
            y=[last_price, price],
            mode='lines+markers',
            name=horizon_label,
            line=dict(dash='dot'),
            marker=dict(size=[0, 10], symbol='star')
        ))

    # Update layout
    fig2.update_layout(
        title=f'{symbol} Prediction Trends',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig2, use_container_width=True)

def plot_predictions_multi(historical_data, live_data, predictions, symbol, horizon_unit='minutes'):
    """
    Plot historical data and multi-point predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon, where each prediction is a dict of {datetime: price}
        symbol (str): Stock symbol
        horizon_unit (str): Unit of time for horizons (minutes, days, weeks)
    """
    st.subheader("Price Forecast")

    # Create figure
    fig = go.Figure()

    # Add historical data
    fig.add_trace(go.Scatter(
        x=historical_data['Date'],
        y=historical_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    colors = ['red', 'orange', 'purple', 'brown', 'pink']

    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon (cycle through colors if needed)
        color = colors[i % len(colors)]

        # Extract times and prices from the prediction dictionary
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # Add multi-point predictions
        fig.add_trace(go.Scatter(
            x=pred_times,
            y=pred_prices,
            mode='lines+markers',
            name=f'{horizon} {horizon_unit}',
            line=dict(color=color, dash='dot'),
            marker=dict(color=color, size=8, symbol='circle')
        ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)

    # Add trend line between last actual price and predictions
    if live_data is not None and not live_data.empty:
        last_price = live_data['Close'].iloc[-1]
        last_time = live_data['Date'].iloc[-1]
    else:
        last_price = historical_data['Close'].iloc[-1]
        last_time = historical_data['Date'].iloc[-1]

    # Create trend lines
    fig2 = go.Figure()

    # Add last actual price point
    fig2.add_trace(go.Scatter(
        x=[last_time],
        y=[last_price],
        mode='markers',
        name='Last Actual',
        marker=dict(color='blue', size=10)
    ))

    # Add prediction lines
    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon
        color = colors[i % len(colors)]

        # Extract times and prices
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # Connect from last actual to first prediction and then show the trend line for all predictions
        all_x = [last_time] + pred_times
        all_y = [last_price] + pred_prices

        fig2.add_trace(go.Scatter(
            x=all_x,
            y=all_y,
            mode='lines+markers',
            name=f'{horizon} {horizon_unit}',
            line=dict(color=color, dash='dot'),
            marker=dict(color=color, size=8)
        ))

    # Update layout
    fig2.update_layout(
        title=f'{symbol} Prediction Trends',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig2, use_container_width=True)
