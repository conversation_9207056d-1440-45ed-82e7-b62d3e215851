"""
Simple TradingView API Server Starter
Starts the integrated FastAPI server for TradingView data scraping
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_api_status():
    """Check if the TradingView API is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ TradingView API is running")
            return True
    except:
        pass
    return False

def test_api_with_egx():
    """Test the API with EGX stock"""
    try:
        test_data = {
            "pairs": ["EGX-COMI"],
            "intervals": ["1D"]
        }
        
        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ API is working with EGX stocks!")
                return True
        
    except Exception as e:
        print(f"⚠️ API test failed: {str(e)}")
    
    return False

def start_api_server():
    """Start the integrated TradingView API server"""
    print("🚀 Starting TradingView API Server...")
    
    # Check if already running
    if check_api_status():
        print("✅ TradingView API is already running!")
        if test_api_with_egx():
            return True
        else:
            print("⚠️ API is running but not responding correctly")
    
    # Look for the API server in scrapers directory
    api_server_path = Path("scrapers/api_server.py")
    if not api_server_path.exists():
        print("❌ API server not found at scrapers/api_server.py")
        print("💡 Make sure you're running this from the main project directory")
        return False
    
    try:
        print(f"📡 Starting API server: {api_server_path}")
        
        # Start the API server
        process = subprocess.Popen([
            sys.executable, str(api_server_path)
        ], cwd="scrapers")
        
        # Wait for the server to start
        print("⏳ Waiting for API to start...")
        for i in range(30):  # Wait up to 30 seconds
            time.sleep(1)
            if check_api_status():
                print("✅ TradingView API started successfully!")
                
                # Test with EGX stock
                if test_api_with_egx():
                    print("🎉 API is ready for EGX stock analysis!")
                    return True
                else:
                    print("⚠️ API started but EGX test failed")
                    return False
        
        print("❌ API failed to start within 30 seconds")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ Error starting API: {str(e)}")
        return False

def main():
    """Main function"""
    print("🌟 TradingView API Server Starter")
    print("=" * 50)
    
    success = start_api_server()
    
    if success:
        print("\n🎯 TradingView API Server is ready!")
        print("📊 Your stock prediction app can now use real TradingView data")
        print("🔗 API URL: http://127.0.0.1:8000/api/scrape_pairs")
        print("🌐 Web Interface: http://127.0.0.1:8000/")
        print("📚 Documentation: http://127.0.0.1:8000/docs")
        print("\n💡 Keep this terminal open to maintain the API connection")
        print("⏹️ Press Ctrl+C to stop the API")
        
        try:
            # Keep the script running
            while True:
                time.sleep(60)
                if not check_api_status():
                    print("⚠️ API seems to have stopped. Attempting restart...")
                    start_api_server()
        except KeyboardInterrupt:
            print("\n🛑 Stopping TradingView API...")
    else:
        print("\n❌ Failed to start TradingView API")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the main project directory")
        print("2. Check that scrapers/api_server.py exists")
        print("3. Verify all dependencies are installed:")
        print("   pip install fastapi uvicorn playwright")
        print("   playwright install chromium")
        print("4. Check if port 8000 is available")

if __name__ == "__main__":
    main()
