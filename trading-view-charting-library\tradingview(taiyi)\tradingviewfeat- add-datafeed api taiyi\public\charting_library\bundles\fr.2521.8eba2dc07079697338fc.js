(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2521],{12978:e=>{e.exports=["Données en temps réel pour {symbolName}"]},64565:e=>{e.exports=["est fourni par la bourse {exchange}."]},19801:e=>{e.exports=["Ven"]},11268:e=>{e.exports="Mo"},63331:e=>{e.exports="Sa"},85954:e=>{e.exports="Su"},26230:e=>{e.exports="We"},24793:e=>{e.exports="Th"},31533:e=>{e.exports="Tu"},89790:e=>{e.exports=["Impossible d'obtenir le code source de Pine."]},39589:e=>{e.exports=["Réduire le volet"]},38154:e=>{e.exports=["Confirmer la suppression de l'arbre d'étude"]},53205:e=>{e.exports=["Contrats à terme continus"]},15993:e=>{e.exports=["Les contrats à terme continus sont des instruments synthétiques combinant des contrats individuels. Le contrat 1 ! représente le premier mois (l'échéance la plus proche) tandis que le contrat 2 ! représente la deuxième échéance la plus proche."]},45e3:e=>{e.exports=["Cboe BZX"]},36004:e=>{e.exports=["Créez un compte gratuit"]},69419:e=>{e.exports=["Tout va bien - Le marché est ouvert"]},97637:e=>{e.exports=["Avril"]},86797:e=>{e.exports=["Août"]},22519:e=>{e.exports=["Valeurs du changement de barre"]},52003:e=>{e.exports=["Voulez-vous vraiment supprimer l'étude principale et ses dérivés ?"]},68854:e=>{e.exports=["Double cliquer"]},97325:e=>{e.exports=["Problème avec les données"]},52916:e=>{e.exports=["Les données sont actualisées une fois par jour."]},25978:e=>{e.exports=["Les données sont mises à jour une fois par seconde, même s'il y a plus de mises à jour sur le marché."]},57310:e=>{e.exports=["Les données sont retardées"]},49321:e=>{e.exports=["Les données de notre plan de base sont mises à jour une fois par seconde, même s'il y a plus de mises à jour sur le marché."]},55669:e=>{e.exports=["Décembre"]},83498:e=>{e.exports=["Supprimer le volet"]},6044:e=>{e.exports=["Données dérivées"]},31461:e=>{e.exports=["Les données dérivées sont des indicateurs financiers créés en combinant et/ou en traitant des données brutes provenant de diverses sources."]},59315:e=>{e.exports=["Données Fin de journée"]},82751:e=>{e.exports=["Erreur"]},40519:e=>{e.exports=["Bonne soirée. Le marché est ouvert pour les transactions post-marché."]},80227:e=>{e.exports=["Fuseau horaire de la bourse"]},16467:e=>{e.exports=["Février"]},25046:e=>{e.exports=["Remplir les Conventions de Bourse"]},93666:e=>{e.exports=["Flagger le symbole"]},564:e=>{e.exports=["Ven"]},72970:e=>{e.exports=["Vendredi"]},88958:e=>{e.exports=["Vacances"]},32960:e=>{e.exports=["Symbole halal"]},21686:e=>{e.exports=["Masquer la légende de l'indicateur"]},26935:e=>{e.exports=["Arguments de l'indicateur"]},26315:e=>{e.exports=["Titres de l'indicateur"]},84098:e=>{e.exports=["Valeurs de l'indicateur"]},91459:e=>{e.exports=["Si vous souhaitez obtenir des données en temps réel de {listedExchange}, vous devez remplir une convention d'échange. Ne vous inquiétez pas, cela ne prend que quelques clics"]},50634:e=>{e.exports=["Passage en trading post-marché dans {remainingTime}."]},74537:e=>{
e.exports=["Ouverture du trading pré-marché dans {remainingTime}."]},26910:e=>{e.exports=["Janvier"]},23230:e=>{e.exports=["Juillet"]},49385:e=>{e.exports=["Juin"]},99487:e=>{e.exports=["Valeurs OHLC"]},15815:e=>{e.exports=["Une mise à jour par seconde"]},90784:e=>{e.exports=["Octobre"]},75991:e=>{e.exports=["Statut de marché ouvert"]},36051:e=>{e.exports=["En savoir plus"]},39899:e=>{e.exports=["Déplacer le volet vers le bas"]},70343:e=>{e.exports=["Déplacer le volet vers le haut"]},83085:e=>{e.exports=["Lun"]},61199:e=>{e.exports=["Lundi"]},41610:e=>{e.exports=["Plus"]},1653:e=>{e.exports=["Bonjour. Le marché est ouvert pour les transactions pré-marché."]},56470:e=>{e.exports=["Maximiser le graphique"]},19603:e=>{e.exports=["Maximiser le volet"]},68327:e=>{e.exports=["Mai"]},35732:e=>{e.exports=["Gérer les volets"]},84675:e=>{e.exports=["Mars"]},83949:e=>{e.exports=["Marché ouvert"]},35701:e=>{e.exports=["Le marché ouvre dans {remainingTime}"]},95814:e=>{e.exports=["Marché fermé"]},98105:e=>{e.exports=["Le marché ferme dans {remainingTime}"]},56086:e=>{e.exports=["Le marché est actuellement en congé. Quelle chance !"]},71194:e=>{e.exports=["Novembre"]},66324:e=>{e.exports=["Code source"]},36835:e=>{e.exports=["Sam"]},1144:e=>{e.exports=["Samedi"]},40653:e=>{e.exports=["Faire défiler vers la gauche"]},26721:e=>{e.exports=["Faire défiler jusqu'à la barre la plus récente"]},35809:e=>{e.exports=["Faire défiler vers la droite"]},61132:e=>{e.exports=["Septembre"]},28705:e=>{e.exports=["Montrer la légende de l'indicateur"]},51072:e=>{e.exports=["Afficher l'arborescence des objets"]},37809:e=>{e.exports=["Afficher les paramètres d'intervalle"]},39045:e=>{e.exports=["Erreur dans l'étude"]},86577:e=>{e.exports=["Dim"]},72149:e=>{e.exports=["Dimanche"]},46041:e=>{e.exports=["Source de prix du symbole"]},63143:e=>{e.exports=["Titre du symbole"]},29985:e=>{e.exports=["Post-marché"]},28412:e=>{e.exports=["Les plans payants permettent une mise à jour plus rapide des données."]},56042:e=>{e.exports=["Pré-marché"]},24680:e=>{e.exports=["Liste primaire"]},89022:e=>{e.exports=["Les données en temps réel pour ce symbole ne sont pas prises en charge pour le moment. Nous envisageons de le proposer ultérieurement."]},6667:e=>{e.exports=["Les données en temps réel pour {symbolName} sont fournies par la Bourse {exchange}."]},48293:e=>{e.exports=["Restaurer le graphique"]},91029:e=>{e.exports=["Restaurer le volet"]},75094:e=>{e.exports=["Mer"]},7147:e=>{e.exports=["Mercredi"]},52984:e=>{e.exports=["Pour obtenir des données en temps réel pour {description}, veuillez acheter le package de données en temps réel."]},9787:e=>{e.exports=["Jeu"]},7951:e=>{e.exports=["Jeudi"]},99214:e=>{e.exports=["La bourse principale, ou la première, où les actions d'une société sont cotées et négociées."]},2310:e=>{e.exports=["Ces données sont en temps réel, mais elles peuvent être légèrement différentes de leur contrepartie officielle provenant des bourses primaires."]},29512:e=>{
e.exports=["Ces données sont en temps réel, mais elles peuvent être légèrement différentes de leur contrepartie officielle provenant de {exchange}."]},52449:e=>{e.exports=["Il s'agit d'une action conforme à la charia, ce qui signifie qu'elle suit la loi islamique. Cette société ne perçoit pas d'intérêts et ne s'engage pas dans certains secteurs (jeux d'argent, alcool, tabac, produits à base de porc)."]},73717:e=>{e.exports=["Ce symbole n'existe pas, veuillez en choisir un autre."]},57048:e=>{e.exports=["Une petite pause? - ce marché est fermé"]},94316:e=>{e.exports=["Mar"]},44979:e=>{e.exports=["Mardi"]},8209:e=>{e.exports=["Déflagger le symbole"]},1111:e=>{e.exports="Volume"},61311:e=>{e.exports=["Grossissement"]},47602:e=>{e.exports=["Réduction"]},57889:e=>{e.exports=["modifier la visibilité des valeurs OHLC"]},18644:e=>{e.exports=["modifier la visibilité du statut de marché ouvert"]},45110:e=>{e.exports=["modifier la visibilité du changement de barre"]},31325:e=>{e.exports=["modifier la visibilité des titres de l'indicateur"]},99774:e=>{e.exports=["modifier la visibilité des valeurs de l'indicateur"]},96162:e=>{e.exports=["modifier la visibilité des arguments de l'indicateur"]},26717:e=>{e.exports=["modifier la visibilité de la description du symbole"]},6091:e=>{e.exports=["modifier la visibilité du champ du symbole"]},9455:e=>{e.exports=["modifier la visibilité des valeurs de volume"]},39348:e=>{e.exports=["moins d'1 minute"]},87358:e=>{e.exports=["afficher {title}"]},7827:e=>{e.exports=["{days} et {hours}"]},7435:e=>{e.exports=["{exchange} par {originalExchange}"]},19830:e=>{e.exports=["{hours} et {minutes}"]},1084:e=>{e.exports=["Les données en temps réel de {listedExchange} sont disponibles gratuitement pour les utilisateurs enregistrés."]},11155:e=>{e.exports=["Les données de {symbolName} sont retardées de {time} minutes."]},77033:e=>{e.exports=["Les données sont mises à jour une fois par {amount} seconde, même s'il y a plus de mises à jour sur le marché.","Les données sont mises à jour une fois toutes les {amount} secondes, même s'il y a plus de mises à jour sur le marché."]},2121:e=>{e.exports=["Les données de notre plan Basic sont mises à jour une fois par {amount} seconde, même s'il y a plus de mises à jour sur le marché.","Les données de notre plan Basic sont mises à jour une fois toutes les {amount} secondes, même s'il y a plus de mises à jour sur le marché."]},5223:e=>{e.exports=["Une mise à jour toutes les {amount} seconde","Une mise à jour toutes les {amount} secondes"]},58609:e=>{e.exports=["{number} jour","{number} jours"]},24430:e=>{e.exports=["{number} heure","{number} heures"]},67151:e=>{e.exports="{number} minute"}}]);