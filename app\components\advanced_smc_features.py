"""
Advanced SMC Features
High-priority enhancements for immediate trading impact:
- Break of Structure (BOS) Detection
- Liquidity Sweeps Alerts
- Premium/Discount Zones
- Dynamic Color Coding
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class BreakOfStructure:
    """Represents a Break of Structure event"""
    timestamp: int  # DataFrame index
    datetime: str   # Actual datetime string
    price: float
    structure_type: str  # 'BOS' or 'CHoCH'
    direction: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    previous_level: float
    volume: float = 0.0
    confirmed: bool = False

@dataclass
class LiquiditySweep:
    """Represents a liquidity sweep event"""
    timestamp: int
    sweep_price: float
    liquidity_level: float
    sweep_type: str  # 'buy_side' or 'sell_side'
    strength: float  # 0-1 strength score
    volume: float = 0.0
    reversal_confirmed: bool = False
    distance_pips: float = 0.0

@dataclass
class PremiumDiscountZone:
    """Represents premium/discount zones based on range"""
    high: float
    low: float
    equilibrium: float  # 50% level
    premium_start: float  # 50% level
    discount_end: float  # 50% level
    current_zone: str  # 'premium', 'discount', 'equilibrium'
    zone_strength: float  # 0-1 based on how extreme the position is

def detect_break_of_structure(df: pd.DataFrame, lookback: int = 20) -> List[BreakOfStructure]:
    """
    Detect Break of Structure (BOS) and Change of Character (CHoCH) events

    Args:
        df: OHLCV DataFrame with datetime index
        lookback: Periods to look back for structure analysis

    Returns:
        List of BreakOfStructure events
    """
    if len(df) < lookback * 2:
        logger.warning(f"Insufficient data for BOS detection: {len(df)} bars, need at least {lookback * 2}")
        return []

    bos_events = []

    try:
        # Find swing highs and lows with reduced lookback for more sensitivity
        swing_highs = _find_swing_points(df, 'high', max(lookback // 2, 5))
        swing_lows = _find_swing_points(df, 'low', max(lookback // 2, 5))

        logger.info(f"Found {len(swing_highs)} swing highs and {len(swing_lows)} swing lows")

        # Convert to sorted lists for easier processing
        high_items = sorted(swing_highs.items(), key=lambda x: x[0], reverse=True)  # Most recent first
        low_items = sorted(swing_lows.items(), key=lambda x: x[0], reverse=True)

        # Detect bullish BOS (break above previous swing high)
        for i in range(len(high_items) - 1):
            current_idx, current_high = high_items[i]
            previous_idx, previous_high = high_items[i + 1]

            if current_high > previous_high:
                # Calculate strength more realistically
                break_magnitude = (current_high - previous_high) / previous_high
                volume_strength = _calculate_volume_strength(df, current_idx)

                # Improved strength calculation (0-1 scale)
                strength = min(break_magnitude * 10 + volume_strength * 0.5, 1.0)

                if strength > 0.2:  # Lower threshold for more detections
                    # Get datetime string
                    datetime_str = df.index[current_idx].strftime('%Y-%m-%d %H:%M') if hasattr(df.index[current_idx], 'strftime') else str(df.index[current_idx])

                    # Relaxed confirmation logic
                    confirmed = _confirm_structure_break(df, current_idx, current_high, 'bullish', confirmation_bars=2)

                    bos_events.append(BreakOfStructure(
                        timestamp=current_idx,
                        datetime=datetime_str,
                        price=current_high,
                        structure_type='BOS',
                        direction='bullish',
                        strength=strength,
                        previous_level=previous_high,
                        volume=df.iloc[current_idx].get('volume', 0),
                        confirmed=confirmed
                    ))

        # Detect bearish BOS (break below previous swing low)
        for i in range(len(low_items) - 1):
            current_idx, current_low = low_items[i]
            previous_idx, previous_low = low_items[i + 1]

            if current_low < previous_low:
                break_magnitude = (previous_low - current_low) / previous_low
                volume_strength = _calculate_volume_strength(df, current_idx)

                strength = min(break_magnitude * 10 + volume_strength * 0.5, 1.0)

                if strength > 0.2:
                    datetime_str = df.index[current_idx].strftime('%Y-%m-%d %H:%M') if hasattr(df.index[current_idx], 'strftime') else str(df.index[current_idx])
                    confirmed = _confirm_structure_break(df, current_idx, current_low, 'bearish', confirmation_bars=2)

                    bos_events.append(BreakOfStructure(
                        timestamp=current_idx,
                        datetime=datetime_str,
                        price=current_low,
                        structure_type='BOS',
                        direction='bearish',
                        strength=strength,
                        previous_level=previous_low,
                        volume=df.iloc[current_idx].get('volume', 0),
                        confirmed=confirmed
                    ))

        # Sort by timestamp (most recent first)
        bos_events.sort(key=lambda x: x.timestamp, reverse=True)

        logger.info(f"Detected {len(bos_events)} BOS events")
        return bos_events[:10]

    except Exception as e:
        logger.error(f"Error in BOS detection: {str(e)}")
        return []

def detect_liquidity_sweeps(df: pd.DataFrame, liquidity_zones: List, lookback: int = 10) -> List[LiquiditySweep]:
    """
    Detect liquidity sweep events where price briefly breaks a level then reverses
    
    Args:
        df: OHLCV DataFrame
        liquidity_zones: List of liquidity zones to monitor
        lookback: Periods to look for reversal confirmation
        
    Returns:
        List of LiquiditySweep events
    """
    if len(df) < lookback * 2:
        return []
    
    sweep_events = []
    
    for zone in liquidity_zones:
        zone_level = (zone.high + zone.low) / 2
        
        # Look for price action around this zone
        for i in range(zone.timestamp + 1, len(df) - lookback):
            candle = df.iloc[i]
            
            if zone.zone_type == 'buy_side':
                # Check for sweep above buy-side liquidity
                if candle['high'] > zone.high:
                    # Check for reversal in next few candles
                    reversal_confirmed = _check_liquidity_reversal(df, i, zone.high, 'sell', lookback)
                    
                    if reversal_confirmed:
                        volume_strength = _calculate_volume_strength(df, i)
                        distance_pips = candle['high'] - zone.high
                        
                        strength = min((volume_strength + min(distance_pips / zone.high * 1000, 1.0)) / 2, 1.0)
                        
                        sweep_events.append(LiquiditySweep(
                            timestamp=i,
                            sweep_price=candle['high'],
                            liquidity_level=zone.high,
                            sweep_type='buy_side',
                            strength=strength,
                            volume=candle.get('volume', 0),
                            reversal_confirmed=True,
                            distance_pips=distance_pips
                        ))
            
            elif zone.zone_type == 'sell_side':
                # Check for sweep below sell-side liquidity
                if candle['low'] < zone.low:
                    reversal_confirmed = _check_liquidity_reversal(df, i, zone.low, 'buy', lookback)
                    
                    if reversal_confirmed:
                        volume_strength = _calculate_volume_strength(df, i)
                        distance_pips = zone.low - candle['low']
                        
                        strength = min((volume_strength + min(distance_pips / zone.low * 1000, 1.0)) / 2, 1.0)
                        
                        sweep_events.append(LiquiditySweep(
                            timestamp=i,
                            sweep_price=candle['low'],
                            liquidity_level=zone.low,
                            sweep_type='sell_side',
                            strength=strength,
                            volume=candle.get('volume', 0),
                            reversal_confirmed=True,
                            distance_pips=distance_pips
                        ))
    
    # Sort by timestamp (most recent first)
    sweep_events.sort(key=lambda x: x.timestamp, reverse=True)
    return sweep_events[:5]

def calculate_premium_discount_zones(df: pd.DataFrame, period: int = 50) -> PremiumDiscountZone:
    """
    Calculate premium/discount zones based on recent range
    
    Args:
        df: OHLCV DataFrame
        period: Period to calculate range over
        
    Returns:
        PremiumDiscountZone object
    """
    if len(df) < period:
        period = len(df)
    
    # Get recent range
    recent_data = df.tail(period)
    range_high = recent_data['high'].max()
    range_low = recent_data['low'].min()
    current_price = df['close'].iloc[-1]
    
    # Calculate key levels
    equilibrium = (range_high + range_low) / 2
    range_size = range_high - range_low
    
    # Determine current zone
    price_position = (current_price - range_low) / range_size
    
    if price_position > 0.7:
        current_zone = 'premium'
        zone_strength = min((price_position - 0.5) * 2, 1.0)  # 0.5-1.0 mapped to 0-1
    elif price_position < 0.3:
        current_zone = 'discount'
        zone_strength = min((0.5 - price_position) * 2, 1.0)  # 0.5-0.0 mapped to 0-1
    else:
        current_zone = 'equilibrium'
        zone_strength = 1.0 - abs(price_position - 0.5) * 2  # Distance from 0.5
    
    return PremiumDiscountZone(
        high=range_high,
        low=range_low,
        equilibrium=equilibrium,
        premium_start=equilibrium,
        discount_end=equilibrium,
        current_zone=current_zone,
        zone_strength=zone_strength
    )

def get_dynamic_colors(structure, age_factor: float = 1.0) -> Dict[str, str]:
    """
    Get dynamic colors based on structure type, strength, and age
    
    Args:
        structure: SMC structure object
        age_factor: Factor to fade colors based on age (0-1)
        
    Returns:
        Dictionary with color information
    """
    base_colors = {
        'bullish_ob': '#4CAF50',
        'bearish_ob': '#F44336', 
        'bullish_fvg': '#2196F3',
        'bearish_fvg': '#FF9800',
        'buy_side_liquidity': '#9C27B0',
        'sell_side_liquidity': '#E91E63',
        'bos_bullish': '#00E676',
        'bos_bearish': '#FF1744',
        'sweep_bullish': '#00BCD4',
        'sweep_bearish': '#FF5722',
        'premium': '#FF6B6B',
        'discount': '#4ECDC4',
        'equilibrium': '#FFE66D'
    }
    
    # Determine structure type and get base color
    if hasattr(structure, 'block_type'):
        color_key = f"{structure.block_type}_ob"
    elif hasattr(structure, 'gap_type'):
        color_key = f"{structure.gap_type}_fvg"
    elif hasattr(structure, 'zone_type'):
        color_key = f"{structure.zone_type}_liquidity"
    elif hasattr(structure, 'direction'):
        if hasattr(structure, 'structure_type'):
            color_key = f"bos_{structure.direction}"
        else:
            color_key = f"sweep_{structure.direction}"
    else:
        color_key = 'equilibrium'
    
    base_color = base_colors.get(color_key, '#FFFFFF')
    
    # Calculate opacity based on strength and age
    strength = getattr(structure, 'strength', 0.5)
    opacity = min(strength * age_factor, 1.0)
    
    # Calculate border intensity
    border_opacity = min(opacity + 0.3, 1.0)
    
    return {
        'fill_color': base_color,
        'border_color': base_color,
        'fill_opacity': max(opacity * 0.3, 0.1),  # Minimum visibility
        'border_opacity': max(border_opacity * 0.8, 0.3),
        'line_width': max(int(strength * 3), 1)
    }

# Helper functions
def _find_swing_points(df: pd.DataFrame, price_type: str, lookback: int) -> Dict[int, float]:
    """Find swing highs or lows with improved sensitivity"""
    swing_points = {}

    # Ensure we have enough data
    if len(df) < lookback * 2 + 1:
        logger.warning(f"Insufficient data for swing point detection: {len(df)} bars")
        return swing_points

    # Use smaller lookback for more recent data to catch more swing points
    min_lookback = max(lookback // 3, 2)

    for i in range(lookback, len(df) - min_lookback):
        if price_type == 'high':
            current = df.iloc[i]['high']

            # Check left side (use full lookback)
            left_data = df.iloc[max(0, i-lookback):i]['high']
            left_max = left_data.max() if len(left_data) > 0 else 0

            # Check right side (use smaller lookback for more sensitivity)
            right_end = min(i + min_lookback + 1, len(df))
            right_data = df.iloc[i+1:right_end]['high']
            right_max = right_data.max() if len(right_data) > 0 else 0

            # More lenient condition for swing high
            if current >= left_max and current >= right_max and current > 0:
                swing_points[i] = current

        elif price_type == 'low':
            current = df.iloc[i]['low']

            left_data = df.iloc[max(0, i-lookback):i]['low']
            left_min = left_data.min() if len(left_data) > 0 else float('inf')

            right_end = min(i + min_lookback + 1, len(df))
            right_data = df.iloc[i+1:right_end]['low']
            right_min = right_data.min() if len(right_data) > 0 else float('inf')

            if current <= left_min and current <= right_min and current > 0:
                swing_points[i] = current

    logger.info(f"Found {len(swing_points)} {price_type} swing points with lookback {lookback}")
    return swing_points

def _calculate_volume_strength(df: pd.DataFrame, idx: int, period: int = 20) -> float:
    """Calculate volume strength at given index"""
    if idx < period:
        return 0.5
    
    current_volume = df.iloc[idx].get('volume', 0)
    avg_volume = df.iloc[idx-period:idx]['volume'].mean()
    
    if avg_volume == 0:
        return 0.5
    
    return min(current_volume / avg_volume / 2, 1.0)

def _confirm_structure_break(df: pd.DataFrame, idx: int, level: float, direction: str, confirmation_bars: int = 2) -> bool:
    """Confirm structure break with follow-through - more lenient logic"""
    if idx + confirmation_bars >= len(df):
        return False

    confirmation_data = df.iloc[idx+1:idx+confirmation_bars+1]

    if len(confirmation_data) == 0:
        return False

    if direction == 'bullish':
        # At least 50% of confirmation bars should close above level
        closes_above = sum(1 for _, candle in confirmation_data.iterrows() if candle['close'] > level)
        return closes_above >= len(confirmation_data) * 0.5
    else:
        # At least 50% of confirmation bars should close below level
        closes_below = sum(1 for _, candle in confirmation_data.iterrows() if candle['close'] < level)
        return closes_below >= len(confirmation_data) * 0.5

def _check_liquidity_reversal(df: pd.DataFrame, sweep_idx: int, level: float, reversal_direction: str, lookback: int) -> bool:
    """Check for reversal after liquidity sweep"""
    if sweep_idx + lookback >= len(df):
        return False
    
    reversal_data = df.iloc[sweep_idx+1:sweep_idx+lookback+1]
    
    if reversal_direction == 'buy':
        # Look for price moving back above the level
        return any(candle['close'] > level for _, candle in reversal_data.iterrows())
    else:
        # Look for price moving back below the level
        return any(candle['close'] < level for _, candle in reversal_data.iterrows())
