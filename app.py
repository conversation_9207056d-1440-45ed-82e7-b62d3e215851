import streamlit as st

# Set page configuration to wide layout
st.set_page_config(layout="wide", page_title="AI Stocks Bot", page_icon="📈")
import os
import sys
import logging
import traceback
from datetime import datetime, timedelta
import time
import threading
import configparser
from functools import wraps

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import application initialization
from app import logger, APP_VERSION, APP_NAME, init_session_state, cleanup_memory

# Import session state management
from app.utils.session_state import (
    initialize_session_state,
    get_session_value,
    set_session_value,
    track_error,
    track_performance
)

# Import configuration management
from app.config import get_config, Config

# Now import numpy and pandas
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go

# Import utility functions
from app.utils.data_processing import load_csv_data, preprocess_data, save_scaler, load_scaler
from app.utils.feature_engineering import prepare_features, add_technical_indicators
from app.utils.backtesting import backtest_component

# Import model functions
from models.train import train_model, train_from_csv, USING_TENSORFLOW
from models.predict import predict_future_prices, predict_from_live_data

# Log which model backend is being used
if USING_TENSORFLOW:
    logger.info("Using TensorFlow-based LSTM model")
else:
    logger.info("Using scikit-learn based model (TensorFlow not available)")

# Import scraper
from scrapers.price_scraper import PriceScraper

# Import components
from app.components.stock_manager import stock_manager_component
from app.components.live_trading import live_trading_component
from app.components.dashboard import dashboard_component
from app.components.modern_dashboard import modern_dashboard_component
from app.components.portfolio import portfolio_component
from app.components.reporting import reporting_component
from app.components.prediction import prediction_component
from app.components.advanced_prediction import advanced_prediction_component
from app.components.enhanced_prediction import enhanced_prediction_component
from app.components.ensemble_predictions import ensemble_predictions_component
from app.components.interactive_dashboard import interactive_dashboard_component
from app.components.ai_services import ai_services_component
from app.components.performance_metrics import performance_metrics_dashboard
from app.components.chat import chat_component
from app.components.ai_chat import ai_chat_component
# Removed Claude chat component import
from app.components.enhanced_chat import EnhancedRuleBasedChat
from app.components.tradingview_page import tradingview_page_component
from app.components.tradingview_predictions import tradingview_predictions_component
# Import the LLM insights component function directly
from app.components.llm_insights_fix import llm_insights_component

# Import Advanced Technical Analysis page
from app.pages.advanced_technical_analysis import show_advanced_technical_analysis

# Import SMC Analysis page
from app.pages.smc_analysis import show_smc_analysis

# Import Advanced Technical Analysis summary
from app.components.advanced_ta_summary import show_advanced_ta_summary

# Import new consolidated pages
from app.pages.stock_management import show_stock_management
from app.pages.predictions_consolidated import show_predictions_consolidated
from app.pages.ai_assistant import show_ai_assistant
from app.pages.tradingview_integration import show_tradingview_integration
from app.pages.predictive_analytics import show_predictive_analytics

# Import state transition components
from app.components.state_transitions import (
    data_selection_component,
    model_selection_component,
    prediction_options_component,
    live_data_component
)

def get_sample_csv_content(year=2025):
    """
    Get sample CSV content with dates in the specified year

    Args:
        year (int): Year for the sample data

    Returns:
        str: CSV content as a string
    """
    if year == 2025:
        # Try to read the 2025 sample file
        sample_path = os.path.join('data', 'sample', 'COMI_2025.csv')
        if os.path.exists(sample_path):
            with open(sample_path, 'r') as f:
                return f.read()

    # Fallback to generating sample data
    import io
    from datetime import datetime, timedelta

    # Create a buffer to write CSV data
    buffer = io.StringIO()
    buffer.write("Date,Open,High,Low,Close,Volume\n")

    # Generate sample data
    start_date = datetime(year, 1, 1)
    price = 60.0

    for i in range(100):
        date = start_date + timedelta(days=i)
        open_price = price
        high_price = round(price + 0.7, 1)
        low_price = round(price - 0.2, 1)
        close_price = round(price + 0.3, 1)
        volume = 1000000 + i * 100000

        buffer.write(f"{date.strftime('%Y-%m-%d')},{open_price},{high_price},{low_price},{close_price},{volume}\n")

        # Slightly increase the price for the next day
        price = round(close_price + 0.2, 1)

    return buffer.getvalue()

def upload_csv_component():
    """
    Streamlit component for uploading CSV files

    Returns:
        tuple: (df, symbol) where df is the DataFrame and symbol is the stock symbol
    """
    st.subheader("Upload Historical Data")

    # Add a download button for sample data
    col1, col2 = st.columns([1, 1])
    with col1:
        st.download_button(
            label="Download 2025 Sample Data",
            data=get_sample_csv_content(2025),
            file_name="COMI_2025_sample.csv",
            mime="text/csv",
            help="Download a sample CSV file with 2025 dates to use as a template"
        )
    with col2:
        st.download_button(
            label="Download 2023 Sample Data",
            data=get_sample_csv_content(2023),
            file_name="COMI_2023_sample.csv",
            mime="text/csv",
            help="Download a sample CSV file with 2023 dates to use as a template"
        )

    uploaded_file = st.file_uploader("Upload CSV file with historical stock data", type=["csv"])

    if uploaded_file is not None:
        try:
            # Read CSV file
            df = pd.read_csv(uploaded_file)

            # Check if required columns exist
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                st.error(f"Missing required columns: {', '.join(missing_columns)}")
                return None, None

            # Convert Date column to datetime with flexible format parsing
            try:
                # Store original date column for format attempts
                original_date_column = df['Date'].copy()

                # First attempt: flexible parsing
                df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
                initial_na_count = df['Date'].isna().sum()

                # If some dates couldn't be parsed, try specific formats
                if initial_na_count > 0:
                    # Try common date formats
                    date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y/%m/%d', '%d-%m-%Y']
                    format_found = False

                    for fmt in date_formats:
                        try:
                            # Reset to original and try this format
                            test_dates = pd.to_datetime(original_date_column, format=fmt, errors='coerce')
                            na_count_with_format = test_dates.isna().sum()

                            # If this format works better, use it
                            if na_count_with_format < initial_na_count:
                                df['Date'] = test_dates
                                if na_count_with_format == 0:
                                    st.success(f"✅ Successfully parsed all dates using format: {fmt}")
                                else:
                                    st.info(f"📅 Parsed dates using format: {fmt} ({na_count_with_format} dates still unparseable)")
                                format_found = True
                                break
                        except:
                            continue

                    # Show warning only if we couldn't improve the parsing
                    if not format_found and initial_na_count > 0:
                        st.warning(f"⚠️ {initial_na_count} dates couldn't be parsed with common formats. Trying flexible parsing...")

                # Handle remaining unparseable dates
                final_na_count = df['Date'].isna().sum()
                if final_na_count > 0:
                    # Only show warning if it's a significant portion of the data
                    total_rows = len(df)
                    na_percentage = (final_na_count / total_rows) * 100

                    if na_percentage > 5:  # More than 5% of data
                        st.warning(f"⚠️ Removing {final_na_count} rows ({na_percentage:.1f}%) with unparseable dates.")
                    else:
                        st.info(f"ℹ️ Cleaned {final_na_count} rows with invalid dates.")

                    df = df.dropna(subset=['Date'])

                    if df.empty:
                        st.error("❌ No valid dates found in the data. Please check your date format.")
                        return None, None
                elif initial_na_count > 0:
                    st.success("✅ All dates successfully parsed and validated.")

            except Exception as e:
                st.error(f"❌ Error parsing dates: {str(e)}")
                st.info("💡 Please ensure your Date column contains valid dates in a recognizable format (e.g., YYYY-MM-DD, DD/MM/YYYY, MM/DD/YYYY)")
                return None, None

            # Sort by date
            df = df.sort_values('Date')

            # Display date range information
            min_date = df['Date'].min().strftime('%Y-%m-%d')
            max_date = df['Date'].max().strftime('%Y-%m-%d')
            st.info(f"Date range in your data: {min_date} to {max_date}")

            # Get stock symbol from filename or ask user
            filename = uploaded_file.name
            symbol = os.path.splitext(filename)[0]

            # Display file information
            st.success(f"Successfully loaded file: {filename}")

            # Allow user to modify the symbol
            symbol_input = st.text_input("Stock Symbol", value=symbol, help="This will be used as the filename when saving the data")

            # Display raw data
            st.subheader("Raw Data")
            # Show more rows to better see the data
            st.write(f"Displaying data from {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")

            # Sort by date in descending order to show the most recent data first
            df_display = df.sort_values('Date', ascending=False).head(10)
            st.dataframe(df_display)

            # Add option to view more data
            if st.toggle("View more data", help="Show additional rows of data"):
                num_rows = st.slider("Number of rows to display", min_value=10, max_value=min(100, len(df)), value=20)
                # Sort by date in descending order to show the most recent data first
                df_display_more = df.sort_values('Date', ascending=False).head(num_rows)
                st.dataframe(df_display_more)

            # Display data info
            st.subheader("Data Information")
            st.write(f"Total rows: {len(df)}")
            st.write(f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")

            # Display summary statistics
            if st.toggle("Show summary statistics", help="Display statistical information about the data"):
                # Create a copy of the DataFrame with only numeric columns
                df_numeric = df.select_dtypes(include=['number'])

                # Add a toggle to show statistics for the most recent data
                show_recent = st.toggle("Show statistics for most recent data only", help="Filter statistics to only show recent data")

                if show_recent:
                    # Get data from 2025 only
                    df_recent = df[df['Date'].dt.year >= 2025]
                    if not df_recent.empty:
                        st.write("Statistics for 2025 data:")
                        st.write(df_recent.select_dtypes(include=['number']).describe())
                    else:
                        st.warning("No data from 2025 found.")
                        st.write(df_numeric.describe())
                else:
                    st.write("Statistics for all data:")
                    st.write(df_numeric.describe())

            # Add feature engineering
            if st.toggle("Apply Feature Engineering", help="Calculate technical indicators and other features"):
                df_features = prepare_features(df)
                st.subheader("Data with Technical Indicators")

                # Sort by date in descending order to show the most recent data first
                df_features_display = df_features.sort_values('Date', ascending=False).head(10)
                st.dataframe(df_features_display)

                # Option to view more data with technical indicators
                if st.toggle("View more data with technical indicators", help="Show additional rows with technical indicators"):
                    num_rows = st.slider("Number of rows with indicators to display",
                                        min_value=10, max_value=min(100, len(df_features)), value=20)
                    # Sort by date in descending order to show the most recent data first
                    df_features_more = df_features.sort_values('Date', ascending=False).head(num_rows)
                    st.dataframe(df_features_more)

                # Return the feature-engineered DataFrame
                return df_features, symbol_input

            # Return the raw DataFrame
            return df, symbol_input

        except Exception as e:
            st.error(f"Error processing CSV file: {str(e)}")
            logger.error(f"Error processing CSV file: {str(e)}")
            return None, None

    return None, None

def save_uploaded_data(df, symbol, data_dir='data/stocks'):
    """
    Save uploaded data to disk in the stocks folder

    Args:
        df (pd.DataFrame): DataFrame to save
        symbol (str): Stock symbol
        data_dir (str): Directory to save the data

    Returns:
        str: Path to the saved file
    """
    if df is None or symbol is None:
        return None

    try:
        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Make a copy of the DataFrame to avoid modifying the original
        df_save = df.copy()

        # Ensure Date column is datetime
        if 'Date' in df_save.columns:
            df_save['Date'] = pd.to_datetime(df_save['Date'], errors='coerce')

            # Remove rows with NaN dates
            df_save = df_save.dropna(subset=['Date'])

            # Sort by date
            df_save = df_save.sort_values('Date')

            # Check if the data includes 2025
            has_2025 = (df_save['Date'].dt.year == 2025).any()
            if has_2025:
                logger.info(f"File {symbol} contains 2025 data")

        # Save DataFrame to CSV
        file_path = os.path.join(data_dir, f"{symbol}.csv")
        df_save.to_csv(file_path, index=False)

        # Log success message
        logger.info(f"Saved data to {file_path}")
        logger.info(f"Date range: {df_save['Date'].min().strftime('%Y-%m-%d')} to {df_save['Date'].max().strftime('%Y-%m-%d')}")

        return file_path

    except Exception as e:
        logger.error(f"Error saving data: {str(e)}")
        return None

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_available_stock_files(data_dir='data/stocks'):
    """
    Get a list of available stock files in the stocks directory

    Args:
        data_dir (str): Directory to look for stock files

    Returns:
        list: List of stock symbols (filenames without extension)
    """
    try:
        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Get all CSV files in the directory
        files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

        # Extract symbols (filenames without extension)
        symbols = [os.path.splitext(f)[0] for f in files]

        logger.info(f"Found {len(symbols)} stock files in {data_dir}")
        return symbols
    except Exception as e:
        logger.error(f"Error getting stock files: {str(e)}")
        return []

@st.cache_data(ttl=3600)  # Cache for 1 hour
def load_stock_data(symbol, data_dir='data/stocks'):
    """
    Load stock data from a CSV file in the stocks directory

    Args:
        symbol (str): Stock symbol (filename without extension)
        data_dir (str): Directory to look for stock files

    Returns:
        pd.DataFrame: DataFrame containing the stock data, or None if file not found
    """
    try:
        start_time = time.time()

        # Construct file path
        file_path = os.path.join(data_dir, f"{symbol}.csv")

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"Stock file not found: {file_path}")
            return None

        # Read CSV file
        df = pd.read_csv(file_path)

        # Convert Date column to datetime
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

        # Check for NaN dates
        if df['Date'].isna().any():
            logger.warning(f"Some dates in {symbol} couldn't be parsed. Please check the file format.")

        # Remove rows with NaN dates
        df = df.dropna(subset=['Date'])

        # Sort by date
        df = df.sort_values('Date')

        # Check if the data includes 2025
        has_2025 = (df['Date'].dt.year == 2025).any()

        end_time = time.time()
        logger.info(f"Loaded stock data for {symbol} from {file_path} in {end_time - start_time:.2f} seconds")
        logger.info(f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
        logger.info(f"Data shape: {df.shape}")

        if has_2025:
            logger.info(f"File {symbol} contains 2025 data")

        return df

    except Exception as e:
        logger.error(f"Error loading stock data: {str(e)}")
        return None

@st.cache_data(ttl=3600)  # Cache for 1 hour
def load_and_process_data(symbol, data_dir='data/stocks'):
    """
    Load and process stock data with feature engineering

    Args:
        symbol (str): Stock symbol
        data_dir (str): Directory to look for stock files

    Returns:
        pd.DataFrame: Processed DataFrame with technical indicators
    """
    from app.utils.feature_engineering import prepare_features

    # Load raw data
    df = load_stock_data(symbol, data_dir)
    if df is None:
        return None

    # Apply feature engineering
    start_time = time.time()
    df_features = prepare_features(df)
    end_time = time.time()

    logger.info(f"Feature engineering for {symbol} completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Features shape: {df_features.shape}")

    return df_features

def live_data_component(symbol=None):
    """
    Streamlit component for displaying live data

    Args:
        symbol (str, optional): Stock symbol. If None, user can select from available stocks.

    Returns:
        pd.DataFrame: DataFrame with live data
    """
    st.subheader("Live Price Data")

    # Always get available stock files
    available_stocks = get_available_stock_files()

    if not available_stocks:
        st.warning("No stock data files found. Please upload some data first.")
        st.info("Go to the 'Upload Data' page to upload stock data.")
        return None

    # If no symbol is provided or we want to select a different stock
    if not symbol or st.toggle("Select a different stock", key="change_stock", value=not symbol):
        # Let user select a stock
        selected_stock = st.selectbox(
            "Select a stock",
            options=available_stocks,
            index=0 if available_stocks else None,
            key="live_data_stock_select"
        )

        if selected_stock:
            # Load the selected stock data
            df = load_stock_data(selected_stock)
            if df is not None:
                st.session_state.historical_data = df
                st.session_state.symbol = selected_stock
                symbol = selected_stock
                st.success(f"Loaded stock data for {selected_stock}")

                # Display date range
                st.info(f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
            else:
                st.error(f"Failed to load data for {selected_stock}")
                return None
        else:
            st.warning("Please select a stock from the dropdown")
            return None

    # Display the selected stock
    st.info(f"Selected stock: {symbol}")

    # Scraper source selection
    source = st.selectbox(
        "Select data source",
        options=["TradingView", "Mubasher"],
        index=0
    )

    # Get live data
    if st.button("Fetch Live Price"):
        try:
            with st.spinner("Fetching live price..."):
                # Initialize scraper with proper resource management
                scraper = PriceScraper(source=source.lower())
                try:
                    price_data = scraper.get_price(symbol)

                    if price_data:
                        # Create DataFrame
                        live_df = pd.DataFrame([price_data])

                        # Check if this is sample data
                        is_sample = 'Note' in price_data and 'sample' in price_data['Note'].lower()

                        if is_sample:
                            st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                            st.info("The sample price is based on historical data with a small random variation to simulate live data.")
                        else:
                            st.success(f"Live price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                        # Display live data
                        st.dataframe(live_df)

                        # Store in session state
                        if 'live_data' not in st.session_state:
                            st.session_state.live_data = live_df
                        else:
                            st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                        # Return live data
                        return live_df
                    else:
                        st.error(f"Failed to fetch live price for {symbol}")
                        st.info("This could be due to network issues or the stock symbol not being avaZilable on the selected source.")
                        return None
                finally:
                    # Ensure the WebDriver is properly closed
                    scraper.close_driver()

        except Exception as e:
            st.error(f"Error fetching live price: {str(e)}")
            logger.error(f"Error fetching live price: {str(e)}")
            st.info("Trying to generate sample price data based on historical data...")

            # Try to generate sample data as a fallback
            try:
                # Initialize a new scraper for the fallback
                scraper = PriceScraper(source=source.lower())
                try:
                    sample_data = scraper._generate_sample_price(symbol)
                    if sample_data:
                        live_df = pd.DataFrame([sample_data])
                        st.warning("⚠️ Using sample price data due to error fetching live data.")
                        st.dataframe(live_df)

                        # Store in session state
                        if 'live_data' not in st.session_state:
                            st.session_state.live_data = live_df
                        else:
                            st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                        return live_df
                finally:
                    # Ensure the WebDriver is properly closed
                    scraper.close_driver()
            except Exception as e2:
                logger.error(f"Error generating sample data: {str(e2)}")

            return None

    # Auto-refresh option
    auto_refresh = st.toggle("Enable auto-refresh", help="Automatically refresh the live data at regular intervals")

    if auto_refresh:
        refresh_interval = st.slider("Refresh interval (seconds)", min_value=30, max_value=300, value=60, step=30)

        # Create a placeholder for live data
        live_data_placeholder = st.empty()

        # Add a note about auto-refresh
        st.info(f"Data will be refreshed every {refresh_interval} seconds. Note: In this version, you need to manually click the button to refresh.")

        # Auto-refresh logic
        try:
            with st.spinner("Fetching live price..."):
                # Initialize scraper with proper resource management
                scraper = PriceScraper(source=source.lower())
                try:
                    price_data = scraper.get_price(symbol)

                    if price_data:
                        # Create DataFrame
                        live_df = pd.DataFrame([price_data])

                        # Check if this is sample data
                        is_sample = 'Note' in price_data and 'sample' in price_data['Note'].lower()

                        if is_sample:
                            live_data_placeholder.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                        else:
                            live_data_placeholder.success(f"Live price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                        # Display live data
                        live_data_placeholder.dataframe(live_df)

                        # Store in session state
                        if 'live_data' not in st.session_state:
                            st.session_state.live_data = live_df
                        else:
                            st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                        # Return live data
                        return live_df
                    else:
                        live_data_placeholder.error(f"Failed to fetch live price for {symbol}")
                        live_data_placeholder.info("Using sample data based on historical prices instead.")

                        # Try to generate sample data as a fallback
                        try:
                            sample_data = scraper._generate_sample_price(symbol)
                            if sample_data:
                                live_df = pd.DataFrame([sample_data])
                                live_data_placeholder.dataframe(live_df)

                                # Store in session state
                                if 'live_data' not in st.session_state:
                                    st.session_state.live_data = live_df
                                else:
                                    st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                                return live_df
                        except Exception:
                            pass

                        return None
                finally:
                    # Ensure the WebDriver is properly closed
                    scraper.close_driver()

        except Exception as e:
            st.error(f"Error fetching live price: {str(e)}")
            logger.error(f"Error fetching live price: {str(e)}")

            # Try to generate sample data as a fallback
            try:
                # Initialize a new scraper for the fallback
                scraper = PriceScraper(source=source.lower())
                try:
                    sample_data = scraper._generate_sample_price(symbol)
                    if sample_data:
                        live_df = pd.DataFrame([sample_data])
                        live_data_placeholder.warning("⚠️ Using sample price data due to error fetching live data.")
                        live_data_placeholder.dataframe(live_df)

                        # Store in session state
                        if 'live_data' not in st.session_state:
                            st.session_state.live_data = live_df
                        else:
                            st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                        return live_df
                finally:
                    # Ensure the WebDriver is properly closed
                    scraper.close_driver()
            except Exception as e2:
                logger.error(f"Error generating sample data: {str(e2)}")

            return None

    # Return existing live data if available
    if 'live_data' in st.session_state:
        st.dataframe(st.session_state.live_data)
        return st.session_state.live_data

    return None

def plot_predictions(historical_data, live_data, predictions, symbol):
    """
    Plot historical data and predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon
        symbol (str): Stock symbol
    """
    st.subheader("Price Forecast")

    # Create figure
    fig = go.Figure()

    # Add historical data
    fig.add_trace(go.Scatter(
        x=historical_data['Date'],
        y=historical_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    current_time = datetime.now()

    # Get the horizon unit from session state
    horizon_unit = st.session_state.get('horizon_unit', 'minutes')

    for horizon, price in predictions.items():
        # Calculate prediction time based on the horizon unit
        if horizon_unit == 'minutes':
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'
        elif horizon_unit == 'days':
            pred_time = current_time + timedelta(days=horizon)
            horizon_label = f'{horizon} days'
        elif horizon_unit == 'weeks':
            pred_time = current_time + timedelta(weeks=horizon)
            horizon_label = f'{horizon} weeks'
        else:
            # Default to minutes
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'

        fig.add_trace(go.Scatter(
            x=[pred_time],
            y=[price],
            mode='markers',
            name=horizon_label,
            marker=dict(color='red', size=10, symbol='star')
        ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)

    # Add trend line between last actual price and predictions
    if live_data is not None and not live_data.empty:
        last_price = live_data['Close'].iloc[-1]
        last_time = live_data['Date'].iloc[-1]
    else:
        last_price = historical_data['Close'].iloc[-1]
        last_time = historical_data['Date'].iloc[-1]

    # Create trend lines
    fig2 = go.Figure()

    # Add last actual price point
    fig2.add_trace(go.Scatter(
        x=[last_time],
        y=[last_price],
        mode='markers',
        name='Last Actual',
        marker=dict(color='blue', size=10)
    ))

    # Add prediction lines
    # Get the horizon unit from session state
    horizon_unit = st.session_state.get('horizon_unit', 'minutes')

    for horizon, price in predictions.items():
        # Calculate prediction time based on the horizon unit
        if horizon_unit == 'minutes':
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'
        elif horizon_unit == 'days':
            pred_time = current_time + timedelta(days=horizon)
            horizon_label = f'{horizon} days'
        elif horizon_unit == 'weeks':
            pred_time = current_time + timedelta(weeks=horizon)
            horizon_label = f'{horizon} weeks'
        else:
            # Default to minutes
            pred_time = current_time + timedelta(minutes=horizon)
            horizon_label = f'{horizon} min'

        fig2.add_trace(go.Scatter(
            x=[last_time, pred_time],
            y=[last_price, price],
            mode='lines+markers',
            name=horizon_label,
            line=dict(dash='dot'),
            marker=dict(size=[0, 10], symbol='star')
        ))

    # Update layout
    fig2.update_layout(
        title=f'{symbol} Prediction Trends',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig2, use_container_width=True)

def prediction_component(historical_data, live_data, symbol):
    """
    Streamlit component for displaying predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        symbol (str): Stock symbol
    """
    st.subheader("Price Predictions")

    if historical_data is None:
        st.warning("Please upload historical data first")
        return

    if symbol is None or symbol == "":
        st.warning("Please provide a stock symbol")
        return

    # Display which model backend is being used
    if USING_TENSORFLOW:
        st.info("Using TensorFlow-based LSTM model")
        # Model type selection for TensorFlow
        model_type = st.selectbox(
            "Select model type",
            options=["LSTM", "BiLSTM"],
            index=0
        )
    else:
        st.info("Using scikit-learn based model (TensorFlow not available)")
        # Model type selection for scikit-learn
        model_options = ["rf", "gb", "lr", "prophet", "hybrid", "ensemble"]

        # Check if XGBoost is available
        try:
            # Just check if we can import it
            __import__("xgboost")
            model_options.insert(4, "xgb")
        except ImportError:
            # XGBoost not available, continue without it
            pass

        model_type = st.selectbox(
            "Select model type",
            options=model_options,
            index=0
        )

        st.markdown("""
        **Model types:**
        - rf: Random Forest Regressor
        - gb: Gradient Boosting Regressor
        - lr: Linear Regression
        - prophet: Facebook Prophet Time Series
        """)

        if "xgb" in model_options:
            st.markdown("- xgb: XGBoost Regressor")

        st.markdown("""
        **Hybrid models:**
        - hybrid: ARIMA + ML Hybrid Model
        - ensemble: Ensemble of multiple models
        """)

        # Show additional options for hybrid models
        if model_type == "hybrid":
            st.info("The hybrid model combines ARIMA for time series patterns with machine learning for feature-based prediction.")
        elif model_type == "ensemble":
            st.info("The ensemble model combines predictions from multiple models (RF, GB, LR, and XGBoost if available).")
        elif model_type == "prophet":
            st.info("The Prophet model is designed for time series forecasting with strong seasonality patterns and is developed by Facebook.")

    # Check if we have live data
    has_live_data = live_data is not None and not live_data.empty

    # Offer different horizon options based on data source
    if has_live_data:
        st.info("Using live data for predictions. Short-term horizons are available.")
        # Short-term horizons for live data (minutes)
        horizons = [4, 25, 30, 69]
        horizon_unit = "minutes"
        # For short-term, we'll use the existing single-point prediction
        num_points = 1
    else:
        st.info("Using historical data for predictions. Both short-term and long-term horizons are available.")
        # Offer both short and long-term horizons for historical data
        horizon_options = [
            "Short-term (minutes)",
            "Medium-term (days)",
            "Long-term (weeks)"
        ]
        horizon_type = st.radio("Select prediction timeframe", horizon_options)

        if horizon_type == "Short-term (minutes)":
            horizons = [4, 25, 30, 69]
            horizon_unit = "minutes"
            # For short-term, we'll use the existing single-point prediction
            num_points = 1
        elif horizon_type == "Medium-term (days)":
            horizons = [1, 2, 3, 5, 7]
            horizon_unit = "days"
            # For medium-term, we'll predict multiple points (one per day)
            num_points = st.slider("Number of prediction points", min_value=1, max_value=7, value=5)
        else:  # Long-term
            horizons = [1, 2, 4, 8, 12]
            horizon_unit = "weeks"
            # For long-term, we'll predict multiple points (one per week)
            num_points = st.slider("Number of prediction points", min_value=1, max_value=12, value=4)

    # Select horizons
    selected_horizons = st.multiselect(
        f"Select prediction horizons ({horizon_unit})",
        options=horizons,
        default=[horizons[0], horizons[-1]]  # Default to first and last
    )

    # Store the horizon unit and number of points in session state
    st.session_state.horizon_unit = horizon_unit
    st.session_state.num_prediction_points = num_points

    if not selected_horizons:
        st.warning("Please select at least one prediction horizon")
        return

    # Check if models are trained for the selected horizons
    from app.utils.data_processing import is_model_trained

    # Check if any models are trained
    models_trained = []
    models_not_trained = []
    for horizon in selected_horizons:
        if is_model_trained(symbol, horizon, model_type.lower(), 'saved_models', horizon_unit):
            models_trained.append(horizon)
        else:
            models_not_trained.append(horizon)

    if not models_trained:
        st.warning("No trained models found for the selected horizons. Please train models first.")
        st.info("Go to the 'Train Model' page to train models for the selected horizons.")

        # Add a button to go to the Train Model page
        if st.button("Go to Train Model page"):
            st.session_state.page = "Train Model"
        return
    elif models_not_trained:
        st.warning(f"Models not trained for horizons: {', '.join(map(str, models_not_trained))}")
        st.info("Only predictions for trained models will be generated.")

    # Make predictions
    if st.button("Generate Predictions"):
        try:
            with st.spinner("Generating predictions..."):
                # Use only horizons with trained models
                trained_horizons = [h for h in selected_horizons if h in models_trained]

                # Convert horizons to minutes for prediction functions
                model_horizons = []
                for h in trained_horizons:
                    if horizon_unit == "days":
                        model_horizons.append(h * 24 * 60)  # days to minutes
                    elif horizon_unit == "weeks":
                        model_horizons.append(h * 7 * 24 * 60)  # weeks to minutes
                    else:  # minutes
                        model_horizons.append(h)

                # Check if we have live data
                if live_data is not None and not live_data.empty:
                    # Make predictions using live data
                    predictions = predict_from_live_data(
                        live_data, historical_data, symbol,
                        horizons=model_horizons,
                        model_type=model_type.lower(),
                        models_path='saved_models'
                    )
                else:
                    # Make predictions using only historical data
                    predictions = predict_future_prices(
                        historical_data, symbol,
                        horizons=model_horizons,
                        model_type=model_type.lower(),
                        models_path='saved_models'
                    )

                # Map the predictions back to the original horizons
                horizon_predictions = {}
                for i, h in enumerate(trained_horizons):
                    horizon_predictions[h] = predictions[model_horizons[i]]

                # Replace predictions with mapped predictions
                predictions = horizon_predictions

                # Display predictions
                st.success("Predictions generated successfully")

                # Create a table of predictions
                pred_data = []
                current_time = datetime.now()

                # Get the horizon unit from session state
                horizon_unit = st.session_state.get('horizon_unit', 'minutes')

                # For medium and long-term predictions, we'll generate multiple points
                if horizon_unit in ['days', 'weeks']:
                    # Number of prediction points to generate
                    num_points = st.session_state.get('num_prediction_points', 5 if horizon_unit == 'days' else 4)

                    # Generate multiple prediction points for each horizon
                    all_predictions = {}

                    for horizon in selected_horizons:
                        # Initialize dictionary to store multiple predictions for this horizon
                        horizon_pred_dict = {}

                        # Base price is the last known price
                        if live_data is not None and not live_data.empty:
                            base_price = live_data['Close'].iloc[-1]
                        else:
                            base_price = historical_data['Close'].iloc[-1]

                        # Use the model's prediction as a trend indicator
                        trend_factor = predictions[horizon] / base_price

                        # Generate predictions for each point
                        for i in range(1, num_points + 1):
                            if horizon_unit == "days":
                                point_time = current_time + timedelta(days=i * horizon)
                                # Adjust trend based on the point number
                                point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                point_price = base_price * point_trend
                            else:  # weeks
                                point_time = current_time + timedelta(weeks=i * horizon)
                                # Adjust trend based on the point number
                                point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                point_price = base_price * point_trend

                            # Add to predictions data
                            pred_data.append({
                                'Horizon': f'{horizon} {horizon_unit}',
                                'Point': i,
                                'Predicted Time': point_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'Predicted Price': round(point_price, 2)
                            })

                            # Store for plotting
                            horizon_pred_dict[point_time] = point_price

                        all_predictions[horizon] = horizon_pred_dict

                    # Create and display the predictions dataframe
                    pred_df = pd.DataFrame(pred_data)
                    st.dataframe(pred_df)

                    # Plot predictions with multiple points
                    plot_predictions_multi(historical_data, live_data, all_predictions, symbol, horizon_unit)
                else:
                    # For short-term predictions (minutes), use the existing single-point prediction
                    for horizon in selected_horizons:
                        pred_time = current_time + timedelta(minutes=horizon)
                        horizon_label = f'{horizon} minutes'

                        pred_data.append({
                            'Horizon': horizon_label,
                            'Predicted Time': pred_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'Predicted Price': round(predictions[horizon], 2)
                        })

                    pred_df = pd.DataFrame(pred_data)
                    st.dataframe(pred_df)

                    # Plot predictions with single points
                    plot_predictions(historical_data, live_data, predictions, symbol)

        except FileNotFoundError as e:
            st.error(f"Error: {str(e)}")
            st.info("This usually means the model or scaler file is missing. Please train the model first.")
            logger.error(f"File not found error: {str(e)}")

            # Add a button to go to the Train Model page
            if st.button("Go to Train Model page", key="goto_train_from_error"):
                st.session_state.page = "Train Model"

        except ValueError as e:
            st.error(f"Error: {str(e)}")
            st.info("This might be due to incompatible data format or model configuration.")
            logger.error(f"Value error: {str(e)}")

        except Exception as e:
            st.error(f"Error generating predictions: {str(e)}")
            st.info("An unexpected error occurred. Please check the logs for more details.")
            logger.error(f"Error generating predictions: {str(e)}")

def is_sample_data(df):
    """
    Check if the data is likely to be the sample data

    Args:
        df (pd.DataFrame): DataFrame to check

    Returns:
        bool: True if it's likely the sample data, False otherwise
    """
    if df is None:
        return False

    # Check if the date range matches the sample data (2023)
    if df['Date'].min().year == 2023 and df['Date'].max().year == 2023:
        # Check if the number of rows is similar to the sample data
        if 90 <= len(df) <= 100:
            return True

    return False

def main():
    """
    Main Streamlit application
    """

    # Initialize session state
    initialize_session_state()

    # Get configuration
    config = get_config()

    # Store config in session state if not already there
    if get_session_value('config') is None:
        set_session_value('config', config)

    # Title with version
    st.title(f"{APP_NAME}")
    st.markdown(f"An AI-powered application to predict future stock prices on the Egyptian Stock Exchange (EGX) - v{APP_VERSION}")

    # Add version info in the sidebar footer
    st.sidebar.markdown("---")
    st.sidebar.markdown(f"<div style='text-align: center; color: gray; font-size: 0.8em;'>{APP_NAME}<br>Version {APP_VERSION}</div>", unsafe_allow_html=True)

    # Sidebar
    st.sidebar.title("Navigation")

    # Display session info in sidebar
    with st.sidebar.expander("Session Info", expanded=False):
        st.write(f"Current Symbol: {get_session_value('symbol', 'None')}")
        st.write(f"Last Update: {get_session_value('last_update', 'Never')}")

        # Add button to clear session state
        if st.button("Clear Session State"):
            from app.utils.session_state import clear_session_state
            clear_session_state()
            st.success("Session state cleared")
            st.rerun

    # Initialize page in session state if not already there
    if 'page' not in st.session_state:
        st.session_state.page = "Home"

    # Use the page from session state as the default value
    # Consolidated pages - removed redundant old pages
    pages = ["Home", "Dashboard", "Upload Data",
             "Stock Management",  # Replaces: Select Stock + Manage Stocks
             "Live Trading",  # Replaces: Live Data (functionality covered)
             "Train Model",
             "Predictions (New)",  # Replaces: Predictions + Advanced Predictions + Ensemble Predictions
             "Predictive Analytics",  # NEW: AI-Powered Price Prediction & Scenario Analysis
             "Performance Metrics", "Backtesting", "Advanced Technical Analysis",
             "SMC Analysis",  # NEW: Smart Money Concepts Analysis
             "Portfolio", "Reports",
             "AI Assistant",  # Replaces: AI Services + LLM Insights + Chat Assistant
             "TradingView Integration"]  # Replaces: TradingView Charts + TradingView Predictions
    page = st.sidebar.radio(
        "Go to",
        pages,
        index=pages.index(st.session_state.page) if st.session_state.page in pages else 0
    )

    # Handle redirects from old pages to new consolidated pages
    page_redirects = {
        "Select Stock": "Stock Management",
        "Manage Stocks": "Stock Management",
        "Live Data": "Live Trading",
        "Predictions": "Predictions (New)",
        "Advanced Predictions": "Predictions (New)",
        "Ensemble Predictions": "Predictions (New)",
        "AI Services": "AI Assistant",
        "LLM Insights": "AI Assistant",
        "Chat Assistant": "AI Assistant",
        "TradingView Charts": "TradingView Integration",
        "TradingView Predictions": "TradingView Integration"
    }

    # Check if current page needs to be redirected
    if page in page_redirects:
        page = page_redirects[page]
        st.info(f"📍 Redirected to consolidated page: **{page}**")

    # Update the session state
    st.session_state.page = page

    # Initialize session state
    if 'historical_data' not in st.session_state:
        st.session_state.historical_data = None

    if 'symbol' not in st.session_state:
        st.session_state.symbol = None

    if 'live_data' not in st.session_state:
        st.session_state.live_data = None

    # Get available stock files
    available_stocks = get_available_stock_files()
    if available_stocks:
        st.sidebar.markdown("---")
        st.sidebar.subheader("Available Stocks")

        # Create a dropdown list for stock selection
        selected_stock = st.sidebar.selectbox(
            "Select a stock to load",
            options=available_stocks,
            key="sidebar_stock_select"
        )

        # Add a load button
        if st.sidebar.button("Load Selected Stock", key="load_selected_stock"):
            # Show a spinner while loading
            with st.spinner(f"Loading and processing data for {selected_stock}..."):
                # Use the cached function to load and process data
                df_features = load_and_process_data(selected_stock)
                if df_features is not None:
                    # Store both raw and processed data
                    df = load_stock_data(selected_stock)  # This will use the cached version
                    st.session_state.historical_data = df
                    st.session_state.processed_data = df_features
                    st.session_state.symbol = selected_stock
                    st.success(f"✅ Loaded and processed data for {selected_stock}")
                    # Force a rerun to show the stock details on the current page
                    st.rerun()

    # Home page
    if page == "Home":
        st.header("Welcome to AI Stocks Bot")
        st.markdown("""
        This application uses AI to predict future stock prices on the Egyptian Stock Exchange (EGX).

        ### Features:

        1. **Historical CSV Upload**
           - Upload .csv files containing historical stock price data (OHLC)
           - Feature engineering includes SMA, EMA, RSI, hour of day, and day of the week

        2. **Live Price Integration**
           - Scrapes near-live prices from TradingView or Mubasher
           - Scraped price is automatically appended and transformed to match the feature format of static data

        3. **Advanced AI Models**
           - Multiple model types: LSTM, Random Forest, Gradient Boosting, SVR, Linear Regression
           - Advanced ensemble and hybrid models for improved accuracy
           - Trains on static data and predicts based on the latest (live) input
           - Supports multiple prediction horizons: minutes, days, and weeks

        4. **Technical Analysis**
           - Comprehensive technical indicators library
           - Interactive charts and visualizations
           - Trading strategy backtesting

        5. **Portfolio Management**
           - Track your stock holdings and performance
           - Buy and sell stocks in a simulated environment
           - Monitor portfolio value and returns

        6. **Reporting**
           - Generate detailed stock analysis reports
           - Export predictions and strategy results
           - PDF and Excel export options

        ### How to use:

        1. Go to the **Upload Data** page to upload historical stock data
        2. Go to the **Dashboard** page to view interactive charts and indicators
        3. Go to the **Train Model** page to train AI models
        4. Go to the **Predictions** page to generate price predictions
        5. Go to the **Portfolio** page to manage your stock holdings
        6. Go to the **Reports** page to generate and export reports

        ### New Features:

        - **Enhanced Dashboard** with technical indicators and trading strategies
        - **Portfolio Management** for tracking your investments
        - **Reporting** capabilities for detailed analysis
        - **🚀 Advanced Technical Analysis** with AI-powered pattern recognition
        """)

        # Add Advanced Technical Analysis showcase
        st.markdown("---")
        st.markdown("## 🚀 **NEW: Advanced Technical Analysis**")

        # Create showcase columns
        showcase_cols = st.columns([2, 1])

        with showcase_cols[0]:
            st.markdown("""
            ### **Professional-Grade Technical Analysis is Now Available!**

            Your AI Stocks Bot has been enhanced with **cutting-edge technical analysis features**:

            🤖 **AI-Powered Pattern Recognition** - Detect Head & Shoulders, Double Tops, Triangles
            📊 **Volume Profile Analysis** - Point of Control, Value Areas, Market Structure
            🔬 **Market Microstructure** - VWAP, Money Flow, Advanced Indicators
            ⚙️ **Custom AI Indicators** - Adaptive Moving Averages, Smart Momentum
            🌍 **Intermarket Analysis** - Currency & Commodity Impact Simulation
            📈 **Options Flow Analysis** - Call/Put Ratios, Unusual Activity Detection

            **✨ Demo Mode Available** - Try realistic patterns without real data!
            """)

        with showcase_cols[1]:
            st.markdown("### **Try It Now!**")

            if st.button("🔬 Explore Advanced Technical Analysis", type="primary", use_container_width=True):
                st.session_state.page = "Advanced Technical Analysis"
                st.rerun()

            if st.button("📊 View Feature Overview", use_container_width=True):
                show_advanced_ta_summary()

            st.markdown("""
            **🎯 Features:**
            - 15+ Pattern Types
            - 85%+ AI Accuracy
            - 20+ Custom Indicators
            - Real-time Analysis
            """)

        # Display loaded stock information if available
        if st.session_state.historical_data is not None and st.session_state.symbol is not None:
            st.markdown("---")
            st.markdown("## 📊 **Currently Loaded Stock**")

            df = st.session_state.historical_data
            symbol = st.session_state.symbol

            # Stock overview
            st.subheader(f"📈 {symbol} - Stock Overview")

            # Key metrics
            latest_price = df['Close'].iloc[-1]
            price_change = df['Close'].iloc[-1] - df['Close'].iloc[-2] if len(df) > 1 else 0
            price_change_pct = (price_change / df['Close'].iloc[-2] * 100) if len(df) > 1 and df['Close'].iloc[-2] != 0 else 0

            # Display metrics in columns
            metric_cols = st.columns(4)

            with metric_cols[0]:
                st.metric(
                    "Latest Price",
                    f"{latest_price:.2f} EGP",
                    delta=f"{price_change:+.2f} ({price_change_pct:+.1f}%)"
                )

            with metric_cols[1]:
                high_52w = df['High'].max()
                st.metric("52W High", f"{high_52w:.2f} EGP")

            with metric_cols[2]:
                low_52w = df['Low'].min()
                st.metric("52W Low", f"{low_52w:.2f} EGP")

            with metric_cols[3]:
                avg_volume = df['Volume'].mean()
                st.metric("Avg Volume", f"{avg_volume:,.0f}")

            # Data info
            st.info(f"📅 Data Range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')} ({len(df)} records)")

            # Quick actions
            st.subheader("🚀 Quick Actions")
            action_cols = st.columns(4)

            with action_cols[0]:
                if st.button("📊 View Dashboard", use_container_width=True):
                    st.session_state.page = "Dashboard"
                    st.rerun()

            with action_cols[1]:
                if st.button("🤖 Train Model", use_container_width=True):
                    st.session_state.page = "Train Model"
                    st.rerun()

            with action_cols[2]:
                if st.button("🔮 Make Predictions", use_container_width=True):
                    st.session_state.page = "Predictions (New)"
                    st.rerun()

            with action_cols[3]:
                if st.button("📈 Live Trading", use_container_width=True):
                    st.session_state.page = "Live Trading"
                    st.rerun()

            # Data preview
            with st.expander("📋 View Data Preview"):
                st.dataframe(df.sort_values('Date', ascending=False).head(10), use_container_width=True)

    # Dashboard page
    elif page == "Dashboard":
        # Add tabs for different dashboard views
        dashboard_tabs = st.tabs(["Modern Dashboard", "Interactive Dashboard", "Basic Dashboard"])

        with dashboard_tabs[0]:
            # Use the new modern dashboard component
            modern_dashboard_component()

        with dashboard_tabs[1]:
            # Use the interactive dashboard component
            interactive_dashboard_component()

        with dashboard_tabs[2]:
            # Use the original dashboard component
            dashboard_component()

    # Upload Data page
    elif page == "Upload Data":
        st.header("Upload Historical Data")

        # Upload CSV component
        historical_data, symbol = upload_csv_component()

        if historical_data is not None and symbol is not None:
            # Check if it's the sample data
            if is_sample_data(historical_data):
                st.warning("⚠️ It looks like you might be using the sample data from 2023. If you intended to upload your own data with dates up to 2025, please check the file format and try again.")

            # Save data to session state
            st.session_state.historical_data = historical_data
            st.session_state.symbol = symbol

            # Save data to disk
            file_path = save_uploaded_data(historical_data, symbol)

            if file_path:
                st.success(f"Data saved to {file_path}")

                # Add a button to clear the data and start fresh
                if st.button("Clear uploaded data and start fresh"):
                    st.session_state.historical_data = None
                    st.session_state.symbol = None
                    st.session_state.live_data = None







    # Train Model page
    elif page == "Train Model":
        st.header("Train AI Model")

        # Add a note about the importance of training before prediction
        st.info("⚠️ **Important**: You must train models before making predictions. Each prediction horizon requires a separate trained model.")

        # Add explanation about horizon units
        st.info("📊 **Horizon Units**: All models are internally trained using minute-based horizons. When you select days or weeks, they will be converted to minutes automatically (1 day = 1440 minutes, 1 week = 10080 minutes).")

        if st.session_state.historical_data is None or st.session_state.symbol is None:
            st.warning("Please upload historical data and provide a stock symbol first")
        else:
            # Create two columns for model selection
            col1, col2 = st.columns([1, 2])

            with col1:
                # Model category selection
                model_category = st.radio(
                    "Select model category",
                    options=["Machine Learning", "Deep Learning", "Hybrid/Ensemble"],
                    index=0
                )

            with col2:
                # Model type selection based on category
                if model_category == "Machine Learning":
                    model_options = ["rf", "gb", "lr", "svr"]

                    # Check if XGBoost is available
                    try:
                        # Just check if we can import it
                        __import__("xgboost")
                        model_options.insert(2, "xgb")
                    except ImportError:
                        # XGBoost not available, continue without it
                        pass

                    # Check if Prophet is available
                    try:
                        __import__("prophet")
                        model_options.append("prophet")
                    except ImportError:
                        # Prophet not available, continue without it
                        pass

                    model_type = st.selectbox(
                        "Select model type",
                        options=model_options,
                        index=0
                    )

                    # No epochs or batch size for scikit-learn models
                    epochs = 1  # Not used for scikit-learn
                    batch_size = 32  # Not used for scikit-learn

                    # Show model descriptions
                    if model_type == "rf":
                        st.info("Random Forest: An ensemble learning method that builds multiple decision trees and merges their predictions.")
                    elif model_type == "gb":
                        st.info("Gradient Boosting: A technique that builds models sequentially, with each new model correcting errors from previous ones.")
                    elif model_type == "xgb":
                        st.info("XGBoost: An optimized gradient boosting library designed for speed and performance.")
                    elif model_type == "lr":
                        st.info("Linear Regression: A simple approach that models the relationship between variables using a linear equation.")
                    elif model_type == "svr":
                        st.info("Support Vector Regression: Uses support vector machines to predict continuous variables.")
                    elif model_type == "prophet":
                        st.info("Prophet: Facebook's time series forecasting tool designed for business forecasting with strong seasonality patterns.")

                elif model_category == "Deep Learning":
                    if USING_TENSORFLOW:
                        model_options = ["lstm", "bilstm"]

                        # Add transformer if available
                        try:
                            from models.transformer_model import StockTransformerModel
                            model_options.append("transformer")
                        except ImportError:
                            # Transformer not available
                            pass

                        model_type = st.selectbox(
                            "Select model type",
                            options=model_options,
                            index=0
                        )

                        # Training parameters specific to TensorFlow
                        epochs = st.slider("Number of epochs", min_value=10, max_value=200, value=50, step=10)
                        batch_size = st.slider("Batch size", min_value=16, max_value=128, value=32, step=16)

                        # Show model descriptions
                        if model_type == "lstm":
                            st.info("LSTM (Long Short-Term Memory): A type of recurrent neural network capable of learning long-term dependencies in time series data.")
                        elif model_type == "bilstm":
                            st.info("BiLSTM (Bidirectional LSTM): Processes data in both forward and backward directions to capture more context.")
                        elif model_type == "transformer":
                            st.info("Transformer: Uses attention mechanisms to capture long-range dependencies in time series data. Effective for complex patterns but requires more training data.")
                    else:
                        st.warning("TensorFlow is not available. Deep Learning models cannot be used.")
                        st.info("Falling back to Random Forest model.")
                        model_type = "rf"
                        epochs = 1
                        batch_size = 32
                else:  # Hybrid/Ensemble
                    model_options = ["hybrid", "ensemble"]

                    # Check if enhanced ensemble is available
                    try:
                        from models.enhanced_ensemble_predict import get_enhanced_ensemble_predictions
                        model_options.append("enhanced_ensemble")
                    except ImportError:
                        # Enhanced ensemble not available
                        pass

                    model_type = st.selectbox(
                        "Select model type",
                        options=model_options,
                        index=0
                    )

                    # No epochs or batch size for hybrid/ensemble models
                    epochs = 1
                    batch_size = 32

                    # Show model descriptions
                    if model_type == "hybrid":
                        st.info("Hybrid Model: Combines ARIMA for time series patterns with machine learning for feature-based prediction.")
                    elif model_type == "ensemble":
                        st.info("Ensemble Model: Combines predictions from multiple models (RF, GB, LR, and XGBoost if available).")
                    elif model_type == "enhanced_ensemble":
                        st.info("Enhanced Ensemble: An advanced ensemble that uses adaptive weighting based on recent performance of each model.")

            # Prediction horizons
            st.subheader("Select Prediction Horizons")

            # Offer different horizon options
            horizon_options = [
                "Short-term (minutes)",
                "Medium-term (days)",
                "Long-term (weeks)"
            ]
            horizon_type = st.radio("Select prediction timeframe", horizon_options)

            if horizon_type == "Short-term (minutes)":
                horizons = [4, 25, 30, 69]
                horizon_unit = "minutes"
            elif horizon_type == "Medium-term (days)":
                horizons = [1, 2, 3, 5, 7]
                horizon_unit = "days"
            else:  # Long-term
                horizons = [1, 2, 4, 8, 12]
                horizon_unit = "weeks"

            selected_horizons = st.multiselect(
                f"Select prediction horizons ({horizon_unit})",
                options=horizons,
                default=horizons
            )

            # Store the horizon unit in session state for later use in predictions
            st.session_state.horizon_unit = horizon_unit

            # Train model
            if st.button("Train Model"):
                try:
                    with st.spinner("Training model..."):
                        # Save data to disk
                        file_path = save_uploaded_data(st.session_state.historical_data, st.session_state.symbol)

                        if file_path:
                            # Convert horizons to minutes for model training
                            model_horizons = []
                            for h in selected_horizons:
                                if horizon_unit == "days":
                                    # Convert days to minutes
                                    model_horizons.append(h * 24 * 60)
                                elif horizon_unit == "weeks":
                                    # Convert weeks to minutes
                                    model_horizons.append(h * 7 * 24 * 60)
                                else:  # minutes
                                    model_horizons.append(h)

                            # Create a placeholder for training logs
                            train_log = st.empty()
                            train_log.info("Starting model training...")

                            # Make sure model type is lowercase
                            actual_model_type = model_type.lower()

                            # Check if TensorFlow is available for deep learning models
                            if not USING_TENSORFLOW and actual_model_type in ['lstm', 'bilstm', 'transformer']:
                                train_log.warning(f"TensorFlow is not available. Falling back to RandomForest model instead of {actual_model_type}")
                                actual_model_type = 'rf'  # Fall back to RandomForest

                            # Train model
                            trained_models = train_from_csv(
                                file_path,
                                st.session_state.symbol,
                                horizons=model_horizons,
                                model_type=actual_model_type,
                                epochs=epochs,
                                batch_size=batch_size
                            )

                            # Check if any models were actually trained
                            if trained_models and len(trained_models) > 0:
                                # At least one model was trained successfully
                                st.success(f"Model trained successfully for {len(trained_models)} horizons!")

                                # Show which horizons were trained
                                trained_horizons = list(trained_models.keys())
                                st.info(f"Models trained for horizons: {', '.join(map(str, trained_horizons))}")

                                # Check if any horizons failed
                                failed_horizons = [h for h in model_horizons if h not in trained_horizons]
                                if failed_horizons:
                                    st.warning(f"Failed to train models for horizons: {', '.join(map(str, failed_horizons))}")
                                    st.info("This could be due to insufficient data for these horizons.")

                                # Add a button to go to the Predictions page
                                if st.button("Go to Predictions page", key="goto_predictions", use_container_width=True):
                                    st.session_state.page = "Predictions"
                            else:
                                # No models were trained
                                st.error("No models were trained successfully!")
                                st.info("This could be due to insufficient data or other issues. Check the logs for details.")

                                # Show data statistics to help diagnose the issue
                                data_info = f"Data statistics: {len(st.session_state.historical_data)} rows"
                                st.info(data_info)

                                # Suggest solutions
                                st.info("Try using a different model type or shorter prediction horizons.")
                        else:
                            st.error("Failed to save data")

                except Exception as e:
                    st.error(f"Error training model: {str(e)}")
                    logger.error(f"Error training model: {str(e)}")

    # Performance Metrics page
    elif page == "Performance Metrics":
        st.header("Performance Metrics Dashboard")

        if st.session_state.symbol is None:
            st.warning("Please select a stock first")
        else:
            # Use the performance metrics dashboard component
            performance_metrics_dashboard(st.session_state.symbol)

    # Backtesting page
    elif page == "Backtesting":
        st.header("Model Backtesting")

        if st.session_state.historical_data is None or st.session_state.symbol is None:
            st.warning("Please upload historical data and provide a stock symbol first")
        else:
            try:
                # Use the backtesting component
                backtest_component(st.session_state.historical_data, st.session_state.symbol)
            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                st.error(f"Error in backtesting component: {str(e)}")
                st.code(error_details)
                logger.error(f"Backtesting error: {str(e)}\n{error_details}")

    # Advanced Technical Analysis page
    elif page == "Advanced Technical Analysis":
        # Use the Advanced Technical Analysis component
        show_advanced_technical_analysis()

    # SMC Analysis page
    elif page == "SMC Analysis":
        # Use the SMC Analysis component
        show_smc_analysis()

    # Live Trading page
    elif page == "Live Trading":
        # Use the integrated live trading component
        live_trading_component()

    # Portfolio page
    elif page == "Portfolio":
        # Use the portfolio management component
        portfolio_component()

    # Reports page
    elif page == "Reports":
        st.title("Reports and Analysis")
        # Use the reporting component
        reporting_component()





    # New consolidated pages
    elif page == "Stock Management":
        show_stock_management()

    elif page == "Predictions (New)":
        show_predictions_consolidated()

    elif page == "AI Assistant":
        show_ai_assistant()

    elif page == "TradingView Integration":
        show_tradingview_integration()



# End of main function


def plot_predictions_multi(historical_data, live_data=None, predictions=None, symbol="", horizon_unit="minutes"):
    """
    Plot historical data and multi-point predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon, where each prediction is a dict of {datetime: price}
        symbol (str): Stock symbol
        horizon_unit (str): Unit of time for horizons (minutes, days, weeks)
    """
    st.subheader("Price Forecast")

    # Create figure
    fig = go.Figure()

    # Add historical data
    fig.add_trace(go.Scatter(
        x=historical_data['Date'],
        y=historical_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    colors = ['red', 'orange', 'purple', 'brown', 'pink']

    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon (cycle through colors if needed)
        color = colors[i % len(colors)]

        # Extract times and prices from the prediction dictionary
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # Add multi-point predictions
        fig.add_trace(go.Scatter(
            x=pred_times,
            y=pred_prices,
            mode='lines+markers',
            name=f'{horizon} {horizon_unit}',
            line=dict(color=color, dash='dot'),
            marker=dict(color=color, size=8, symbol='circle')
        ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)

    # Add trend line between last actual price and predictions
    if live_data is not None and not live_data.empty:
        last_price = live_data['Close'].iloc[-1]
        last_time = live_data['Date'].iloc[-1]
    else:
        last_price = historical_data['Close'].iloc[-1]
        last_time = historical_data['Date'].iloc[-1]

    # Create trend lines
    fig2 = go.Figure()

    # Add last actual price point
    fig2.add_trace(go.Scatter(
        x=[last_time],
        y=[last_price],
        mode='markers',
        name='Last Actual',
        marker=dict(color='blue', size=10)
    ))

    # Add prediction lines
    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon
        color = colors[i % len(colors)]

        # Extract times and prices
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # Connect from last actual to first prediction and then show the trend line for all predictions
        all_x = [last_time] + pred_times
        all_y = [last_price] + pred_prices

        fig2.add_trace(go.Scatter(
            x=all_x,
            y=all_y,
            mode='lines+markers',
            name=f'{horizon} {horizon_unit}',
            line=dict(color=color, dash='dot'),
            marker=dict(color=color, size=8)
        ))

    # Update layout
    fig2.update_layout(
        title=f'{symbol} Prediction Trends',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig2, use_container_width=True)

if __name__ == "__main__":
    try:
        # Initialize session state
        initialize_session_state()

        # Start performance tracking
        start_time = time.time()

        # Run the main application
        main()

        # Track performance
        end_time = time.time()
        track_performance('app_run', end_time - start_time)

    except Exception as e:
        # Track error
        error_message = str(e)
        track_error(error_message, error_type="app_crash", details={"traceback": traceback.format_exc()})

        # Log error
        logger.error(f"Application crashed: {error_message}")

        # Show error to user
        st.error(f"An error occurred: {error_message}")

        # Clean up resources
        cleanup_memory()

    finally:
        # Ensure resources are cleaned up
        cleanup_memory()
