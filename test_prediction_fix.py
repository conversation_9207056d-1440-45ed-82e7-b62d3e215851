"""
Quick test for prediction fix
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_prediction_result():
    """Test PredictionResult creation"""
    try:
        from app.components.predictive_analytics import PredictionResult
        from datetime import datetime
        
        # Test creating a PredictionResult with correct parameters
        prediction = PredictionResult(
            prediction_type='technical_analysis',
            time_horizon='1W',
            predicted_price=85000.0,
            confidence=0.7,
            probability_up=0.6,
            probability_down=0.4,
            target_levels=[85000, 86000, 84000],
            risk_level='medium',
            model_used='Technical Analysis',
            prediction_date=datetime.now(),
            expected_accuracy=0.6
        )
        
        print("✅ PredictionResult creation successful!")
        print(f"   Price: {prediction.predicted_price}")
        print(f"   Confidence: {prediction.confidence}")
        print(f"   Model: {prediction.model_used}")
        
        return True
        
    except Exception as e:
        print(f"❌ PredictionResult test failed: {str(e)}")
        return False

def test_fallback_predictions():
    """Test fallback predictions"""
    try:
        from app.components.predictive_analytics import PredictiveAnalytics
        
        predictor = PredictiveAnalytics()
        
        # Test fallback predictions
        smc_results = {
            'confluence': {'total_score': 0.7},
            'market_structure': {'trend': 'bullish'}
        }
        
        predictions = predictor._generate_fallback_predictions(82500.0, '1W', smc_results)
        
        print("✅ Fallback predictions successful!")
        print(f"   Generated: {len(predictions)} predictions")
        
        if predictions:
            pred = predictions[0]
            print(f"   Price: {pred.predicted_price}")
            print(f"   Confidence: {pred.confidence}")
            print(f"   Prob Up: {pred.probability_up}")
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback predictions test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Quick Prediction Fix Test")
    print("=" * 40)
    
    success1 = test_prediction_result()
    success2 = test_fallback_predictions()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ PredictionResult parameters fixed")
        print("✅ Fallback predictions working")
        print("\n🚀 Predictive Analytics should now work!")
    else:
        print("\n❌ Some tests failed")
