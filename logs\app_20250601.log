2025-06-01 00:04:10,173 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 00:04:10,858 - app - INFO - Memory management utilities loaded
2025-06-01 00:04:10,860 - app - INFO - Error handling utilities loaded
2025-06-01 00:04:10,861 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 00:04:10,862 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 00:04:10,863 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 00:04:10,864 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 00:04:10,865 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 00:04:10,867 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 00:04:10,867 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 00:04:10,868 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 00:04:10,868 - app - INFO - Applied NumPy fix
2025-06-01 00:04:10,869 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:04:10,869 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:04:10,871 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 00:04:14,518 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 00:04:14,518 - app - INFO - Applied TensorFlow fix
2025-06-01 00:04:14,521 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 00:04:15,715 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 00:04:15,715 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 00:04:43,258 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:04:43,278 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-01 00:04:43,281 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 00:04:43,283 - app.utils.common - INFO - Data shape: (581, 36)
2025-06-01 00:04:43,285 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-01 00:04:43,286 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 00:04:44,471 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 00:04:44,472 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 00:05:10,410 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:05:10,415 - app - INFO - Cleaning up resources...
2025-06-01 00:05:10,420 - app.utils.memory_management - INFO - Memory before cleanup: 316.86 MB
2025-06-01 00:05:10,530 - app.utils.memory_management - INFO - Garbage collection: collected 76 objects
2025-06-01 00:05:10,531 - app.utils.memory_management - INFO - Memory after cleanup: 316.87 MB (freed -0.01 MB)
2025-06-01 00:05:10,531 - app - INFO - Application shutdown complete
2025-06-01 00:31:45,526 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 00:31:47,063 - app - INFO - Memory management utilities loaded
2025-06-01 00:31:47,066 - app - INFO - Error handling utilities loaded
2025-06-01 00:31:47,067 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 00:31:47,069 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 00:31:47,069 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 00:31:47,069 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 00:31:47,070 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 00:31:47,071 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 00:31:47,071 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 00:31:47,071 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 00:31:47,072 - app - INFO - Applied NumPy fix
2025-06-01 00:31:47,073 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:31:47,074 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:31:47,074 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:31:47,075 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 00:31:47,075 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:31:47,075 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:31:47,076 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:31:47,076 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 00:31:51,399 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 00:31:51,422 - app - INFO - Applied TensorFlow fix
2025-06-01 00:31:58,655 - app - INFO - Cleaning up resources...
2025-06-01 00:31:58,660 - app.utils.memory_management - INFO - Memory before cleanup: 319.45 MB
2025-06-01 00:31:58,775 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-01 00:31:58,775 - app.utils.memory_management - INFO - Memory after cleanup: 319.87 MB (freed -0.42 MB)
2025-06-01 00:31:58,777 - app - INFO - Application shutdown complete
2025-06-01 10:28:35,191 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 10:28:44,896 - app - INFO - Memory management utilities loaded
2025-06-01 10:28:44,964 - app - INFO - Error handling utilities loaded
2025-06-01 10:28:45,011 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 10:28:45,013 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 10:28:45,015 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 10:28:45,015 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 10:28:45,100 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 10:28:45,100 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 10:28:45,102 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 10:28:45,104 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 10:28:45,104 - app - INFO - Applied NumPy fix
2025-06-01 10:28:45,126 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 10:28:45,132 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 10:28:45,136 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 10:28:45,146 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 10:28:45,150 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 10:28:45,152 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 10:28:45,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 10:28:45,167 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 10:29:13,664 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 10:29:13,676 - app - INFO - Applied TensorFlow fix
2025-06-01 10:29:13,703 - app.config - INFO - Configuration initialized
2025-06-01 10:29:13,744 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-01 10:29:14,135 - models.train - INFO - TensorFlow test successful
2025-06-01 10:29:20,345 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-01 10:29:20,347 - models.train - INFO - Transformer model is available
2025-06-01 10:29:20,348 - models.train - INFO - Using TensorFlow-based models
2025-06-01 10:29:20,357 - models.predict - INFO - Transformer model is available for predictions
2025-06-01 10:29:20,359 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-01 10:29:20,408 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-01 10:29:23,624 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-01 10:29:23,627 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-01 10:29:23,630 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 10:29:23,632 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 10:29:23,651 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 10:29:23,652 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-01 10:29:23,653 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-01 10:29:23,654 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 10:29:23,654 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 10:29:23,654 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 10:29:25,049 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-01 10:29:25,078 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:26,837 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-01 10:29:31,677 - app.services.llm_service - INFO - llama_cpp is available
2025-06-01 10:29:31,769 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:29:31,779 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:29:33,256 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:29:33,277 - app.utils.memory_management - INFO - Memory before cleanup: 430.40 MB
2025-06-01 10:29:33,492 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 10:29:33,494 - app.utils.memory_management - INFO - Memory after cleanup: 430.40 MB (freed -0.00 MB)
2025-06-01 10:29:51,805 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:51,864 - app.utils.memory_management - INFO - Memory before cleanup: 434.44 MB
2025-06-01 10:29:52,203 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 10:29:52,207 - app.utils.memory_management - INFO - Memory after cleanup: 434.44 MB (freed 0.00 MB)
2025-06-01 10:29:52,911 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:53,245 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.23 seconds
2025-06-01 10:29:53,262 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 10:29:53,264 - app - INFO - Data shape: (581, 36)
2025-06-01 10:29:53,266 - app - INFO - File COMI contains 2025 data
2025-06-01 10:29:53,338 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-06-01 10:29:53,340 - app - INFO - Features shape: (581, 36)
2025-06-01 10:29:53,377 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-01 10:29:53,387 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 10:29:53,388 - app - INFO - Data shape: (581, 36)
2025-06-01 10:29:53,389 - app - INFO - File COMI contains 2025 data
2025-06-01 10:29:53,392 - app.utils.memory_management - INFO - Memory before cleanup: 438.01 MB
2025-06-01 10:29:53,655 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-01 10:29:53,655 - app.utils.memory_management - INFO - Memory after cleanup: 438.02 MB (freed -0.01 MB)
2025-06-01 10:29:53,879 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:54,121 - app.utils.memory_management - INFO - Memory before cleanup: 439.10 MB
2025-06-01 10:29:54,357 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-01 10:29:54,359 - app.utils.memory_management - INFO - Memory after cleanup: 439.11 MB (freed -0.00 MB)
2025-06-01 10:29:57,089 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:57,133 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:29:57,469 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:29:57,474 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-01 10:29:57,476 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:29:57,478 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-01 10:29:57,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:29:57,497 - app.utils.error_handling - INFO - live_trading_component executed in 0.38 seconds
2025-06-01 10:29:57,499 - app.utils.memory_management - INFO - Memory before cleanup: 441.40 MB
2025-06-01 10:29:57,739 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-01 10:29:57,742 - app.utils.memory_management - INFO - Memory after cleanup: 441.40 MB (freed 0.00 MB)
2025-06-01 10:30:00,774 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:30:02,110 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:30:02,111 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:30:21,840 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:30:21,843 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-01 10:30:21,844 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:30:21,861 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-01 10:30:21,878 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:30:21,889 - app.utils.error_handling - INFO - live_trading_component executed in 21.09 seconds
2025-06-01 10:30:21,903 - app.utils.memory_management - INFO - Memory before cleanup: 443.07 MB
2025-06-01 10:30:22,230 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-01 10:30:22,231 - app.utils.memory_management - INFO - Memory after cleanup: 443.07 MB (freed 0.00 MB)
2025-06-01 10:32:09,040 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:32:11,103 - app.utils.memory_management - INFO - Memory before cleanup: 437.02 MB
2025-06-01 10:32:11,310 - app.utils.memory_management - INFO - Garbage collection: collected 342 objects
2025-06-01 10:32:11,311 - app.utils.memory_management - INFO - Memory after cleanup: 437.10 MB (freed -0.08 MB)
2025-06-01 10:34:45,957 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:34:45,963 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:34:45,966 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:34:45,986 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:34:46,005 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-06-01 10:34:46,289 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-01 10:34:46,290 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-06-01 10:34:50,236 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:34:52,330 - app.utils.memory_management - INFO - Memory before cleanup: 438.84 MB
2025-06-01 10:34:52,563 - app.utils.memory_management - INFO - Garbage collection: collected 328 objects
2025-06-01 10:34:52,565 - app.utils.memory_management - INFO - Memory after cleanup: 438.84 MB (freed 0.00 MB)
2025-06-01 10:35:35,378 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:35:35,412 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:35:35,441 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:35:35,511 - app.utils.memory_management - INFO - Memory before cleanup: 438.63 MB
2025-06-01 10:35:35,781 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-01 10:35:35,783 - app.utils.memory_management - INFO - Memory after cleanup: 438.63 MB (freed 0.00 MB)
2025-06-01 10:35:38,466 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:35:38,616 - app.utils.memory_management - INFO - Memory before cleanup: 438.84 MB
2025-06-01 10:35:38,924 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-01 10:35:38,926 - app.utils.memory_management - INFO - Memory after cleanup: 438.84 MB (freed 0.00 MB)
2025-06-01 10:35:46,766 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:35:46,820 - app.utils.memory_management - INFO - Memory before cleanup: 438.84 MB
2025-06-01 10:35:47,075 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:35:47,075 - app.utils.memory_management - INFO - Memory after cleanup: 438.84 MB (freed 0.00 MB)
2025-06-01 10:35:48,289 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:36:05,061 - app.utils.memory_management - INFO - Memory before cleanup: 438.94 MB
2025-06-01 10:36:05,270 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:36:05,275 - app.utils.memory_management - INFO - Memory after cleanup: 438.94 MB (freed 0.00 MB)
2025-06-01 10:37:16,922 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:37:16,974 - app.utils.memory_management - INFO - Memory before cleanup: 438.93 MB
2025-06-01 10:37:17,208 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-06-01 10:37:17,208 - app.utils.memory_management - INFO - Memory after cleanup: 438.93 MB (freed 0.00 MB)
2025-06-01 10:37:18,674 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:37:18,699 - app.utils.memory_management - INFO - Memory before cleanup: 438.95 MB
2025-06-01 10:37:18,911 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:37:18,912 - app.utils.memory_management - INFO - Memory after cleanup: 438.95 MB (freed 0.00 MB)
2025-06-01 10:37:19,095 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:37:19,125 - app.utils.memory_management - INFO - Memory before cleanup: 438.96 MB
2025-06-01 10:37:19,346 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-01 10:37:19,347 - app.utils.memory_management - INFO - Memory after cleanup: 438.94 MB (freed 0.02 MB)
2025-06-01 10:37:25,011 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:37:31,059 - app.utils.memory_management - INFO - Memory before cleanup: 438.95 MB
2025-06-01 10:37:31,280 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-01 10:37:31,282 - app.utils.memory_management - INFO - Memory after cleanup: 438.95 MB (freed 0.00 MB)
2025-06-01 10:37:45,156 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:37:45,179 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:37:45,193 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:37:45,195 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-01 10:37:45,198 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:37:45,200 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-01 10:37:45,211 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:37:45,215 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-06-01 10:37:45,216 - app.utils.memory_management - INFO - Memory before cleanup: 438.95 MB
2025-06-01 10:37:45,435 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-06-01 10:37:45,436 - app.utils.memory_management - INFO - Memory after cleanup: 438.95 MB (freed 0.00 MB)
2025-06-01 10:37:47,451 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:37:48,615 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:37:48,615 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:38:12,783 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:12,783 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-01 10:38:12,784 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:12,788 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-01 10:38:12,794 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:38:12,799 - app.utils.error_handling - INFO - live_trading_component executed in 25.33 seconds
2025-06-01 10:38:12,802 - app.utils.memory_management - INFO - Memory before cleanup: 439.00 MB
2025-06-01 10:38:13,073 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-01 10:38:13,074 - app.utils.memory_management - INFO - Memory after cleanup: 439.00 MB (freed 0.00 MB)
2025-06-01 10:38:32,530 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:38:32,610 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:32,614 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:32,625 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:32,660 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:32,662 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:32,664 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-01 10:38:32,665 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:38:32,682 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:32,691 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:32,698 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:32,728 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-01 10:38:32,746 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-06-01 10:38:32,748 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-01 10:38:32,749 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-01 10:38:32,760 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-01 10:38:32,776 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:32,787 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-01 10:38:32,789 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-06-01 10:38:32,791 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-01 10:38:32,792 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-01 10:38:32,793 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-01 10:38:32,810 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:32,813 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-01 10:38:32,815 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-06-01 10:38:32,815 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:32,816 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:32,824 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:32,831 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:32,832 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:32,838 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-01 10:38:32,897 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-01 10:38:32,915 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:32,924 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:32,943 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:32,963 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-01 10:38:32,964 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-01 10:38:32,965 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-01 10:38:32,975 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-01 10:38:32,977 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-01 10:38:32,978 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-01 10:38:32,981 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-01 10:38:32,982 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-01 10:38:32,993 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-01 10:38:32,995 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-01 10:38:32,997 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-01 10:38:32,999 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-01 10:38:33,011 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-01 10:38:33,012 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-01 10:38:33,014 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-01 10:38:33,028 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-01 10:38:33,030 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-01 10:38:33,031 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-01 10:38:33,032 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-01 10:38:33,443 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-01 10:38:33,528 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:33,529 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:33,531 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:33,547 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:33,550 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:33,560 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:38:33,578 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:33,579 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:33,595 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:33,612 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-01 10:38:33,613 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-01 10:38:33,615 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-01 10:38:33,616 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-01 10:38:33,648 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:33,663 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-01 10:38:33,666 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-01 10:38:33,680 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-01 10:38:33,698 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-01 10:38:33,728 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:33,731 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-01 10:38:33,733 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:33,743 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:33,744 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:33,760 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:33,760 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:33,778 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:33,796 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:33,798 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-01 10:38:33,807 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-01 10:38:33,809 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:33,848 - app.utils.memory_management - INFO - Memory before cleanup: 439.14 MB
2025-06-01 10:38:34,264 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-01 10:38:34,265 - app.utils.memory_management - INFO - Memory after cleanup: 439.14 MB (freed 0.00 MB)
2025-06-01 10:38:47,845 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:38:47,905 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-01 10:38:47,916 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:47,919 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:47,930 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:47,943 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-01 10:38:47,945 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-01 10:38:47,947 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-01 10:38:47,949 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-01 10:38:47,949 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-01 10:38:47,950 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-01 10:38:47,952 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-01 10:38:47,953 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-01 10:38:47,954 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-01 10:38:47,955 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-01 10:38:48,238 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-01 10:38:48,280 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:48,281 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:48,282 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:48,292 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,293 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:48,293 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:38:48,298 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:48,299 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:48,305 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,311 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-01 10:38:48,312 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-01 10:38:48,313 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-01 10:38:48,313 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-01 10:38:48,321 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,324 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-01 10:38:48,325 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-01 10:38:48,325 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-01 10:38:48,325 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-01 10:38:48,331 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,332 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-01 10:38:48,333 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:48,333 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:48,334 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:48,341 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,342 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:48,358 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,377 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,385 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,393 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:48,409 - app.utils.memory_management - INFO - Memory before cleanup: 440.24 MB
2025-06-01 10:38:48,655 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 10:38:48,657 - app.utils.memory_management - INFO - Memory after cleanup: 440.24 MB (freed 0.00 MB)
2025-06-01 10:38:56,142 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:38:56,221 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-01 10:38:56,228 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:56,229 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:56,235 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,242 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-01 10:38:56,243 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-01 10:38:56,243 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-01 10:38:56,244 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-01 10:38:56,244 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-01 10:38:56,245 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-01 10:38:56,247 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-01 10:38:56,247 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-01 10:38:56,248 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-01 10:38:56,249 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-01 10:38:56,450 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-01 10:38:56,492 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:56,493 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:56,494 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:56,502 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,502 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:56,503 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:38:56,512 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-01 10:38:56,512 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-01 10:38:56,524 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,530 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-01 10:38:56,531 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-01 10:38:56,531 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-01 10:38:56,532 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-01 10:38:56,539 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,540 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-01 10:38:56,540 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-01 10:38:56,541 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-01 10:38:56,541 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-01 10:38:56,547 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,548 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-01 10:38:56,549 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:38:56,549 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-01 10:38:56,550 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:38:56,555 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,556 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-01 10:38:56,572 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,581 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,587 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,593 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,599 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-01 10:38:56,606 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:38:57,829 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:38:57,829 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:39:23,684 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 10:39:23,720 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-01 10:39:23,904 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-06-01 10:39:24,061 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-01 10:39:24,070 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-06-01 10:39:24,072 - models.hybrid_model - INFO - XGBoost is available
2025-06-01 10:39:24,073 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-06-01 10:39:24,074 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-06-01 10:39:24,074 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-06-01 10:39:24,075 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-06-01 10:39:24,076 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-06-01 10:39:24,076 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-06-01 10:39:24,076 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-06-01 10:39:24,160 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-06-01 10:39:24,191 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-01 10:39:24,192 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-01 10:39:24,192 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-01 10:39:24,197 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-01 10:39:24,197 - models.predict - INFO - Current price: 80.72, Predicted scaled value: 0.6977810347080231
2025-06-01 10:39:24,198 - models.predict - INFO - Prediction for 60 minutes horizon: 79.93840070199965
2025-06-01 10:39:24,202 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:39:25,363 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:39:25,364 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:39:46,799 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 10:39:46,830 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-01 10:39:47,023 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-06-01 10:39:47,193 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-01 10:39:47,196 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-01 10:39:47,197 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-06-01 10:39:47,197 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-06-01 10:39:47,197 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-06-01 10:39:47,211 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:39:48,555 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:39:48,555 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:40:14,386 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 10:40:14,422 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-01 10:40:14,616 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-06-01 10:40:14,767 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-01 10:40:14,769 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-06-01 10:40:14,769 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-06-01 10:40:14,770 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-06-01 10:40:14,770 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-06-01 10:40:14,771 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-06-01 10:40:14,772 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-06-01 10:40:14,773 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-06-01 10:40:14,781 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-06-01 10:40:14,786 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-01 10:40:14,804 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-01 10:40:14,804 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-01 10:40:14,805 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-01 10:40:14,806 - models.predict - INFO - Current price: 80.75, Predicted scaled value: 0.6974384807872603
2025-06-01 10:40:14,807 - models.predict - INFO - Prediction for 60 minutes horizon: 79.91962874714187
2025-06-01 10:40:14,811 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:40:15,968 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:40:15,969 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:40:40,612 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 10:40:40,644 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-01 10:40:40,818 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-06-01 10:40:40,970 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-01 10:40:40,971 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-06-01 10:40:40,971 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-06-01 10:40:40,972 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-06-01 10:40:40,972 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-06-01 10:40:40,973 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-06-01 10:40:40,974 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-06-01 10:40:40,974 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-06-01 10:40:40,974 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-06-01 10:40:40,975 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-01 10:40:40,976 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-06-01 10:40:40,976 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-01 10:40:40,984 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-01 10:40:40,985 - models.predict - INFO - Current price: 80.75, Predicted scaled value: 0.7262229230360004
2025-06-01 10:40:40,985 - models.predict - INFO - Prediction for 60 minutes horizon: 81.49701618237282
2025-06-01 10:40:40,988 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:40:42,139 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:40:42,139 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:41:09,183 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 10:41:09,228 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-01 10:41:09,624 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-06-01 10:41:09,827 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-01 10:41:09,829 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-06-01 10:41:09,831 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-06-01 10:41:09,834 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-06-01 10:41:09,835 - models.predict - INFO - Created fallback ensemble model with base price: 80.76
2025-06-01 10:41:09,835 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-06-01 10:41:09,835 - models.predict - INFO - Ensemble model already loaded
2025-06-01 10:41:09,855 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-06-01 10:41:09,856 - models.predict - INFO - Current price: 80.76, Predicted scaled value: 82.070169688442
2025-06-01 10:41:09,857 - models.predict - WARNING - Prediction 4539.145298926621 is too far from current price 80.76, using fallback
2025-06-01 10:41:09,857 - models.predict - INFO - Prediction for 60 minutes horizon: 80.89405215903523
2025-06-01 10:41:09,889 - app.utils.memory_management - INFO - Memory before cleanup: 447.55 MB
2025-06-01 10:41:10,122 - app.utils.memory_management - INFO - Garbage collection: collected 9 objects
2025-06-01 10:41:10,123 - app.utils.memory_management - INFO - Memory after cleanup: 447.55 MB (freed 0.00 MB)
2025-06-01 10:41:50,593 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:41:50,623 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:41:50,644 - app.utils.memory_management - INFO - Memory before cleanup: 447.52 MB
2025-06-01 10:41:50,861 - app.utils.memory_management - INFO - Garbage collection: collected 355 objects
2025-06-01 10:41:50,863 - app.utils.memory_management - INFO - Memory after cleanup: 447.52 MB (freed 0.00 MB)
2025-06-01 10:41:54,018 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:42:02,428 - app.utils.memory_management - INFO - Memory before cleanup: 447.51 MB
2025-06-01 10:42:02,692 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-01 10:42:02,693 - app.utils.memory_management - INFO - Memory after cleanup: 447.51 MB (freed 0.00 MB)
2025-06-01 10:47:14,931 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:14,958 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:47:14,984 - app.utils.memory_management - INFO - Memory before cleanup: 439.33 MB
2025-06-01 10:47:15,236 - app.utils.memory_management - INFO - Garbage collection: collected 207 objects
2025-06-01 10:47:15,237 - app.utils.memory_management - INFO - Memory after cleanup: 439.33 MB (freed 0.00 MB)
2025-06-01 10:47:25,877 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 10:47:26,673 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:26,685 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:47:26,689 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:47:26,721 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:47:26,750 - app.utils.memory_management - INFO - Memory before cleanup: 439.35 MB
2025-06-01 10:47:26,986 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-06-01 10:47:26,988 - app.utils.memory_management - INFO - Memory after cleanup: 439.35 MB (freed 0.00 MB)
2025-06-01 10:47:29,454 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:29,480 - app.utils.memory_management - INFO - Memory before cleanup: 439.33 MB
2025-06-01 10:47:29,688 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-01 10:47:29,690 - app.utils.memory_management - INFO - Memory after cleanup: 439.33 MB (freed 0.00 MB)
2025-06-01 10:47:31,209 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:31,234 - app.utils.memory_management - INFO - Memory before cleanup: 439.33 MB
2025-06-01 10:47:31,414 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-01 10:47:31,416 - app.utils.memory_management - INFO - Memory after cleanup: 439.33 MB (freed 0.00 MB)
2025-06-01 10:47:32,213 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:32,254 - app.utils.memory_management - INFO - Memory before cleanup: 439.34 MB
2025-06-01 10:47:32,476 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-01 10:47:32,477 - app.utils.memory_management - INFO - Memory after cleanup: 439.34 MB (freed 0.00 MB)
2025-06-01 10:47:36,516 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:36,543 - app.utils.memory_management - INFO - Memory before cleanup: 439.34 MB
2025-06-01 10:47:36,782 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:47:36,783 - app.utils.memory_management - INFO - Memory after cleanup: 439.34 MB (freed 0.00 MB)
2025-06-01 10:47:37,586 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:47:52,276 - app.utils.memory_management - INFO - Memory before cleanup: 439.34 MB
2025-06-01 10:47:52,527 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:47:52,529 - app.utils.memory_management - INFO - Memory after cleanup: 439.34 MB (freed 0.00 MB)
2025-06-01 10:50:13,971 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:50:16,025 - app.utils.memory_management - INFO - Memory before cleanup: 439.35 MB
2025-06-01 10:50:16,201 - app.utils.memory_management - INFO - Garbage collection: collected 324 objects
2025-06-01 10:50:16,202 - app.utils.memory_management - INFO - Memory after cleanup: 439.35 MB (freed 0.00 MB)
2025-06-01 10:51:26,656 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:51:26,662 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:51:26,663 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:51:26,680 - app.utils.memory_management - INFO - Memory before cleanup: 439.34 MB
2025-06-01 10:51:26,880 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-01 10:51:26,880 - app.utils.memory_management - INFO - Memory after cleanup: 439.34 MB (freed 0.00 MB)
2025-06-01 11:18:28,669 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 11:18:33,480 - app - INFO - Memory management utilities loaded
2025-06-01 11:18:33,482 - app - INFO - Error handling utilities loaded
2025-06-01 11:18:33,484 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 11:18:33,488 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 11:18:33,488 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 11:18:33,488 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 11:18:33,489 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 11:18:33,490 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 11:18:33,490 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 11:18:33,490 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 11:18:33,491 - app - INFO - Applied NumPy fix
2025-06-01 11:18:33,492 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 11:18:33,493 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 11:18:33,493 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 11:18:33,493 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 11:18:33,494 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 11:18:33,494 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 11:18:33,494 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 11:18:33,497 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 11:18:50,097 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 11:18:50,098 - app - INFO - Applied TensorFlow fix
2025-06-01 11:18:51,001 - app - INFO - Cleaning up resources...
2025-06-01 11:18:51,009 - app.utils.memory_management - INFO - Memory before cleanup: 319.82 MB
2025-06-01 11:18:51,133 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-01 11:18:51,134 - app.utils.memory_management - INFO - Memory after cleanup: 320.20 MB (freed -0.38 MB)
2025-06-01 11:18:51,134 - app - INFO - Application shutdown complete
