2025-06-01 00:04:10,173 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 00:04:10,858 - app - INFO - Memory management utilities loaded
2025-06-01 00:04:10,860 - app - INFO - Error handling utilities loaded
2025-06-01 00:04:10,861 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 00:04:10,862 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 00:04:10,863 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 00:04:10,864 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 00:04:10,865 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 00:04:10,867 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 00:04:10,867 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 00:04:10,868 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 00:04:10,868 - app - INFO - Applied NumPy fix
2025-06-01 00:04:10,869 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:04:10,869 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:04:10,871 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 00:04:14,518 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 00:04:14,518 - app - INFO - Applied TensorFlow fix
2025-06-01 00:04:14,521 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 00:04:15,715 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 00:04:15,715 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 00:04:43,258 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:04:43,278 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-01 00:04:43,281 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 00:04:43,283 - app.utils.common - INFO - Data shape: (581, 36)
2025-06-01 00:04:43,285 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-01 00:04:43,286 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 00:04:44,471 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 00:04:44,472 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 00:05:10,410 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:05:10,415 - app - INFO - Cleaning up resources...
2025-06-01 00:05:10,420 - app.utils.memory_management - INFO - Memory before cleanup: 316.86 MB
2025-06-01 00:05:10,530 - app.utils.memory_management - INFO - Garbage collection: collected 76 objects
2025-06-01 00:05:10,531 - app.utils.memory_management - INFO - Memory after cleanup: 316.87 MB (freed -0.01 MB)
2025-06-01 00:05:10,531 - app - INFO - Application shutdown complete
2025-06-01 00:31:45,526 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 00:31:47,063 - app - INFO - Memory management utilities loaded
2025-06-01 00:31:47,066 - app - INFO - Error handling utilities loaded
2025-06-01 00:31:47,067 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 00:31:47,069 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 00:31:47,069 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 00:31:47,069 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 00:31:47,070 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 00:31:47,071 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 00:31:47,071 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 00:31:47,071 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 00:31:47,072 - app - INFO - Applied NumPy fix
2025-06-01 00:31:47,073 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:31:47,074 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:31:47,074 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:31:47,075 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 00:31:47,075 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:31:47,075 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:31:47,076 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:31:47,076 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 00:31:51,399 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 00:31:51,422 - app - INFO - Applied TensorFlow fix
2025-06-01 00:31:58,655 - app - INFO - Cleaning up resources...
2025-06-01 00:31:58,660 - app.utils.memory_management - INFO - Memory before cleanup: 319.45 MB
2025-06-01 00:31:58,775 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-01 00:31:58,775 - app.utils.memory_management - INFO - Memory after cleanup: 319.87 MB (freed -0.42 MB)
2025-06-01 00:31:58,777 - app - INFO - Application shutdown complete
2025-06-01 10:28:35,191 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 10:28:44,896 - app - INFO - Memory management utilities loaded
2025-06-01 10:28:44,964 - app - INFO - Error handling utilities loaded
2025-06-01 10:28:45,011 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 10:28:45,013 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 10:28:45,015 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 10:28:45,015 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 10:28:45,100 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 10:28:45,100 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 10:28:45,102 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 10:28:45,104 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 10:28:45,104 - app - INFO - Applied NumPy fix
2025-06-01 10:28:45,126 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 10:28:45,132 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 10:28:45,136 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 10:28:45,146 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 10:28:45,150 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 10:28:45,152 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 10:28:45,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 10:28:45,167 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 10:29:13,664 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 10:29:13,676 - app - INFO - Applied TensorFlow fix
2025-06-01 10:29:13,703 - app.config - INFO - Configuration initialized
2025-06-01 10:29:13,744 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-01 10:29:14,135 - models.train - INFO - TensorFlow test successful
2025-06-01 10:29:20,345 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-01 10:29:20,347 - models.train - INFO - Transformer model is available
2025-06-01 10:29:20,348 - models.train - INFO - Using TensorFlow-based models
2025-06-01 10:29:20,357 - models.predict - INFO - Transformer model is available for predictions
2025-06-01 10:29:20,359 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-01 10:29:20,408 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-01 10:29:23,624 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-01 10:29:23,627 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-01 10:29:23,630 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 10:29:23,632 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 10:29:23,651 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 10:29:23,652 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-01 10:29:23,653 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-01 10:29:23,654 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 10:29:23,654 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 10:29:23,654 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 10:29:25,049 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-01 10:29:25,078 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:26,837 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-01 10:29:31,677 - app.services.llm_service - INFO - llama_cpp is available
2025-06-01 10:29:31,769 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:29:31,779 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:29:33,256 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:29:33,277 - app.utils.memory_management - INFO - Memory before cleanup: 430.40 MB
2025-06-01 10:29:33,492 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 10:29:33,494 - app.utils.memory_management - INFO - Memory after cleanup: 430.40 MB (freed -0.00 MB)
2025-06-01 10:29:51,805 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:51,864 - app.utils.memory_management - INFO - Memory before cleanup: 434.44 MB
2025-06-01 10:29:52,203 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 10:29:52,207 - app.utils.memory_management - INFO - Memory after cleanup: 434.44 MB (freed 0.00 MB)
2025-06-01 10:29:52,911 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:53,245 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.23 seconds
2025-06-01 10:29:53,262 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 10:29:53,264 - app - INFO - Data shape: (581, 36)
2025-06-01 10:29:53,266 - app - INFO - File COMI contains 2025 data
2025-06-01 10:29:53,338 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-06-01 10:29:53,340 - app - INFO - Features shape: (581, 36)
2025-06-01 10:29:53,377 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-01 10:29:53,387 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 10:29:53,388 - app - INFO - Data shape: (581, 36)
2025-06-01 10:29:53,389 - app - INFO - File COMI contains 2025 data
2025-06-01 10:29:53,392 - app.utils.memory_management - INFO - Memory before cleanup: 438.01 MB
2025-06-01 10:29:53,655 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-01 10:29:53,655 - app.utils.memory_management - INFO - Memory after cleanup: 438.02 MB (freed -0.01 MB)
2025-06-01 10:29:53,879 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:54,121 - app.utils.memory_management - INFO - Memory before cleanup: 439.10 MB
2025-06-01 10:29:54,357 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-01 10:29:54,359 - app.utils.memory_management - INFO - Memory after cleanup: 439.11 MB (freed -0.00 MB)
2025-06-01 10:29:57,089 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:29:57,133 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 10:29:57,469 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:29:57,474 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-01 10:29:57,476 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:29:57,478 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-01 10:29:57,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:29:57,497 - app.utils.error_handling - INFO - live_trading_component executed in 0.38 seconds
2025-06-01 10:29:57,499 - app.utils.memory_management - INFO - Memory before cleanup: 441.40 MB
2025-06-01 10:29:57,739 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-01 10:29:57,742 - app.utils.memory_management - INFO - Memory after cleanup: 441.40 MB (freed 0.00 MB)
2025-06-01 10:30:00,774 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:30:02,110 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 10:30:02,111 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 10:30:21,840 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-01 10:30:21,843 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-01 10:30:21,844 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-01 10:30:21,861 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-01 10:30:21,878 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-01 10:30:21,889 - app.utils.error_handling - INFO - live_trading_component executed in 21.09 seconds
2025-06-01 10:30:21,903 - app.utils.memory_management - INFO - Memory before cleanup: 443.07 MB
2025-06-01 10:30:22,230 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-01 10:30:22,231 - app.utils.memory_management - INFO - Memory after cleanup: 443.07 MB (freed 0.00 MB)
2025-06-01 10:32:09,040 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:32:11,103 - app.utils.memory_management - INFO - Memory before cleanup: 437.02 MB
2025-06-01 10:32:11,310 - app.utils.memory_management - INFO - Garbage collection: collected 342 objects
2025-06-01 10:32:11,311 - app.utils.memory_management - INFO - Memory after cleanup: 437.10 MB (freed -0.08 MB)
2025-06-01 10:34:45,957 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:34:45,963 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:34:45,966 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:34:45,986 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 10:34:46,005 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-06-01 10:34:46,289 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-01 10:34:46,290 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-06-01 10:34:50,236 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:34:52,330 - app.utils.memory_management - INFO - Memory before cleanup: 438.84 MB
2025-06-01 10:34:52,563 - app.utils.memory_management - INFO - Garbage collection: collected 328 objects
2025-06-01 10:34:52,565 - app.utils.memory_management - INFO - Memory after cleanup: 438.84 MB (freed 0.00 MB)
2025-06-01 10:35:35,378 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:35:35,412 - app.utils.session_state - INFO - Initializing session state
2025-06-01 10:35:35,441 - app.utils.session_state - INFO - Session state initialized
2025-06-01 10:35:35,511 - app.utils.memory_management - INFO - Memory before cleanup: 438.63 MB
2025-06-01 10:35:35,781 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-01 10:35:35,783 - app.utils.memory_management - INFO - Memory after cleanup: 438.63 MB (freed 0.00 MB)
2025-06-01 10:35:38,466 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:35:38,616 - app.utils.memory_management - INFO - Memory before cleanup: 438.84 MB
2025-06-01 10:35:38,924 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-01 10:35:38,926 - app.utils.memory_management - INFO - Memory after cleanup: 438.84 MB (freed 0.00 MB)
2025-06-01 10:35:46,766 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:35:46,820 - app.utils.memory_management - INFO - Memory before cleanup: 438.84 MB
2025-06-01 10:35:47,075 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:35:47,075 - app.utils.memory_management - INFO - Memory after cleanup: 438.84 MB (freed 0.00 MB)
2025-06-01 10:35:48,289 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 10:36:05,061 - app.utils.memory_management - INFO - Memory before cleanup: 438.94 MB
2025-06-01 10:36:05,270 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-01 10:36:05,275 - app.utils.memory_management - INFO - Memory after cleanup: 438.94 MB (freed 0.00 MB)
