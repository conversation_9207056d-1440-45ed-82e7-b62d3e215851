2025-06-01 00:04:10,173 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 00:04:10,858 - app - INFO - Memory management utilities loaded
2025-06-01 00:04:10,860 - app - INFO - Error handling utilities loaded
2025-06-01 00:04:10,861 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 00:04:10,862 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 00:04:10,863 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 00:04:10,864 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 00:04:10,865 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-01 00:04:10,867 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-01 00:04:10,867 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-01 00:04:10,868 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 00:04:10,868 - app - INFO - Applied NumPy fix
2025-06-01 00:04:10,869 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:04:10,869 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:04:10,870 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:04:10,871 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 00:04:14,518 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 00:04:14,518 - app - INFO - Applied TensorFlow fix
2025-06-01 00:04:14,521 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 00:04:15,715 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 00:04:15,715 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 00:04:43,258 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:04:43,278 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-01 00:04:43,281 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 00:04:43,283 - app.utils.common - INFO - Data shape: (581, 36)
2025-06-01 00:04:43,285 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-01 00:04:43,286 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-01 00:04:44,471 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-01 00:04:44,472 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-01 00:05:10,410 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:05:10,415 - app - INFO - Cleaning up resources...
2025-06-01 00:05:10,420 - app.utils.memory_management - INFO - Memory before cleanup: 316.86 MB
2025-06-01 00:05:10,530 - app.utils.memory_management - INFO - Garbage collection: collected 76 objects
2025-06-01 00:05:10,531 - app.utils.memory_management - INFO - Memory after cleanup: 316.87 MB (freed -0.01 MB)
2025-06-01 00:05:10,531 - app - INFO - Application shutdown complete
