import asyncio
import requests
import pandas as pd
from datetime import datetime
import time
import random
import logging
import os
import re
from typing import Dict, List, Any, Optional, Union

# Import the advanced scraper
from .advanced_scraper import (
    scrape_multiple_pairs, 
    FinancialDTO, 
    TimeInterval, 
    PLAYWRIGHT_AVAILABLE
)

# Import error handling utilities
try:
    from app.utils.error_handling import (
        ScrapingError,
        ResourceError,
        ConfigError,
        APIError,
        ErrorHandler
    )
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    ERROR_HANDLING_AVAILABLE = False
    logging.warning("Error handling utilities not available. Using basic error handling.")

    # Define fallback error classes if not available
    class ScrapingError(Exception):
        pass

    class ResourceError(Exception):
        pass

    class ConfigError(Exception):
        pass

    class APIError(Exception):
        pass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PriceScraper:
    """
    Advanced scraper for getting comprehensive stock data from TradingView
    Uses Playwright for modern, efficient scraping with technical analysis data
    """

    def __init__(self, source="tradingview", use_real_time=False, username=None, password=None, tradingview_email=None, tradingview_password=None, is_gmail=False):
        """
        Initialize PriceScraper with advanced capabilities

        Args:
            source (str): Source for price data (tradingview, mubasher, api)
            use_real_time (bool): Whether to use real-time data (legacy parameter)
            username (str): Legacy parameter for backwards compatibility
            password (str): Legacy parameter for backwards compatibility
            tradingview_email (str): TradingView email/username (legacy)
            tradingview_password (str): TradingView password (legacy)
            is_gmail (bool): Whether the TradingView account uses Gmail login (legacy)
        """
        self.source = source.lower()
        self.use_real_time = use_real_time
        
        # Legacy support for backwards compatibility
        self.tradingview_email = tradingview_email or username
        self.tradingview_password = tradingview_password or password
        self.is_logged_in = False
        self.is_gmail = is_gmail

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
        }

        # For backwards compatibility - no longer used but kept for existing code
        self.driver = None
        
        # API endpoint for advanced scraper
        self.api_endpoint = "http://127.0.0.1:8000/api/scrape_pairs"
        
        logger.info(f"Initialized PriceScraper with source: {self.source}")
        if not PLAYWRIGHT_AVAILABLE and self.source == "tradingview":
            logger.warning("Playwright not available. Advanced features will be limited.")
            logger.info("Install with: pip install playwright && playwright install chromium")

    def __del__(self):
        """
        Destructor - no longer needed for Playwright but kept for compatibility
        """
        pass

    def close(self):
        """
        Close method - no longer needed for Playwright but kept for compatibility
        """
        pass

    def __enter__(self):
        """
        Context manager entry
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Context manager exit
        """
        pass

    def format_symbol_for_tradingview(self, symbol):
        """
        Format symbol for TradingView (EGX stocks)
        
        Args:
            symbol (str): Stock symbol (e.g., 'COMI', 'CIB')
            
        Returns:
            str: Formatted symbol for TradingView (e.g., 'EGX-COMI')
        """
        if symbol.upper().startswith('EGX-'):
            return symbol.upper()
        return f"EGX-{symbol.upper()}"

    async def get_advanced_data(self, symbols, intervals=None):
        """
        Get comprehensive financial data using the advanced scraper
        
        Args:
            symbols (list): List of stock symbols
            intervals (list): List of time intervals (default: ['1D', '1W'])
            
        Returns:
            dict: Comprehensive financial data with technical indicators
        """
        if not PLAYWRIGHT_AVAILABLE:
            logger.error("Playwright not available. Cannot get advanced data.")
            return {}
            
        if intervals is None:
            intervals = ['1D', '1W']  # Default intervals for EGX
            
        # Format symbols for TradingView
        formatted_symbols = [self.format_symbol_for_tradingview(symbol) for symbol in symbols]
        
        try:
            logger.info(f"Getting advanced data for symbols: {formatted_symbols} with intervals: {intervals}")
            result = await scrape_multiple_pairs(formatted_symbols, intervals)
            return result
        except Exception as e:
            logger.error(f"Error getting advanced data: {str(e)}")
            return {}

    def get_advanced_data_sync(self, symbols, intervals=None):
        """
        Synchronous wrapper for get_advanced_data

        Args:
            symbols (list): List of stock symbols
            intervals (list): List of time intervals (default: ['1D', '1W'])

        Returns:
            dict: Comprehensive financial data with technical indicators
        """
        try:
            # Check if we're already in an event loop
            try:
                current_loop = asyncio.get_running_loop()
                # If we're in a loop, we can't use run_until_complete
                # Instead, we'll return empty data and log a warning
                logger.warning("Already in event loop, cannot run sync wrapper. Use async version instead.")
                return {}
            except RuntimeError:
                # No event loop running, we can create one
                pass

            # Run the async function in a new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.get_advanced_data(symbols, intervals))
                return result
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error in sync wrapper: {str(e)}")
            return {}

    def get_price(self, symbol, use_cache=True):
        """
        Get stock price using the advanced scraper (backwards compatible method)
        
        Args:
            symbol (str): Stock symbol
            use_cache (bool): Legacy parameter (ignored)
            
        Returns:
            dict: Price data with additional technical analysis
        """
        try:
            # Get comprehensive data for the symbol
            data = self.get_advanced_data_sync([symbol], ['1D'])
            
            if not data:
                return self._generate_sample_price(symbol)
                
            formatted_symbol = self.format_symbol_for_tradingview(symbol)
            
            if formatted_symbol in data and data[formatted_symbol]:
                financial_data = data[formatted_symbol][0]  # Get first interval data
                
                # Extract price and additional data
                price = financial_data.price
                
                # Get technical indicators summary
                oscillators = financial_data.oscillators
                moving_averages = financial_data.moving_averages
                
                # Count buy/sell signals
                buy_signals = sum(1 for ind in oscillators + moving_averages if ind.action == "Buy")
                sell_signals = sum(1 for ind in oscillators + moving_averages if ind.action == "Sell")
                neutral_signals = sum(1 for ind in oscillators + moving_averages if ind.action == "Neutral")
                
                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView Advanced',
                    'real_time': True,
                    'technical_analysis': {
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'neutral_signals': neutral_signals,
                        'total_indicators': len(oscillators) + len(moving_averages)
                    },
                    'oscillators_count': len(oscillators),
                    'moving_averages_count': len(moving_averages),
                    'pivots_count': len(financial_data.pivots)
                }
            else:
                return self._generate_sample_price(symbol)
                
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {str(e)}")
            return self._generate_sample_price(symbol)

    def _generate_sample_price(self, symbol):
        """
        Generate sample price data for testing/fallback
        """
        base_prices = {
            'COMI': 82.5,
            'CIB': 45.2,
            'ETEL': 18.7,
            'HRHO': 12.3,
            'OCDI': 156.8
        }
        
        base_price = base_prices.get(symbol.upper(), 50.0)
        variation = random.uniform(-0.05, 0.05)
        current_price = round(base_price * (1 + variation), 2)
        
        return {
            'symbol': symbol,
            'price': current_price,
            'currency': 'EGP',
            'timestamp': datetime.now().isoformat(),
            'source': 'Sample Data',
            'real_time': False,
            'technical_analysis': {
                'buy_signals': random.randint(3, 8),
                'sell_signals': random.randint(2, 6),
                'neutral_signals': random.randint(4, 10),
                'total_indicators': 20
            }
        }

    # Legacy methods for backwards compatibility
    def get_tradingview_price(self, symbol):
        """Legacy method - redirects to get_price"""
        return self.get_price(symbol)

    def get_mubasher_price(self, symbol):
        """Legacy method - redirects to get_price"""
        return self.get_price(symbol)

    def login_to_tradingview(self):
        """Legacy method - no longer needed"""
        logger.info("Login method called but not needed for advanced scraper")
        return True

    def is_logged_in_to_tradingview(self):
        """Legacy method - always returns True for advanced scraper"""
        return True

    def initialize_driver(self):
        """Legacy method - no longer needed"""
        logger.info("Driver initialization called but not needed for advanced scraper")

    def close_driver(self):
        """Legacy method - no longer needed"""
        logger.info("Driver close called but not needed for advanced scraper")

    # Property getters/setters for backward compatibility
    @property
    def username(self):
        """Backward compatibility for username attribute"""
        return self.tradingview_email

    @username.setter
    def username(self, value):
        """Backward compatibility for username attribute"""
        self.tradingview_email = value

    @property
    def password(self):
        """Backward compatibility for password attribute"""
        return self.tradingview_password

    @password.setter
    def password(self, value):
        """Backward compatibility for password attribute"""
        self.tradingview_password = value
