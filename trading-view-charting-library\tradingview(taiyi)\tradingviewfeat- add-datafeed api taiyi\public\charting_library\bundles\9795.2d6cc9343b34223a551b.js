(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9795,3016,3179],{38446:e=>{e.exports={wrapper:"wrapper-VB9J73Gf",focused:"focused-VB9J73Gf",readonly:"readonly-VB9J73Gf",disabled:"disabled-VB9J73Gf","size-small":"size-small-VB9J73Gf","size-medium":"size-medium-VB9J73Gf","size-large":"size-large-VB9J73Gf","font-size-small":"font-size-small-VB9J73Gf","font-size-medium":"font-size-medium-VB9J73Gf","font-size-large":"font-size-large-VB9J73Gf","border-none":"border-none-VB9J73Gf",shadow:"shadow-VB9J73Gf","border-thin":"border-thin-VB9J73Gf","border-thick":"border-thick-VB9J73Gf","intent-default":"intent-default-VB9J73Gf","intent-success":"intent-success-VB9J73Gf","intent-warning":"intent-warning-VB9J73Gf","intent-danger":"intent-danger-VB9J73Gf","intent-primary":"intent-primary-VB9J73Gf","corner-top-left":"corner-top-left-VB9J73Gf","corner-top-right":"corner-top-right-VB9J73Gf","corner-bottom-right":"corner-bottom-right-VB9J73Gf","corner-bottom-left":"corner-bottom-left-VB9J73Gf",childrenContainer:"childrenContainer-VB9J73Gf"}},36547:e=>{e.exports={defaultSelect:"defaultSelect-OM7V5ndi"}},14619:e=>{e.exports={itemWrap:"itemWrap-srH7jxJB",item:"item-srH7jxJB",icon:"icon-srH7jxJB",selected:"selected-srH7jxJB",label:"label-srH7jxJB"}},7625:e=>{e.exports={lineEndSelect:"lineEndSelect-gw7ESiZg",right:"right-gw7ESiZg"}},66220:e=>{e.exports={lineStyleSelect:"lineStyleSelect-GcXENVb4",multipleStyles:"multipleStyles-GcXENVb4"}},99118:e=>{e.exports={lineWidthSelect:"lineWidthSelect-EUDB1YgB",bar:"bar-EUDB1YgB",isActive:"isActive-EUDB1YgB",item:"item-EUDB1YgB"}},68089:e=>{e.exports={container:"container-dhpv13DH",active:"active-dhpv13DH",disabled:"disabled-dhpv13DH",icon:"icon-dhpv13DH"}},45707:e=>{e.exports={wrap:"wrap-b6_0ORMg",disabled:"disabled-b6_0ORMg"}},3115:e=>{e.exports={dropdown:"dropdown-gZlS9p6t",dropdownMenu:"dropdownMenu-gZlS9p6t",firstColorPicker:"firstColorPicker-gZlS9p6t"}},47543:e=>{e.exports={row:"row-nGXZ4vJz",empty:"empty-nGXZ4vJz",wrap:"wrap-nGXZ4vJz",breakpointNormal:"breakpointNormal-nGXZ4vJz",breakpointMedium:"breakpointMedium-nGXZ4vJz",breakpointSmall:"breakpointSmall-nGXZ4vJz"}},50540:e=>{e.exports={coordinates:"coordinates-mb1bDWNb",input:"input-mb1bDWNb",selectionCoordinates:"selectionCoordinates-mb1bDWNb",selectionCoordinates__inputs:"selectionCoordinates__inputs-mb1bDWNb",selectionCoordinates__description:"selectionCoordinates__description-mb1bDWNb",hintButton:"hintButton-mb1bDWNb"}},35199:e=>{e.exports={wrapper:"wrapper-NVcHMTVy",checkbox:"checkbox-NVcHMTVy",colorSelect:"colorSelect-NVcHMTVy",hintButton:"hintButton-NVcHMTVy"}},22497:e=>{e.exports={withoutPadding:"withoutPadding-KtEcG0Q0"}},54970:e=>{e.exports={input:"input-mIsHGNhw",control:"control-mIsHGNhw",item:"item-mIsHGNhw",cell:"cell-mIsHGNhw",fragmentCell:"fragmentCell-mIsHGNhw",largeWidth:"largeWidth-mIsHGNhw",withTitle:"withTitle-mIsHGNhw",title:"title-mIsHGNhw"}},89232:e=>{e.exports={line:"line-j5rMaiWF",control:"control-j5rMaiWF",valueInput:"valueInput-j5rMaiWF",valueUnit:"valueUnit-j5rMaiWF",input:"input-j5rMaiWF"}},
76739:e=>{e.exports={unit:"unit-ZtRdVxiD",input:"input-ZtRdVxiD",normal:"normal-ZtRdVxiD",big:"big-ZtRdVxiD",dropdown:"dropdown-ZtRdVxiD",dropdownMenu:"dropdownMenu-ZtRdVxiD"}},22332:e=>{e.exports={optionalTwoColors:"optionalTwoColors-LDRcAXEV",colorPicker:"colorPicker-LDRcAXEV",dropdown:"dropdown-LDRcAXEV",dropdownMenu:"dropdownMenu-LDRcAXEV"}},13784:e=>{e.exports={dropdown:"dropdown-RxdEkbF0",normal:"normal-RxdEkbF0",big:"big-RxdEkbF0",dropdownMenu:"dropdownMenu-RxdEkbF0"}},66586:e=>{e.exports={range:"range-GLEBGed4",valueInput:"valueInput-GLEBGed4",rangeSlider:"rangeSlider-GLEBGed4",rangeSlider_mixed:"rangeSlider_mixed-GLEBGed4",input:"input-GLEBGed4",hintButton:"hintButton-GLEBGed4"}},42793:e=>{e.exports={colorPicker:"colorPicker-VK3h8amb",fontStyleButton:"fontStyleButton-VK3h8amb",dropdown:"dropdown-VK3h8amb",dropdownMenu:"dropdownMenu-VK3h8amb",hintButton:"hintButton-VK3h8amb"}},81364:e=>{e.exports={twoColors:"twoColors-C2hZXnYv",colorPicker:"colorPicker-C2hZXnYv"}},27394:e=>{e.exports={dropdown:"dropdown-eLkGg0Ft",menu:"menu-eLkGg0Ft"}},19175:e=>{e.exports={dialog:"dialog-CPGBbsmc",rounded:"rounded-CPGBbsmc",shadowed:"shadowed-CPGBbsmc",fullscreen:"fullscreen-CPGBbsmc",darker:"darker-CPGBbsmc",backdrop:"backdrop-CPGBbsmc"}},8326:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","tooltip-offset":"20px",dialog:"dialog-qyCw0PaN",dragging:"dragging-qyCw0PaN",dialogAnimatedAppearance:"dialogAnimatedAppearance-qyCw0PaN",dialogAnimation:"dialogAnimation-qyCw0PaN",dialogTooltip:"dialogTooltip-qyCw0PaN"}},64104:e=>{e.exports={desktopSize:"desktopSize-icygBqe7",drawer:"drawer-icygBqe7",menuBox:"menuBox-icygBqe7"}},40191:e=>{e.exports={menuWrap:"menuWrap-Kq3ruQo8",isMeasuring:"isMeasuring-Kq3ruQo8",scrollWrap:"scrollWrap-Kq3ruQo8",momentumBased:"momentumBased-Kq3ruQo8",menuBox:"menuBox-Kq3ruQo8",isHidden:"isHidden-Kq3ruQo8"}},65542:e=>{e.exports={range:"range-mFgGeMmT",disabled:"disabled-mFgGeMmT",rangeSlider:"rangeSlider-mFgGeMmT",rangeSliderMiddleWrap:"rangeSliderMiddleWrap-mFgGeMmT",rangeSliderMiddle:"rangeSliderMiddle-mFgGeMmT",dragged:"dragged-mFgGeMmT",pointer:"pointer-mFgGeMmT",rangePointerWrap:"rangePointerWrap-mFgGeMmT"}},27306:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},36383:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>r});var o=n(50959),i=n(27267);function r(e){const{click:t,mouseDown:n,touchEnd:r,touchStart:s,handler:a,reference:l,ownerDocument:c=document}=e,d=(0,o.useRef)(null),u=(0,o.useRef)(new CustomEvent("timestamp").timeStamp);return(0,o.useLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:r,touchStart:s},o=l?l.current:d.current;return(0,i.addOutsideEventListener)(u.current,o,a,c,e)}),[t,n,r,s,a]),l||d}},9745:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>i});var o=n(50959);const i=o.forwardRef(((e,t)=>{const{icon:n="",...i}=e;return o.createElement("span",{...i,ref:t,dangerouslySetInnerHTML:{__html:n}})}))},83021:(e,t,n)=>{"use strict";n.d(t,{SubmenuContext:()=>i,
SubmenuHandler:()=>r});var o=n(50959);const i=o.createContext(null);function r(e){const[t,n]=(0,o.useState)(null),r=(0,o.useRef)(null),s=(0,o.useRef)(new Map);return(0,o.useEffect)((()=>()=>{null!==r.current&&clearTimeout(r.current)}),[]),o.createElement(i.Provider,{value:{current:t,setCurrent:function(e){null!==r.current&&(clearTimeout(r.current),r.current=null);null===t?n(e):r.current=setTimeout((()=>{r.current=null,n(e)}),100)},registerSubmenu:function(e,t){return s.current.set(e,t),()=>{s.current.delete(e)}},isSubmenuNode:function(e){return Array.from(s.current.values()).some((t=>t(e)))}}},e.children)}},67961:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>r,getRootOverlapManager:()=>a});var o=n(50151);class i{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class r{constructor(e=document){this._storage=new i,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const o=this._document.createElement("div");if(o.style.position=t.position,o.style.zIndex=this._index.toString(),o.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(o);else if(t.index<=0)this._container.insertBefore(o,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(o,e)}}else"reverse"===t.direction?this._container.insertBefore(o,this._container.firstChild):this._container.appendChild(o);return this._windows.set(e,o),++this._index,o}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){this.ensureWindow(e).style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const s=new WeakMap;function a(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,o.ensureDefined)(s.get(t));{const t=new r(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return s.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}},99054:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>c});const o=(()=>{let e;return()=>{var t;if(void 0===e){const n=document.createElement("div"),o=n.style;o.visibility="hidden",o.width="100px",o.msOverflowStyle="scrollbar",
document.body.appendChild(n);const i=n.offsetWidth;n.style.overflow="scroll";const r=document.createElement("div");r.style.width="100%",n.appendChild(r);const s=r.offsetWidth;null===(t=n.parentNode)||void 0===t||t.removeChild(n),e=i-s}return e}})();function i(e,t,n){null!==e&&e.style.setProperty(t,n)}function r(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function s(e,t){return parseInt(r(e,t))}let a=0,l=!1;function c(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=r(t,"overflow"),a=s(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(i(n,"right",`${o()}px`),t.style.paddingRight=`${a+o()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),l)){i(n,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=o()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},66045:(e,t,n)=>{"use strict";n.d(t,{FontSizeSelect:()=>c});var o=n(50959),i=n(97754),r=n.n(i),s=n(90405),a=n(90186),l=n(36547);function c(e){const{id:t,fontSize:n,fontSizes:i=[],className:c,disabled:d,fontSizeChange:u}=e;return o.createElement(s.Select,{id:t,disabled:d,className:r()(c,l.defaultSelect),menuClassName:l.defaultSelect,items:(p=i,p.map((e=>({value:e.value,content:e.title})))),value:n,onChange:u,...(0,a.filterDataProps)(e)});var p}},94697:(e,t,n)=>{"use strict";n.d(t,{DisplayItem:()=>d,DropItem:()=>u,IconDropdown:()=>c});var o=n(50959),i=n(97754),r=n.n(i),s=n(90405),a=n(9745),l=n(14619);function c(e){const{menuItemClassName:t,...n}=e;return o.createElement(s.Select,{...n,menuItemClassName:r()(t,l.itemWrap)})}function d(e){return o.createElement("div",{className:r()(l.item,l.selected,e.className)},o.createElement(a.Icon,{className:l.icon,icon:e.icon}))}function u(e){return o.createElement("div",{className:l.item},o.createElement(a.Icon,{className:r()(l.icon,e.iconClassName),icon:e.icon}),o.createElement("div",{className:l.label},e.label))}},53598:(e,t,n)=>{"use strict";n.d(t,{LineStyleSelect:()=>f});var o=n(44352),i=n(50959),r=n(97754),s=n.n(r),a=n(79849),l=n(94697),c=n(501),d=n(23851),u=n(57740),p=n(80427),h=n(66220);const m=[{type:a.LINESTYLE_SOLID,icon:c,label:o.t(null,void 0,n(1277))},{type:a.LINESTYLE_DASHED,icon:d,label:o.t(null,void 0,n(59317))},{type:a.LINESTYLE_DOTTED,icon:u,label:o.t(null,void 0,n(42973))}];class f extends i.PureComponent{render(){const{id:e,lineStyle:t,className:n,lineStyleChange:o,disabled:r,additionalItems:a,allowedLineStyles:c}=this.props;let d=function(e){let t=[...m];return void 0!==e&&(t=t.filter((t=>e.includes(t.type)))),t.map((e=>({value:e.type,selectedContent:i.createElement(l.DisplayItem,{icon:e.icon}),content:i.createElement(l.DropItem,{icon:e.icon,label:e.label})})))}(c);return a&&(d=[{readonly:!0,content:a},...d]),i.createElement(l.IconDropdown,{id:e,disabled:r,className:s()(h.lineStyleSelect,n),hideArrowButton:!0,items:d,value:t,onChange:o,"data-name":"line-style-select",addPlaceholderToItems:!1,placeholder:i.createElement(l.DisplayItem,{icon:p,className:h.multipleStyles})})}}},
50890:(e,t,n)=>{"use strict";n.d(t,{LineWidthSelect:()=>d});var o=n(50959),i=n(97754),r=n(90405),s=n(99118);const a=[1,2,3,4];function l(e){const{id:t,value:n,items:l=a,disabled:c,onChange:d}=e;return o.createElement(r.Select,{id:t,disabled:c,hideArrowButton:!0,className:s.lineWidthSelect,items:(u=l,u.map((e=>({value:e,selectedContent:p(e,!0),content:p(e)})))),value:n,onChange:d,"data-name":"line-width-select"});var u;function p(e,t){const r={borderTopWidth:e};return o.createElement("div",{className:s.item},o.createElement("div",{className:i(s.bar,{[s.isActive]:e===n&&!t}),style:r}," "))}}var c=n(45560);function d(e){const{property:t}=e,[n,i]=(0,c.useDefinitionProperty)({property:t});return o.createElement(l,{...e,value:n,onChange:i})}},66849:(e,t,n)=>{"use strict";n.d(t,{ControlCustomHeightContext:()=>r,ControlCustomWidthContext:()=>i});var o=n(50959);const i=o.createContext({}),r=o.createContext({})},15650:(e,t,n)=>{"use strict";n.d(t,{Section:()=>Rt});var o=n(50959),i=n(46141),r=n(48897),s=n(45560),a=n(31356);function l(e){const{definition:{id:t,properties:{checked:n,disabled:i,visible:r},title:l,solutionId:c},offset:d}=e,[u]=(0,s.useDefinitionProperty)({property:i,defaultValue:!1}),[p]=(0,s.useDefinitionProperty)({property:r,defaultValue:!0});return p?o.createElement(a.CommonSection,{id:t,offset:d,checked:n,title:l,solutionId:c,disabled:e.disabled||u}):null}var c=n(97754),d=n.n(c),u=n(22064),p=n(53598);function h(e){const{property:t}=e,[n,i]=(0,s.useDefinitionProperty)({property:t});return o.createElement(p.LineStyleSelect,{...e,lineStyle:n,lineStyleChange:i})}var m=n(50890),f=n(60521),g=n(50151);function v(e){return"mixed"===e}function y(e,t,n){const[i,r]=(0,o.useState)(e),s=(0,o.useRef)(i);return(0,o.useEffect)((()=>{r(e)}),[e,n]),[i,function(e){s.current=e,r(e)},function(){t(s.current)},function(){s.current=e,r(e)}]}var b=n(68335),_=n(92399),E=n(37160),w=n(87663),C=n(49483);function D(e){const{property:t,...n}=e,[i,r]=(0,o.useState)(performance.now()),[a,l]=(0,s.useDefinitionProperty)({property:t,handler:()=>r(performance.now())}),c=y(a,l,i);return o.createElement(S,{...n,valueHash:i,sharedBuffer:c})}function S(e){const{sharedBuffer:t,min:n,max:i,step:r,...s}=e,[a,l,c,d]=t,u=(0,o.useRef)(null),p=(0,o.useRef)(null),h={flushed:!1};return o.createElement(P,{...s,ref:p,onValueChange:function(e,t){l(e),"step"!==t||h.flushed||(c(),h.flushed=!0)},onKeyDown:function(e){if(e.defaultPrevented||h.flushed)return;switch((0,b.hashFromEvent)(e.nativeEvent)){case 27:d(),h.flushed=!0;break;case 13:e.preventDefault();const t=(0,g.ensureNotNull)(p.current).getClampedValue();null!==t&&(l(t),c(),h.flushed=!0)}},onBlur:function(e){const t=(0,g.ensureNotNull)(u.current);if(!t.contains(document.activeElement)&&!t.contains(e.relatedTarget)){const e=(0,g.ensureNotNull)(p.current).getClampedValue();null===e||h.flushed||(l(e),c(),h.flushed=!0)}},value:a,roundByStep:!1,containerReference:function(e){u.current=e},inputMode:C.CheckMobile.iOS()?void 0:"numeric",min:n,max:i,step:r,stretch:!1})}const x={mode:"float",min:-Number.MAX_VALUE,
max:Number.MAX_VALUE,step:1,precision:0,inheritPrecisionFromStep:!0};class P extends o.PureComponent{constructor(e){super(e),this._selection=null,this._restoreSelection=!1,this._input=null,this._handleSelectionChange=()=>{this._restoreSelection||document.activeElement!==(0,g.ensureNotNull)(this._input)||this._saveSelection((0,g.ensureNotNull)(this._input))},this._handleInputReference=e=>{this._input=e,this.props.inputReference&&this.props.inputReference(e)},this._onFocus=e=>{this._saveSelection((0,g.ensureNotNull)(this._input)),this.setState({focused:!0}),this.props.onFocus&&this.props.onFocus(e)},this._onBlur=e=>{this._selection=null,this.setState({displayValue:V(this.props,this.props.value,M(this.props)),focused:!1}),this.props.onBlur&&this.props.onBlur(e)},this._onValueChange=e=>{const t=e.currentTarget,n=t.value,o=function(e,t,n){switch(n){case"integer":return T.test(t)?t:e;case"float":return t=t.replace(/,/g,"."),N.test(t)?t:e}}(this.state.displayValue,n,this.props.mode),i=B(o),r=this._checkValueBoundaries(i);var s,a;this.setState({displayValue:o}),o!==n&&(s=this.state.displayValue,a=(a=o).replace(/,/g,"."),(s=s.replace(/,/g,".")).includes(".")||!a.includes("."))?(this._restoreSelection=!0,this.forceUpdate()):this._saveSelection(t),r.value&&V(this.props,i)===o&&this.props.onValueChange(i,"input")},this._onValueByStepChange=e=>{const{roundByStep:t=!0,step:n=1}=this.props,o=B(this.state.displayValue);let i;if(isNaN(o)){const{defaultValue:e}=this.props;if(void 0===e)return;i=e}else{const r=new f.Big(o),s=new f.Big(n),a=r.mod(s);let l=r.plus(e*n);!a.eq(0)&&t&&(l=l.plus((e>0?0:1)*n).minus(a)),i=l.toNumber()}this._checkValueBoundaries(i).value&&(this.setState({displayValue:V(this.props,i,M(this.props))}),this.props.onValueChange(i,"step"))},this.state={value:R(this.props.value),displayValue:V(this.props,this.props.value,M(this.props)),focused:!1,valueHash:this.props.valueHash}}componentDidMount(){document.addEventListener("selectionchange",this._handleSelectionChange)}componentWillUnmount(){document.removeEventListener("selectionchange",this._handleSelectionChange)}componentDidUpdate(){const e=(0,g.ensureNotNull)(this._input),t=this._selection;if(null!==t&&this._restoreSelection&&document.activeElement===e){const{start:n,end:o,direction:i}=t;e.setSelectionRange(n,o,i)}this._restoreSelection=!1}render(){return o.createElement(_.NumberInputView,{type:"text",inputMode:this.props.inputMode,name:this.props.name,fontSizeStyle:"medium",value:this.state.displayValue,className:this.props.className,placeholder:this.props.placeholder,forceShowControls:this.props.forceShowControls,disabled:this.props.disabled,stretch:this.props.stretch,error:Boolean(this.props.error),errorMessage:this.props.error,onValueChange:this._onValueChange,onValueByStepChange:this._onValueByStepChange,containerReference:this.props.containerReference,inputReference:this._handleInputReference,onClick:this.props.onClick,onFocus:this._onFocus,onBlur:this._onBlur,onKeyDown:this.props.onKeyDown,autoSelectOnFocus:!0,"data-name":this.props["data-name"],
highlight:this.props.highlight})}getClampedValue(){const{min:e,max:t}=this.props,n=B(this.state.displayValue);return isNaN(n)?null:(0,E.clamp)(n,e,t)}static getDerivedStateFromProps(e,t){const{valueHash:n}=e,o=R(e.value);if(t.value!==o||t.valueHash!==n){return{value:o,valueHash:n,displayValue:V(e,o,t.focused&&t.valueHash===n?void 0:M(e))}}return null}_saveSelection(e){const{selectionStart:t,selectionEnd:n,selectionDirection:o}=e;null!==t&&null!==n&&null!==o&&(this._selection={start:t,end:n,direction:o})}_checkValueBoundaries(e){const{min:t,max:n}=this.props,o=function(e,t,n){const o=e>=t,i=e<=n;return{passMin:o,passMax:i,pass:o&&i,clamped:(0,E.clamp)(e,t,n)}}(e,t,n);return{value:o.pass}}}P.defaultProps=x;const T=/^-?[0-9]*$/,N=/^(-?([0-9]+\.?[0-9]*)|(-?[0-9]*))$/;function V(e,t,n){return v(t=R(t))?"—":(null!==t&&void 0!==n&&(n=Math.max(k(t),n)),function(e,t){if(null===e)return"";return new w.NumericFormatter(t).format(e)}(t,n))}function M(e){let t=0;return e.inheritPrecisionFromStep&&e.step<=1&&(t=k(e.step)),Math.max(e.precision,t)||void 0}function k(e){const t=Math.trunc(e).toString();return(0,E.clamp)(w.NumericFormatter.formatNoE(e).length-t.length-1,0,15)}function B(e,t){return new w.NumericFormatter(t).parse(e)}function R(e){return"number"==typeof e&&Number.isFinite(e)||v(e)?e:null}var I=n(24377),F=n(58593),W=n(87095);function L(e){const{color:t,thickness:n,thicknessItems:i,noAlpha:r}=e,[a,l]=(0,s.useDefinitionProperty)({property:t}),[c,d]=(0,s.useDefinitionProperty)(n?{property:n}:{defaultValue:void 0});return o.createElement(F.ColorSelect,{...e,color:function(){if(!a)return null;if("mixed"===a)return"mixed";return(0,I.rgbToHexString)((0,I.parseRgb)(a))}(),onColorChange:function(e){const t=a&&"mixed"!==a?(0,W.alphaToTransparency)((0,I.parseRgba)(a)[3]):0;l((0,W.generateColor)(String(e),t,!0))},thickness:c,thicknessItems:i,onThicknessChange:d,opacity:r?void 0:a&&"mixed"!==a?(0,I.parseRgba)(a)[3]:void 0,onOpacityChange:r?void 0:function(e){l((0,W.generateColor)(a,(0,W.alphaToTransparency)(e),!0))}})}var A=n(44352),z=n(73436),G=n(94697),H=n(90186),O=n(43382),U=n(98853),$=n(7625);const J=[{type:z.LineEnd.Normal,icon:O,label:A.t(null,void 0,n(55362))},{type:z.LineEnd.Arrow,icon:U,label:A.t(null,void 0,n(96237))}];class Y extends o.PureComponent{constructor(e){super(e),this._items=[],this._items=J.map((t=>({value:t.type,selectedContent:o.createElement(G.DisplayItem,{icon:t.icon}),content:o.createElement(G.DropItem,{icon:t.icon,iconClassName:d()(e.isRight&&$.right),label:t.label})})))}render(){const{id:e,lineEnd:t,className:n,lineEndChange:i,isRight:r,disabled:s}=this.props;return o.createElement(G.IconDropdown,{id:e,disabled:s,className:d()($.lineEndSelect,r&&$.right,n),items:this._items,value:t,onChange:i,hideArrowButton:!0,...(0,H.filterDataProps)(this.props)})}}function X(e){const{property:t}=e,[n,i]=(0,s.useDefinitionProperty)({property:t});return o.createElement(Y,{...e,lineEnd:n,lineEndChange:i})}var q=n(78260),K=n(47543);function j(e){const{children:t,className:n,breakPoint:i="Normal"}=e
;return o.createElement(q.CellWrap,{className:c(K.wrap,n,K[`breakpoint${i}`])},o.Children.map(t,(e=>o.isValidElement(e)?o.createElement("span",{key:null===e.key?void 0:e.key,className:c(K.row,r(e)&&K.empty)},e):e)));function r(e){return!(!o.isValidElement(e)||e.type!==o.Fragment||!Array.isArray(e.props.children))&&e.props.children.every((e=>null===e))}}const Z={1:"float",0:"integer"};var Q=n(77975),ee=n(89232);function te(e){const{definition:{id:t,properties:{checked:n,disabled:i,visible:r,leftEnd:l,rightEnd:d,value:p,extendLeft:f,extendRight:g},title:v,valueMin:y,valueMax:b,valueStep:_,valueUnit:E,extendLeftTitle:w,extendRightTitle:C,solutionId:S},offset:x}=e,[P]=(0,s.useDefinitionProperty)({property:n,defaultValue:!0}),[T]=(0,s.useDefinitionProperty)({property:i,defaultValue:!1}),[N]=(0,s.useDefinitionProperty)({property:r,defaultValue:!0}),V=(0,Q.useWatchedValueReadonly)({watchedValue:y,defaultValue:void 0}),M=(0,Q.useWatchedValueReadonly)({watchedValue:b,defaultValue:void 0}),k=(0,Q.useWatchedValueReadonly)({watchedValue:_,defaultValue:void 0}),B=(0,Q.useWatchedValueReadonly)({watchedValue:E,defaultValue:void 0}),R=e.disabled||!P;return N?o.createElement(o.Fragment,null,o.createElement(a.CommonSection,{id:t,offset:x,checked:n,title:v,solutionId:S,disabled:e.disabled||T},o.createElement(j,{className:ee.line,breakPoint:"Small"},o.createElement(o.Fragment,null,function(){const{definition:{properties:{color:n,width:i},widthValues:r}}=e;if(n)return o.createElement("span",{className:ee.control},o.createElement(L,{color:n,thickness:i,disabled:R,thicknessItems:r}));return i&&o.createElement("span",{className:ee.control},o.createElement(m.LineWidthSelect,{id:(0,u.createDomId)(t,"line-width-select"),items:r,property:i,disabled:R}))}(),function(){const{definition:{properties:{style:n}}}=e;return n&&o.createElement("span",{className:ee.control},o.createElement(h,{id:(0,u.createDomId)(t,"line-style-select"),property:n,disabled:R}))}()),(l||d||p)&&o.createElement(o.Fragment,null,o.createElement(o.Fragment,null,l&&o.createElement(X,{id:(0,u.createDomId)(t,"left-end-select"),"data-name":"left-end-select",className:ee.control,property:l,disabled:R}),d&&o.createElement(X,{id:(0,u.createDomId)(t,"right-end-select"),"data-name":"right-end-select",className:ee.control,property:d,disabled:R,isRight:!0})),function(){const{definition:{valueType:t}}=e;return p&&o.createElement("span",{className:c(ee.valueInput,ee.control)},o.createElement(D,{className:ee.input,property:p,min:V,max:M,step:k,disabled:R,mode:void 0!==t?Z[t]:void 0,name:"line-value-input"}),o.createElement("span",{className:ee.valueUnit},B))}()))),f&&o.createElement(a.CommonSection,{id:`${t}ExtendLeft`,offset:x,checked:f,title:w,disabled:e.disabled||T}),g&&o.createElement(a.CommonSection,{id:`${t}ExtendRight`,offset:x,checked:g,title:C,disabled:e.disabled||T})):null}var ne=n(93613),oe=n(90405),ie=n(37968);function re(e){const{property:t,options:n,...i}=e,[r,a]=(0,s.useDefinitionProperty)({property:t}),l=(0,ie.useForceUpdate)();return(0,o.useEffect)((()=>{const e=()=>l()
;return Array.isArray(n)||n.subscribe(e),()=>{Array.isArray(n)||n.unsubscribe(e)}}),[]),o.createElement(oe.Select,{...i,onChange:a,value:r,items:(Array.isArray(n)?n:n.value()).map((e=>e.readonly?{content:e.title,readonly:e.readonly}:{content:e.title,value:e.value,disabled:e.disabled,id:e.id}))})}var se=n(3115);const ae=[{title:A.t(null,void 0,n(35637)),value:ne.ColorType.Solid},{title:A.t(null,void 0,n(16079)),value:ne.ColorType.Gradient}];function le(e){const{id:t,disabled:n,noAlpha:i,properties:r}=e,{color:a,gradientColor1:l,gradientColor2:c,type:d}=r,[p]=(0,s.useDefinitionProperty)({property:d,defaultValue:ne.ColorType.Solid});return o.createElement(j,null,o.createElement(re,{id:(0,u.createDomId)(t,"background-type-options-dropdown"),"data-name":"background-type-options-dropdown",className:se.dropdown,menuClassName:se.dropdownMenu,disabled:n,property:d,options:ae}),p===ne.ColorType.Solid?o.createElement(L,{color:a,disabled:n,noAlpha:i}):o.createElement(o.Fragment,null,o.createElement(L,{className:se.firstColorPicker,color:l,disabled:n,noAlpha:i}),o.createElement(L,{color:c,disabled:n,noAlpha:i})))}function ce(e){const{definition:{id:t,properties:n,title:i,noAlpha:r,solutionId:l},offset:c}=e,{color:d,checked:u,disabled:p,visible:h}=n,[m]=(0,s.useDefinitionProperty)({property:u,defaultValue:!0}),[f]=(0,s.useDefinitionProperty)({property:p,defaultValue:!1}),[g]=(0,s.useDefinitionProperty)({property:h,defaultValue:!0}),v=e.disabled||!m;return g?o.createElement(a.CommonSection,{id:t,offset:c,checked:u,title:i,solutionId:l,disabled:e.disabled||f},o.createElement(q.CellWrap,null,n.hasOwnProperty("type")?o.createElement(le,{id:t,properties:n,disabled:v,noAlpha:r}):o.createElement(L,{color:d,disabled:v,noAlpha:r}))):null}var de=n(54368),ue=n(48891),pe=n(45707);function he(e){const{value:t,disabled:n,onChange:i,className:r}=e;return o.createElement("div",{className:c(pe.wrap,r,{[pe.disabled]:n})},o.createElement(de.Opacity,{hideInput:!0,color:ue.colorsPalette["color-tv-blue-500"],opacity:1-t/100,onChange:function(e){n||i(100-100*e)},disabled:n}))}function me(e){const{property:t,...n}=e,[i,r]=(0,s.useDefinitionProperty)({property:t});return o.createElement(he,{...n,value:i,onChange:r})}function fe(e){const{definition:{id:t,properties:{transparency:n,checked:i,disabled:r,visible:l},title:c,solutionId:d},offset:u}=e,[p]=(0,s.useDefinitionProperty)({property:i,defaultValue:!0}),[h]=(0,s.useDefinitionProperty)({property:r,defaultValue:!1}),[m]=(0,s.useDefinitionProperty)({property:l,defaultValue:!0}),f=e.disabled||!p;return m?o.createElement(a.CommonSection,{id:t,offset:u,checked:i,title:c,solutionId:d,disabled:e.disabled||h},o.createElement(q.CellWrap,null,o.createElement(me,{property:n,disabled:f}))):null}var ge=n(81364);function ve(e){const{definition:{id:t,properties:{color1:n,color2:i,checked:r,disabled:l,visible:c},title:d,noAlpha1:u,noAlpha2:p,solutionId:h},offset:m}=e,[f]=(0,s.useDefinitionProperty)({property:r,defaultValue:!0}),[g]=(0,s.useDefinitionProperty)({property:l,defaultValue:!1}),[v]=(0,
s.useDefinitionProperty)({property:c,defaultValue:!0}),y=e.disabled||!f||g;return v?o.createElement(a.CommonSection,{id:t,offset:m,checked:r,solutionId:h,title:d,disabled:e.disabled||g},o.createElement(q.CellWrap,{className:ge.twoColors},b(n,u),b(i,p))):null;function b(e,t){return o.createElement("span",{className:ge.colorPicker},o.createElement(L,{color:e,disabled:y,noAlpha:t}))}}var ye=n(66849),be=n(76739);function _e(e){const{definition:{id:t,properties:{checked:n,value:i,unitOptionsValue:r,disabled:l,visible:d},min:p,max:h,step:m,title:f,unit:v,unitOptions:y,type:b,solutionId:_},offset:E}=e,[w]=(0,s.useDefinitionProperty)({property:n,defaultValue:!0}),[C]=(0,s.useDefinitionProperty)({property:l,defaultValue:!1}),[S]=(0,s.useDefinitionProperty)({property:d,defaultValue:!0}),x=(0,Q.useWatchedValueReadonly)({watchedValue:p,defaultValue:void 0}),P=(0,Q.useWatchedValueReadonly)({watchedValue:h,defaultValue:void 0}),T=(0,Q.useWatchedValueReadonly)({watchedValue:m,defaultValue:void 0}),N=(0,Q.useWatchedValueReadonly)({watchedValue:v,defaultValue:void 0}),V=(0,o.useContext)(ye.ControlCustomWidthContext),M=e.disabled||!w;return S?o.createElement(a.CommonSection,{id:t,offset:E,checked:n,title:f,solutionId:_,disabled:e.disabled||C},o.createElement(q.CellWrap,null,o.createElement(j,null,o.createElement(D,{className:c(be.input,V[t]&&be[V[t]]),property:i,min:x,max:P,step:T,disabled:M,mode:Z[b],name:"number-input","data-name":t}),r&&o.createElement(re,{id:(0,u.createDomId)(t,"unit-options-dropdown"),"data-name":"unit-options-dropdown",className:be.dropdown,menuClassName:be.dropdownMenu,disabled:M,property:r,options:(0,g.ensureDefined)(y)})),N&&o.createElement("span",{className:be.unit},N))):null}function Ee(e){const{definition:{id:t,properties:{checked:n,disabled:i,visible:r},childrenDefinitions:l,title:c},offset:d}=e,[u]=(0,s.useDefinitionProperty)({property:n,defaultValue:!0}),[p]=(0,s.useDefinitionProperty)({property:i,defaultValue:!1}),[h]=(0,s.useDefinitionProperty)({property:r,defaultValue:!0}),m=e.disabled||!u;return h?o.createElement(o.Fragment,null,o.createElement(a.CommonSection,{id:t,offset:d,checked:n,title:c,disabled:e.disabled||p}),l.map((e=>o.createElement(Rt,{key:e.id,disabled:m,definition:e,offset:!0})))):null}var we=n(66045);function Ce(e){const{property:t}=e,[n,i]=(0,s.useDefinitionProperty)({property:t});return o.createElement(we.FontSizeSelect,{...e,fontSize:n,fontSizeChange:i,"data-name":"font-size-select"})}var De=n(9745),Se=n(68089);function xe(e){const{className:t,checked:n,icon:i,disabled:r,onClick:s}=e;return o.createElement("div",{className:d()(t,Se.container,n&&!r&&Se.active,r&&Se.disabled),onClick:r?void 0:s,"data-role":"button",...(0,H.filterDataProps)(e)},o.createElement(De.Icon,{className:Se.icon,icon:i}))}function Pe(e){const{icon:t,className:n,property:i,disabled:r}=e,[a,l]=(0,s.useDefinitionProperty)({property:i});return o.createElement(xe,{className:n,icon:t,checked:a,onClick:function(){l(!a)},disabled:r,...(0,H.filterDataProps)(e)})}var Te=n(67029),Ne=n(11062),Ve=n(2568);function Me(e){
const{property:t,...n}=e,[i,r]=(0,s.useDefinitionProperty)({property:t}),a=(0,o.useCallback)((e=>r(e.target.value)),[r]);return o.createElement(Ve.Textarea,{...n,value:i,onChange:a})}var ke=n(8295),Be=n(29285),Re=n(42793);const Ie=e=>({content:e.title,title:e.title,value:e.value,id:e.id}),Fe=e=>({content:e.title,title:e.title,value:e.value,id:e.id});function We(e){const{definition:{id:t,properties:{color:n,size:i,checked:r,disabled:l,bold:c,italic:d,text:p,alignmentHorizontal:h,alignmentVertical:m,orientation:f,backgroundVisible:g,backgroundColor:v,borderVisible:y,borderColor:b,borderWidth:_,wrap:E},title:w,solutionId:C,sizeItems:D,alignmentTitle:S,alignmentHorizontalItems:x,alignmentVerticalItems:P,orientationTitle:T,orientationItems:N,backgroundTitle:V,borderTitle:M,borderWidthItems:k,wrapTitle:B},offset:R}=e,I=(0,o.useContext)(ye.ControlCustomHeightContext),[F]=(0,s.useDefinitionProperty)({property:r,defaultValue:!0}),[W]=(0,s.useDefinitionProperty)({property:l,defaultValue:!1}),[A,z]=(0,s.useDefinitionProperty)({property:m,defaultValue:void 0}),[G,H]=(0,s.useDefinitionProperty)({property:f,defaultValue:"horizontal"}),[O,U]=(0,s.useDefinitionProperty)({property:h,defaultValue:void 0}),[$]=(0,s.useDefinitionProperty)({property:g,defaultValue:!1}),[J]=(0,s.useDefinitionProperty)({property:y,defaultValue:!1}),Y=e.disabled||!F;return o.createElement(o.Fragment,null,function(){if(w)return o.createElement(a.CommonSection,{id:t,offset:R,checked:r,title:w,solutionId:C,disabled:e.disabled||W},o.createElement(j,{breakPoint:"Small"},Z(),Q()));return o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{placement:"first",colSpan:2,offset:R,"data-section-name":t},Z(),Q(),C&&!1))}(),p&&o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{placement:"first",colSpan:2,offset:R,"data-section-name":t},o.createElement(Me,{className:Te.InputClasses.FontSizeMedium,rows:(X=I[t],"big"===X?9:5),stretch:!0,property:p,disabled:Y,onFocus:function(e){e.target.select()},name:"text-input"}))),(h||m)&&o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:R,"data-section-name":t},o.createElement(q.CellWrap,null,S)),o.createElement(Ne.PropertyTable.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},o.createElement(j,{breakPoint:"Small"},void 0!==A&&void 0!==P&&o.createElement(oe.Select,{id:(0,u.createDomId)(t,"alignment-vertical-select"),"data-name":"alignment-vertical-select",className:Re.dropdown,menuClassName:Re.dropdownMenu,disabled:Y,value:A,items:P.map(Ie),onChange:z}),void 0!==O&&void 0!==x&&o.createElement(oe.Select,{id:(0,u.createDomId)(t,"alignment-horizontal-select"),"data-name":"alignment-horizontal-select",className:Re.dropdown,menuClassName:Re.dropdownMenu,disabled:Y,value:O,items:x.map(Ie),onChange:U})))),void 0!==f&&void 0!==N&&o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{placement:"first",verticalAlign:"adaptive",offset:R,"data-section-name":t
},o.createElement(q.CellWrap,null,T)),o.createElement(Ne.PropertyTable.Cell,{placement:"last",verticalAlign:"adaptive","data-section-name":t},o.createElement(j,{breakPoint:"Small"},o.createElement(oe.Select,{id:(0,u.createDomId)(t,"orientation-select"),"data-name":"orientation-select",className:Re.dropdown,menuClassName:Re.dropdownMenu,disabled:Y,value:G,items:N.map(Fe),onChange:H})))),ee(V,g,v,!!g&&!$),ee(M,y,b,!!y&&!J,_,k),E&&o.createElement(a.CommonSection,{id:`${t}Wrap`,offset:R,checked:E,title:B,disabled:e.disabled||W}));var X;function K(e,t,n){return e?o.createElement(Pe,{className:Re.fontStyleButton,icon:t,property:e,disabled:Y,"data-name":n}):null}function Z(){return o.createElement(o.Fragment,null,n&&o.createElement("div",{className:Re.colorPicker},o.createElement(L,{color:n,disabled:Y})),i&&D&&o.createElement(Ce,{id:(0,u.createDomId)(t,"font-size-select"),property:i,fontSizes:D,disabled:Y}))}function Q(){return o.createElement(o.Fragment,null,K(c,ke,"toggle-bold"),K(d,Be,"toggle-italic"))}function ee(e,n,i,r,s,l){return i||n?o.createElement(a.CommonSection,{id:`${t}ColorSelect`,offset:R,checked:n,title:e,disabled:Y},i&&o.createElement(L,{color:i,thickness:s,thicknessItems:l,disabled:Y||r})):null}}var Le=n(86623),Ae=n(1722);function ze(e){const{property:t,mathOperations:n="+/*",mode:i="float",disabled:r,...a}=e,[l,c]=(0,o.useState)(performance.now()),[d,u]=(0,s.useDefinitionProperty)({property:t,handler:()=>c(performance.now())}),[p,h,m,f]=y(d,u,l),g=(0,o.useMemo)((()=>{const e=new RegExp(`^[${n.split("").join("\\")}-]?(${"float"===i?"(\\d+\\.\\d*)|":""}(\\d*))$`);return t=>(0,Ae.isString)(t)&&e.test(t)}),[n,i]);return o.createElement(Le.FormInput,{...a,type:"text",value:p,onChange:function(e){const{value:t}=e.currentTarget;h(g(t)?t:p)},onKeyDown:function(e){if(e.defaultPrevented)return;switch((0,b.hashFromEvent)(e.nativeEvent)){case 27:f();break;case 13:v()}},onBlur:function(){v()},disabled:r,stretch:!1,autoSelectOnFocus:!0});function v(){p.length&&m()}}var Ge=n(50540);function He(e){const{definition:{properties:{x:t,y:n,disabled:i},id:r,title:s,solutionId:a},definition:l,offset:c}=e,d=i&&i.value()||e.disabled;return o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{verticalAlign:"top",placement:"first",offset:c,"data-section-name":r},o.createElement("span",{className:Ge.coordinates},s)),(t||n)&&o.createElement(Ne.PropertyTable.Cell,{placement:"last",offset:c,"data-section-name":r},o.createElement(j,{breakPoint:"Medium"},"coordinates"===l.propType?o.createElement(Oe,{definition:l,disabled:d}):o.createElement(Ue,{definition:l,disabled:d})),a&&!1))}function Oe(e){const{definition:{properties:{x:t,y:n},minX:i,maxX:r,stepX:s,minY:a,maxY:l,stepY:c,typeX:d,typeY:u},disabled:p}=e,h=(0,Q.useWatchedValueReadonly)({watchedValue:i,defaultValue:void 0}),m=(0,Q.useWatchedValueReadonly)({watchedValue:r,defaultValue:void 0}),f=(0,Q.useWatchedValueReadonly)({watchedValue:s,defaultValue:void 0}),g=(0,Q.useWatchedValueReadonly)({watchedValue:a,defaultValue:void 0}),v=(0,
Q.useWatchedValueReadonly)({watchedValue:l,defaultValue:void 0}),y=(0,Q.useWatchedValueReadonly)({watchedValue:c,defaultValue:void 0});return o.createElement(o.Fragment,null,n&&o.createElement(D,{className:Ge.input,property:n,min:g,max:v,step:y,disabled:p,name:"y-input",mode:void 0!==u?Z[u]:"integer"}),t&&o.createElement(D,{className:Ge.input,property:t,min:h,max:m,step:f,disabled:p,name:"x-input",mode:void 0!==d?Z[d]:"integer"}))}function Ue(e){const{definition:{properties:{x:t,y:i},mathOperationsX:r,mathOperationsY:s,modeX:a,modeY:l},disabled:c}=e;return o.createElement("div",{className:Ge.selectionCoordinates},o.createElement("div",{className:Ge.selectionCoordinates__inputs},i&&o.createElement(ze,{property:i,mathOperations:s,mode:l,disabled:c,className:Ge.input,placeholder:A.t(null,void 0,n(95166))}),t&&o.createElement(ze,{property:t,mathOperations:r,mode:a,disabled:c,className:Ge.input,placeholder:A.t(null,void 0,n(76080))})),o.createElement("div",{className:Ge.selectionCoordinates__description},A.t(null,void 0,n(78019))))}var $e=n(13784);function Je(e){const{definition:{id:t,properties:{checked:n,option:i,disabled:r,visible:l},title:c,solutionId:p,options:h},offset:m}=e,[f]=(0,s.useDefinitionProperty)({property:n,defaultValue:!0}),[g]=(0,s.useDefinitionProperty)({property:r,defaultValue:!1}),[v]=(0,s.useDefinitionProperty)({property:l,defaultValue:!0}),y=(0,o.useContext)(ye.ControlCustomWidthContext),b=e.disabled||!f;return v?o.createElement(a.CommonSection,{id:t,offset:m,checked:n,title:c,solutionId:p,disabled:e.disabled||g},o.createElement(q.CellWrap,null,o.createElement(re,{id:(0,u.createDomId)(t,"options-dropdown"),"data-name":"options-dropdown",className:d()($e.dropdown,y[t]&&$e[y[t]]),menuClassName:d()($e.dropdownMenu,y[t]&&$e[y[t]]),disabled:b||g,property:i,options:h}))):null}var Ye=n(71953);var Xe=n(38223),qe=n(65542);class Ke extends o.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._rafPosition=null,this._rafDragStop=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null!==this._rafPosition||this.props.disabled||(this._rafPosition=requestAnimationFrame((()=>{const{from:t,to:n,min:o,max:i}=this.props,r=this._getNewPosition(e),s=1===this._detectPointerMode(e),a=s?(0,E.clamp)(r,o,n):t,l=s?n:(0,E.clamp)(r,t,i);a<=l&&this._handleChange(a,l),this._rafPosition=null})))},this._handleDragStop=()=>{null!==this._rafDragStop||this.props.disabled||(this._rafDragStop=requestAnimationFrame((()=>{this.setState({pointerDragMode:0}),this._rafDragStop=null,this.props.onCommit()})))},this._onSliderClick=e=>{C.CheckMobile.any()||(this._handlePosition(e.nativeEvent),this._dragSubscribe())},this._mouseUp=e=>{this._dragUnsubscribe(),this._handlePosition(e),this._handleDragStop()},this._mouseMove=e=>{this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this._handleDragStop()},this.state={
pointerDragMode:0}}componentWillUnmount(){null!==this._rafPosition&&(cancelAnimationFrame(this._rafPosition),this._rafPosition=null),null!==this._rafDragStop&&(cancelAnimationFrame(this._rafDragStop),this._rafDragStop=null),this._dragUnsubscribe()}render(){const{className:e,disabled:t,from:n,to:i,min:r,max:s}=this.props,{pointerDragMode:a}=this.state,l=0!==a,d=s-r,u=0===d?r:(n-r)/d,p=0===d?s:(i-r)/d,h=(0,Xe.isRtl)()?"right":"left";return o.createElement("div",{className:c(e,qe.range,t&&qe.disabled)},o.createElement("div",{className:qe.rangeSlider,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd},o.createElement("div",{className:qe.rangeSliderMiddleWrap},o.createElement("div",{className:c(qe.rangeSliderMiddle,l&&qe.dragged),style:{[h]:100*u+"%",width:100*(p-u)+"%"}})),o.createElement("div",{className:qe.rangePointerWrap},o.createElement("div",{className:c(qe.pointer,l&&qe.dragged),style:{[h]:100*u+"%"},ref:this._refPointer})),o.createElement("div",{className:qe.rangePointerWrap},o.createElement("div",{className:c(qe.pointer,l&&qe.dragged),style:{[h]:100*p+"%"}}))))}_dragSubscribe(){const e=(0,g.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=(0,g.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}_getNewPosition(e){const{min:t,max:n}=this.props,o=n-t,i=(0,g.ensureNotNull)(this._container),r=(0,g.ensureNotNull)(this._pointer),s=i.getBoundingClientRect(),a=r.offsetWidth;let l=e.clientX-a/2-s.left;return(0,Xe.isRtl)()&&(l=s.width-l-a),(0,E.clamp)(l/(s.width-a),0,1)*o+t}_detectPointerMode(e){const{from:t,to:n}=this.props,{pointerDragMode:o}=this.state;if(0!==o)return o;const i=this._getNewPosition(e),r=Math.abs(t-i),s=Math.abs(n-i),a=r===s?i<t?1:2:r<s?1:2;return this.setState({pointerDragMode:a}),a}_handleChange(e,t){const{from:n,to:o,onChange:i}=this.props;e===n&&t===o||i(e,t)}}var je=n(90692),Ze=n(66586);function Qe(e){const{definition:{id:t,properties:{checked:n,disabled:i,from:r,to:l},title:c,solutionId:u,max:p,min:h},offset:m,disabled:f}=e,[g]=(0,s.useDefinitionProperty)({property:n,defaultValue:!0}),[b]=(0,s.useDefinitionProperty)({property:i,defaultValue:!1}),_=(0,Q.useWatchedValueReadonly)({watchedValue:h,defaultValue:void 0}),E=(0,Q.useWatchedValueReadonly)({watchedValue:p,defaultValue:void 0}),[w,C]=(0,s.useDefinitionProperty)({property:r}),[D,x]=(0,s.useDefinitionProperty)({property:l}),P=v(w)||v(D),T=y(P?"mixed":w,(function(e){if(C(e),v(B)){const e=E||100;R(e),x(e)}})),[N,V,M]=T,k=y(P?"mixed":D,(function(e){if(x(e),v(N)){const e=_||0;V(e),C(e)}})),[B,R,I]=k,F=v(N)||v(B),W=f||v(g)||!g,L={flushed:!1};return o.createElement(a.CommonSection,{id:t,offset:m,checked:n,title:c,disabled:f||b},o.createElement(q.CellWrap,{className:Ze.range},function(){if(!_||!E)return null
;return o.createElement(je.MatchMedia,{rule:"screen and (max-width: 460px)"},(e=>o.createElement(j,{breakPoint:"Medium"},o.createElement(o.Fragment,null,o.createElement("span",{className:Ze.valueInput},o.createElement(S,{className:Ze.input,sharedBuffer:T,min:_,max:v(B)?E:B,step:1,disabled:W,name:"from-input",mode:"integer",defaultValue:_}),e?o.createElement("span",{className:Ze.rangeSlider},"—"):o.createElement(Ke,{className:d()(Ze.rangeSlider,F&&Ze.rangeSlider_mixed),from:F?_:N,to:F?E:B,min:_,max:E,onChange:A,onCommit:z,disabled:W}))),o.createElement(o.Fragment,null,o.createElement("span",{className:Ze.valueInput},o.createElement(S,{className:Ze.input,sharedBuffer:k,min:v(N)?_:N,max:E,step:1,disabled:W,name:"to-input",mode:"integer",defaultValue:E}),u&&!1)))))}()));function A(e,t){V(Math.round(e)),R(Math.round(t))}function z(){L.flushed||(M(),I(),L.flushed=!0)}}var et=n(86067),tt=n(53424),nt=n(54970);function ot(e){const{definitions:t,name:n,offset:i}=e,r=d()(nt.cell,nt.fragmentCell,t.some((e=>void 0!==e.solutionId))&&nt.largeWidth);return o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{className:r,offset:i,placement:"first",verticalAlign:"adaptive",colSpan:2,"data-section-name":n,checkableTitle:!0},t.map((e=>o.createElement("div",{className:nt.item,key:e.id,"data-section-name":e.id},o.createElement(rt,{definition:e}))))))}function it(e){const{definition:t,offset:n}=e;return o.createElement(Ne.PropertyTable.Row,null,o.createElement(Ne.PropertyTable.Cell,{className:nt.cell,offset:n,placement:"first",verticalAlign:"adaptive",colSpan:2,checkableTitle:!0},o.createElement(rt,{definition:t})))}function rt(e){const{definition:{id:t,properties:{disabled:n,checked:i,color:r,level:a,width:l,style:c},solutionId:p,title:m,widthValues:f,styleValues:g}}=e,[v]=(0,s.useDefinitionProperty)({property:i,defaultValue:!0}),[y]=(0,s.useDefinitionProperty)({property:n,defaultValue:!1}),b=y||!v;return o.createElement(o.Fragment,null,o.createElement(tt.CheckableTitle,{name:`is-enabled-${t}`,className:d()(m&&nt.withTitle),title:m&&o.createElement("span",{className:nt.title},m),property:i,disabled:y}),a&&o.createElement(D,{className:d()(nt.input,nt.control),property:a,disabled:b}),r&&o.createElement(L,{className:nt.control,disabled:b,color:r,thickness:l,thicknessItems:f}),c&&o.createElement(h,{id:(0,u.createDomId)(t,"leveled-line-style-select"),className:nt.control,property:c,disabled:b,allowedLineStyles:g}),p&&!1)}var st=n(27394);function at(e){const{definition:{id:t,properties:{option1:n,option2:i,checked:r,disabled:l},title:c,solutionId:d,optionsItems1:p,optionsItems2:h},offset:m}=e,[f]=(0,s.useDefinitionProperty)({property:r,defaultValue:!0}),[g]=(0,s.useDefinitionProperty)({property:l,defaultValue:!1}),v=e.disabled||!f;return o.createElement(a.CommonSection,{id:t,offset:m,checked:r,title:c,solutionId:d,disabled:e.disabled||g},o.createElement(j,{className:st.twoOptions},o.createElement(re,{id:(0,u.createDomId)(t,"two-options-dropdown-1"),"data-name":"two-options-dropdown-1",className:st.dropdown,
menuClassName:st.menu,property:n,disabled:v,options:p}),o.createElement(re,{id:(0,u.createDomId)(t,"two-options-dropdown-2"),"data-name":"two-options-dropdown-2",className:st.dropdown,menuClassName:st.menu,property:i,disabled:v,options:h})))}var lt=n(22332);function ct(e){const{definition:{id:t,properties:{color1:n,color2:i,option:r},options:s,color1Visible:l,color2Visible:c,title:p,noAlpha1:h,noAlpha2:m,solutionId:f},offset:g}=e,v=(0,Q.useWatchedValueReadonly)({watchedValue:l,defaultValue:!1}),y=(0,Q.useWatchedValueReadonly)({watchedValue:c,defaultValue:!1}),b=(0,o.useContext)(ye.ControlCustomWidthContext);return o.createElement(a.CommonSection,{id:t,offset:g,solutionId:f,title:p},o.createElement(q.CellWrap,{className:lt.optionalTwoColors},o.createElement(j,null,o.createElement(re,{id:(0,u.createDomId)(t,"options-dropdown"),"data-name":"options-dropdown",className:d()(lt.dropdown,b[t]&&lt[b[t]]),menuClassName:d()(lt.dropdownMenu,b[t]&&lt[b[t]]),property:r,options:s}),o.createElement(o.Fragment,null,v&&_(n,h),y&&_(i,m)))));function _(e,t){return o.createElement("span",{className:lt.colorPicker},o.createElement(L,{color:e,noAlpha:t}))}}var dt=n(39828),ut=n(22497);function pt(e){const{source:t,inputs:n,model:i,inputsTabProperty:r,studyMetaInfo:s}=e.definition;return o.createElement(dt.InputsTabContent,{className:ut.withoutPadding,property:r,model:i,study:t,studyMetaInfo:s,inputs:n})}var ht=n(56840),mt=n(38297),ft=n(83682),gt=n(173);var vt=n(20520),yt=n(37558),bt=n(41590),_t=n(27317),Et=n(40173);function wt(e){!function(e,t){(0,o.useEffect)((()=>{const n=t||document;return n.addEventListener("scroll",e),()=>n.removeEventListener("scroll",e)}),[e])}(e,document)}var Ct=n(12811),Dt=n(24437),St=n(38446);function xt(e){const{children:t,highlight:n,disabled:i,reference:r,...s}=e,a=n?"primary":"default";return o.createElement("div",{...s,ref:r,className:d()(St.wrapper,St[`intent-${a}`],St["border-thin"],St["size-medium"],n&&St.highlight,n&&St.focused,i&&St.disabled),"data-role":"button"},o.createElement("div",{className:d()(St.childrenContainer,i&&St.disabled)},t),n&&o.createElement("span",{className:St.shadow}))}var Pt=n(64104);const Tt=()=>null,Nt=(0,Et.mergeThemes)(_t.DEFAULT_MENU_THEME,{menuBox:Pt.menuBox});function Vt(e){const{value:t,disabled:n,onSelect:i}=e,r=(0,o.useRef)(null),{current:s}=(0,o.useRef)(ht.getJSON("RecentlyUsedEmojis",[t])),[a,l]=(0,o.useState)(s),[c,d]=(0,o.useState)(!1),u=(0,o.useCallback)((()=>d(!1)),[]);wt(u);const p=(0,o.useCallback)((e=>{const t=Array.from(new Set([e,...a])).slice(0,18);ht.setJSON("RecentlyUsedEmojis",t),l(t),i(e),u()}),[a,i]),h=(m=a,(0,o.useMemo)((()=>(gt.emojiGroups[0].emojis=m,[...gt.emojiGroups])),[m]));var m;return o.createElement(o.Fragment,null,o.createElement(xt,{reference:r,highlight:c,disabled:n,"data-name":"emoji-picker"},o.createElement(ft.EmojiWrap,{emoji:t,onClick:function(){n||d(!0)}})),o.createElement(je.MatchMedia,{rule:Dt.DialogBreakpoints.TabletSmall},(e=>c&&o.createElement(yt.DrawerManager,null,e?o.createElement(bt.Drawer,{className:Pt.drawer,position:"Bottom",
onClose:u},o.createElement(mt.EmojiList,{emojis:h,onSelect:p,height:378})):o.createElement(vt.PopupMenu,{theme:Nt,isOpened:!0,position:(0,Ct.getPopupPositioner)(r.current,{horizontalDropDirection:Ct.HorizontalDropDirection.FromLeftToRight,horizontalAttachEdge:Ct.HorizontalAttachEdge.Left}),onClickOutside:u,onClose:Tt},o.createElement(mt.EmojiList,{className:Pt.desktopSize,emojis:h,onSelect:p,height:378}))))))}var Mt=n(35199);function kt(e){const{definition:{id:t,title:n,properties:i,solutionId:r},offset:l}=e,{checked:c,emoji:d,backgroundColor:u}=i,[p]=(0,s.useDefinitionProperty)({property:c,defaultValue:!1}),[h,m]=(0,s.useDefinitionProperty)({property:d,defaultValue:"🙂"}),[f,g]=(0,s.useDefinitionProperty)({property:u,defaultValue:ue.colorsPalette["color-tv-blue-a600"]}),[v]=(0,s.useDefinitionProperty)({property:i.disabled,defaultValue:!1}),y=e.disabled||!p;return o.createElement(a.CommonSection,{id:t,offset:l,checked:c,title:n,solutionId:r,disabled:e.disabled||v},o.createElement(Vt,{value:h,disabled:y,onSelect:m}),o.createElement(F.ColorSelect,{className:Mt.colorSelect,disabled:y,color:function(){if("mixed"===f)return f;return(0,I.rgbToHexString)((0,I.parseRgb)(f))}(),opacity:f&&"mixed"!==f?(0,I.parseRgba)(f)[3]:void 0,onColorChange:function(e){const t=f&&"mixed"!==f?(0,W.alphaToTransparency)((0,I.parseRgba)(f)[3]):0;g((0,W.generateColor)(String(e),t,!0))},onOpacityChange:function(e){g((0,W.generateColor)(f,(0,W.alphaToTransparency)(e),!0))}}))}function Bt(e){const{definition:{id:t,properties:{disabled:n,visible:i},childrenDefinitions:r,title:l},offset:c}=e,[d]=(0,s.useDefinitionProperty)({property:n,defaultValue:!1}),[u]=(0,s.useDefinitionProperty)({property:i,defaultValue:!0}),p=e.disabled;return u?o.createElement(o.Fragment,null,l&&o.createElement(a.CommonSection,{id:t,offset:c,title:l,disabled:e.disabled||d}),r.map((e=>o.createElement(Rt,{key:e.id,disabled:p,definition:e,offset:Boolean(l)})))):null}function Rt(e){const{definition:t,offset:n,disabled:s}=e;if(function(e){(0,o.useEffect)((()=>{if(void 0===e)return;const t={...e.properties};return Object.entries(t).forEach((([n,o])=>{void 0!==o&&o.subscribe(t,(()=>Ye.logger.logNormal(`Property "${n}" in definition "${e.id}" was updated to value "${o.value()}"`)))})),()=>{Object.entries(t).forEach((([,e])=>{null==e||e.unsubscribeAll(t)}))}}),[e])}((0,i.isPropertyDefinitionsGroup)(t)?void 0:t),(0,i.isPropertyDefinitionsGroup)(t))return o.createElement(It,{definition:t,offset:n,disabled:s});switch(t.propType){case"line":return o.createElement(te,{...e,definition:t});case"checkable":return o.createElement(l,{...e,definition:t});case"color":return o.createElement(ce,{...e,definition:t});case"transparency":return o.createElement(fe,{...e,definition:t});case"twoColors":return o.createElement(ve,{...e,definition:t});case"optionalTwoColors":return o.createElement(ct,{...e,definition:t});case"number":return o.createElement(_e,{...e,definition:t});case"symbol":return o.createElement(r.SymbolInputsButton,{...e,definition:t});case"text":return o.createElement(We,{...e,
definition:t});case"checkableSet":return o.createElement(Ee,{...e,definition:t});case"set":return o.createElement(Bt,{...e,definition:t});case"options":return o.createElement(Je,{...e,definition:t});case"soundSelect":case"image":default:return null;case"range":return o.createElement(Qe,{...e,definition:t});case"coordinates":case"selectionCoordinates":return o.createElement(He,{...e,definition:t});case"twoOptions":return o.createElement(at,{...e,definition:t});case"leveledLine":return o.createElement(it,{...e,definition:t});case"emoji":return o.createElement(kt,{...e,definition:t});case"studyInputs":return o.createElement(pt,{...e,definition:t})}}function It(e){const{definition:t}=e,n=(0,Q.useWatchedValueReadonly)({watchedValue:t.definitions});return(0,Q.useWatchedValueReadonly)({watchedValue:t.visible,defaultValue:!0})?o.createElement(o.Fragment,null,t.title&&o.createElement(et.GroupTitleSection,{title:t.title,name:t.id}),n&&function(e){const t=[];return e.reduce(((e,t)=>{if((0,i.isPropertyDefinitionsGroup)(t)||"leveledLine"!==t.propType)e.push(t);else{const n=e[e.length-1];Array.isArray(n)?n.push(t):e.push([t])}return e}),t)}(n).map((n=>Array.isArray(n)?o.createElement(ot,{key:n[0].id,name:t.id,definitions:n}):o.createElement(Rt,{key:n.id,...e,definition:n}))),"general"===t.groupType&&o.createElement(Ne.PropertyTable.GroupSeparator,{size:1})):null}},95711:(e,t,n)=>{"use strict";n.d(t,{PopupContext:()=>o});const o=n(50959).createContext(null)},63016:(e,t,n)=>{"use strict";n.d(t,{PopupDialog:()=>x});var o=n(50959),i=n(97754),r=n(50151),s=n(60508),a=n(67961),l=n(90186),c=n(19175);class d extends o.PureComponent{constructor(){super(...arguments),this._manager=new a.OverlapManager,this._handleSlot=e=>{this._manager.setContainer(e)}}render(){const{rounded:e=!0,shadowed:t=!0,fullscreen:n=!1,darker:r=!1,className:a,backdrop:d,containerTabIndex:u=-1}=this.props,p=i(a,c.dialog,e&&c.rounded,t&&c.shadowed,n&&c.fullscreen,r&&c.darker),h=(0,l.filterDataProps)(this.props),m=this.props.style?{...this._createStyles(),...this.props.style}:this._createStyles();return o.createElement(o.Fragment,null,o.createElement(s.SlotContext.Provider,{value:this._manager},d&&o.createElement("div",{onClick:this.props.onClickBackdrop,className:c.backdrop}),o.createElement("div",{...h,className:p,style:m,ref:this.props.reference,onFocus:this.props.onFocus,onMouseDown:this.props.onMouseDown,onMouseUp:this.props.onMouseUp,onClick:this.props.onClick,onKeyDown:this.props.onKeyDown,tabIndex:u,"aria-label":this.props.containerAriaLabel},this.props.children)),o.createElement(s.Slot,{reference:this._handleSlot}))}_createStyles(){const{bottom:e,left:t,width:n,right:o,top:i,zIndex:r,height:s}=this.props;return{bottom:e,left:t,right:o,top:i,zIndex:r,maxWidth:n,height:s}}}var u=n(86431),p=n(76594),h=n(37160);function m(e,t,n,o){return e+t>o&&(e=o-t),e<n&&(e=n),e}function f(e){return{x:(0,h.clamp)(e.x,20,document.documentElement.clientWidth-20),y:(0,h.clamp)(e.y,20,window.innerHeight-20)}}function g(e){return{x:e.clientX,y:e.clientY}}function v(e){return{
x:e.touches[0].clientX,y:e.touches[0].clientY}}class y{constructor(e,t,n={boundByScreen:!0}){this._drag=null,this._canBeTouchClick=!1,this._frame=null,this._onMouseDragStart=e=>{if(0!==e.button||this._isTargetNoDraggable(e))return;e.preventDefault(),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("mouseup",this._onMouseDragEnd);const t=f(g(e));this._dragStart(t)},this._onTouchDragStart=e=>{if(this._isTargetNoDraggable(e))return;this._canBeTouchClick=!0,e.preventDefault(),this._header.addEventListener("touchmove",this._onTouchDragMove,{passive:!1});const t=f(v(e));this._dragStart(t)},this._onMouseDragEnd=e=>{e.target instanceof Node&&this._header.contains(e.target)&&e.preventDefault(),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("mouseup",this._onMouseDragEnd),this._onDragStop()},this._onTouchDragEnd=e=>{this._header.removeEventListener("touchmove",this._onTouchDragMove),this._onDragStop(),this._canBeTouchClick&&(this._canBeTouchClick=!1,function(e){if(e instanceof SVGElement){const t=document.createEvent("SVGEvents");t.initEvent("click",!0,!0),e.dispatchEvent(t)}e instanceof HTMLElement&&e.click()}(e.target))},this._onMouseDragMove=e=>{const t=f(g(e));this._dragMove(t)},this._onTouchDragMove=e=>{this._canBeTouchClick=!1,e.preventDefault();const t=f(v(e));this._dragMove(t)},this._onDragStop=()=>{this._drag=null,this._header.classList.remove("dragging"),this._options.onDragEnd&&this._options.onDragEnd()},this._dialog=e,this._header=t,this._options=n,this._header.addEventListener("mousedown",this._onMouseDragStart),this._header.addEventListener("touchstart",this._onTouchDragStart),this._header.addEventListener("touchend",this._onTouchDragEnd)}destroy(){null!==this._frame&&cancelAnimationFrame(this._frame),this._header.removeEventListener("mousedown",this._onMouseDragStart),document.removeEventListener("mouseup",this._onMouseDragEnd),this._header.removeEventListener("touchstart",this._onTouchDragStart),this._header.removeEventListener("touchend",this._onTouchDragEnd),document.removeEventListener("mouseleave",this._onMouseDragEnd)}updateOptions(e){this._options=e}_dragStart(e){const t=this._dialog.getBoundingClientRect();this._drag={startX:e.x,startY:e.y,finishX:e.x,finishY:e.y,dialogX:t.left,dialogY:t.top};const n=Math.round(t.left),o=Math.round(t.top);this._dialog.style.transform=`translate(${n}px, ${o}px)`,this._header.classList.add("dragging"),this._options.onDragStart&&this._options.onDragStart()}_dragMove(e){if(this._drag){if(this._drag.finishX=e.x,this._drag.finishY=e.y,null!==this._frame)return;this._frame=requestAnimationFrame((()=>{if(this._drag){const t=e.x-this._drag.startX,n=e.y-this._drag.startY;this._moveDialog(this._drag.dialogX+t,this._drag.dialogY+n)}this._frame=null}))}}_moveDialog(e,t){const n=this._dialog.getBoundingClientRect(),{boundByScreen:o}=this._options,i=m(e,n.width,o?0:-1/0,o?window.innerWidth:1/0),r=m(t,n.height,o?0:-1/0,o?window.innerHeight:1/0)
;this._dialog.style.transform=`translate(${Math.round(i)}px, ${Math.round(r)}px)`}_isTargetNoDraggable(e){return e.target instanceof Element&&null!==e.target.closest("[data-disable-drag]")}}const b={vertical:0};class _{constructor(e,t){this._frame=null,this._isFullscreen=!1,this._handleResize=()=>{null===this._frame&&(this._frame=requestAnimationFrame((()=>{this.recalculateBounds(),this._frame=null})))},this._dialog=e,this._guard=t.guard||b,this._calculateDialogPosition=t.calculateDialogPosition,this._initialHeight=e.style.height,window.addEventListener("resize",this._handleResize)}updateOptions(e){this._guard=e.guard||b,this._calculateDialogPosition=e.calculateDialogPosition}setFullscreen(e){this._isFullscreen!==e&&(this._isFullscreen=e,this.recalculateBounds())}centerAndFit(){const{x:e,y:t}=this.getDialogsTopLeftCoordinates(),n=this._calcAvailableHeight(),o=this._calcDialogHeight();if(n===o)if(this._calculateDialogPosition){const{left:e,top:t}=this._calculateDialogPosition(this._dialog,document.documentElement,this._guard);this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else this._dialog.style.height=o+"px";this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${e}px, ${t}px)`}getDialogsTopLeftCoordinates(){const{clientHeight:e,clientWidth:t}=document.documentElement,n=this._calcDialogHeight(),o=t/2-this._dialog.clientWidth/2,i=e/2-n/2;return{x:Math.round(o),y:Math.round(i)}}recalculateBounds(){var e;const{clientHeight:t,clientWidth:n}=document.documentElement,{vertical:o}=this._guard,i=null===(e=this._calculateDialogPosition)||void 0===e?void 0:e.call(this,this._dialog,{clientWidth:n,clientHeight:t},{vertical:o});if(this._isFullscreen){if(this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.width="100%",this._dialog.style.height="100%",this._dialog.style.transform="none",i){const{left:e,top:t,width:n,height:o}=i;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`,n&&(this._dialog.style.width=`${n}px`,this._dialog.style.minWidth="unset"),o&&(this._dialog.style.height=`${o}px`,this._dialog.style.minHeight="unset")}}else if(i){const{left:e,top:t}=i;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else{this._dialog.style.width="",this._dialog.style.height="";const e=this._dialog.getBoundingClientRect(),i=t-2*o,r=m(e.left,e.width,0,n),s=m(e.top,e.height,o,t);this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${Math.round(r)}px, ${Math.round(s)}px)`,this._dialog.style.height=i<e.height?i+"px":this._initialHeight}}destroy(){window.removeEventListener("resize",this._handleResize),null!==this._frame&&(cancelAnimationFrame(this._frame),this._frame=null)}_calcDialogHeight(){const e=this._calcAvailableHeight();return e<this._dialog.clientHeight?e:this._dialog.clientHeight}_calcAvailableHeight(){return document.documentElement.clientHeight-2*this._guard.vertical}}var E=n(65718),w=n(95711),C=n(99054),D=n(8326)
;D["tooltip-offset"];class S extends o.PureComponent{constructor(e){super(e),this._dialog=null,this._cleanUpFunctions=[],this._prevActiveElement=null,this._handleDialogRef=e=>{const{reference:t}=this.props;this._dialog=e,"function"==typeof t&&t(e)},this._handleFocus=e=>{this._moveToTop()},this._handleMouseDown=e=>{this._moveToTop()},this._handleTouchStart=e=>{this._moveToTop()},this.state={canFitTooltip:!1}}render(){return o.createElement(w.PopupContext.Provider,{value:this},o.createElement(p.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this.props.onClickOutside},(e=>o.createElement("div",{ref:e,"data-outside-boundary-for":this.props.name,onFocus:this._handleFocus,onMouseDown:this._handleMouseDown,onTouchStart:this._handleTouchStart,"data-dialog-name":this.props["data-dialog-name"]},o.createElement(d,{style:this._applyAnimationCSSVariables(),...this.props,reference:this._handleDialogRef,className:i(D.dialog,this.props.className)},!1,this.props.children)))))}componentDidMount(){const{draggable:e,boundByScreen:t,onDragStart:n}=this.props,o=(0,r.ensureNotNull)(this._dialog);if(e){const e=o.querySelector("[data-dragg-area]");if(e&&e instanceof HTMLElement){const i=new y(o,e,{boundByScreen:Boolean(t),onDragStart:n});this._cleanUpFunctions.push((()=>i.destroy())),this._drag=i}}this._prevActiveElement=document.activeElement,this.props.autofocus&&!o.contains(document.activeElement)&&o.focus(),(this._isFullScreen()||this.props.fixedBody)&&(0,C.setFixedBodyState)(!0);const{guard:i,calculateDialogPosition:s}=this.props;if(this.props.resizeHandler)this._resize=this.props.resizeHandler;else{const e=new _(o,{guard:i,calculateDialogPosition:s});this._cleanUpFunctions.push((()=>e.destroy())),this._resize=e}if(this.props.isAnimationEnabled&&this.props.growPoint&&this._applyAppearanceAnimation(this.props.growPoint),this.props.centeredOnMount&&this._resize.centerAndFit(),this._resize.setFullscreen(this._isFullScreen()),this.props.shouldForceFocus){if(this.props.onForceFocus)return void this.props.onForceFocus(o);o.focus()}}componentDidUpdate(){if(this._resize){const{guard:e,calculateDialogPosition:t}=this.props;this._resize.updateOptions({guard:e,calculateDialogPosition:t}),this._resize.setFullscreen(this._isFullScreen())}this._drag&&this._drag.updateOptions({boundByScreen:Boolean(this.props.boundByScreen),onDragStart:this.props.onDragStart})}componentWillUnmount(){var e;if(this.props.shouldReturnFocus&&this._prevActiveElement&&document.body.contains(this._prevActiveElement)&&(null===document.activeElement||document.activeElement===document.body||(null===(e=this._dialog)||void 0===e?void 0:e.contains(document.activeElement))))try{this._prevActiveElement.focus({preventScroll:!0})}catch(e){}for(const e of this._cleanUpFunctions)e();(this._isFullScreen()||this.props.fixedBody)&&(0,C.setFixedBodyState)(!1)}focus(){this._dialog&&this._dialog.focus()}centerAndFit(){this._resize&&this._resize.centerAndFit()}recalculateBounds(){this._resize&&this._resize.recalculateBounds()}_moveToTop(){null!==this.context&&this.context.moveToTop()}
_applyAnimationCSSVariables(){return{"--animationTranslateStartX":null,"--animationTranslateStartY":null,"--animationTranslateEndX":null,"--animationTranslateEndY":null}}_applyAppearanceAnimation(e){if(this._resize&&this._dialog){const{x:t,y:n}=e,{x:o,y:i}=this._resize.getDialogsTopLeftCoordinates();this._dialog.style.setProperty("--animationTranslateStartX",`${t}px`),this._dialog.style.setProperty("--animationTranslateStartY",`${n}px`),this._dialog.style.setProperty("--animationTranslateEndX",`${o}px`),this._dialog.style.setProperty("--animationTranslateEndY",`${i}px`),this._dialog.classList.add(D.dialogAnimatedAppearance)}}_handleTooltipFit(){0}_isFullScreen(){return Boolean(this.props.fullscreen)}}S.contextType=E.PortalContext,S.defaultProps={boundByScreen:!0,draggable:!0,centeredOnMount:!0,shouldReturnFocus:!0};const x=(0,u.makeOverlapable)(S)},95276:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosure:()=>d});var o=n(50959),i=n(38528),r=n(26597),s=n(59054),a=n(36104),l=n(68335),c=n(66986);const d=o.forwardRef(((e,t)=>{const{id:n,tabIndex:d,disabled:u,highlight:p,intent:h,children:m,onClick:f,onFocus:g,onBlur:v,listboxAria:y,onListboxKeyDown:b,..._}=e,E=(0,o.useRef)({"aria-labelledby":n}),{listboxId:w,isOpened:C,isFocused:D,buttonTabIndex:S,listboxTabIndex:x,highlight:P,intent:T,onOpen:N,close:V,toggle:M,buttonFocusBindings:k,onButtonClick:B,buttonRef:R,listboxRef:I,buttonAria:F}=(0,a.useControlDisclosure)({id:n,disabled:u,buttonTabIndex:d,intent:h,highlight:p,onFocus:g,onBlur:v,onClick:f}),W=(0,r.useKeyboardToggle)(M),L=(0,r.useKeyboardClose)(C,V),A=(0,r.useKeyboardEventHandler)([W,L]);return o.createElement(s.ControlDisclosureView,{..._,...k,...F,id:n,role:"button",tabIndex:S,disabled:u,isOpened:C,isFocused:D,ref:(0,i.useMergedRefs)([R,t]),highlight:P,intent:T,onClose:V,onOpen:N,onClick:B,onKeyDown:A,listboxId:w,listboxTabIndex:x,listboxReference:I,listboxAria:null!=y?y:E.current,onListboxKeyDown:function(e){if(27===(0,l.hashFromEvent)(e))return e.preventDefault(),void V();null==b||b(e)}},m,o.createElement("span",{className:c.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:()=>V()}))}));d.displayName="ControlDisclosure"},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>i});var o=n(50959);class i extends o.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addListener(this._handleChange)}_unsubscribe(e){e.removeListener(this._handleChange)}}},64706:(e,t,n)=>{"use strict";n.d(t,{MenuContext:()=>o});const o=n(50959).createContext(null)},27317:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_MENU_THEME:()=>g,Menu:()=>v})
;var o=n(50959),i=n(97754),r=n.n(i),s=n(50151),a=n(37160),l=n(21861),c=n(60508),d=n(59064),u=n(67961),p=n(4741),h=n(83021),m=n(64706),f=n(40191);const g=f;class v extends o.PureComponent{constructor(e){super(e),this._containerRef=null,this._scrollWrapRef=null,this._raf=null,this._scrollRaf=null,this._scrollTimeout=void 0,this._manager=new u.OverlapManager,this._hotkeys=null,this._scroll=0,this._handleContainerRef=e=>{this._containerRef=e,this.props.reference&&("function"==typeof this.props.reference&&this.props.reference(e),"object"==typeof this.props.reference&&(this.props.reference.current=e))},this._handleScrollWrapRef=e=>{this._scrollWrapRef=e,"function"==typeof this.props.scrollWrapReference&&this.props.scrollWrapReference(e),"object"==typeof this.props.scrollWrapReference&&(this.props.scrollWrapReference.current=e)},this._handleCustomRemeasureDelegate=()=>{this._resizeForced(),this._handleMeasure()},this._handleMeasure=({callback:e,forceRecalcPosition:t}={})=>{var n,o,i,r,l,c,d,u,p,h,m,f;if(this.state.isMeasureValid&&!t)return;const{position:g}=this.props,v=(0,s.ensureNotNull)(this._containerRef);let y=v.getBoundingClientRect();const b=document.documentElement.clientHeight,_=document.documentElement.clientWidth,E=null!==(n=this.props.closeOnScrollOutsideOffset)&&void 0!==n?n:0;let w=b-0-E;const C=y.height>w;if(C){(0,s.ensureNotNull)(this._scrollWrapRef).style.overflowY="scroll",y=v.getBoundingClientRect()}const{width:D,height:S}=y,x="function"==typeof g?g(D,S,_,b):g,P=null!==(i=null===(o=null==x?void 0:x.indentFromWindow)||void 0===o?void 0:o.left)&&void 0!==i?i:0,T=_-(null!==(r=x.overrideWidth)&&void 0!==r?r:D)-(null!==(c=null===(l=null==x?void 0:x.indentFromWindow)||void 0===l?void 0:l.right)&&void 0!==c?c:0),N=(0,a.clamp)(x.x,P,Math.max(P,T)),V=(null!==(u=null===(d=null==x?void 0:x.indentFromWindow)||void 0===d?void 0:d.top)&&void 0!==u?u:0)+E,M=b-(null!==(p=x.overrideHeight)&&void 0!==p?p:S)-(null!==(m=null===(h=null==x?void 0:x.indentFromWindow)||void 0===h?void 0:h.bottom)&&void 0!==m?m:0);let k=(0,a.clamp)(x.y,V,Math.max(V,M));if(x.forbidCorrectYCoord&&k<x.y&&(w-=x.y-k,k=x.y),t&&void 0!==this.props.closeOnScrollOutsideOffset&&x.y<=this.props.closeOnScrollOutsideOffset)return void this._handleGlobalClose(!0);const B=null!==(f=x.overrideHeight)&&void 0!==f?f:C?w:void 0;this.setState({appearingMenuHeight:t?this.state.appearingMenuHeight:B,appearingMenuWidth:t?this.state.appearingMenuWidth:x.overrideWidth,appearingPosition:{x:N,y:k},isMeasureValid:!0},(()=>{this._restoreScrollPosition(),e&&e()}))},this._restoreScrollPosition=()=>{const e=document.activeElement,t=(0,s.ensureNotNull)(this._containerRef);if(null!==e&&t.contains(e))try{e.scrollIntoView()}catch(e){}else(0,s.ensureNotNull)(this._scrollWrapRef).scrollTop=this._scroll},this._resizeForced=()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0})},this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,
appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleGlobalClose=e=>{this.props.onClose(e)},this._handleSlot=e=>{this._manager.setContainer(e)},this._handleScroll=()=>{this._scroll=(0,s.ensureNotNull)(this._scrollWrapRef).scrollTop},this._handleScrollOutsideEnd=()=>{clearTimeout(this._scrollTimeout),this._scrollTimeout=setTimeout((()=>{this._handleMeasure({forceRecalcPosition:!0})}),80)},this._handleScrollOutside=e=>{e.target!==this._scrollWrapRef&&(this._handleScrollOutsideEnd(),null===this._scrollRaf&&(this._scrollRaf=requestAnimationFrame((()=>{this._handleMeasure({forceRecalcPosition:!0}),this._scrollRaf=null}))))},this.state={}}componentDidMount(){this._handleMeasure({callback:this.props.onOpen});const{customCloseDelegate:e=d.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.subscribe(this,this._handleGlobalClose),null==t||t.subscribe(null,this._handleCustomRemeasureDelegate),window.addEventListener("resize",this._resize);const n=null!==this.context;this._hotkeys||n||(this._hotkeys=p.createGroup({desc:"Popup menu"}),this._hotkeys.add({desc:"Close",hotkey:27,handler:()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleGlobalClose()}})),this.props.repositionOnScroll&&window.addEventListener("scroll",this._handleScrollOutside,{capture:!0})}componentDidUpdate(){this._handleMeasure()}componentWillUnmount(){const{customCloseDelegate:e=d.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.unsubscribe(this,this._handleGlobalClose),null==t||t.unsubscribe(null,this._handleCustomRemeasureDelegate),window.removeEventListener("resize",this._resize),window.removeEventListener("scroll",this._handleScrollOutside,{capture:!0}),this._hotkeys&&(this._hotkeys.destroy(),this._hotkeys=null),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),null!==this._scrollRaf&&(cancelAnimationFrame(this._scrollRaf),this._scrollRaf=null),this._scrollTimeout&&clearTimeout(this._scrollTimeout)}render(){const{id:e,role:t,"aria-label":n,"aria-labelledby":i,"aria-activedescendant":s,"aria-hidden":a,"aria-describedby":d,"aria-invalid":u,children:p,minWidth:g,theme:v=f,className:b,maxHeight:_,onMouseOver:E,onMouseOut:w,onKeyDown:C,onFocus:D,onBlur:S}=this.props,{appearingMenuHeight:x,appearingMenuWidth:P,appearingPosition:T,isMeasureValid:N}=this.state,V={"--ui-kit-menu-max-width":`${T&&T.x}px`,maxWidth:"calc(100vw - var(--ui-kit-menu-max-width) - 6px)"};return o.createElement(m.MenuContext.Provider,{value:this},o.createElement(h.SubmenuHandler,null,o.createElement(c.SlotContext.Provider,{value:this._manager},o.createElement("div",{id:e,role:t,"aria-label":n,"aria-labelledby":i,"aria-activedescendant":s,"aria-hidden":a,"aria-describedby":d,"aria-invalid":u,className:r()(b,v.menuWrap,!N&&v.isMeasuring),style:{height:x,left:T&&T.x,minWidth:g,position:"fixed",top:T&&T.y,width:P,...this.props.limitMaxWidth&&V},"data-name":this.props["data-name"],ref:this._handleContainerRef,onScrollCapture:this.props.onScroll,onContextMenu:l.preventDefaultForContextMenu,
tabIndex:this.props.tabIndex,onMouseOver:E,onMouseOut:w,onKeyDown:C,onFocus:D,onBlur:S},o.createElement("div",{className:r()(v.scrollWrap,!this.props.noMomentumBasedScroll&&v.momentumBased),style:{overflowY:void 0!==x?"scroll":"auto",maxHeight:_},onScrollCapture:this._handleScroll,ref:this._handleScrollWrapRef},o.createElement(y,{className:v.menuBox},p)))),o.createElement(c.Slot,{reference:this._handleSlot})))}update(e){e?this._resizeForced():this._resize()}focus(e){var t;null===(t=this._containerRef)||void 0===t||t.focus(e)}blur(){var e;null===(e=this._containerRef)||void 0===e||e.blur()}}function y(e){const t=(0,s.ensureNotNull)((0,o.useContext)(h.SubmenuContext)),n=o.useRef(null);return o.createElement("div",{ref:n,className:e.className,onMouseOver:function(e){if(!(null!==t.current&&e.target instanceof Node&&(o=e.target,null===(i=n.current)||void 0===i?void 0:i.contains(o))))return;var o,i;t.isSubmenuNode(e.target)||t.setCurrent(null)},"data-name":"menu-inner"},e.children)}v.contextType=h.SubmenuContext},76594:(e,t,n)=>{"use strict";n.d(t,{OutsideEvent:()=>i});var o=n(36383);function i(e){const{children:t,...n}=e;return t((0,o.useOutsideEvent)(n))}},86431:(e,t,n)=>{"use strict";n.d(t,{makeOverlapable:()=>r});var o=n(50959),i=n(65718);function r(e){return class extends o.PureComponent{render(){const{isOpened:t,root:n}=this.props;if(!t)return null;const r=o.createElement(e,{...this.props,zIndex:150});return"parent"===n?r:o.createElement(i.Portal,null,r)}}}},29197:(e,t,n)=>{"use strict";n.d(t,{CloseDelegateContext:()=>r});var o=n(50959),i=n(59064);const r=o.createContext(i.globalCloseDelegate)},65718:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>l,PortalContext:()=>c});var o=n(50959),i=n(962),r=n(36174),s=n(67961),a=n(60508);class l extends o.PureComponent{constructor(){super(...arguments),this._uuid=(0,r.guid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);return e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"",i.createPortal(o.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,s.getRootOverlapManager)():this.context}}l.contextType=a.SlotContext;const c=o.createContext(null)},96040:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var o=n(44352),i=n(50959),r=n(97754),s=n(9745),a=n(33765),l=n(27306);function c(e){const{className:t,isActive:c,onClick:d,onMouseDown:u,title:p,hidden:h,"data-name":m="remove-button",...f}=e;return i.createElement(s.Icon,{...f,"data-name":m,className:r(l.button,"apply-common-tooltip",c&&l.active,h&&l.hidden,t),icon:a,onClick:d,onMouseDown:u,title:p||o.t(null,void 0,n(34596))})}},60508:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>i,SlotContext:()=>r});var o=n(50959);class i extends o.Component{shouldComponentUpdate(){return!1}render(){return o.createElement("div",{
style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const r=o.createContext(null)},12811:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>i,HorizontalDropDirection:()=>s,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>r,getPopupPositioner:()=>c});var o,i,r,s,a=n(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(i||(i={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(r||(r={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(s||(s={}));const l={verticalAttachEdge:o.Bottom,horizontalAttachEdge:i.Left,verticalDropDirection:r.FromTopToBottom,horizontalDropDirection:s.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return(n,c,d,u)=>{var p,h;const m=(0,a.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:f=l.horizontalAttachEdge,horizontalDropDirection:g=l.horizontalDropDirection,horizontalMargin:v=l.horizontalMargin,verticalMargin:y=l.verticalMargin,matchButtonAndListboxWidths:b=l.matchButtonAndListboxWidths}=t;let _=null!==(p=t.verticalAttachEdge)&&void 0!==p?p:l.verticalAttachEdge,E=null!==(h=t.verticalDropDirection)&&void 0!==h?h:l.verticalDropDirection;_===o.AutoStrict&&(u<m.y+m.height+y+c?(_=o.Top,E=r.FromBottomToTop):(_=o.Bottom,E=r.FromTopToBottom));const w=_===o.Top?-1*y:y,C=f===i.Right?m.right:m.left,D=_===o.Top?m.top:m.bottom,S={x:C-(g===s.FromRightToLeft?n:0)+v,y:D-(E===r.FromBottomToTop?c:0)+w};return b&&(S.overrideWidth=m.width),S}}},3347:(e,t,n)=>{"use strict";n.d(t,{convertToDefinitionProperty:()=>r,makeProxyDefinitionProperty:()=>i});var o=n(51768);function i(e,t,n){const o=new Map,i=void 0!==t?t[0]:e=>e,r=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,s={value:()=>i(e.value()),setValue:t=>{e.setValue(r(t))},subscribe:(t,n)=>{const i=e=>{n(s)};o.set(n,i),e.subscribe(t,i)},unsubscribe:(t,n)=>{const i=o.get(n);i&&(e.unsubscribe(t,i),o.delete(n))},unsubscribeAll:t=>{e.unsubscribeAll(t),o.clear()},destroy:()=>{e.release(),null==n||n()}};return s}function r(e,t,n,r,s,a,l){const c=i(t.weakReference(),r,a),d=void 0!==r?void 0!==r[1]?r[1]:r[0]:e=>e,u=null!=s?s:o=>e.setProperty(t,d(o),n);return c.setValue=e=>{var t;l&&(0,o.trackEvent)(l.category,l.event,null===(t=l.label)||void 0===t?void 0:t.call(l,e)),u(e)},c}},43715:(e,t,n)=>{"use strict";n.d(t,{createLinePropertyDefinition:()=>l});var o=n(73436),i=n(79849);const r=[i.LINESTYLE_SOLID,i.LINESTYLE_DOTTED,i.LINESTYLE_DASHED],s=[1,2,3,4],a=[o.LineEnd.Normal,o.LineEnd.Arrow];function l(e,t){const n={propType:"line",properties:e,...t};return void 0!==n.properties.style&&(n.styleValues=r),void 0!==n.properties.width&&(n.widthValues=s),void 0===n.properties.leftEnd&&void 0===n.properties.rightEnd||void 0!==n.endsValues||(n.endsValues=a),void 0!==n.properties.value&&void 0===n.valueType&&(n.valueType=1),n}},46141:(e,t,n)=>{"use strict";function o(e,t){return{propType:"checkable",
properties:e,...t}}function i(e,t,n){return{propType:"checkableSet",properties:e,childrenDefinitions:n,...t}}function r(e,t){return{propType:"color",properties:e,noAlpha:!1,...t}}n.d(t,{convertFromReadonlyWVToDefinitionProperty:()=>A,convertFromWVToDefinitionProperty:()=>L,convertToDefinitionProperty:()=>F.convertToDefinitionProperty,createCheckablePropertyDefinition:()=>o,createCheckableSetPropertyDefinition:()=>i,createColorPropertyDefinition:()=>r,createCoordinatesPropertyDefinition:()=>S,createEmojiPropertyDefinition:()=>M,createLeveledLinePropertyDefinition:()=>d,createLinePropertyDefinition:()=>s.createLinePropertyDefinition,createNumberPropertyDefinition:()=>u,createOptionalTwoColorsPropertyDefinition:()=>D,createOptionsPropertyDefinition:()=>p,createPropertyDefinitionsGeneralGroup:()=>R,createPropertyDefinitionsLeveledLinesGroup:()=>I,createRangePropertyDefinition:()=>P,createSelectionCoordinatesPropertyDefinition:()=>x,createSessionPropertyDefinition:()=>V,createStudyInputsPropertyDefinition:()=>k,createSymbolPropertyDefinition:()=>N,createTextPropertyDefinition:()=>w,createTransparencyPropertyDefinition:()=>T,createTwoColorsPropertyDefinition:()=>C,createTwoOptionsPropertyDefinition:()=>h,destroyDefinitions:()=>K,getColorDefinitionProperty:()=>Y,getLockPriceScaleDefinitionProperty:()=>G,getPriceScaleSelectionStrategyDefinitionProperty:()=>z,getScaleRatioDefinitionProperty:()=>H,getSymbolDefinitionProperty:()=>X,isPropertyDefinitionsGroup:()=>q,makeProxyDefinitionProperty:()=>F.makeProxyDefinitionProperty});var s=n(43715),a=n(79849);const l=[a.LINESTYLE_SOLID,a.LINESTYLE_DOTTED,a.LINESTYLE_DASHED],c=[1,2,3,4];function d(e,t){const n={propType:"leveledLine",properties:e,...t};return void 0!==n.properties.style&&(n.styleValues=l),void 0!==n.properties.width&&(n.widthValues=c),n}function u(e,t){return{propType:"number",properties:e,type:1,...t}}function p(e,t){return{propType:"options",properties:e,...t}}function h(e,t){return{propType:"twoOptions",properties:e,...t}}var m=n(44352);const f=[{id:"bottom",value:"bottom",title:m.t(null,void 0,n(65994))},{id:"middle",value:"middle",title:m.t(null,void 0,n(76476))},{id:"top",value:"top",title:m.t(null,void 0,n(91757))}],g=[{id:"left",value:"left",title:m.t(null,void 0,n(19286))},{id:"center",value:"center",title:m.t(null,void 0,n(72171))},{id:"right",value:"right",title:m.t(null,void 0,n(21141))}],v=[{id:"horizontal",value:"horizontal",title:m.t(null,void 0,n(77405))},{id:"vertical",value:"vertical",title:m.t(null,void 0,n(44085))}],y=[10,11,12,14,16,20,24,28,32,40].map((e=>({title:String(e),value:e}))),b=[1,2,3,4],_=m.t(null,void 0,n(92960)),E=m.t(null,void 0,n(90581));function w(e,t){const n={propType:"text",properties:e,...t,isEditable:t.isEditable||!1};return void 0!==n.properties.size&&void 0===n.sizeItems&&(n.sizeItems=y),void 0!==n.properties.alignmentVertical&&void 0===n.alignmentVerticalItems&&(n.alignmentVerticalItems=f),void 0!==n.properties.alignmentHorizontal&&void 0===n.alignmentHorizontalItems&&(n.alignmentHorizontalItems=g),
(n.alignmentVerticalItems||n.alignmentHorizontalItems)&&void 0===n.alignmentTitle&&(n.alignmentTitle=_),void 0!==n.properties.orientation&&(void 0===n.orientationItems&&(n.orientationItems=v),void 0===n.orientationTitle&&(n.orientationTitle=E)),void 0!==n.properties.borderWidth&&void 0===n.borderWidthItems&&(n.borderWidthItems=b),n}function C(e,t){return{propType:"twoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function D(e,t){return{propType:"optionalTwoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function S(e,t){return{propType:"coordinates",properties:e,...t}}function x(e,t){return{propType:"selectionCoordinates",properties:e,...t}}function P(e,t){return{propType:"range",properties:e,...t}}function T(e,t){return{propType:"transparency",properties:e,...t}}function N(e,t){return{propType:"symbol",properties:e,...t}}function V(e,t){return{propType:"session",properties:e,...t}}function M(e,t){return{propType:"emoji",properties:e,...t}}function k(e,t){return{propType:"studyInputs",properties:e,...t}}var B=n(97145);function R(e,t,n,o){return{id:t,title:n,visible:o,groupType:"general",definitions:new B.WatchedValue(e)}}function I(e,t,n){return{id:t,title:n,groupType:"leveledLines",definitions:new B.WatchedValue(e)}}var F=n(3347);function W(e,t,n){const o=new Map,i=void 0!==t?t[0]:e=>e,r=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,s={value:()=>i(e.value()),setValue:t=>{var n;null===(n=e.setValue)||void 0===n||n.call(e,r(t))},subscribe:(t,n)=>{const i=()=>{n(s)};let r=o.get(t);void 0===r?(r=new Map,r.set(n,i),o.set(t,r)):r.set(n,i),e.subscribe(i)},unsubscribe:(t,n)=>{const i=o.get(t);if(void 0!==i){const t=i.get(n);void 0!==t&&(e.unsubscribe(t),i.delete(n))}},unsubscribeAll:t=>{const n=o.get(t);void 0!==n&&(n.forEach(((t,n)=>{e.unsubscribe(t)})),n.clear())}};return n&&(s.destroy=()=>n()),s}function L(e,t,n,o){const i=W(t,o),r=void 0!==o?void 0!==o[1]?o[1]:o[0]:e=>e;return i.setValue=o=>e.setWatchedValue(t,r(o),n),i}function A(e,t){return function(e,t,n,o){const i=new Map;return W({subscribe:(n,o)=>{const r=e=>n(t(e));i.set(n,r),e.subscribe(r,o)},unsubscribe:t=>{if(t){const n=i.get(t);n&&(e.unsubscribe(n),i.delete(t))}else i.clear(),e.unsubscribe()},value:()=>t(e.value())},n,o)}(e,(e=>e),t,(()=>e.release()))}function z(e,t){const n=(0,F.makeProxyDefinitionProperty)(t.weakReference());return n.setValue=t=>e.setPriceScaleSelectionStrategy(t),n}function G(e,t,n,o){const i=(0,F.makeProxyDefinitionProperty)(t.weakReference());return i.setValue=t=>{const i={lockScale:t};e.setPriceScaleMode(i,n,o)},i}function H(e,t,n,o){const i=(0,F.makeProxyDefinitionProperty)(t.weakReference(),o);return i.setValue=o=>{e.setScaleRatioProperty(t,o,n)},i}var O=n(24377),U=n(87095),$=n(49152);function J(e,t){if((0,U.isHexColor)(e)){const n=(0,O.parseRgb)(e);return(0,O.rgbaToString)((0,O.rgba)(n,(100-t)/100))}return e}function Y(e,t,n,o,i){let r;if(null!==n){const e=(0,$.combineProperty)(J,t.weakReference(),n.weakReference());r=(0,F.makeProxyDefinitionProperty)(e.ownership())}else r=(0,
F.makeProxyDefinitionProperty)(t.weakReference(),[()=>J(t.value(),0),e=>e]);return r.setValue=n=>{i&&e.beginUndoMacro(o),e.setProperty(t,n,o),i&&e.endUndoMacro()},r}function X(e,t,n,o,i,r){const s=[(a=n,l=t,e=>{const t=a(l);if(e===l.value()&&null!==t){const e=t.ticker||t.full_name;if(e)return e}return e}),e=>e];var a,l;const c=(0,F.convertToDefinitionProperty)(e,t,i,s);r&&(c.setValue=r);const d=new Map;c.subscribe=(e,n)=>{const o=e=>{n(c)};d.set(n,o),t.subscribe(e,o)},c.unsubscribe=(e,n)=>{const o=d.get(n);o&&(t.unsubscribe(e,o),d.delete(n))};const u={};return o.subscribe(u,(()=>{d.forEach(((e,t)=>{t(c)}))})),c.destroy=()=>{o.unsubscribeAll(u),d.clear()},c}function q(e){return e.hasOwnProperty("groupType")}function K(e){e.forEach((e=>{var t;if(e.hasOwnProperty("propType")){Object.keys(e.properties).forEach((t=>{const n=e.properties[t];void 0!==n&&void 0!==n.destroy&&n.destroy()}))}else K(e.definitions.value()),null===(t=e.visible)||void 0===t||t.destroy()}))}},64420:(e,t,n)=>{"use strict";n.d(t,{getInputGroups:()=>s,isGroup:()=>i,isInputInlines:()=>r});var o=n(50151);function i(e){return e.hasOwnProperty("groupType")}function r(e){return i(e)&&"inline"===e.groupType}function s(e){const t=[],n=new Map,i=new Map;return i.set(void 0,new Map),e.forEach((e=>{const{group:r,inline:s}=e;if(void 0!==r||void 0!==s)if(void 0!==r)if(void 0!==s)if(n.has(r)){const t=(0,o.ensureDefined)(n.get(r));let l;i.has(t)?l=(0,o.ensureDefined)(i.get(t)):(l=new Map,i.set(t,l)),a(e,"inline",s,l,t.children)}else{const o={id:s,groupType:"inline",children:[e]},a={id:r,groupType:"group",children:[o]},l=new Map;l.set(s,o),i.set(a,l),n.set(r,a),t.push(a)}else a(e,"group",r,n,t);else{const n=(0,o.ensureDefined)(i.get(void 0));a(e,"inline",(0,o.ensureDefined)(s),n,t)}else t.push(e)})),t}function a(e,t,n,i,r){if(i.has(n))(0,o.ensureDefined)(i.get(n)).children.push(e);else{const o={id:n,groupType:t,children:[e]};i.set(n,o),r.push(o)}}},44996:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},23851:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},57740:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},
80427:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M5.5 7a.5.5 0 0 0 0 1h17a.5.5 0 0 0 0-1h-17Zm0 6a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Zm7 0a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Zm6.5.5c0-.28.22-.5.5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5ZM7 20a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm5-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"/></svg>'},501:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},98853:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 13.5H24m-19.5 0L8 17m-3.5-3.5L8 10"/></svg>'},43382:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 13.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm0 0H24"/></svg>'},8295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 21h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3c2 0 4 1 4 3 0 1 0 2-1.5 3 1.5.5 2.5 2 2.5 4 0 2.75-2.638 4-5 4zM12 9l.004 3c.39.026.82 0 1.25 0C14.908 12 16 11.743 16 10.5c0-1.1-.996-1.5-2.5-1.5-.397 0-.927-.033-1.5 0zm0 5v5h1.5c1.5 0 3.5-.5 3.5-2.5S15 14 13.5 14c-.5 0-.895-.02-1.5 0z"/></svg>'},29285:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12.143 20l1.714-12H12V7h5v1h-2.143l-1.714 12H15v1h-5v-1h2.143z"/></svg>'}}]);