webpackJsonp([6],{108:function(e,t,n){"use strict";function o(e){return t=function(t){function n(e,n){var o=t.call(this,e,n)||this;return o._getWrappedComponent=function(e){o._instance=e},o}return r.__extends(n,t),n.prototype.componentDidMount=function(){this._instance.componentWillEnter&&this.context.lifecycleProvider.registerWillEnterCb(this._instance.componentWillEnter.bind(this._instance)),this._instance.componentDidEnter&&this.context.lifecycleProvider.registerDidEnterCb(this._instance.componentDidEnter.bind(this._instance)),this._instance.componentWillLeave&&this.context.lifecycleProvider.registerWillLeaveCb(this._instance.componentWillLeave.bind(this._instance)),this._instance.componentDidLeave&&this.context.lifecycleProvider.registerDidLeaveCb(this._instance.componentDidLeave.bind(this._instance))},n.prototype.render=function(){return i.createElement(e,r.__assign({},this.props,{ref:this._getWrappedComponent}),this.props.children)},n}(i.PureComponent),t.displayName="Lifecycle Consumer",t.contextTypes={lifecycleProvider:s.object},t;var t}var r,i,s,a;Object.defineProperty(t,"__esModule",{value:!0}),r=n(5),i=n(2),s=n(86),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t}(i.PureComponent),t.LifecycleConsumer=a,t.makeTransitionGroupLifecycleConsumer=o},153:function(e,t){e.exports={body:"body-1yAIljnK-"}},154:function(e,t){e.exports={footer:"footer-AwxgkLHY-"}},155:function(e,t){e.exports={header:"header-2Hz0Lxou-",close:"close-3H_MMLbw-"}},156:function(e,t){e.exports={message:"message-1CYdTy_T-",error:"error-1u_m-Ehm-"}},157:function(e,t){e.exports={dialog:"dialog-13-QRYuG-",rounded:"rounded-1GLivxii-",shadowed:"shadowed-30OTTAts-"}},182:function(e,t,n){"use strict";function o(e){return r.createElement("div",{className:i.body,ref:e.reference},e.children)}var r,i;Object.defineProperty(t,"__esModule",{value:!0}),r=n(2),i=n(153),t.Body=o},183:function(e,t,n){"use strict";var o,r,i,s;Object.defineProperty(t,"__esModule",{value:!0}),o=n(185),t.Header=o.Header,r=n(184),t.Footer=r.Footer,i=n(182),t.Body=i.Body,s=n(186),t.Message=s.Message},184:function(e,t,n){"use strict";function o(e){return r.createElement("div",{className:i.footer,ref:e.reference},e.children)}var r,i;Object.defineProperty(t,"__esModule",{value:!0}),r=n(2),i=n(154),t.Footer=o},185:function(e,t,n){"use strict";function o(e){return r.createElement("div",{className:i.header,"data-dragg-area":!0,ref:e.reference},e.children,r.createElement(a.Icon,{className:i.close,icon:s,onClick:e.onClose}))}var r,i,s,a;Object.defineProperty(t,"__esModule",{value:!0}),r=n(2),i=n(155),s=n(109),a=n(77),t.Header=o},186:function(e,t,n){"use strict";function o(e){var t,n;return e.text?t=r.createElement("span",null,e.text):e.html&&(t=r.createElement("span",{dangerouslySetInnerHTML:{__html:e.html}})),n=i.message,e.isError&&(n+=" "+i.error),r.createElement(s.CSSTransitionGroup,{transitionName:"message",transitionEnterTimeout:c.dur,transitionLeaveTimeout:c.dur},t?r.createElement("div",{className:n,key:"0"
},r.createElement(a.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:e.onClickOutside},t)):null)}var r,i,s,a,c;Object.defineProperty(t,"__esModule",{value:!0}),r=n(2),i=n(156),s=n(46),a=n(107),c=n(28),t.Message=o},187:function(e,t,n){"use strict";function o(e){var t,n=e.rounded,o=void 0===n||n,a=e.shadowed,c=void 0===a||a,u=e.className,l=void 0===u?"":u,d=s(i.dialog,(t={},t[l]=!!l,t[i.rounded]=o,t[i.shadowed]=c,t[i.animated]=i.animated,t)),p={bottom:e.bottom,left:e.left,maxWidth:e.width,right:e.right,top:e.top,zIndex:e.zIndex};return r.createElement("div",{className:d,ref:e.reference,style:p,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onClick:e.onClick},e.children)}var r,i,s;Object.defineProperty(t,"__esModule",{value:!0}),r=n(2),i=n(157),s=n(26),t.Dialog=o},188:function(e,t,n){"use strict";function o(){d=document.createElement("div"),document.body.appendChild(d),i()}function r(e,t){p.getItems().forEach(function(n){n!==t&&m.emitEvent(e+":"+n)})}function i(e){a.render(s.createElement(c.TransitionGroup,{component:"div"},Array.from(f.values())),d,e)}var s,a,c,u,l,d,p,h,m,f;Object.defineProperty(t,"__esModule",{value:!0}),s=n(2),a=n(55),c=n(46),u=n(110),l=function(){function e(){this._storage=[]}return e.prototype.add=function(e){this._storage.push(e)},e.prototype.remove=function(e){this._storage=this._storage.filter(function(t){return e!==t})},e.prototype.getIndex=function(e){return this._storage.findIndex(function(t){return e===t})},e.prototype.toTop=function(e){this.remove(e),this.add(e)},e.prototype.has=function(e){return this._storage.includes(e)},e.prototype.getItems=function(){return this._storage},e}(),t.EVENTS={ZindexUpdate:"ZindexUpdate"},p=new l,h=150,m=new u,f=new Map,function(e){function n(e){p.has(e)||(p.add(e),r(t.EVENTS.ZindexUpdate,e))}function o(e){p.remove(e),f.delete(e)}function s(e){return p.getIndex(e)+h}function a(e,t,n){f.set(e,t),i(n)}function c(e,t){o(e),i(t)}function u(){return m}e.registerWindow=n,e.unregisterWindow=o,e.getZindex=s,e.renderWindow=a,e.removeWindow=c,e.getStream=u}(t.OverlapManager||(t.OverlapManager={})),o()},189:function(e,t,n){"use strict";function o(e){return t=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return i.__extends(n,t),n.prototype.componentWillEnter=function(e){this.props.beforeOpen?this.props.beforeOpen(e):e()},n.prototype.componentDidEnter=function(){this.props.afterOpen&&this.props.afterOpen()},n.prototype.componentWillLeave=function(e){this.props.beforeClose?this.props.beforeClose(e):e()},n.prototype.componentDidLeave=function(){this.props.afterClose&&this.props.afterClose()},n.prototype.render=function(){return s.createElement(e,i.__assign({},this.props))},n}(s.PureComponent),t.displayName="OverlapLifecycle("+(e.displayName||"Component")+")",t;var t}function r(e){var t,n=u.makeTransitionGroupLifecycleProvider(l.makeTransitionGroupLifecycleConsumer(o(e)));return t=function(e){function t(t){var n=e.call(this,t)||this;return n._onZIndexUpdate=function(){
n.props.isOpened&&("parent"===n.props.root?n.forceUpdate():n._renderWindow(n.props))},n._uuid=d.guid(),n._zIndexUpdateEvent=c.EVENTS.ZindexUpdate+":"+n._uuid,n}return i.__extends(t,e),t.prototype.componentDidMount=function(){this._subscribe()},t.prototype.componentWillUnmount=function(){this._unsubscribe(),c.OverlapManager.removeWindow(this._uuid)},t.prototype.componentWillReceiveProps=function(e){"parent"!==this.props.root&&this._renderWindow(e)},t.prototype.render=function(){return"parent"===this.props.root?s.createElement(a.TransitionGroup,{component:"div"},this._createContent(this.props)):null},t.prototype._renderWindow=function(e){c.OverlapManager.renderWindow(this._uuid,this._createContent(e))},t.prototype._createContent=function(e){return e.isOpened?(c.OverlapManager.registerWindow(this._uuid),s.createElement(n,i.__assign({},e,{key:this._uuid,zIndex:c.OverlapManager.getZindex(this._uuid)}),e.children)):(c.OverlapManager.unregisterWindow(this._uuid),null)},t.prototype._subscribe=function(){c.OverlapManager.getStream().on(this._zIndexUpdateEvent,this._onZIndexUpdate)},t.prototype._unsubscribe=function(){c.OverlapManager.getStream().off(this._zIndexUpdateEvent,this._onZIndexUpdate)},t}(s.PureComponent),t.displayName="Overlapable("+(e.displayName||"Component")+")",t}var i,s,a,c,u,l,d;Object.defineProperty(t,"__esModule",{value:!0}),i=n(5),s=n(2),a=n(46),c=n(188),u=n(191),l=n(108),d=n(64),t.makeOverlapable=r},191:function(e,t,n){"use strict";function o(e){return t=function(t){function n(e){var n=t.call(this,e)||this;return n._registerWillEnterCb=function(e){n._willEnter.push(e)},n._registerDidEnterCb=function(e){n._didEnter.push(e)},n._registerWillLeaveCb=function(e){n._willLeave.push(e)},n._registerDidLeaveCb=function(e){n._didLeave.push(e)},n._willEnter=[],n._didEnter=[],n._willLeave=[],n._didLeave=[],n}return r.__extends(n,t),n.prototype.getChildContext=function(){return{lifecycleProvider:{registerWillEnterCb:this._registerWillEnterCb,registerDidEnterCb:this._registerDidEnterCb,registerWillLeaveCb:this._registerWillLeaveCb,registerDidLeaveCb:this._registerDidLeaveCb}}},n.prototype.componentWillEnter=function(e){var t=this._willEnter.map(function(e){return new Promise(function(t){e(t)})});Promise.all(t).then(e)},n.prototype.componentDidEnter=function(){this._didEnter.forEach(function(e){e()})},n.prototype.componentWillLeave=function(e){var t=this._willLeave.map(function(e){return new Promise(function(t){e(t)})});Promise.all(t).then(e)},n.prototype.componentDidLeave=function(){this._didLeave.forEach(function(e){e()})},n.prototype.render=function(){return i.createElement(e,r.__assign({},this.props),this.props.children)},n}(i.PureComponent),t.displayName="Lifecycle Provider",t.childContextTypes={lifecycleProvider:s.object},t;var t}var r,i,s;Object.defineProperty(t,"__esModule",{value:!0}),r=n(5),i=n(2),s=n(86),t.makeTransitionGroupLifecycleProvider=o},201:function(e,t){e.exports={inputWrapper:"inputWrapper-23iUt2Ae-",textInput:"textInput-3hvomQp9-",error:"error-2ydrzcvg-",success:"success-2y4Cbw_L-",
xsmall:"xsmall-2Dz_yiDV-",small:"small-3g0mV7FW-",large:"large-CZ_w52Xn-",iconed:"iconed-2X3rffL9-",inputIcon:"inputIcon-nJhQLYdg-",clearable:"clearable-3HVD9v3B-",clearIcon:"clearIcon-UZyPhrkf-",grouped:"grouped-2xS7_jZ2-"}},255:function(e,t){e.exports={ghost:"ghost-2xkVjo4o-",primary:"primary-nwbhr-QZ-",success:"success-13aJxw0L-",danger:"danger-1jVFBwRj-",warning:"warning-1sksz0Rq-",secondary:"secondary-2H-Jwmir-",button:"button-2jCWN5M1-",withPadding:"withPadding-UPhHkA1c-",hiddenText:"hiddenText-13D-S4nC-",text:"text-1d01iK8L-",loader:"loader-1aihbDEL-",base:"base-2PiGQ3aT-",secondaryScript:"secondaryScript-3pO7_bSM-",link:"link-1v4pOPj2-",xsmall:"xsmall-1XIYvP4K-",small:"small-2fwP8rrU-",large:"large-1a-qmr37-",grouped:"grouped-3T2Y_EXg-",growable:"growable-S0qQuxEB-",active:"active-3Eem2fUt-",disabled:"disabled-2jaDvv_v-"}},256:function(e,t){e.exports={dialog:"dialog-2MaUXXMw-",dragging:"dragging-3oeGgbIQ-"}},257:function(e,t){e.exports={loader:"loader-3q6az1FO-",item:"item-I1y2jPt8-","tv-button-loader":"tv-button-loader-or3q47Bu-",black:"black-29_3FgL9-",white:"white-PhPAMkPg-"}},258:function(e,t){e.exports={calendar:"calendar-31T-f8xW-",header:"header-1IspLgQG-",title:"title-165i_Hrv-",switchBtn:"switchBtn-3TukPHdl-",prev:"prev-x9isN-kp-",next:"next-34HZFkxR-",month:"month-2hQY5F0z-",weekdays:"weekdays-uTDB6j1H-",weeks:"weeks-2XtkuRel-",week:"week-5_tvG-8b-",day:"day-1IQ3pxmF-",disabled:"disabled-3CCvhen1-",selected:"selected-1HN89txM-",currentDay:"currentDay-3Ojny1au-",otherMonth:"otherMonth-3Wqk07bY-"}},259:function(e,t){e.exports={clock:"clock-24NOooZu-",header:"header-2zhGNKci-",number:"number-2B8nA4rf-",active:"active-p8XQNXmZ-",body:"body-1lkcbofs-",clockFace:"clockFace-ratNcUJ8-",face:"face-2S_dz-Lt-",inner:"inner-1O9iOFrM-",hand:"hand-14LdqePA-",knob:"knob-3OZv5WLP-",centerDot:"centerDot-1Fru2d4j-"}},322:function(e,t,n){"use strict";function o(e){switch(e){case"default":return u.base;case"primary":return u.primary;case"secondary":return u.secondary;case"secondary-script":return u.secondaryScript;case"success":return u.success;case"warning":return u.warning;case"danger":return u.danger;case"link":return u.link;default:return""}}function r(e){switch(e){case"xsmall":return u.xsmall;case"small":return u.small;case"large":return u.large;default:return""}}function i(e){switch(e){case"ghost":return u.ghost;default:return""}}function s(e){var t,n=e.active,s=void 0===n||n,h=e.children,m=e.className,f=void 0===m?"":m,_=e.disabled,v=void 0!==_&&_,g=e.grouped,y=void 0!==g&&g,w=e.growable,E=void 0!==w&&w,M=e.id,b=void 0===M?0:M,C=e.onClick,k=e.reference,x=e.size,D=e.theme,S=e.type,P=void 0===S?"default":S,T=e.loading,N=void 0!==T&&T,L=e.withPadding,O=void 0===L||L,H=e.title,I=void 0===H?"":H,W=e.disabledClassName,z=e.tabIndex,j=void 0===z?0:z,F=e.component,V=void 0===F?"button":F,R=e.target,Y=void 0===R?"":R,G=e.href,U=void 0===G?"":G,B=c((t={},t[f]=!!f,t[u.button]=!0,t[u.active]=s&&!v,t[W||u.disabled]=v,t[u.grouped]=y,t[u.growable]=E,t[u.withPadding]=O,t[r(x)]=!!x,t[i(D)]=!!D,t[o(P)]=!0,
t)),A="default"===P?"black":"white",X=a.createElement("span",{key:"1",className:u.text},h);return N&&(X=a.createElement("span",{key:"2",className:u.loader},a.createElement(d.Loader,{color:A}))),a.createElement(l.CSSTransitionGroup,{component:V,tabIndex:j,transitionEnterTimeout:p.dur,transitionLeaveTimeout:p.dur,transitionName:"body",className:B,disabled:v,key:b,onClick:N?void 0:C,ref:k,title:I,target:Y,href:U},a.createElement("span",{className:u.hiddenText},h),X)}var a,c,u,l,d,p;Object.defineProperty(t,"__esModule",{value:!0}),a=n(2),c=n(26),u=n(255),l=n(46),d=n(327),p=n(28),t.Button=s},323:function(e,t){"use strict";function n(e,t,n){return e+t>n&&(e=n-t),e<0&&(e=0),e}function o(e){return{x:e.pageX,y:e.pageY}}function r(e){return{x:e.touches[0].pageX,y:e.touches[0].pageY}}Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e,t){var n=this;this._drag=null,this._onMouseDragStart=function(e){e.preventDefault(),n._dragStart(o(e))},this._onTouchDragStart=function(e){n._dragStart(r(e))},this._onMouseDragMove=function(e){n._dragMove(o(e))},this._onTouchDragMove=function(e){e.preventDefault(),n._dragMove(r(e))},this._onDragStop=function(){n._drag=null,n._header.classList.remove("dragging")},this._dialog=e,this._header=t,t.addEventListener("mousedown",this._onMouseDragStart),t.addEventListener("touchstart",this._onTouchDragStart),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("touchmove",this._onTouchDragMove),document.addEventListener("mouseup",this._onDragStop),document.addEventListener("touchend",this._onDragStop)}return e.prototype.destroy=function(){this._header.removeEventListener("mousedown",this._onMouseDragStart),this._header.removeEventListener("touchstart",this._onTouchDragStart),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("touchmove",this._onTouchDragMove),document.removeEventListener("mouseup",this._onDragStop),document.removeEventListener("touchend",this._onDragStop)},e.prototype._dragStart=function(e){var t=this._dialog.getBoundingClientRect();this._drag={startX:e.x,startY:e.y,dialogX:t.left,dialogY:t.top},this._dialog.style.left=t.left+"px",this._dialog.style.top=t.top+"px",this._header.classList.add("dragging")},e.prototype._dragMove=function(e){var t,n;this._drag&&(t=e.x-this._drag.startX,n=e.y-this._drag.startY,this._moveDialog(this._drag.dialogX+t,this._drag.dialogY+n))},e.prototype._moveDialog=function(e,t){var o=this._dialog.getBoundingClientRect();this._dialog.style.left=n(e,o.width,window.innerWidth)+"px",this._dialog.style.top=n(t,o.height,window.innerHeight)+"px"},e}();t.DragHandler=i},324:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l,d,p,h;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(187),s=n(189),a=n(108),c=n(107),u=n(256),l=n(323),d=n(325),p=n(26),h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._setDialog=function(e){e&&(t._dialog=e)},t}return o.__extends(t,e),t.prototype.render=function(){return r.createElement("span",{
style:{zIndex:this.props.zIndex}},r.createElement(c.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this.props.onClickOutside},r.createElement(i.Dialog,o.__assign({},this.props,{reference:this._setDialog,className:p(u.dialog,this.props.className)}),this.props.children)))},t.prototype.componentDidMount=function(){if(this._dialog){var e=this._dialog.querySelector("[data-dragg-area]");e&&(this._drag=new l.DragHandler(this._dialog,e)),this._resize=new d.ResizeHandler(this._dialog)}},t.prototype.componentWillEnter=function(e){this._resize&&this._resize.position(),e()},t.prototype.componentWillUnmount=function(){this._drag&&this._drag.destroy(),this._resize&&this._resize.destroy()},t}(r.PureComponent),t.PopupDialog=s.makeOverlapable(a.makeTransitionGroupLifecycleConsumer(h))},325:function(e,t){"use strict";function n(e,t,n){return e+t>n&&(e=n-t),e<0&&(e=0),e}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e){this._onResizeThrottled=requestAnimationFrame.bind(null,this._onResize.bind(this)),this._dialog=e,this._initialHeight=e.style.height,window.addEventListener("resize",this._onResizeThrottled)}return e.prototype.position=function(){var e,t=this._dialog.getBoundingClientRect(),n=window.innerWidth/2-t.width/2;this._dialog.style.left=n+"px",e=window.innerHeight/2-t.height/2,this._dialog.style.top=e+"px"},e.prototype.destroy=function(){window.removeEventListener("resize",this._onResizeThrottled)},e.prototype._onResize=function(){var e,t=this._dialog.getBoundingClientRect(),o=n(t.left,t.width,window.innerWidth);o!==t.left&&(this._dialog.style.left=o+"px"),e=n(t.top,t.height,window.innerHeight),e!==t.top&&(this._dialog.style.top=e+"px"),this._dialog.style.height=window.innerHeight<t.height?window.innerHeight+"px":this._initialHeight},e}();t.ResizeHandler=o},326:function(e,t,n){"use strict";function o(e){var t,n=e.className,o=e.icon,d=e.clearable,p=e.onClear,h=e.size,m=r.__rest(e,["className","icon","clearable","onClear","size"]),f=s(u.inputWrapper,(t={},t[n]=!!n,t[u.iconed]=!!o,t[u.clearable]=d,t));return i.createElement(l,r.__assign({theme:u,className:f,leftComponent:o?i.createElement(a.Icon,{key:"inputIcon",icon:o,className:u.inputIcon}):void 0,rightComponent:d?i.createElement(a.Icon,{className:u.clearIcon,icon:c,key:"clearIcon",onClick:p}):void 0,sizeMode:h},m))}var r,i,s,a,c,u,l;Object.defineProperty(t,"__esModule",{value:!0}),r=n(5),i=n(2),s=n(26),a=n(77),c=n(331),u=n(201),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r.__extends(t,e),t.prototype.render=function(){var e,t,n,o,a=this.props,c=a.theme,u=a.error,l=a.success,d=a.sizeMode,p=a.leftComponent,h=a.rightComponent,m=a.grouped,f=a.fontSize,_=a.reference,v=a.className,g=r.__rest(a,["theme","error","success","sizeMode","leftComponent","rightComponent","grouped","fontSize","reference","className"]),y={fontSize:f},w=s(c.textInput,(n={},n[c.error]=u,n[c.success]=l,n[c[d]]=!!d,n)),E=s(c.inputWrapper,(o={},o[v]=!!v,o[c.grouped]=m,o)),M=[],b=i.createElement("input",r.__assign({ref:_,className:w,key:"textInput",
style:y},g));return p&&(e={className:s(c.leftComponent,p.props.className),key:"leftComponent"},M.push(i.cloneElement(p,e))),M.push(b),h&&(t={className:s(c.leftComponent,h.props.className),key:"rightComponent"},M.push(i.cloneElement(h,t))),i.createElement("div",{className:E},M)},t}(i.PureComponent),t.CommonTextInput=l,t.TextInput=o},327:function(e,t,n){"use strict";function o(e){var t,n=e.color,o=void 0===n?"black":n,u=s(c.item,(t={},t[c[o]]=!!o,t));return r.createElement(i.CSSTransitionGroup,{transitionName:"loader",transitionAppear:!0,transitionAppearTimeout:2*a.dur,transitionEnter:!1,transitionLeave:!1},r.createElement("span",{className:c.loader},r.createElement("span",{className:u}),r.createElement("span",{className:u}),r.createElement("span",{className:u})))}var r,i,s,a,c;Object.defineProperty(t,"__esModule",{value:!0}),r=n(2),i=n(46),s=n(26),a=n(28),c=n(257),t.Loader=o},500:function(e,t,n){"use strict";function o(e){i({isOpened:!1}),i({isOpened:!0,onClose:function(){i({isOpened:!1}),u=null},dateOnly:e.model().mainSeries().isDWM(),onGoToDate:function(t){r(e,t)}})}function r(e,t){void 0!==e.model().timeScale().tickMarks().minIndex&&(i({isOpened:!0,processing:!0}),e.model().gotoTime(t).done(function(t){e.model().mainSeries().setGotoDateResult(t)}).always(function(){i({isOpened:!1,processing:!1})}))}function i(e){u||(u=document.createElement("div"),document.body.appendChild(u)),a.render(s.createElement(c.GoToDateDialog,e),u)}var s,a,c,u;Object.defineProperty(t,"__esModule",{value:!0}),s=n(2),a=n(55),c=n(1104),t.showGoToDateDialog=o},504:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l,d,p,h;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(77),s=n(326),a=n(201),c=n(26),u=n(46),l=n(28),d=n(107),p=n(662),h=function(e){function t(t){var n=e.call(this,t)||this;return n._onShowPicker=function(e){if(e){var t=e.getBoundingClientRect();t.width&&t.right>window.innerWidth?e.style.right="0":e.style.right="auto"}},n._onChange=function(){var e=n._input.value;n.setState({value:e}),n.props.onType(n.props.isValid(e)?e:null)},n._onKeyDown=function(e){n.props.onHidePicker()},n._onKeyPress=function(e){if(e.charCode){var t=String.fromCharCode(e.charCode);n.props.inputRegex.test(t)||e.preventDefault()}},n._onKeyUp=function(e){var t,o;8!==e.keyCode&&(t=n._input.value,(o=n.props.fixValue(t))!==t&&n.setState({value:o}))},n.state={value:t.value},n}return o.__extends(t,e),t.prototype.componentWillReceiveProps=function(e){e.value!==this.props.value&&this.setState({value:e.value})},t.prototype.render=function(){var e,t=this,n=c(p.inputIcon,(e={},e[p.disabled]=this.props.disabled,e));return r.createElement("div",{className:p.pickerInput},r.createElement(s.CommonTextInput,{value:this.state.value,onKeyDown:this._onKeyDown,onKeyPress:this._onKeyPress,onKeyUp:this._onKeyUp,onChange:this._onChange,onFocus:this.props.onShowPicker,onClick:this.props.onShowPicker,reference:function(e){return t._input=e},rightComponent:r.createElement(i.Icon,{icon:this.props.icon,className:n,onClick:this.props.disabled?void 0:this.props.onShowPicker}),
theme:a,className:a.inputWrapper,disabled:this.props.disabled,error:!this.props.isValid(this.state.value)}),r.createElement(d.OutsideEvent,{mouseDown:!0,handler:this.props.onHidePicker},r.createElement(u.CSSTransitionGroup,{transitionName:"tv-picker",transitionEnterTimeout:l.dur,transitionLeaveTimeout:l.dur},this.props.showPicker?r.createElement("div",{className:p.picker,key:"0",ref:this._onShowPicker},this.props.children):null)))},t}(r.PureComponent),t.PickerInput=h},505:function(e,t,n){"use strict";var o,r,i,s,a;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(259),s=n(26),a=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._renderNumber=function(e,n){var o,a=s(i.number,(o={},o[i.active]=e===t.props.activeNumber,o)),c=t.props.format?t.props.format(e):""+e;return r.createElement("span",{key:e,className:a,style:t._numberStyle(t.props.radius-t.props.spacing,n),"data-value":c},r.createElement("span",null,c))},t}return o.__extends(t,e),t.prototype.render=function(){return r.createElement("div",{className:i.face,style:this._faceStyle(),onMouseDown:this.props.onMouseDown,onTouchStart:this.props.onTouchStart},this.props.numbers.map(this._renderNumber))},t.prototype._faceStyle=function(){return{height:2*this.props.radius,width:2*this.props.radius}},t.prototype._numberStyle=function(e,t){var n=Math.PI/180*360/12*t;return{left:e+e*Math.sin(n)+this.props.spacing,top:e-e*Math.cos(n)+this.props.spacing}},t}(r.PureComponent),t.Face=a},506:function(e,t,n){"use strict";function o(e){return{x:e.pageX,y:e.pageY}}function r(e){return{x:e.touches[0].pageX,y:e.touches[0].pageY}}function i(e,t,n,o){var r=s(e,t,n,o);return r<0?360+r:r}function s(e,t,n,o){return 180*(Math.atan2(o-t,n-e)+Math.PI/2)/Math.PI}var a,c,u,l;Object.defineProperty(t,"__esModule",{value:!0}),a=n(5),c=n(2),u=n(259),l=function(e){function t(t){var n=e.call(this,t)||this;return n._onMouseMove=function(e){n._move(o(e))},n._onTouchMove=function(e){n._move(r(e))},n._onMouseUp=function(){document.removeEventListener("mousemove",n._onMouseMove),document.removeEventListener("mouseup",n._onMouseUp),n._endMove()},n._onTouchEnd=function(){document.removeEventListener("touchmove",n._onTouchMove),document.removeEventListener("touchend",n._onTouchEnd),n._endMove()},n}return a.__extends(t,e),t.prototype.render=function(){var e=this,t={height:this.props.length,transform:"rotate("+this.props.angle+"deg)"};return c.createElement("div",{className:u.hand,style:t},c.createElement("span",{ref:function(t){return e._knob=t},className:u.knob}))},t.prototype.mouseStart=function(e){document.addEventListener("mousemove",this._onMouseMove),document.addEventListener("mouseup",this._onMouseUp),this._move(o(e.nativeEvent))},t.prototype.touchStart=function(e){document.addEventListener("touchmove",this._onTouchMove),document.addEventListener("touchend",this._onTouchEnd),this._move(r(e.nativeEvent)),e.preventDefault(),e.stopPropagation()},t.prototype._endMove=function(){this.props.onMoveEnd&&this.props.onMoveEnd()},t.prototype._move=function(e){
var t=this._trimAngleToValue(this._positionToAngle(e)),n=this._getPositionRadius(e);!this.props.onMove||isNaN(t)||isNaN(n)||this.props.onMove(360===t?0:t,n)},t.prototype._trimAngleToValue=function(e){return this.props.step*Math.round(e/this.props.step)},t.prototype._positionToAngle=function(e){return i(this.props.center.x,this.props.center.y,e.x,e.y)},t.prototype._getPositionRadius=function(e){var t=this.props.center.x-e.x,n=this.props.center.y-e.y;return Math.sqrt(t*t+n*n)},t}(c.PureComponent),t.Hand=l},507:function(e,t){"use strict";function n(e,t,n){var o,r,i;for(void 0===n&&(n=1),o=Math.max(Math.ceil((t-e)/n),0),r=Array(o),i=0;i<o;i++,e+=n)r[i]=e;return r}function o(e){return("0"+e).slice(-2)}Object.defineProperty(t,"__esModule",{value:!0}),t.range=n,t.twoDigitsFormat=o},658:function(e,t){e.exports={dialog:"dialog-1u03BRLN-",formRow:"formRow-2yaURS6j-",cell:"cell-EVXJGc2w-",input:"input-iWIICkSL-",btn:"btn-2vvoNhxg-",button:"button-3N2pepAa-"}},661:function(e,t){e.exports={calendar:"calendar-3TJkQWuB-"}},662:function(e,t){e.exports={pickerInput:"pickerInput-1KuQJx85-",inputIcon:"inputIcon-2iqNmiTT-",disabled:"disabled-3mdc0fm2-",picker:"picker-145BnyQ1-"}},1104:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l,d,p,h,m,f,_;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),n(23),r=n(2),i=n(324),s=n(183),a=n(1171),c=n(1175),u=n(77),l=n(1188),d=n(322),p=n(658),h=n(36),m=n(234),f=n(26),_=function(e){function t(t){var n=e.call(this,t)||this;return n._todayMidnight=h("00:00","HH:mm"),n._onDatePick=function(e){n.setState({date:e})},n._onTimePick=function(e){n.setState({time:e})},n._onGoToDate=function(){var e,t;n.props.onGoToDate&&n.state.date&&n.state.time&&(e=n.state.date.clone(),e.hours(n.state.time.hours()),e.minutes(n.state.time.minutes()),t=new Date(e.format("YYYY-MM-DD[T]HH:mm[:00Z]")).valueOf(),n.props.onGoToDate(t))},n._onEscape=function(){n.props.onClose&&n.props.onClose()},n.state={date:h(),time:h("00:00","HH:mm")},n}return o.__extends(t,e),t.prototype.render=function(){return r.createElement(i.PopupDialog,{isOpened:this.props.isOpened,onClickOutside:this.props.onClose,className:p.dialog},r.createElement(s.Header,{onClose:this.props.onClose},window.t("Go to")),r.createElement(s.Body,null,r.createElement(m.KeyboardDocumentListener,{keyCode:27,handler:this._onEscape}),r.createElement(m.KeyboardDocumentListener,{keyCode:13,handler:this._onGoToDate}),r.createElement("div",{className:p.formRow},r.createElement("div",{className:f(p.cell,p.input)},r.createElement(a.DatePicker,{initial:this.state.date||this._todayMidnight,onPick:this._onDatePick,maxDate:this._todayMidnight,disabled:this.props.processing})),r.createElement("div",{className:f(p.cell,p.input)},r.createElement(c.TimePicker,{initial:this.state.time||this._todayMidnight,onPick:this._onTimePick,disabled:this.props.processing||this.props.dateOnly||!this.state.date})),r.createElement("div",{className:f(p.cell,p.btn)},r.createElement(d.Button,{type:"primary",disabled:!this.state.date||!this.state.time||this.props.processing,onClick:this._onGoToDate,
className:p.button},r.createElement(u.Icon,{icon:l}))))))},t}(r.PureComponent),t.GoToDateDialog=_},1168:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l,d;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(1170),s=n(77),a=n(1344),c=n(512),u=n(258),l=n(26),d=function(e){function t(t){var n=e.call(this,t)||this;return n._prevMonth=function(){n.setState({viewDate:n.state.viewDate.clone().subtract(1,"months")})},n._nextMonth=function(){n.setState({viewDate:n.state.viewDate.clone().add(1,"months")})},n._onClickDay=function(e){var t=e.clone();n.setState({viewDate:t}),n.props.onSelect&&n.props.onSelect(t.clone())},n.state={viewDate:t.selectedDate},n}return o.__extends(t,e),t.prototype.render=function(){return r.createElement("div",{className:l(u.calendar,this.props.className)},r.createElement("div",{className:u.header},r.createElement(s.Icon,{icon:a,onClick:this._prevMonth,className:l(u.switchBtn,u.prev)}),r.createElement("div",{className:u.title},this.state.viewDate.format("MMMM YYYY")),r.createElement(s.Icon,{icon:c,onClick:this._nextMonth,className:l(u.switchBtn,u.next)})),r.createElement(i.Month,{viewDate:this.state.viewDate,selectedDate:this.props.selectedDate,maxDate:this.props.maxDate,onClickDay:this._onClickDay}))},t}(r.PureComponent),t.Calendar=d},1169:function(e,t,n){"use strict";var o,r,i,s,a,c;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(258),s=n(26),a=n(36),c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onClick=function(){t.props.onClick&&!t.props.isDisabled&&t.props.onClick(t.props.day.clone())},t}return o.__extends(t,e),t.prototype.render=function(){var e,t=s(i.day,(e={},e[i.selected]=this.props.isSelected,e[i.disabled]=this.props.isDisabled,e[i.currentDay]=a(new Date).isSame(this.props.day,"day"),e[i.otherMonth]=this.props.isOtherMonth,e));return r.createElement("span",{className:t,onClick:this._onClick,"data-day":this.props.day.format("YYYY-MM-DD")},r.createElement("span",null,this.props.day.date()))},t}(r.PureComponent),t.Day=c},1170:function(e,t,n){"use strict";var o,r,i,s,a,c;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(1169),s=n(258),a=n(36),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o.__extends(t,e),t.prototype.render=function(){return r.createElement("div",{className:s.month},r.createElement("div",{className:s.weekdays},this._renderWeekdays()),r.createElement("div",{className:s.weeks},this._renderWeeks()))},t.prototype._renderWeekdays=function(){var e,t,n=[];for(e=0;e<7;e++)t=a().day(e).format("dd"),n.push(r.createElement("span",{key:e},t));return n},t.prototype._renderWeeks=function(){var e,t=[],n=this.props.viewDate.clone().startOf("month").day(0);for(e=0;e<6;e++)t.push(this._renderWeek(n)),n=n.clone().add(1,"weeks");return t},t.prototype._renderWeek=function(e){var t,n,o=[];for(t=0;t<7;t++)n=e.clone().add(t,"days"),o.push(r.createElement(i.Day,{key:t,day:n,isDisabled:!this._isInRange(n),isSelected:n.isSame(this.props.selectedDate,"day"),
isOtherMonth:!n.isSame(this.props.viewDate,"month"),onClick:this.props.onClickDay}));return r.createElement("div",{className:s.week,key:e.week()},o)},t.prototype._isInRange=function(e){return!this.props.maxDate||this.props.maxDate.startOf("day").diff(e.startOf("day"),"days")>=0},t}(r.PureComponent),t.Month=c},1171:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(1168),s=n(1337),a=n(661),c=n(36),u=n(504),l=function(e){function t(t){var n=e.call(this,t)||this;return n._format="YYYY-MM-DD",n._fixValue=function(e){return e=e.substr(0,10),e=e.replace(/\-+/g,"-"),e.endsWith(".")||4!==e.length&&7!==e.length||(e+="-"),e},n._isValid=function(e){if(/^[0-9]{4}(\-[0-9]{2}){2}/.test(e)){var t=c(e,n._format);return t.isValid()&&n._isInRange(t)}return!1},n._onType=function(e){var t=e?c(e,n._format):null;t&&n.setState({date:t}),n.props.onPick(t)},n._onSelect=function(e){n.setState({date:e,showCalendar:!1}),n.props.onPick(e)},n._showCalendar=function(){n.setState({showCalendar:!0})},n._hideCalendar=function(){n.setState({showCalendar:!1})},n.state={date:t.initial,showCalendar:!1},n}return o.__extends(t,e),t.prototype.render=function(){return r.createElement(u.PickerInput,{value:this.state.date.format(this._format),inputRegex:/[0-9\.]/,fixValue:this._fixValue,isValid:this._isValid,onType:this._onType,onShowPicker:this._showCalendar,onHidePicker:this._hideCalendar,showPicker:this.state.showCalendar,icon:s,disabled:this.props.disabled},r.createElement(i.Calendar,{selectedDate:this.state.date,maxDate:this.props.maxDate,onSelect:this._onSelect,className:a.calendar}))},t.prototype._isInRange=function(e){return!this.props.maxDate||this.props.maxDate.startOf("day").diff(e.startOf("day"),"days")>=0},t}(r.PureComponent),t.DatePicker=l},1172:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l,d,p,h;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(259),s=n(1173),a=n(1174),c=n(26),u=n(46),l=n(28),d=.18,function(e){e[e.Hours=0]="Hours",e[e.Minutes=1]="Minutes"}(p=t.ClockFaceType||(t.ClockFaceType={})),h=function(e){function t(t){var n=e.call(this,t)||this;return n._calculateShapeThrottled=requestAnimationFrame.bind(null,n._calculateShape.bind(n)),n._onChangeHours=function(e){n.state.time.hours()!==e&&n._onChange(n.state.time.clone().hours(e))},n._onChangeMinutes=function(e){n.state.time.minutes()!==e&&n._onChange(n.state.time.clone().minutes(e))},n._onSelectHours=function(){n._displayMinutes()},n._onSelectMinutes=function(){n.props.onSelect&&n.props.onSelect(n.state.time.clone())},n._displayHours=function(){n.setState({faceType:p.Hours})},n._displayMinutes=function(){n.setState({faceType:p.Minutes})},n._setClockFace=function(e){e&&(n._clockFace=e)},n.state={center:{x:0,y:0},radius:0,time:n.props.selectedTime,faceType:p.Hours},n}return o.__extends(t,e),t.prototype.render=function(){return r.createElement("div",{className:c(i.clock,this.props.className)},r.createElement("div",{className:i.header},r.createElement("span",{className:c(i.number,(e={},
e[i.active]=this.state.faceType===p.Hours,e)),onClick:this._displayHours},this.state.time.format("HH")),r.createElement("span",null,":"),r.createElement("span",{className:c(i.number,(t={},t[i.active]=this.state.faceType===p.Minutes,t)),onClick:this._displayMinutes},this.state.time.format("mm"))),r.createElement("div",{className:i.body},r.createElement("div",{className:i.clockFace,ref:this._setClockFace},r.createElement(u.CSSTransitionGroup,{transitionName:"tv-clock-face-animate",transitionEnter:!0,transitionEnterTimeout:l.dur,transitionLeave:!0,transitionLeaveTimeout:l.dur},this.state.faceType===p.Hours?this._renderHours():null,this.state.faceType===p.Minutes?this._renderMinutes():null),r.createElement("span",{className:i.centerDot}))));var e,t},t.prototype.componentDidMount=function(){this._calculateShape(),setTimeout(this._calculateShape.bind(this),1),window.addEventListener("resize",this._calculateShapeThrottled),window.addEventListener("scroll",this._calculateShapeThrottled,!0)},t.prototype.componentWillUnmount=function(){window.removeEventListener("resize",this._calculateShapeThrottled),window.removeEventListener("scroll",this._calculateShapeThrottled,!0)},t.prototype._renderHours=function(){return r.createElement(s.Hours,{center:this.state.center,radius:this.state.radius,spacing:this.state.radius*d,selected:this.state.time.hours(),onChange:this._onChangeHours,onSelect:this._onSelectHours})},t.prototype._renderMinutes=function(){return r.createElement(a.Minutes,{center:this.state.center,radius:this.state.radius,spacing:this.state.radius*d,selected:this.state.time.minutes(),onChange:this._onChangeMinutes,onSelect:this._onSelectMinutes})},t.prototype._onChange=function(e){this.setState({time:e}),this.props.onChange&&this.props.onChange(e.clone())},t.prototype._calculateShape=function(){var e=this._clockFace.getBoundingClientRect(),t=e.left,n=e.top,o=e.width;this.setState({center:{x:t+o/2,y:n+o/2},radius:o/2})},t}(r.PureComponent),t.Clock=h},1173:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l,d,p;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(505),s=n(506),a=n(507),c=[0].concat(a.range(13,24)),u=[12].concat(a.range(1,12)),l=30,d=.46,p=function(e){function t(t){var n=e.call(this,t)||this;return n._onMouseDown=function(e){n._hand.mouseStart(e)},n._onTouchStart=function(e){n._hand.touchStart(e)},n._onHandMove=function(e,t){var o=t<n.props.radius-n.props.spacing;n.state.isInner!==o?n.setState({isInner:o},function(){n.props.onChange(n._valueFromDegrees(e))}):n.props.onChange(n._valueFromDegrees(e))},n._onHandMoveEnd=function(){n.props.onSelect&&n.props.onSelect()},n.state={isInner:n.props.selected>0&&n.props.selected<=12},n}return o.__extends(t,e),t.prototype.render=function(){var e=this,t=this.props,n=t.center,o=t.radius,u=t.spacing,p=t.selected;return r.createElement("div",null,r.createElement(i.Face,{radius:o,spacing:u,numbers:c,activeNumber:p,format:a.twoDigitsFormat,onMouseDown:this._onMouseDown,onTouchStart:this._onTouchStart}),this._renderInnerFace(o*d),r.createElement(s.Hand,{
ref:function(t){return e._hand=t},length:o-(this.state.isInner?o*d:u),angle:p*l,step:l,center:n,onMove:this._onHandMove,onMoveEnd:this._onHandMoveEnd}))},t.prototype._renderInnerFace=function(e){return r.createElement(i.Face,{radius:this.props.radius,spacing:e,numbers:u,activeNumber:this.props.selected,onMouseDown:this._onMouseDown,onTouchStart:this._onTouchStart})},t.prototype._valueFromDegrees=function(e){return this.state.isInner?u[e/l]:c[e/l]},t}(r.PureComponent),t.Hours=p},1174:function(e,t,n){"use strict";var o,r,i,s,a,c,u,l;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(505),s=n(506),a=n(507),c=a.range(0,60,5),u=6,l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onMouseDown=function(e){t._hand.mouseStart(e)},t._onTouchStart=function(e){t._hand.touchStart(e)},t._onHandMove=function(e){t.props.onChange(e/u)},t._onHandMoveEnd=function(){t.props.onSelect&&t.props.onSelect()},t}return o.__extends(t,e),t.prototype.render=function(){var e=this;return r.createElement("div",null,r.createElement(i.Face,{radius:this.props.radius,spacing:this.props.spacing,numbers:c,activeNumber:this.props.selected,format:a.twoDigitsFormat,onMouseDown:this._onMouseDown,onTouchStart:this._onTouchStart}),r.createElement(s.Hand,{ref:function(t){return e._hand=t},length:this.props.radius-this.props.spacing,angle:this.props.selected*u,step:u,center:this.props.center,onMove:this._onHandMove,onMoveEnd:this._onHandMoveEnd}))},t}(r.PureComponent),t.Minutes=l},1175:function(e,t,n){"use strict";var o,r,i,s,a,c,u;Object.defineProperty(t,"__esModule",{value:!0}),o=n(5),r=n(2),i=n(1172),s=n(1335),a=n(36),c=n(504),u=function(e){function t(t){var n=e.call(this,t)||this;return n._format="HH:mm",n._fixValue=function(e){return e=e.substr(0,5),e=e.replace(/:+/g,":"),e.endsWith(":")||2!==e.length||(e+=":"),e},n._isValid=function(e){return/^[0-9]{2}:[0-9]{2}/.test(e)&&a(e,n._format).isValid()},n._onType=function(e){var t=e?a(e,n._format):null;t&&n.setState({time:t}),n.props.onPick(t)},n._onSelect=function(e){n.setState({time:e,showClock:!1}),n.props.onPick(e)},n._showClock=function(){n.setState({showClock:!0})},n._hideClock=function(){n.setState({showClock:!1})},n.state={time:t.initial,showClock:!1},n}return o.__extends(t,e),t.prototype.render=function(){return r.createElement(c.PickerInput,{value:this.state.time.format(this._format),inputRegex:/[0-9:]/,fixValue:this._fixValue,isValid:this._isValid,onType:this._onType,onShowPicker:this._showClock,onHidePicker:this._hideClock,showPicker:this.state.showClock,icon:s,disabled:this.props.disabled},r.createElement(i.Clock,{selectedTime:this.state.time,onSelect:this._onSelect}))},t}(r.PureComponent),t.TimePicker=u},1188:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path fill="none" d="M0 0h16v16H0z"/><path d="M8 .034l-1.41 1.41 5.58 5.59H0v2h12.17l-5.58 5.59L8 16.034l8-8z"/></svg>'},1335:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14px" height="14px"><path fill-rule="evenodd" d="M7 0C3.15 0 0 3.15 0 7s3.15 7 7 7 7-3.15 7-7-3.15-7-7-7zm0 12.25c-2.888 0-5.25-2.363-5.25-5.25 0-2.888 2.362-5.25 5.25-5.25 2.887 0 5.25 2.362 5.25 5.25 0 2.887-2.363 5.25-5.25 5.25zm.25-8H6V8h3.75V6.75h-2.5v-2.5z"/></svg>'},1337:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path d="M4 0c-.6 0-1 .4-1 1v1H1c-.6 0-1 .4-1 1v12c0 .6.4 1 1 1h14c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1h-2V1c0-.6-.4-1-1-1h-1c-.6 0-1 .4-1 1v1H6V1c0-.6-.4-1-1-1H4zM2 5h12v9H2V5zm5 2v2h2V7H7zm3 0v2h2V7h-2zm-6 3v2h2v-2H4zm3 0v2h2v-2H7zm3 0v2h2v-2h-2z"/></svg>'},1344:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 16" width="10" height="16"><path d="M9.4 1.4l-1.4-1.4-8 8 8 8 1.4-1.4-6.389-6.532 6.389-6.668z"/></svg>'}});