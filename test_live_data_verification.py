"""
Test script to verify what type of data we're actually getting
"""

import sys
import os
from datetime import datetime, timedelta
import pytz

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def check_egx_trading_status():
    """Check if EGX should be open now"""
    print("🕐 EGX Trading Status Check")
    print("=" * 40)
    
    # Cairo timezone
    cairo_tz = pytz.timezone('Africa/Cairo')
    now_cairo = datetime.now(cairo_tz)
    
    print(f"Current Cairo time: {now_cairo.strftime('%A, %Y-%m-%d %H:%M:%S %Z')}")
    
    # EGX trading days: Sunday (6) to Thursday (3)
    weekday = now_cairo.weekday()  # Monday=0, Sunday=6
    
    # Convert to EGX format: Sunday=0, Monday=1, ..., Thursday=4
    egx_weekday = (weekday + 1) % 7
    
    is_trading_day = egx_weekday <= 4  # Sunday(0) to Thursday(4)
    
    # EGX trading hours: 10:00 AM - 2:30 PM Cairo time
    trading_start = now_cairo.replace(hour=10, minute=0, second=0, microsecond=0)
    trading_end = now_cairo.replace(hour=14, minute=30, second=0, microsecond=0)
    
    is_trading_hours = trading_start <= now_cairo <= trading_end
    
    print(f"Day of week: {now_cairo.strftime('%A')} (EGX day {egx_weekday})")
    print(f"Is trading day: {'✅ Yes' if is_trading_day else '❌ No (Weekend)'}")
    print(f"Trading hours: 10:00 AM - 2:30 PM Cairo")
    print(f"Is trading hours: {'✅ Yes' if is_trading_hours else '❌ No'}")
    
    market_open = is_trading_day and is_trading_hours
    print(f"EGX Market Status: {'🟢 OPEN' if market_open else '🔴 CLOSED'}")
    
    if not market_open:
        if not is_trading_day:
            next_trading_day = "Sunday"
            print(f"⏳ Next trading day: {next_trading_day}")
        else:
            print(f"⏳ Next trading session: Tomorrow 10:00 AM Cairo time")
    
    return market_open

def test_current_data_source():
    """Test what data source we're actually getting"""
    print("\n🔍 Data Source Verification")
    print("=" * 40)
    
    try:
        from scrapers.price_scraper import PriceScraper
        
        # Test with COMI
        scraper = PriceScraper(source="tradingview")
        price_data = scraper.get_price("COMI")
        scraper.close_driver()
        
        if price_data:
            print(f"✅ Data received:")
            print(f"   Symbol: {price_data.get('symbol')}")
            print(f"   Price: {price_data.get('price')} {price_data.get('currency')}")
            print(f"   Source: {price_data.get('source')}")
            print(f"   Real-time: {price_data.get('real_time')}")
            print(f"   Timestamp: {price_data.get('timestamp')}")
            
            # Analyze the data
            source = price_data.get('source', '').lower()
            real_time = price_data.get('real_time', False)
            
            if source == 'sample data':
                print("\n📊 Analysis: This is SAMPLE DATA (fallback)")
                print("   - TradingView scraping failed")
                print("   - Using generated sample price")
            elif source == 'tradingview':
                if real_time:
                    print("\n🔴 Analysis: This is REAL-TIME TradingView data")
                    print("   - Requires TradingView paid account")
                    print("   - Updates during market hours")
                else:
                    print("\n⏱️ Analysis: This is DELAYED TradingView data")
                    print("   - Free TradingView account")
                    print("   - 15-minute delay during market hours")
                    print("   - Last available price when market closed")
            
            return price_data
        else:
            print("❌ No data received")
            return None
            
    except Exception as e:
        print(f"❌ Error testing data source: {str(e)}")
        return None

def compare_with_historical_data():
    """Compare current price with latest historical data"""
    print("\n📈 Historical Data Comparison")
    print("=" * 40)
    
    try:
        from app.utils.common import load_stock_data
        
        # Load historical data
        historical_data = load_stock_data("COMI")
        
        if historical_data is not None and not historical_data.empty:
            latest_historical = historical_data.iloc[-1]
            latest_date = latest_historical['Date']
            latest_price = latest_historical['Close']
            
            print(f"Latest historical data:")
            print(f"   Date: {latest_date}")
            print(f"   Close price: {latest_price} EGP")
            
            # Get current scraped price
            current_data = test_current_data_source()
            
            if current_data:
                current_price = current_data.get('price', 0)
                price_diff = current_price - latest_price
                price_diff_pct = (price_diff / latest_price) * 100
                
                print(f"\nComparison:")
                print(f"   Historical: {latest_price} EGP")
                print(f"   Current: {current_price} EGP")
                print(f"   Difference: {price_diff:+.2f} EGP ({price_diff_pct:+.2f}%)")
                
                if abs(price_diff_pct) < 0.1:
                    print("   📊 Analysis: Prices are very similar - likely last market price")
                elif abs(price_diff_pct) < 2:
                    print("   📊 Analysis: Small difference - could be delayed live data")
                else:
                    print("   📊 Analysis: Significant difference - investigate further")
        else:
            print("❌ No historical data available")
            
    except Exception as e:
        print(f"❌ Error comparing with historical data: {str(e)}")

def create_monitoring_plan():
    """Create a plan for monitoring live data tomorrow"""
    print("\n📋 Tomorrow's Testing Plan")
    print("=" * 40)
    
    cairo_tz = pytz.timezone('Africa/Cairo')
    now_cairo = datetime.now(cairo_tz)
    
    # Calculate next Sunday (trading day)
    days_until_sunday = (6 - now_cairo.weekday()) % 7
    if days_until_sunday == 0 and now_cairo.weekday() == 6:  # If today is Sunday
        days_until_sunday = 0
    elif days_until_sunday == 0:  # If today is not Sunday but calculation gives 0
        days_until_sunday = 7
    
    next_sunday = now_cairo + timedelta(days=days_until_sunday)
    market_open = next_sunday.replace(hour=10, minute=0, second=0, microsecond=0)
    market_close = next_sunday.replace(hour=14, minute=30, second=0, microsecond=0)
    
    print(f"Next EGX trading session:")
    print(f"   Date: {market_open.strftime('%A, %Y-%m-%d')}")
    print(f"   Market opens: {market_open.strftime('%H:%M %Z')}")
    print(f"   Market closes: {market_close.strftime('%H:%M %Z')}")
    
    print(f"\n🧪 Recommended Testing Schedule:")
    print(f"1. **Before Market Opens** (9:30 AM Cairo)")
    print(f"   - Test scraper to see last available price")
    print(f"   - Should show Thursday's closing price")
    
    print(f"2. **Market Opening** (10:00 AM Cairo)")
    print(f"   - Test immediately when market opens")
    print(f"   - Check if price updates from Thursday's close")
    
    print(f"3. **During Trading** (11:00 AM, 12:00 PM, 1:00 PM Cairo)")
    print(f"   - Test multiple times during trading hours")
    print(f"   - Verify prices are updating")
    
    print(f"4. **After Market Close** (3:00 PM Cairo)")
    print(f"   - Test to see final closing price")
    print(f"   - Verify it matches official EGX close")

def main():
    """Run all verification tests"""
    print("🔍 Live Data Verification Test")
    print("=" * 50)
    
    # Check current market status
    market_open = check_egx_trading_status()
    
    # Test current data source
    test_current_data_source()
    
    # Compare with historical data
    compare_with_historical_data()
    
    # Create monitoring plan
    create_monitoring_plan()
    
    print("\n📝 Summary:")
    if market_open:
        print("✅ EGX is currently OPEN - data should be live!")
    else:
        print("⏳ EGX is currently CLOSED - data is last available price")
    
    print("\n🎯 Key Questions for Tomorrow:")
    print("1. Does the price change when market opens at 10:00 AM Cairo?")
    print("2. Do prices update during trading hours (10:00 AM - 2:30 PM)?")
    print("3. Is there a 15-minute delay (free account) or real-time (paid)?")
    print("4. Does TradingView popup appear when fetching data?")
    
    print("\n💡 How to Test Tomorrow:")
    print("1. Run your Streamlit app: streamlit run app.py")
    print("2. Go to Live Trading page")
    print("3. Click 'Fetch Live Price' at different times")
    print("4. Watch for price changes and popup behavior")

if __name__ == "__main__":
    main()
