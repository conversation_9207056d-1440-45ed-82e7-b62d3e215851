webpackJsonp([10],{695:function(A,h,i){"use strict";Object.defineProperty(h,"__esModule",{value:!0}),h.fallbackImages={tvLogoBlack:i(1349),tvLogoWhite:i(1350)},h.logoSizes={benzingapro:{width:135,height:25},bovespa:{width:135,height:50},cme:{width:175,height:26},currencywiki:{width:135,height:25},dailyfx:{width:135,height:25},fxstreet:{width:137,height:33},investopedia:{width:135,height:23},smartlab:{width:135,height:37},lse:{width:135,height:31},arabictrader:{width:135,height:40},goldprice:{width:135,height:27},silverprice:{width:135,height:27},inbestia:{width:195,height:50},immfx:{width:122,height:26},kitco:{width:130,height:35},enbourse:{width:135,height:40},rankia:{width:65,height:17},stockwatch:{width:135,height:19},tradecapitan:{width:121,height:45}}},1349:function(A,h){A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAmCAMAAACmhKjHAAAAXVBMVEUAAAAAAAAAAAAFEhgrg6g4rNsznskLIy0AAAAQMkAdWXIAAAAxlb4UQFEAAAAAAAAAAAAkcI8ujbQgZYEZTWMAAAAAAAAAAAA2pNIAAAAAAAAAAAAoe50AAAA7s+Q2ucaKAAAAHnRSTlMAmU2f2fnspiWswAnms3xCE8zfxrmPhjfybV9W0hyxWOJtAAABrElEQVRIx8WVbbOCIBCFrwmKCpnvlsb//5mXXcrlxcpm7sw9nwx4hrPLgX7+XBchxPdQfU2srlP1BXZLXC1H0WlIfI3HLC+4OC+ZlG1TWnQ6wN0QY/ohzlIYqC+fuBMsa7Qj3lu/71kBa5j2VSZWpzfgmbgd8ly9bKiZ7XUsLrscyVd24dhnva8VujRU+3kxU6V+pbkw07cYE+cE1MKaVql2x7CxO4ZY9YwZcvARkwy81gFnt0sVtkbBt4o5lN8f5MqMLBFInNU1zGcKGHLQBPztKksxi8as286Iy3MzwkMulStsWXnHJ50VKUNnBQ+4TEs/QANURCvy7JEzRQeBHJwlkk6yGXHqsVEPaSD3xUw9miihdlQartNOsStx3OnuSHfQjtnyNjK1Tnrinqd830BuuTwLj4Dh81EShw0SW40SOeXMb9kLkz/DU4LgHV+LbiuPBIPxFUUQNcKrBuVFahBs/AyhVRTcQyovDqiMXDzvxpDYzscqyCl1jG6lIEOhWMN8rtic0jO8Sv1BWQehXbwH/JjiV7IejlHxv1e1jIe2o/pI9+n0XjVQ/6ZfnFlWaxVUZmcAAAAASUVORK5CYII="},1350:function(A,h){A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAAmCAMAAACmhKjHAAAAYFBMVEUAAAD////////q9/1ux+tSvOfZ8fr////I6vid2fL///9bwOm45Pb///////////+E0O9jw+mQ1fCq3vT///////9KuOb///////////94y+3///////////////87s+R/+BgSAAAAH3RSTlMAmU2f2eymJazACeazfEITzN/GuY+G8l9WOdJyaRwwr8L7/gAAAaRJREFUSMfFld2SgyAMhZeCP4i11qpV2y7v/5ZLwrgBYls7szN7rhT5hpOQxK8/16kois+h5ia8bnP1AXYVoaa96NyLWMM+y5MA5VoZc261R+cd3BUxZVcpCQvN6R13gG2tDXXxfl+zBexRNpYWXocX4JG4DfJYPU2o+3qxGzJdjuQzu3Dto93WAlnqq+16cZ+0faaxdJ+vHCuOAnSGPee6Pm+gzu6QYtVaZsjBAycVeG0Szh8na0xNDc8151BxfpDTGVkiMOHEPa1PCRgKkoDvoTKJtejMhulkXJ67FcZJs8CRdCV392qCHVKhszLlMmviAuohItqR4+E6DDNHDu4SyaCyFXE1dYUO3Jcj5WimCvWrxnGdDYJdiAuzO1AP+jUMjyS9kwtx6y0/IlBBeGkqVavjCsYEFb8xGuDYpWPtJZwdYZQg+MBp0VF4JLfIWxRB1ABTDcJjagWfQRlaRUEfUni8QA1zsfZGL3zmuUrmNJNBVxZkKJVqVcyV4DQZw4uxb5R1ULRTNMD3iU/Jpt9H8b9XNQ27jqP4SI/vw2s1QP2bfgDhqloD84hV5AAAAABJRU5ErkJggg=="}});