"""
Startup script for the TradingView Advanced Scraper API
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    try:
        import playwright
        print("✅ Playwright is installed")
    except ImportError:
        print("❌ Playwright not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "playwright"])
        subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"])
        print("✅ Playwright installed")
    
    try:
        import fastapi
        import uvicorn
        print("✅ FastAPI and Uvicorn are installed")
    except ImportError:
        print("❌ FastAPI/Uvicorn not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn"])
        print("✅ FastAPI and Uvicorn installed")
    
    try:
        import pydantic
        print("✅ Pydantic is installed")
    except ImportError:
        print("❌ Pydantic not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pydantic"])
        print("✅ Pydantic installed")

def check_api_health(max_retries=10, delay=2):
    """Check if the API server is running and healthy"""
    url = "http://127.0.0.1:8000/health"
    
    for i in range(max_retries):
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API server is healthy!")
                print(f"   Status: {data.get('status', 'unknown')}")
                print(f"   Playwright: {data.get('playwright_available', 'unknown')}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if i < max_retries - 1:
            print(f"⏳ Waiting for API server... ({i+1}/{max_retries})")
            time.sleep(delay)
    
    return False

def start_api_server():
    """Start the API server"""
    print("🚀 Starting TradingView Advanced Scraper API...")
    
    # Change to scrapers directory
    scrapers_dir = Path("scrapers")
    if not scrapers_dir.exists():
        print("❌ Scrapers directory not found!")
        return False
    
    # Check if api_server.py exists
    api_server_path = scrapers_dir / "api_server.py"
    if not api_server_path.exists():
        print("❌ api_server.py not found in scrapers directory!")
        return False
    
    try:
        # Start the server in a subprocess
        print("📡 Starting API server on http://127.0.0.1:8000")
        process = subprocess.Popen([
            sys.executable, str(api_server_path)
        ], cwd=str(scrapers_dir))
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        # Check if the server is running
        if check_api_health():
            print("\n🎉 API Server started successfully!")
            print("\n📚 Available endpoints:")
            print("   • API Documentation: http://127.0.0.1:8000/docs")
            print("   • Interactive API: http://127.0.0.1:8000/redoc")
            print("   • Health Check: http://127.0.0.1:8000/health")
            print("   • Scrape Pairs: http://127.0.0.1:8000/api/scrape_pairs")
            print("\n💡 Example usage:")
            print("   curl -X POST http://127.0.0.1:8000/api/scrape_pairs \\")
            print("        -H 'Content-Type: application/json' \\")
            print("        -d '{\"pairs\": [\"EGX-COMI\"], \"intervals\": [\"1D\"]}'")
            print("\n⏹️ To stop the server, press Ctrl+C")
            
            # Keep the script running
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping API server...")
                process.terminate()
                process.wait()
                print("✅ API server stopped")
            
            return True
        else:
            print("❌ Failed to start API server")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"❌ Error starting API server: {str(e)}")
        return False

def main():
    """Main function"""
    print("🌟 TradingView Advanced Scraper API Startup")
    print("=" * 50)
    
    # Check dependencies
    check_dependencies()
    
    print("\n📋 System Information:")
    print(f"   Python: {sys.version}")
    print(f"   Working Directory: {os.getcwd()}")
    print(f"   Platform: {sys.platform}")
    
    # Start the API server
    print("\n" + "=" * 50)
    success = start_api_server()
    
    if not success:
        print("\n❌ Failed to start the API server")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the correct directory")
        print("2. Check that scrapers/api_server.py exists")
        print("3. Verify all dependencies are installed")
        print("4. Check the console for error messages")
        sys.exit(1)

if __name__ == "__main__":
    main()
