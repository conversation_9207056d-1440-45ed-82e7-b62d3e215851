(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4862],{25398:e=>{e.exports={"round-button":"round-button-FujgyDpN",icon:"icon-FujgyDpN",content:"content-FujgyDpN",link:"link-FujgyDpN","color-brand":"color-brand-FujgyDpN","variant-primary":"variant-primary-FujgyDpN","disable-themes":"disable-themes-FujgyDpN",activated:"activated-FujgyDpN","variant-quiet-primary":"variant-quiet-primary-FujgyDpN","variant-secondary":"variant-secondary-FujgyDpN","variant-ghost":"variant-ghost-FujgyDpN","color-gray":"color-gray-FujgyDpN","color-red":"color-red-FujgyDpN","color-black":"color-black-FujgyDpN","color-invertedblack":"color-invertedblack-FujgyDpN","size-xsmall":"size-xsmall-FujgyDpN","icon-only":"icon-only-FujgyDpN","with-start-icon":"with-start-icon-FujgyDpN","with-end-icon":"with-end-icon-FujgyDpN",startIcon:"startIcon-FujgyDpN",endIcon:"endIcon-FujgyDpN","size-small":"size-small-FujgyDpN","size-medium":"size-medium-FujgyDpN","size-large":"size-large-FujgyDpN","size-xlarge":"size-xlarge-FujgyDpN","size-xxlarge":"size-xxlarge-FujgyDpN",animated:"animated-FujgyDpN",stretch:"stretch-FujgyDpN"}},81026:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},7236:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},30930:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF",
"with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},48729:e=>{e.exports={"round-tabs":"round-tabs-J4T7wK54","align-start":"align-start-J4T7wK54","align-center":"align-center-J4T7wK54",tab:"tab-J4T7wK54","tab-primary":"tab-primary-J4T7wK54",checked:"checked-J4T7wK54","tab-ghost":"tab-ghost-J4T7wK54","size-xsmall":"size-xsmall-J4T7wK54","size-small":"size-small-J4T7wK54","size-large":"size-large-J4T7wK54"}},23490:e=>{e.exports={dropTargetInside:"dropTargetInside-e_nPSSdZ",dropTarget:"dropTarget-e_nPSSdZ",before:"before-e_nPSSdZ",after:"after-e_nPSSdZ"}},75564:e=>{e.exports={wrap:"wrap-IEe5qpW4",selected:"selected-IEe5qpW4",childOfSelected:"childOfSelected-IEe5qpW4",disabled:"disabled-IEe5qpW4",expandHandle:"expandHandle-IEe5qpW4",expanded:"expanded-IEe5qpW4"}},76106:e=>{e.exports={separator:"separator-MgF6KBas",tree:"tree-MgF6KBas",overlayScrollWrap:"overlayScrollWrap-MgF6KBas",listContainer:"listContainer-MgF6KBas"}},28355:e=>{e.exports={title:"title-QPktCwTY",tabs:"tabs-QPktCwTY",empty:"empty-QPktCwTY",image:"image-QPktCwTY",spinner:"spinner-QPktCwTY",contentList:"contentList-QPktCwTY"}},17742:e=>{e.exports={dialog:"dialog-VUnQLSMH",button:"button-VUnQLSMH"}},22813:e=>{e.exports={title:"title-uNZ8yW1y",withoutIcon:"withoutIcon-uNZ8yW1y",buttons:"buttons-uNZ8yW1y",button:"button-uNZ8yW1y",disabled:"disabled-uNZ8yW1y",spacing:"spacing-uNZ8yW1y",toolbar:"toolbar-uNZ8yW1y"}},87769:e=>{e.exports={wrap:"wrap-C8ln3wvp",dialog:"dialog-C8ln3wvp",offset:"offset-C8ln3wvp",title:"title-C8ln3wvp",main:"main-C8ln3wvp",disabled:"disabled-C8ln3wvp",icon:"icon-C8ln3wvp",pathIcon:"pathIcon-C8ln3wvp",syncIconWrap:"syncIconWrap-C8ln3wvp",syncIcon:"syncIcon-C8ln3wvp",rightButtons:"rightButtons-C8ln3wvp",hover:"hover-C8ln3wvp",expandHandle:"expandHandle-C8ln3wvp",button:"button-C8ln3wvp",selected:"selected-C8ln3wvp",childOfSelected:"childOfSelected-C8ln3wvp",renameInput:"renameInput-C8ln3wvp",warn:"warn-C8ln3wvp",visible:"visible-C8ln3wvp"}},6909:e=>{e.exports={wrap:"wrap-ukH4sVzT",space:"space-ukH4sVzT",tree:"tree-ukH4sVzT"}},86928:e=>{e.exports={button:"button-w6lVe_oI",hovered:"hovered-w6lVe_oI",disabled:"disabled-w6lVe_oI"}},27011:(e,t,n)=>{"use strict";function o(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}n.d(t,{isIconOnly:()=>o})},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>o});const o=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>b,InputClasses:()=>p});var o=n(50959),r=n(97754),i=n(50151),s=n(38528),l=n(90186),a=n(86332),c=n(95604);var u=n(81026),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),
8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function g(e,t,n,o){const{removeRoundBorder:i,className:s,intent:l="default",borderStyle:a="thin",size:u,highlight:g,disabled:m,readonly:p,stretch:f,noReadonlyStyles:v,isFocused:b}=e,y=h(null!=i?i:(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${l}`],d()[`border-${a}`],u&&d()[`size-${u}`],y,g&&d()["with-highlight"],m&&d().disabled,p&&!v&&d().readonly,b&&d().focused,f&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],s)}function m(e,t,n){const{highlight:o,highlightRemoveRoundBorder:i}=e;if(!o)return d().highlight;const s=h(null!=i?i:(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],s)}const p={FontSizeMedium:(0,i.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,i.ensureDefined)(d()["font-size-large"])},f={passive:!1};function v(e,t){const{style:n,id:r,role:i,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:p,onMouseUp:v,onKeyDown:b,onClick:y,tabIndex:_,startSlot:S,middleSlot:w,endSlot:C,onWheel:T,onWheelNoPassive:E=null,size:I}=e,{isGrouped:M,cellState:k,disablePositionAdjustment:D=!1}=(0,o.useContext)(a.ControlGroupContext),N=function(e,t=null,n){const r=(0,o.useRef)(null),i=(0,o.useRef)(null),s=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),l=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),a=(0,o.useCallback)((e=>{l(),r.current=e,s()}),[]);return(0,o.useEffect)((()=>(i.current=[e,t,n],s(),l)),[e,t,n]),a}("wheel",E,f);return o.createElement("span",{style:n,id:r,role:i,className:g(e,M,k,D),tabIndex:_,ref:(0,s.useMergedRefs)([t,N]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:p,onMouseUp:v,onKeyDown:b,onClick:y,onWheel:T,...(0,l.filterDataProps)(e),...(0,l.filterAriaProps)(e)},S,w,C,o.createElement("span",{className:m(e,k,I)}))}v.displayName="ControlSkeleton";const b=o.forwardRef(v)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,MiddleSlot:()=>a,StartSlot:()=>l});var o=n(50959),r=n(97754),i=n(7236),s=n.n(i);function l(e){const{className:t,interactive:n=!0,icon:i=!1,children:l}=e;return o.createElement("span",{className:r(s()["inner-slot"],n&&s().interactive,i&&s().icon,t)},l)}function a(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(s()["inner-slot"],s()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:i=!1,children:l}=e;return o.createElement("span",{className:r(s()["inner-slot"],n&&s().interactive,i&&s().icon,t)},l)}function u(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(s()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>b})
;var o=n(50959),r=n(97754),i=n(90186),s=n(47201),l=n(48907),a=n(38528),c=n(48027),u=n(29202),d=n(45812),h=n(67029),g=n(78274),m=n(30930),p=n.n(m);function f(e){return!(0,i.isAriaAttribute)(e)&&!(0,i.isDataAttribute)(e)}function v(e){const{id:t,title:n,role:s,tabIndex:l,placeholder:a,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:b,autoFocus:y,maxLength:_,min:S,max:w,step:C,pattern:T,inputMode:E,onSelect:I,onFocus:M,onBlur:k,onKeyDown:D,onKeyUp:N,onKeyPress:L,onChange:x,onDragStart:z,size:j="small",className:A,inputClassName:R,disabled:O,readonly:B,containerTabIndex:F,startSlot:P,endSlot:W,reference:H,containerReference:V,onContainerFocus:G,...U}=e,Z=(0,i.filterProps)(U,f),K={...(0,i.filterAriaProps)(U),...(0,i.filterDataProps)(U),id:t,title:n,role:s,tabIndex:l,placeholder:a,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:b,autoFocus:y,maxLength:_,min:S,max:w,step:C,pattern:T,inputMode:E,onSelect:I,onFocus:M,onBlur:k,onKeyDown:D,onKeyUp:N,onKeyPress:L,onChange:x,onDragStart:z};return o.createElement(h.ControlSkeleton,{...Z,disabled:O,readonly:B,tabIndex:F,className:r(p().container,A),size:j,ref:V,onFocus:G,startSlot:P,middleSlot:o.createElement(g.MiddleSlot,null,o.createElement("input",{...K,className:r(p().input,p()[`size-${j}`],R,P&&p()["with-start-slot"],W&&p()["with-end-slot"]),disabled:O,readOnly:B,ref:H})),endSlot:W})}function b(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:i,onBlur:h,reference:g,containerReference:m=null}=e,p=(0,o.useRef)(null),f=(0,o.useRef)(null),[b,y]=(0,u.useFocus)(),_=t?void 0:b?-1:r,S=t?void 0:b?r:-1,{isMouseDown:w,handleMouseDown:C,handleMouseUp:T}=(0,d.useIsMouseDown)(),E=(0,s.createSafeMulticastEventHandler)(y.onFocus,(function(e){n&&!w.current&&(0,l.selectAllContent)(e.currentTarget)}),i),I=(0,s.createSafeMulticastEventHandler)(y.onBlur,h),M=(0,o.useCallback)((e=>{p.current=e,g&&("function"==typeof g&&g(e),"object"==typeof g&&(g.current=e))}),[p,g]);return o.createElement(v,{...e,isFocused:b,containerTabIndex:_,tabIndex:S,onContainerFocus:function(e){f.current===e.target&&null!==p.current&&p.current.focus()},onFocus:E,onBlur:I,reference:M,containerReference:(0,a.useMergedRefs)([f,m]),onMouseDown:C,onMouseUp:T})}},38952:(e,t,n)=>{"use strict";function o(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>o})},21593:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>i});var o=n(50959),r=n(38952);function i(e){return o.createElement("a",{...(0,r.renameRef)(e)})}o.PureComponent},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>i});var o=n(47201),r=n(29202);function i(e){const{onFocus:t,onBlur:n,intent:i,highlight:s,disabled:l}=e,[a,c]=(0,r.useFocus)(void 0,l),u=(0,o.createSafeMulticastEventHandler)(l?void 0:c.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(l?void 0:c.onBlur,n);return{...e,intent:i||(a?"primary":"default"),highlight:null!=s?s:a,onFocus:u,onBlur:d}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,
o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const i={onFocus:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,i]}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},38528:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>i});var o=n(50959),r=n(53017);function i(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},48907:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},47201:(e,t,n)=>{"use strict";function o(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>o})},23263:(e,t,n)=>{"use strict";n.d(t,{ManageDrawings:()=>O});var o=n(50959),r=n(43370),i=(n(50151),n(9745)),s=n(44352),l=n(36298),a=n(97145),c=n(59224),u=n(92249),d=n(64530),h=n(1722),g=n(63932),m=n(97754),p=n.n(m);var f=n(90186),v=n(27011),b=n(25398),y=n.n(b);function _(e){const{className:t,color:n,variant:o,size:r,stretch:i,animated:s,disableThemes:l,isInvertedColorTheme:a,...c}=e;return{...c,...(0,f.filterDataProps)(e),...(0,f.filterAriaProps)(e)}}function S(e){const{reference:t,children:n,iconOnly:r,startIcon:s,endIcon:l,...a}=e,c=function(e,t){const{className:n,color:o="brand",variant:r="primary",size:i="xlarge",stretch:s,animated:l=!1,disableThemes:a=!1,iconOnly:c=!1,isAnchor:u=!1,isActivated:d=!1,isInvertedColorTheme:h=!1,endIcon:g,startIcon:p}=t,f=function(e,t){return!!t&&"black"===e}(o,h);return m(n,e["round-button"],f?e[`color-inverted${o}`]:e[`color-${o}`],e[`variant-${r}`],e[`size-${i}`],l&&e.animated,s&&e.stretch,a&&e["disable-themes"],c&&e["icon-only"],u&&e.link,d&&e.activated,p&&e["with-start-icon"],g&&e["with-end-icon"])}(y(),e);return o.createElement("button",{..._(a),className:c,ref:t},s&&o.createElement(i.Icon,{className:p()(y().icon,y().startIcon),icon:s}),!(0,v.isIconOnly)(n,r)&&o.createElement("span",{className:y().content},n),l&&o.createElement(i.Icon,{className:p()(y().icon,y().endIcon),icon:l}))}n(21593);function w(e,t){const{align:n="start",size:o="xsmall"}=t;return m(e["round-tabs"],e[`align-${n}`],e[`size-${o}`])}function C(e,t){const{variant:n="primary",isChecked:o}=t;return m(e.tab,e[`tab-${n}`],o&&e.checked)}var T=n(48729),E=n.n(T);const I=o.forwardRef(((e,t)=>{const{tabs:n,size:r="xsmall",variant:i="primary",theme:s=E()}=e;return o.createElement("div",{className:w(s,e),ref:t},n.map((e=>{const{isChecked:t,...n}=e;return o.createElement(S,{...n,className:C(s,{...n,variant:i,isChecked:t}),size:r,variant:i})})))}));I.displayName="RoundTabsButtons";s.t(null,void 0,n(87871)),s.t(null,void 0,n(10538)),s.t(null,void 0,n(74860));var M=n(29540),k=n(28355);const D=(0,c.getLogger)("Chart.ManageDrawings"),N=new Map;function L(e){let t=N.get(e);return void 0===t&&(t=new a.WatchedValue([]),N.set(e,t)),t}
const x=new l.TranslatedString("remove all line tools for {symbol}",s.t(null,void 0,n(23481))),z=e=>s.t(null,{plural:"{drawingsCount} drawings",count:e},n(88143)).format({drawingsCount:e.toString()}),j=s.t(null,void 0,n(85128)),A=s.t(null,void 0,n(18570));function R(e){const[t,n]=o.useState(null),[i,s]=o.useState(null),[l,a]=o.useState(null),[c,d]=(o.useRef(null),o.useState([]));return o.useEffect((()=>(e.withModel(null,(()=>{const t=e.model();n(t),s(t.mainSeries().symbol())})),()=>n(null))),[e]),o.useEffect((()=>{if(null!==t){const e={},n=(0,r.default)(f,250,{leading:!1});return f(),t.model().dataSourceCollectionChanged().subscribe(e,n),()=>{t.model().dataSourceCollectionChanged().unsubscribe(e,n)}}}),[t]),o.useEffect((()=>{if(null!==t){const e=L(t.model().id()).spawn();return d([...e.value()]),e.subscribe((()=>d([...e.value()]))),()=>null==e?void 0:e.destroy()}}),[t]),o.useMemo((()=>({currentSymbol:i,symbolDrawingsMaps:l,removeSymbolDrawings:g,changeSymbol:m,hiddenSymbols:c})),[i,l,g,m,c]);async function g(e,n){if(t&&l){const o=l[n].get(e);if(o){const n=Array.from(o).map((e=>t.model().dataSourceForId(e))).filter(h.notNull);n.length>0&&t.removeSources(n,!1,x.format({symbol:e}));const r=L(t.model().id());r.setValue([...r.value(),e]);try{await f()}catch(e){D.logError(`Error removing line tools: ${e}`)}r.setValue(r.value().filter((t=>t!==e)))}}}function m(n){e.setSymbol(n),null!==t&&s(n)}async function p(e){const t=function(e){const t=[new Map,new Map,new Map];{const n=t[0];e.forEach((e=>{var t;if((0,u.isLineTool)(e)&&e.showInObjectTree()){const o=null!==(t=e.symbol())&&void 0!==t?t:"",r=n.get(o)||new Set;r.add(e.id()),n.set(o,r)}}))}return t}(e);return(await async function(){return[new Map,new Map,new Map]}()).forEach(((e,n)=>{const o=t[n];e.forEach(((e,t)=>{const n=o.get(t)||new Set;e.forEach((e=>n.add(e))),o.set(t,n)}))})),t}async function f(){null!==t&&a(await p(t.dataSources()))}}function O(e){const{isMobile:t,chartWidget:r,onClose:l,onInitialized:a}=e,{currentSymbol:c,symbolDrawingsMaps:u,removeSymbolDrawings:h,changeSymbol:m,hiddenSymbols:p}=R(r),[f,v]=o.useState(0),[b,y,_]=o.useMemo((()=>{var e;if(null!==c&&null!==u){const t=[];let n=0,o=f;if(null===o)for(o=2;o>0&&!(((null===(e=u[o].get(c))||void 0===e?void 0:e.size)||0)>0);)o--;return u[o].forEach(((e,o)=>{p.includes(o)||(t.push({symbol:o,drawingsCount:e.size,onRemove:()=>function(e){h(e,_)}(o),onClick:()=>function(e){""!==e&&(m(e),null==l||l())}(o)}),n+=e.size)})),t.sort(((e,t)=>e.drawingsCount===t.drawingsCount?e.symbol.localeCompare(t.symbol):e.drawingsCount>t.drawingsCount?-1:1)),[t,n,o]}return[[],0,0]}),[c,f,u,p]);return o.useEffect((()=>{null!==u&&(null==a||a())}),[u]),o.createElement(o.Fragment,null,b.length>0&&o.createElement("div",{className:k.title},`${S=u?u[_].size:0,s.t(null,{plural:"{symbolsCount} symbols",context:"symbols_and_drawings_count",count:S},n(52908)).format({symbolsCount:S.toString()})} ${(e=>s.t(null,{plural:"with {drawingsCount} drawings",context:"symbols_and_drawings_count",count:e},n(42743)).format({drawingsCount:e.toString()
}))(y)}`),0===b.length?null===u?o.createElement(g.Spinner,{className:k.spinner}):o.createElement("div",{className:k.empty},o.createElement(i.Icon,{className:k.image,icon:M}),o.createElement("span",null,A)):b.map((({symbol:e,drawingsCount:n,onRemove:r,onClick:i})=>o.createElement(d.DialogContentItem,{key:e,title:e,subtitle:z(n),removeBtnLabel:j,isActive:e===c,isMobile:t,onClick:i,onClickRemove:r,showFavorite:!1}))));var S}},67165:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ObjectTreeDialogRenderer:()=>Xo});var o=n(50959),r=n(962);async function i(e,t,n){let o;for(let r=0;r<t;++r)try{return await e(o)}catch(e){o=e,await n(r)}throw o}async function s(e,t){return i(e,t,(()=>Promise.resolve()))}var l=n(59224);const a=(0,l.getLogger)("DataSourcesIcons");let c=null;function u(){const e=n.c[57673];return e?Promise.resolve(e.exports.lineToolsIcons):n.e(1890).then(n.bind(n,57673)).then((e=>e.lineToolsIcons))}function d(){const e=n.c[45876];return e?Promise.resolve(e.exports.SERIES_ICONS):n.e(9685).then(n.bind(n,45876)).then((e=>e.SERIES_ICONS))}let h=null;function g(){return null===h&&(h=function(){const e=s(u,2).then((e=>e)).catch((e=>(a.logWarn(e),{}))),t=s(d,2).then((e=>e)).catch((e=>(a.logWarn(e),{})));return Promise.all([e,t])}()),h.then((e=>(c={linetool:e[0],series:e[1]},c)))}var m=n(41590),p=n(37558),f=n(90692),v=n(44352),b=n(35057),y=n(24437),_=n(23263);var S=n(36349),w=n(32563),C=n(68335),T=n(50151),E=n(97754),I=n.n(E),M=n(9745),k=n(16396),D=n(50298),N=n(37968);const L=o.createContext(null);var x=n(48889),z=n(54079),j=n(16838),A=n(36296),R=n(74059),O=n(80465),B=n(22813),F=n(51768);function P(e){const{hideTitle:t}=e,{viewModel:r}=(0,T.ensureNotNull)((0,o.useContext)(L)),i=(0,N.useForceUpdate)(),s=r.selection();(0,o.useEffect)((()=>{const e={};return r.onChange().subscribe(e,(()=>i())),()=>{r.onChange().unsubscribeAll(e)}}),[r]),(0,o.useEffect)((()=>{const e={};return s.onChange().subscribe(e,(()=>i())),()=>{s.onChange().unsubscribeAll(e)}}),[s]),(0,o.useEffect)((()=>{(0,j.updateTabIndexes)()}),[]);const l=!r.canSelectionBeUnmerged(),a=r.isSelectionCopiable(),c=r.isSelectionCloneable(),u=!a&&!c,d=r.canSelectionBeGrouped(),h=!1;return o.createElement(z.Toolbar,{orientation:"horizontal",className:B.toolbar},!t&&o.createElement("div",{className:E(B.title,B.withoutIcon)},v.t(null,void 0,n(31095)),h),o.createElement("div",{className:B.buttons},o.createElement(x.ToolbarIconButton,{className:E(B.button,!d&&B.disabled),icon:O,onClick:function(){r.createGroupFromSelection()},isDisabled:!d,tooltip:v.t(null,void 0,n(91073)),"data-name":"group-button"}),o.createElement(D.ToolbarMenuButton,{className:E(B.button,u&&B.disabled),isDisabled:u,content:o.createElement(M.Icon,{icon:A}),tooltip:v.t(null,void 0,n(98129)),arrow:!1,isShowTooltip:!0,"data-name":"copy-clone-button"},a&&o.createElement(k.PopupMenuItem,{"data-name":"copy",label:v.t(null,void 0,n(35216)),onClick:function(){r.copySelection()}}),c&&o.createElement(k.PopupMenuItem,{"data-name":"clone",label:v.t(null,void 0,n(52977)),onClick:function(){r.cloneSelection()}
})),o.createElement(D.ToolbarMenuButton,{className:E(B.button,l&&B.disabled),isDisabled:l,content:o.createElement(M.Icon,{icon:R}),tooltip:v.t(null,void 0,n(45828)),arrow:!1,isShowTooltip:!0,"data-name":"move-to-button"},o.createElement(k.PopupMenuItem,{"data-name":"new-pane-above",label:v.t(null,void 0,n(40887)),onClick:function(){r.unmergeSelectionUp()}}),o.createElement(k.PopupMenuItem,{"data-name":"new-pane-below",label:v.t(null,void 0,n(96712)),onClick:function(){r.unmergeSelectionDown()}})),t&&o.createElement(o.Fragment,null,o.createElement("div",{className:B.spacing}),o.createElement(x.ToolbarIconButton,{className:B.button,icon:manageDrawingsIcon,tooltip:v.t(null,void 0,n(72357)),"data-name":"manage-drawings-button",onClick:g}))));function g(){h}}var W=n(54773),H=n(86942),V=n(10170),G=n(7809),U=n(23642);const Z=(K="OBJECT_TREE",e=>K+"__"+e);var K;const Y=Z("SET_NODES"),$=Z("SYNC_NODES"),q=Z("UPDATE_NODE"),J=Z("UPDATE_NODES"),Q=Z("RESET_TREE"),X=Z("SET_SELECTED_IDS"),ee=Z("DROP_SELECTION"),te=Z("SELECT_PREVIOUS"),ne=Z("SELECT_NEXT"),oe=Z("MULTI_SELECT_PREVIOUS"),re=Z("MULTI_SELECT_NEXT"),ie=Z("PROCESS_DROP_TARGET"),se=Z("UPDATE_DROP_TARGET"),le=Z("HIDE_DROP_TARGET"),ae=Z("START_MULTI_SELECT"),ce=Z("STOP_MULTI_SELECT"),ue=(Z("REMOVE_NODE"),Z("SET_FOCUSED_NODE")),de=Z("SCROLL_TO_ID"),he=Z("SET_IS_SELECTED"),ge=Z("SET_IS_EXPANDED"),me=Z("SET_DISABLED_NODES"),pe=Z("MOVE_NODES"),fe=(Z("START_DRAG"),Z("END_DRAG")),ve=()=>({type:te}),be=()=>({type:ne}),ye=()=>({type:oe}),_e=()=>({type:re}),Se=(e,t,n,o,r)=>({type:ie,dropTarget:e,dropType:t,isHoveredLeft:n,boundBox:o,isLastChild:r}),we=()=>({type:ee}),Ce=e=>({type:X,ids:e}),Te=(e,t,n)=>({type:pe,ids:e,targetId:t,dropType:n}),Ee=()=>({type:ae}),Ie=()=>({type:ce}),Me=e=>({type:ue,nodeId:e}),ke=e=>({type:de,nodeId:e}),De=(e,t,n=0)=>({type:he,nodeId:e,isSelected:t,mode:n}),Ne=(e,t)=>({type:ge,nodeId:e,isExpanded:t}),Le=e=>({type:me,ids:e}),xe=()=>({type:fe});var ze=n(77145);const je=e=>e.nodes,Ae=e=>e.selection,Re=e=>e.dropTarget,Oe=e=>e.expanded,Be=e=>e.scrollToId,Fe=(e,t)=>t,Pe=(0,ze.createSelector)([je,Fe],((e,t)=>e[t])),We=(0,ze.createSelector)([Ae,Fe],((e,t)=>e.ids.includes(t))),He=(0,ze.createSelector)([Oe,Fe],((e,t)=>e.includes(t))),Ve=(0,ze.createSelector)([e=>e.disabled,Ae,Fe],((e,t,n)=>!t.ids.includes(n)&&e.includes(n))),Ge=(0,ze.createSelector)(je,(e=>Object.keys(e))),Ue=(0,ze.createSelector)(Ae,(({ids:e})=>e)),Ze=(0,ze.createSelector)(Ae,(({lastFocusedNodeId:e})=>e)),Ke=(0,ze.createSelector)(Ae,(({isMultiSelecting:e})=>e)),Ye=(0,ze.createSelector)([je,Ue],((e,t)=>t.map((t=>e[t])))),$e=(0,ze.createSelector)(je,(e=>Object.values(e).filter((e=>0===e.level)))),qe=(0,ze.createSelector)([je,$e],((e,t)=>t.reduce(((t,n)=>[...t,...Je(e,(0,T.ensureDefined)(n))]),[])));function Je(e,t){const n=[];for(const o of t.children)n.push(e[o]),n.push(...Je(e,e[o]));return n}const Qe=(0,ze.createSelector)([je,$e,Oe],((e,t,n)=>{const o=new Set(n);return t.reduce(((t,n)=>[...t,...et(e,(0,T.ensureDefined)(n),o)]),[])})),Xe=(0,ze.createSelector)([je,Ue,Oe],((e,t,n)=>{const o=new Set(n)
;return[{id:"drag-list",level:-1,children:t}].reduce(((t,n)=>[...t,...et(e,(0,T.ensureDefined)(n),o)]),[])}));function et(e,t,n){const o=[];for(const r of t.children){const t=e[r];void 0!==t&&(o.push(t),n.has(r)&&o.push(...et(e,t,n)))}return o}function*tt(e){const{selectedIds:t,nodes:n}=yield(0,S.call)(e),o={};for(let e=0;e<n.length;++e){const t=n[e];o[t.id]=t}yield(0,S.put)((e=>({type:Y,nodes:e}))(o)),yield(0,S.put)(Ce(t));!Ze(yield(0,S.select)())&&t.length>0&&(yield(0,S.put)(Me(t[0])),yield(0,S.put)(ke(t[0])))}function*nt(e){for(;;){if((yield(0,S.take)([ae,ce])).type===ae){const t=Ge(yield(0,S.select)()).filter((t=>!e(t)));yield(0,S.put)(Le(t))}else yield(0,S.put)(Le([]))}}function*ot(){for(;;){const{type:e}=yield(0,S.take)([re,oe]),t=yield(0,S.select)(),n=qe(t),o=n.length,r=Ze(t),i=[...Ue(t)],s=1===i.length&&i[0]!==r,l=n.findIndex((e=>e.id===(s?i[0]:r)));if(e===oe&&0===l||e===re&&l===o-1)continue;const a=ut(t,e===re?"next":"previous",n,l),{id:c}=a;i.includes(c)&&r?(yield(0,S.put)(De(r,!1,1)),yield(0,S.put)(Me(c))):yield(0,S.put)(De(c,!0,1)),yield(0,S.put)(ke(c))}}function*rt(e,t){for(;;){const{type:n}=yield(0,S.take)([ne,te]),o=yield(0,S.select)(),r=qe(o),i=Ye(o),s=Ze(o);if(1===i.length&&i[0].id!==s&&!s){if(n===ne){yield(0,S.put)(Me(i[0].id));continue}if(n===te){const e=r.findIndex((e=>e.id===i[0].id)),t=ut(o,"previous",r,e);yield(0,S.put)(Me(t.id));continue}}const l=r.findIndex((e=>e.id===s)),a=n===ne?"next":"previous",c=ut(o,a,r,l),{id:u}=c;e?e([u],a):yield(0,S.put)(Ce([u])),t&&t(u),yield(0,S.put)(Me(u))}}function*it(e,t=(()=>!0)){for(;;){const{mode:n,nodeId:o,isSelected:r}=yield(0,S.take)(he);let i=[...Ue(yield(0,S.select)())];const s=qe(yield(0,S.select)());if(1===n)r?i.push(o):i.splice(i.indexOf(o),1);else if(2===n&&i.length>0){const e=Ze(yield(0,S.select)());let n=s.findIndex((t=>t.id===e));-1===n&&(n=s.reduce(((e,t,n)=>i.includes(t.id)?n:e),-1));const r=s.findIndex((e=>e.id===o));if(n!==r)for(let e=Math.min(n,r);e<=Math.max(n,r);e++){const n=s[e].id;!i.includes(n)&&t(n)&&i.push(n)}}else i=o?[o]:[];const l=new Set(i);i=s.reduce(((e,t)=>(l.has(t.id)&&e.push(t.id),e)),[]),e?e(i):yield(0,S.put)(Ce(i)),yield(0,S.put)(Me(o))}}function*st(e=(()=>!0),t){const{dropTarget:n,dropType:o,isHoveredLeft:r,boundBox:i,isLastChild:s}=t,l=Re(yield(0,S.select)()),a=Pe(yield(0,S.select)(),(0,T.ensureDefined)(n.parentId)),c=s&&"after"===o,u=Ye(yield(0,S.select)()),d=!c||!r&&e(u,n,o)?n:a,h=l.node&&l.node.id!==d.id||l.dropType!==o;u.map((e=>e.id)).includes(d.id)?yield(0,S.put)({type:le}):h&&e(u,d,o)&&(yield(0,S.put)(((e,t,n)=>({type:se,node:e,dropType:t,boundBox:n}))(d,o,i)))}function*lt(e){yield(0,S.throttle)(0,ie,st,e)}function*at(e){for(;;){yield(0,S.take)(ee);const t=Ye(yield(0,S.select)()),{node:n,dropType:o}=Re(yield(0,S.select)());if(n&&o){const r=new CustomEvent("tree-node-drop",{detail:{nodes:t,target:n.id,type:o}});if(e&&e(r),!r.defaultPrevented){const e=Ue(yield(0,S.select)());yield(0,S.put)(Te(e,n.id,o))}}}}function*ct(e){for(;;){yield(0,S.take)(pe);e(je(yield(0,S.select)()))}}function ut(e,t,n,o){const r=n.length
;let i;-1===o&&"previous"===t&&(o=r);let s=0;for(;!i||Math.abs(s)<r&&((l=i).level>1&&!He(e,(0,T.ensureDefined)(l.parentId)));)s+="next"===t?1:-1,i=n[(o+s+r)%r];var l;return i}function*dt(e={}){const{saga:t,onDrop:n,canMove:o,onMove:r,onSelect:i,onKeyboardSelect:s,initState:l,canBeAddedToSelection:a}=e,c=[(0,S.fork)(lt,o),(0,S.fork)(at,n),(0,S.fork)(it,i,a),(0,S.fork)(rt,i,s),(0,S.fork)(ot)];for(t&&c.push((0,S.fork)(t)),r&&c.push((0,S.fork)(ct,r)),a&&c.push((0,S.fork)(nt,a));;){l&&(yield(0,S.call)(tt,l));const e=yield(0,S.all)(c);yield(0,S.take)(Q);for(const t of e)yield(0,S.cancel)(t)}}var ht=n(91622),gt=n(1722);const mt=(0,l.getLogger)("Platform.GUI.ObjectTree.CallApi");const pt={ids:[],lastFocusedNodeId:void 0,isMultiSelecting:!1};const ft={node:void 0,dropType:void 0,boundBox:void 0};const vt=(0,ht.combineReducers)({nodes:function(e={},t){switch(t.type){case Y:return t.nodes;case $:{const{nodes:n}=t,o=n.map((e=>e.id)),r={...e};for(const t of Object.keys(e))if(!o.includes(t)){const{parentId:e}=r[t];e&&(r[e]={...r[e],children:r[e].children.filter((e=>e!==t))}),delete r[t]}for(const e of n){const t=e.id;if(r.hasOwnProperty(t)){!(0,gt.deepEquals)(r[t].children,e.children)[0]&&(r[t]={...r[t],children:[...e.children]})}else{r[t]=e;const{parentId:n}=e;if(n&&!r[n].children.includes(t))throw new Error("Not implemented")}}return r}case q:{const{type:n,nodeId:o,...r}=t;return{...e,[o]:{...e[o],...r}}}case J:{const{nodes:n}=t,o={...e};return Object.keys(n).forEach((e=>{o[e]={...o[e],...n[e]}})),{...e,...o}}case pe:{const{ids:n,targetId:o,dropType:r}=t,i=(0,T.ensureDefined)(e[o].parentId),s=e[i],l={};for(const t of n){const n=e[t];if(n.parentId){const o=l[n.parentId]||e[n.parentId];l[n.parentId]={...o,children:o.children.filter((e=>e!==t))}}l[t]={...n,parentId:i,level:s.level+1}}const a=s.children.filter((e=>!n.includes(e)));return a.splice(((e,t,n)=>{switch(n){case"before":return e.indexOf((0,T.ensureDefined)(t));case"inside":return e.length;case"after":return e.indexOf((0,T.ensureDefined)(t))+1;default:return 0}})(a,o,r),0,...n),l[i]={...e[i],children:a,isExpanded:!0},{...e,...l}}default:return e}},selection:function(e=pt,t){switch(t.type){case X:{const{ids:n}=t;return{...e,ids:n,lastFocusedNodeId:n.length>0?e.lastFocusedNodeId:void 0}}case ae:return{...e,isMultiSelecting:!0};case ce:return{...e,isMultiSelecting:!1};case ue:return{...e,lastFocusedNodeId:t.nodeId};case $:{const n=new Set(t.nodes.map((e=>e.id)));return e.lastFocusedNodeId&&!n.has(e.lastFocusedNodeId)&&delete e.lastFocusedNodeId,{...e,ids:e.ids.filter((e=>n.has(e)))}}default:return e}},dropTarget:function(e=ft,t){switch(t.type){case se:{const{node:n,dropType:o,boundBox:r}=t;return{...e,node:n,dropType:o,boundBox:r}}case le:case fe:case Q:return{...ft};default:return e}},expanded:function(e=[],t){if(t.type===ge){const{nodeId:n,isExpanded:o}=t;if(o)return[...e,n];const r=[...e];return r.splice(e.indexOf(n),1),r}return e},disabled:function(e=[],t){return t.type===me?[...t.ids]:e},scrollToId:function(e=null,t){return t.type===de?null===t.nodeId?null:{id:t.nodeId}:e
}});var bt=n(40933),yt=n(20037),_t=n(49483);var St=n(42357),wt=n(98314),Ct=n(47201),Tt=n(70412);const Et=o.createContext({size:0,smallSizeTreeNodeAction:1}),It={[C.Modifiers.Mod]:1,[C.Modifiers.Shift]:2};var Mt=n(69533),kt=n(75564);const Dt=()=>{};class Nt extends o.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e;const{connectDragSource:t,connectDropTarget:n,connectDragPreview:o}=this.props;(0,T.ensureDefined)(n)(this._ref),(0,T.ensureDefined)(t)(this._ref),(0,T.ensureDefined)(o)((0,wt.getEmptyImage)(),{captureDraggingState:!0})},this._handleTouchStart=e=>{const t=(e,t)=>{const n=function(e,t){try{const n=document.createEvent("TouchEvent");return n.initTouchEvent(e,!0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,t.touches,t.targetTouches,t.changedTouches),n}catch(e){return null}}(e,t);if(n)return n;const o=Array.from(t.changedTouches),r=Array.from(t.touches),i=Array.from(t.targetTouches);return new TouchEvent(e,{bubbles:!0,changedTouches:o,touches:r,targetTouches:i})},n=e.target;if(n instanceof Element){const e=e=>{const o=e;if(!n.isConnected){o.preventDefault();const e=t("touchmove",o);document.body.dispatchEvent(e)}},o=r=>{const i=r;if(!n.isConnected){i.preventDefault();const e=t("touchend",i);document.body.dispatchEvent(e)}n.removeEventListener("touchend",o),n.removeEventListener("touchmove",e)};n.addEventListener("touchend",o),n.addEventListener("touchmove",e)}}}componentDidMount(){var e;null===(e=this._ref)||void 0===e||e.addEventListener("touchstart",this._handleTouchStart)}componentWillUnmount(){var e;null===(e=this._ref)||void 0===e||e.removeEventListener("touchstart",this._handleTouchStart)}render(){return o.createElement(Lt,{...this.props,reference:this._handleRef})}getNode(){return(0,T.ensureNotNull)(this._ref)}}const Lt=e=>{const{id:t,isSelected:n,isOffset:r,isExpandable:i,setIsSelected:s,isDisabled:l,isExpanded:a,onClick:c,parentId:u,setIsExpanded:d,reference:h,isFirstListItem:g,isLastListItem:m,nodeRenderer:p,isChildOfSelected:f=!1}=e,{size:v,smallSizeTreeNodeAction:b}=(0,o.useContext)(Et),y=(0,o.useRef)(null),_=(0,Ct.createSafeMulticastEventHandler)((e=>y.current=e),h);let[S,w]=(0,Tt.useHover)();return(_t.CheckMobile.any()||_t.CheckMobile.isIPad())&&(S=n,w={onMouseOut:Dt,onMouseOver:Dt}),o.createElement("div",{className:E(kt.wrap,n&&kt.selected,f&&kt.childOfSelected,l&&kt.disabled,i&&kt.expandable),onClick:1===v&&0===b?T:function(e){if(e.defaultPrevented)return;const o=It[(0,C.modifiersFromEvent)(e)]||0;!l&&s&&s(t,!n,o);c&&0===o&&c(e,t)},onContextMenu:T,ref:_,...w},i&&o.createElement(M.Icon,{icon:Mt,className:E(kt.expandHandle,a&&kt.expanded),onClick:function(e){e.preventDefault(),i&&d(t,!a)},onMouseDown:function(e){e.preventDefault()}}),p({id:t,isOffset:r,parentId:u,isDisabled:l,isSelected:n,isChildOfSelected:f,isHovered:S,isExpanded:a,isFirstListItem:g,isLastListItem:m}));function T(){l||n||!s||s(t,!0)}},xt=o.createContext({});function zt(e,t){const{id:n}=t,o=Pe(e,n),r=We(e,n);let i=!1,s=o.parentId;for(;s&&!i;)i=We(e,s),
s=Pe(e,s).parentId;return{...o,isSelected:r,isChildOfSelected:i,isExpanded:o.children.length>0&&He(e,n),isExpandable:o.children.length>0,isDisabled:Ve(e,n)}}function jt(e){return(0,ht.bindActionCreators)({setIsExpanded:Ne,processDropTarget:Se,dropSelection:we,selectNext:be,selectPrevious:ve,setIsSelected:De,endDrag:xe},e)}const At=(0,H.connect)(zt,jt,null,{context:xt})((function(e){const t=(0,o.useRef)(null),[,n,r]=(0,St.useDrag)({type:"node",item:t=>{const{id:n,isDisabled:o,isSelected:r}=e;return o||r||e.setIsSelected(n,!0),e},end:e=>{e.endDrag()}}),[,i]=(0,bt.useDrop)({accept:"node",hover:(n,o)=>{const r=t.current;if(!r)return;const i=r.getNode(),s=i.getBoundingClientRect(),l=s.bottom-s.top,a=o.getClientOffset();if(a){const t=a.y-s.top;let n,o;if(n=0===e.children.length?t<l/2?"before":"after":t<l/3?"before":e.isExpanded||t>=l/3&&t<2*l/3?"inside":"after",void 0!==e.getContainerElement){const t=e.getContainerElement().getBoundingClientRect();o={top:s.top-t.top,left:s.left-t.left,bottom:s.top-t.top+s.height,right:s.left-t.left+s.width,height:s.height,width:s.width}}else o={top:i.offsetTop,left:i.offsetLeft,bottom:i.offsetTop+i.offsetHeight,right:i.offsetLeft+i.offsetWidth,height:i.offsetHeight,width:i.offsetWidth};e.processDropTarget(e,n,a.x-s.left<48,o,e.isLastChild)}}});return o.createElement(Nt,{...e,connectDragSource:n,connectDropTarget:i,connectDragPreview:r,ref:t})})),Rt=(0,H.connect)(zt,jt,null,{context:xt})(Lt);var Ot=n(35749),Bt=n(85783),Ft=n(65718);function Pt(e){const t=e(),n=(0,o.useRef)(t);n.current=t;const[r,i]=(0,o.useState)(n.current),s=(0,o.useRef)(null);return(0,o.useEffect)((()=>{null===s.current&&(s.current=requestAnimationFrame((()=>{s.current=null,i(n.current)})))})),(0,o.useEffect)((()=>()=>{s.current&&cancelAnimationFrame(s.current)}),[]),r}function Wt(e){const{dropTargetOffset:t,mousePosition:n}=e;if(!t)return{display:"none"};const{x:o,y:r}=t,i=n&&t?n.y-t.y:0,s=`translate(${o+(n&&t?n.x-t.x:0)}px, ${r+i}px)`;return{transform:s,WebkitTransform:s}}const Ht={top:0,left:0,position:"fixed",pointerEvents:"none",zIndex:100,opacity:.5,width:300,backgroundColor:"red"};function Vt(e){return{isDragging:e.isDragging()&&"node"===e.getItemType(),mousePosition:e.getClientOffset(),dropTargetOffset:e.getSourceClientOffset()}}const Gt=(0,H.connect)((function(e){return{items:Xe(e)}}),null,null,{context:xt})((function(e){const{items:t,isDragging:n,nodeRenderer:r,dragPreviewRenderer:i}=e;return Pt((function(){return n?o.createElement(Ft.Portal,null,o.createElement("div",{style:{...Ht,...Wt(e)}},t.map((e=>{if(i){const t=i;return o.createElement(t,{key:e.id,...e})}return o.createElement(Rt,{id:e.id,key:e.id,nodeRenderer:r,isDragPreview:!0,isOffset:e.level>1})})))):null}))}));function Ut(e){return o.createElement(Gt,{...e,...(0,Bt.useDragLayer)(Vt)})}var Zt=n(3085),Kt=n(33127);const Yt=o.forwardRef(((e,t)=>{const n=(0,o.useRef)(null);return e.connectDropTarget(n),(0,o.useImperativeHandle)(t,(()=>({getNode:()=>(0,T.ensureNotNull)(n.current)})),[]),o.createElement("div",{ref:n,style:{height:"100%",width:"100%"}})}))
;function $t(e){const t=(0,o.useRef)(null),[,n]=(0,bt.useDrop)({accept:"node",hover:(n,o)=>{if(!t.current)return;const r=o.getClientOffset();if(null===r)return;const i=e.getOrderedNodes();if(0===i.length)return;const s=t.current.getNode().getBoundingClientRect(),l=e.getContainerElement().getBoundingClientRect();if("first"===e.type){const t={top:s.top-l.top+s.height,left:s.left-l.left,bottom:s.top-l.top+s.height,right:s.left-l.left+s.width,height:0,width:s.width};e.processDropTarget(i[0],"before",!1,t,!1)}if("last"===e.type){const t=r.x-s.left<48,n=i[i.length-1],o=t&&2===n.level?(0,T.ensureDefined)(i.find((e=>e.id===n.parentId))):n,a={top:s.top-l.top,left:s.left-l.left,bottom:s.top-l.top,right:s.left-l.left+s.width,height:s.height,width:s.width};e.processDropTarget(o,"after",t,a,!1)}}});return o.createElement(Yt,{...e,connectDropTarget:n,ref:t})}const qt=o.createContext({isOver:!1,transform:void 0});var Jt=n(23490);function Qt(e){const{dropType:t,boundBox:n}=e,{top:o,bottom:r,left:i}=(0,T.ensureDefined)(n);return[i,"before"===t||"inside"===t?o:r]}function Xt(e){return{isDragging:e.isDragging()}}const en=(0,H.connect)((function(e){const{boundBox:t,dropType:n,node:o}=Re(e);return{boundBox:t,dropType:n,level:o?o.level:void 0}}),null,null,{context:xt})((function(e){const{dropType:t,boundBox:n,isDragging:r,level:i,transform:s=Qt}=e;return Pt((function(){if(!r||!t||!n)return null;const l={[Jt.dropTarget]:"inside"!==t,[Jt.dropTargetInside]:"inside"===t},{width:a,height:c}=n,[u,d]=s(e),h=`translate(${u}px, ${d}px)`;return o.createElement("div",{className:E(l),style:{position:"absolute",transform:h,WebkitTransform:h,top:0,left:2===i?"46px":0,width:2===i?a-46+"px":a,height:"inside"===t?c:"2px"}})}))}));function tn(e){const{isDragging:t}=(0,Bt.useDragLayer)(Xt);return o.createElement(en,{...e,isDragging:t})}const nn=o.forwardRef(((e,t)=>{const n=(0,o.useContext)(qt);return o.createElement("div",{...e,ref:t},e.children,n.isOver&&o.createElement(tn,{transform:n.transform}))}));var on=n(38223),rn=n(76106);const sn=38+C.Modifiers.Shift,ln=40+C.Modifiers.Shift;const an=o.forwardRef((function(e,t){const{navigationKeys:n,renderList:r,stopMultiSelect:i,startMultiSelect:s,isMultiSelecting:l,nodeRenderer:a,dragPreviewRenderer:c,className:u,connectDropTarget:d,readOnly:h,onClick:g,dropLayerTransform:m,setFocusedNode:p,scrollToId:f,rowHeight:v,onMultiSelectPrevious:b,onMultiSelectNext:y,onMoveCursorToNext:_,onMoveCursorToPrevious:S,onKeyDown:w,outerRef:E,width:M,height:k,isOver:D,processDropTarget:N,autofocus:x}=e,z=(0,o.useContext)(L),A=(0,o.useRef)(null);(0,o.useEffect)((()=>{var e;x&&(null===(e=A.current)||void 0===e||e.focus())}),[]),(0,o.useEffect)((()=>{const e=e=>{[C.Modifiers.Mod,C.Modifiers.Shift].includes((0,C.modifiersFromEvent)(e))&&s()},t=e=>{l&&![C.Modifiers.Mod,C.Modifiers.Shift].includes((0,C.modifiersFromEvent)(e))&&i()};return document.addEventListener("keydown",e),document.addEventListener("keyup",t),document.addEventListener("mousemove",t),()=>{document.removeEventListener("keydown",e),
document.removeEventListener("keyup",t),document.removeEventListener("mousemove",t)}}),[l]),function(e){(0,o.useEffect)((()=>{if(_t.isEdge){let t=null;const n=(0,T.ensureNotNull)(e.current),o=e=>{if(e.target instanceof Element){const n=(0,T.ensureNotNull)(e.target.closest("[draggable]"));n instanceof HTMLElement&&(n.style.opacity="0",t=requestAnimationFrame((()=>n.style.opacity="1")))}};return n.addEventListener("dragstart",o),()=>{n.removeEventListener("dragstart",o),null!==t&&cancelAnimationFrame(t)}}return()=>{}}),[])}(A);const R=(0,o.useCallback)((()=>(0,T.ensureNotNull)($.current)),[]),O=(0,o.useCallback)((()=>r),[r]),B=(0,o.useMemo)((()=>{const e=h?Rt:At,t=[];let n;t.push({type:"padding",node:o.createElement($t,{type:"first",key:"padding-top",getContainerElement:R,getOrderedNodes:O,processDropTarget:N})});for(let i=0;i<r.length;i++){const s=r[i];1===s.level&&(void 0!==n&&n!==s.parentId&&t.push({type:"separator",node:o.createElement("div",{key:n+"_separator",className:rn.separator})}),n=s.parentId),t.push({type:"node",node:o.createElement(e,{id:s.id,key:s.id,isFirstListItem:0===i,isLastListItem:i===r.length-1,isExpandable:s.children.length>0,nodeRenderer:a,readOnly:h,onClick:g,isOffset:s.level>1,getContainerElement:R})})}return t.push({type:"padding",node:o.createElement($t,{type:"last",key:"padding-bottom",getContainerElement:R,getOrderedNodes:O,processDropTarget:N})}),t}),[r]),F=(0,o.useRef)([]);F.current=B;const P=(0,o.useCallback)((e=>{let{style:t}=e;const{index:n}=e;return n===F.current.length-1&&(t={...t,bottom:0,minHeight:t.height},delete t.height),o.createElement("div",{style:t},F.current[n].node)}),[]),W=(0,o.useCallback)((e=>{const t=F.current[e];return"padding"===t.type?6:"function"==typeof v?v(e,t):v}),[v]),H=(0,o.useCallback)((e=>(0,T.ensure)(F.current[e].node.key)),[]),V=(0,o.useMemo)((()=>null===f?{index:-1}:{index:F.current.findIndex((e=>e.node.key===f.id))}),[f]);d(A);const[G,U,Z,K]=(0,Kt.useOverlayScroll)(),Y=(0,o.useRef)(null);(0,o.useEffect)((()=>(0,T.ensureNotNull)(Y.current).resetAfterIndex(0,!0)),[B]),(0,o.useEffect)((()=>(0,T.ensureNotNull)(Y.current).scrollToItem(V.index)),[V]);const $=(0,o.useRef)(null),q=(0,o.useMemo)((()=>({isOver:D,transform:m})),[D,m]),J=(0,o.useRef)(null),Q=(0,o.useRef)({startScroll(e){const t=()=>{null!==Z.current&&(J.current=requestAnimationFrame(t),Z.current.scrollBy({top:e}))};this.stopScroll(),t()},stopScroll(){null!==J.current&&(cancelAnimationFrame(J.current),J.current=null)},getListElement:()=>Z.current});return(0,o.useImperativeHandle)(t,(()=>Q.current),[]),(0,o.useEffect)((()=>()=>Q.current.stopScroll()),[D]),(0,o.useEffect)((()=>{if(!A.current||!j.PLATFORM_ACCESSIBILITY_ENABLED)return;function e(e){if(!t.matches(":focus-visible"))return;if(!z)return;const{viewModel:n}=z,o=n.selection();e.defaultPrevented||e.currentTarget!==e.target||o.selected().length||_()}const t=A.current;return t.addEventListener("focus",e),()=>{t.removeEventListener("focus",e)}}),[A,_,z]),o.createElement(qt.Provider,{value:q},o.createElement("div",{...U,className:I()(rn.tree,u),
ref:A,"data-name":"tree",tabIndex:j.PLATFORM_ACCESSIBILITY_ENABLED?0:-1,onKeyDown:function(e){const t=(0,C.hashFromEvent)(e);if(e.defaultPrevented||(0,Ot.isNativeUIInteraction)(t,e.target))return;const o=(0,T.ensureDefined)(G.scrollPosTop),r=(0,T.ensureDefined)(G.contentHeight),i=(0,T.ensureDefined)(G.containerHeight);if(i){const n=.875*i,s=o+i===r;switch(t){case 35:s||(e.preventDefault(),X(r));break;case 36:0!==o&&(e.preventDefault(),X(0));break;case 33:0!==o&&(e.preventDefault(),X(Math.max(0,o-n)));break;case 34:s||(e.preventDefault(),X(Math.min(o+n,r)))}}z||t!==sn||(e.preventDefault(),b());z||t!==ln||(e.preventDefault(),y());(38===t||void 0!==n&&"previous"===n[t])&&(e.preventDefault(),S());(40===t||void 0!==n&&"next"===n[t])&&(e.preventDefault(),_());if((8===t||46===t)&&z){const{viewModel:e}=z,t=e.selection(),n=t.selected();if(1!==n.length)return;const o=e.getNextNodeIdAfterRemove(n[0]);if(null===o)return;e.onChange().subscribe(null,(()=>{if(t.selected().length)return;const n=e.entity(o);n&&(t.set([n]),p(o))}),!0)}null==w||w(e)}},o.createElement(Zt.OverlayScrollContainer,{...G,className:rn.overlayScrollWrap}),o.createElement(yt.VariableSizeList,{ref:function(e){Y.current=e},className:rn.listContainer,width:M,height:k,itemCount:B.length,itemSize:W,children:P,itemKey:H,outerRef:function(e){Z.current=e,E&&E(e)},innerRef:function(e){$.current=e},innerElementType:nn,onItemsRendered:function(){K()},overscanCount:20,direction:(0,on.isRtl)()?"rtl":"ltr"}),o.createElement(Ut,{dragPreviewRenderer:c,nodeRenderer:a})));function X(e){var t;null===(t=Z.current)||void 0===t||t.scrollTo({left:0,top:e})}}));const cn=(0,H.connect)((function(e){return{renderList:Qe(e),orderedNodes:qe(e),isMultiSelecting:Ke(e),selectedIds:Ue(e),scrollToId:Be(e)}}),(function(e){return(0,ht.bindActionCreators)({startMultiSelect:Ee,stopMultiSelect:Ie,setFocusedNode:Me,processDropTarget:Se,onMoveCursorToNext:be,onMoveCursorToPrevious:ve,onMultiSelectPrevious:ye,onMultiSelectNext:_e},e)}),null,{context:xt})((function(e){const t=(0,o.useRef)(null),[{isOver:n},r]=(0,bt.useDrop)({accept:"node",drop:(n,o)=>{var r;("touch"===e.drag||_t.isFF)&&(null===(r=t.current)||void 0===r||r.stopScroll()),o.getItem().dropSelection()},hover:(n,o)=>{var r,i;if("touch"!==e.drag&&!_t.isFF)return;const s=o.getClientOffset();if(null===s)return;const l=null!==(i=null===(r=t.current)||void 0===r?void 0:r.getListElement())&&void 0!==i?i:null;if(null===l)return;const a=l.getBoundingClientRect();((n,o,r)=>{var i;const s=Math.abs(n-r),l=Math.abs(n-o);if(l>40&&s>40||s<=40&&l<=40)return void(null===(i=t.current)||void 0===i||i.stopScroll());((n,o,r,i)=>{var s,l,a,c;o||n?"touch"===e.drag?null===(s=t.current)||void 0===s||s.startScroll(o?-5:5):null===(l=t.current)||void 0===l||l.startScroll(o?-2:2):(r||i)&&("touch"===e.drag?null===(a=t.current)||void 0===a||a.startScroll(r?-10:10):null===(c=t.current)||void 0===c||c.startScroll(r?-5:5))})(l>20&&l<=40,s>20&&s<=40,s<=20,l<=20)})(s.y,a.bottom,a.top)},collect:e=>({isOver:e.isOver()})});return o.createElement(an,{...e,isOver:n,
connectDropTarget:r,ref:t})})),un={delayTouchStart:100};function dn(e){const{canBeAddedToSelection:t,initState:n,onSelect:r,canMove:i,onDrop:s,onMove:l,nodes:a,selectedIds:c,onKeyboardSelect:u,saga:d,lastFocusedNodeObject:h,lastSyncTimestampRef:g,scrollToId:m,...p}=e,[f,v]=(0,o.useState)(null);return(0,o.useEffect)((()=>{const e=(0,W.default)();v(function(e){const t=(0,ht.applyMiddleware)(e);return(0,ht.createStore)(vt,t)}(e));const o=e.run(dt,{initState:n,onKeyboardSelect:u,saga:d,canMove:i,onMove:l,onDrop:s,onSelect:r,canBeAddedToSelection:t});return()=>o.cancel()}),[]),(0,o.useEffect)((()=>(null!==f&&a&&(g&&(g.current=performance.now()),f.dispatch((e=>({type:$,nodes:e}))(a))),()=>{})),[f,a]),(0,o.useEffect)((()=>{null!==f&&c&&f.dispatch(Ce(c))}),[f,c]),(0,o.useEffect)((()=>{null!==f&&(null==h?void 0:h.id)&&f.dispatch(Me(h.id))}),[f,h]),null===f?null:o.createElement(hn,{store:f,scrollToId:m,...p})}const hn=o.memo((function(e){const{store:t,scrollToId:n,...r}=e,i="touch"===e.drag?G.TouchBackend:V.HTML5Backend;return(0,o.useEffect)((()=>{var e;t.dispatch(ke(null!==(e=null==n?void 0:n.id)&&void 0!==e?e:null))}),[n]),o.createElement(U.DndProvider,{backend:i,options:un},o.createElement(H.Provider,{store:t,context:xt},o.createElement(cn,{...r})))}));function gn(e,t){(0,F.trackEvent)("Object Tree",e,t)}function mn(e){return e.length>1?"Multi select":e[0].gaLabel()}function pn(e){return(0,W.eventChannel)((t=>{const n={};return e.onChange().subscribe(n,(()=>t({type:Q}))),e.onGroupCreated().subscribe(n,(e=>t(Ne(e,!0)))),e.selection().onChange().subscribe(n,(e=>t(Ce(e)))),()=>{e.onChange().unsubscribeAll(n),e.selection().onChange().unsubscribeAll(n),e.onGroupCreated().unsubscribeAll(n)}}),W.buffers.expanding())}function*fn(){for(;;)yield(0,S.take)([ne,te]),gn("Select","Arrow")}function*vn(){for(;;){const{mode:e}=yield(0,S.take)(he);1===e&&gn("Multi select","Ctrl"),2===e&&gn("Multi select","Shift")}}function*bn(e){for(;;){yield(0,S.take)(ee);const{node:t,dropType:n}=Re(yield(0,S.select)());if(t){const o=Ye(yield(0,S.select)()),r=o.map((t=>(0,T.ensureNotNull)(e.entity(t.id))));let i="Drag";1===t.level&&"inside"!==n&&o.some((e=>2===e.level))?i="From the group":2!==t.level&&"inside"!==n||!o.some((e=>1===e.level))?1===o.length&&o[0].parentId!==t.parentId&&(i="Existing pane"):i="To the group",gn(i,mn(r))}}}function*yn(e){yield(0,S.fork)(fn),yield(0,S.fork)(vn),yield(0,S.fork)(bn,e)}function*_n(e){yield(0,S.fork)(yn,e);const t=yield(0,S.call)(pn,e);mt.logNormal("Opened object tree data source channel");try{for(;;){const e=yield(0,S.take)(t);yield(0,S.put)(e)}}finally{mt.logNormal("Closed object tree data source channel"),t.close()}}const Sn=o.createContext(null);var wn=n(21861),Cn=n(898),Tn=n(6909);const En=w.mobiletouch?"touch":"native";function In(e){const{viewModel:t,showHeader:n=!0,nodeRenderer:r,isDialog:i=!1,hideHeaderTitle:s=!1}=e,l=(0,o.useRef)(null),a=function(e){const[t,n]=(0,o.useState)(e.getChartId()),r=(0,o.useRef)(t);return r.current=t,(0,o.useEffect)((()=>{return e.onChange().subscribe(null,t),()=>{
e.onChange().unsubscribe(null,t)};function t(){const t=e.getChartId();r.current!==t&&n(t)}}),[]),t}(t),[c,u]=(0,Cn.useDimensions)(),[d,h]=(0,o.useState)(null),g=(0,o.useMemo)((()=>({isTouch:w.touch,isDialog:i})),[i]);return o.createElement(Sn.Provider,{value:g},o.createElement(L.Provider,{value:{viewModel:t}},o.createElement("div",{className:Tn.wrap,onContextMenu:wn.preventDefaultForContextMenu},n&&o.createElement(P,{hideTitle:s}),o.createElement("div",{className:Tn.space,onClick:function(e){if(e.defaultPrevented)return;if(!(e.target instanceof Element)||null===l.current)return;e.target===l.current&&t.selection().set([])},ref:c},null!==u&&o.createElement(dn,{key:a,height:u.height,width:u.width,canBeAddedToSelection:function(e){const n=t.entity(e);return t.selection().canBeAddedToSelection(n)},nodeRenderer:r,initState:function(){const{nodes:e,selection:n}=t.getState();return{selectedIds:n,nodes:e}},canMove:function(e,n,o){return t.isSelectionDropable(n.id,o)},drag:En,rowHeight:Mn,onSelect:function(e){const n=e.map((e=>t.entity(e))).filter((e=>null!==e));t.selection().set(n)},onDrop:function(e){e.preventDefault();const{detail:{target:n,type:o}}=e;t.insertSelection(n,o)},scrollToId:d,saga:function*(){yield(0,S.fork)(_n,t)},onKeyboardSelect:function(e){h({id:e})},outerRef:function(e){l.current=e},onKeyDown:function(e){if(13===(0,C.hashFromEvent)(e)){e.preventDefault();const n=t.selection().selected(),o=n.length>0?t.entity(n[0]):void 0;o&&t.openProperties(o)}},autofocus:i})))))}function Mn(e,t){switch(t.type){case"node":return 38;case"separator":return 13}}var kn,Dn=n(14483);!function(e){e.Svg="svg"}(kn||(kn={}));var Nn=n(31261),Ln=n(86928);function xn(e){const{className:t,disabled:n,...r}=e;return o.createElement(M.Icon,{className:I()(Ln.button,n&&Ln.disabled,t),...r})}var zn=n(77975);const jn=v.t(null,void 0,n(91073)),An=v.t(null,void 0,n(35038)),Rn=v.t(null,void 0,n(15101)),On=v.t(null,void 0,n(42284)),Bn=v.t(null,void 0,n(31971)),Fn=v.t(null,void 0,n(81428)),Pn=v.t(null,void 0,n(34596));var Wn=n(14787),Hn=n(33765),Vn=n(52870),Gn=n(49756),Un=n(94007),Zn=n(62766),Kn=n(87769);function Yn(e){const{id:t}=e,n=(0,o.useContext)(L),{viewModel:r}=(0,T.ensureNotNull)(n),i=r.entity(t);return null===i?null:o.createElement($n,{...e,entity:i})}function $n(e){const{id:t,isOffset:r,isDisabled:i,isSelected:s,isChildOfSelected:l,isHovered:a,parentId:c,entity:u,isExpanded:d}=e,h=(0,o.useContext)(L),{viewModel:g}=(0,T.ensureNotNull)(h),m=(0,o.useContext)(Sn),{size:p}=(0,o.useContext)(Et),[f,b]=(0,o.useState)(!1),y=(0,o.useRef)(null),[_,S]=(0,o.useState)(u.title()),[w,I]=(0,o.useState)(u.getIcon()),[k,D]=(0,o.useState)(u.isLocked()),[N,x]=(0,o.useState)(u.isVisible()),[z,j]=(0,o.useState)(u.isActualInterval()),[A,R]=(0,o.useState)(u.getDrawingSyncState()),[O,B]=(0,o.useState)(!1),[F,P]=((0,zn.useWatchedValueReadonly)({watchedValue:g.getChartLayout()}),(0,o.useState)(!1)),W=(0,o.useRef)(null);(0,o.useEffect)((()=>{const e={};u.onLockChanged().subscribe(e,(()=>D(u.isLocked()))),u.onVisibilityChanged().subscribe(e,(()=>x(u.isVisible())))
;const t=u.onTitleChanged();t&&t.subscribe(e,(()=>S(u.title()))),u.onIsActualIntervalChange().subscribe(e,(()=>j(u.isActualInterval()))),u.onSyncStateChanged().subscribe(e,(()=>R(u.getDrawingSyncState())));const n=u.onIconChanged?u.onIconChanged():void 0;return n&&n.subscribe(e,(()=>I(u.getIcon()))),()=>{u.onIsActualIntervalChange().unsubscribeAll(e),u.onLockChanged().unsubscribeAll(e),u.onVisibilityChanged().unsubscribeAll(e),u.onSyncStateChanged().unsubscribeAll(e),t&&t.unsubscribeAll(e),W.current&&clearTimeout(W.current),n&&n.unsubscribeAll(e)}}),[u]),(0,o.useEffect)((()=>{f&&y.current&&(y.current.focus(),y.current.setSelectionRange(0,_.length))}),[f]),(0,o.useEffect)((()=>{const e={};return g.hoveredObjectChanged().subscribe(e,X),()=>{g.hoveredObjectChanged().unsubscribeAll(e)}}),[d]),(0,o.useEffect)((()=>{g.setHoveredObject(a?t:null)}),[a]),(0,o.useEffect)((()=>{!s&&W.current&&(clearTimeout(W.current),W.current=null),b(!1)}),[s]);const H={};if(c){const e=g.entity(c);e&&(H["data-parent-name"]=e.title()),H["data-type"]=u.hasChildren()?"group":"data-source"}const V=Dn.enabled("test_show_object_tree_debug")?`<${u.id()}> (${u.zOrder()}) ${u.title()}`:u.title(),G=a||O,U=f&&s,Z=!!m&&m.isTouch,K=!!m&&m.isDialog,Y=z&&N?Un:Zn,$=u.hasChildren()?v.t(null,void 0,n(38207)):v.t(null,void 0,n(39781));let q=null;return w&&w.type===kn.Svg&&(q=o.createElement(M.Icon,{icon:w.content||"",className:Kn.icon})),o.createElement("span",{className:E(Kn.wrap,i&&Kn.disabled,s&&Kn.selected,r&&Kn.offset,l&&Kn.childOfSelected,O&&!i&&!s&&!l&&Kn.hover,K&&!i&&!s&&!l&&Kn.dialog),onMouseDown:function(e){f&&!(0,T.ensureNotNull)(y.current).contains(e.target)&&P(!0)},onClick:1===p?J:function(e){if(e.defaultPrevented)return;if(0!==(0,C.modifiersFromEvent)(e))return;if(W.current)e.preventDefault(),clearTimeout(W.current),W.current=null,g.openProperties(u),P(!1);else{const e=g.selection().selected();W.current=setTimeout((()=>{W.current=null,s&&!F&&1===e.length&&g.rename(u,(()=>b(!0))),P(!1)}),500)}},onContextMenu:Z?void 0:J},!U&&o.createElement(o.Fragment,null,q,!1,o.createElement("span",{className:E(Kn.title,g.isMain(u)&&Kn.main,(!u.isVisible()||!z)&&Kn.disabled),...H},V),o.createElement("span",{className:Kn.rightButtons},u.canBeLocked()&&o.createElement(xn,{title:k?Rn:On,icon:k?Vn:Gn,className:E(Kn.button,(G||k)&&Kn.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),g.setIsLocked(t,!u.isLocked())},"data-role":"button","data-name":"lock","data-active":k}),o.createElement(xn,{icon:Y,className:E(Kn.button,!z&&Kn.warn,(G||!N||!z)&&Kn.visible,"apply-common-tooltip"),onClick:z?function(e){if(e.defaultPrevented)return;e.preventDefault(),g.setIsVisible(t,!u.isVisible())}:function(e){if(e.defaultPrevented)return;e.preventDefault(),g.openProperties(u,Wn.TabNames.visibility)},title:function(){if(!z)return $;return N?Bn:Fn}(),"data-role":"button","data-name":"hide","data-active":!N}),u.canBeRemoved()&&o.createElement(xn,{title:Pn,icon:Hn,className:E(Kn.button,(Z||G)&&Kn.visible,"apply-common-tooltip"),
onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),e.stopPropagation(),g.remove(t)},"data-role":"button","data-name":"remove"}))),U&&o.createElement(Nn.InputControl,{value:_,onChange:function(e){S(e.currentTarget.value)},onClick:wn.preventDefault,className:Kn.renameInput,onKeyDown:function(e){27===(0,C.hashFromEvent)(e)?(e.preventDefault(),S(u.title()),b(!1)):13===(0,C.hashFromEvent)(e)&&(e.preventDefault(),Q())},reference:function(e){y.current=e},onBlur:Q,onDragStart:function(e){e.preventDefault(),e.stopPropagation()},draggable:!0,stretch:!0}));function J(e){e.defaultPrevented||f||!u.fullyConstructed()||(e.preventDefault(),e.persist(),g.openContextMenu(u,(()=>b(!0)),e))}function Q(){""!==_&&u.setName(_),S(u.title()),b(!1)}function X(e){if(u.hasChildren()&&!d){const t=null!==e&&u.childrenIds().has(e);B(t)}else B(t===e)}}var qn=n(17742);class Jn extends o.PureComponent{constructor(e){super(e),this._isMounted=!1,this._dialogRef=o.createRef(),this._renderChildren=e=>o.createElement(Qn,{isSmallTablet:e,viewModel:this.props.viewModel}),this._handleMediaChange=()=>{this.state.showDrawer&&!window.matchMedia(y.DialogBreakpoints.TabletSmall).matches&&this.setState({showDrawer:!1})},this._onManageDrawings=e=>{throw new Error("not supported")},this._closeDrawer=()=>{this.setState({showDrawer:!1})},this._handleContextMenuOpened=e=>{this.setState({isContextMenuOpened:e})},this._matchMedia=window.matchMedia(y.DialogBreakpoints.TabletSmall),this.state={showDrawer:!1,showDialog:!1,isContextMenuOpened:!1}}componentDidMount(){this._isMounted=!0,this._matchMedia.addListener(this._handleMediaChange),this.props.viewModel.isContextMenuOpened().subscribe(this._handleContextMenuOpened)}componentWillUnmount(){this._isMounted=!1,this._matchMedia.removeListener(this._handleMediaChange),this.props.viewModel.isContextMenuOpened().unsubscribe(this._handleContextMenuOpened)}render(){return o.createElement(o.Fragment,null,o.createElement(f.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>o.createElement(b.AdaptivePopupDialog,{additionalElementPos:"after",additionalHeaderElement:!1,className:qn.dialog,dataName:"object-tree-dialog",isOpened:!0,onClickOutside:this.state.showDialog||e||this.state.isContextMenuOpened?()=>{}:this.props.onClose,onClose:this.props.onClose,ref:this._dialogRef,render:()=>this._renderChildren(e),title:v.t(null,void 0,n(31095)),showSeparator:!0}))),o.createElement(p.DrawerManager,null,this.state.showDrawer&&o.createElement(m.Drawer,{onClose:this._closeDrawer,position:"Bottom"},o.createElement(_.ManageDrawings,{onClose:this._closeDrawer,chartWidget:this.props.activeChartWidget,isMobile:!0}))))}}function Qn(e){const{isSmallTablet:t,viewModel:n}=e,r=(0,o.useMemo)((()=>({size:t?1:0,smallSizeTreeNodeAction:0})),[t]);return o.createElement(Et.Provider,{value:r},o.createElement(In,{nodeRenderer:Yn,showHeader:!1,viewModel:n,isDialog:!0}))}var Xn=n(16216),eo=n(98310),to=n(57898);var no=n(32112);function oo(e,t){return`${e}:${t}`}function ro(e){const t=e.split(":");return{persistentId:t[0],instanceId:t[1]}}
class io{constructor(e){this._onChange=new to.Delegate,this._recalculate=()=>{const e=this._groupModel.groups().map((e=>oo(e.id,e.instanceId()))),t=this._selectionApi.allSources();this._selected=this._selected.filter((n=>e.includes(n)||t.includes(n))),this._onChange.fire(this._selected)},this._model=e,this._selectionApi=new no.SelectionApi(this._model),this._groupModel=this._model.lineToolsGroupModel(),this._selected=this._getSelectedIds(),this._selectionApi.onChanged().subscribe(this,(()=>{this._selected=this._getSelectedIds(),this._onChange.fire(this._selected)})),this._groupModel.onChanged().subscribe(this,this._recalculate)}destroy(){this._selectionApi.onChanged().unsubscribeAll(this),this._groupModel.onChanged().unsubscribeAll(this)}set(e){const t=[];let n=e.map((e=>e.id()));for(const o of e)if(o.hasChildren()){const e=o.childrenIds();t.push(...Array.from(e.values())),n=n.filter((t=>!e.has(t)))}else t.push(o.id());this._selectionApi.set(t.map((e=>ro(e).persistentId))),this._selected=n,this._onChange.fire(this._selected)}canBeAddedToSelection(e){return null!==e&&e.canBeAddedToSelection()}onChange(){return this._onChange}selected(){return this._selected}_getSelectedIds(){return this._selectionApi.allSources().map((e=>this._model.dataSourceForId(e))).filter(gt.notNull).filter((e=>e.showInObjectTree())).map((e=>oo(e.id(),e.instanceId())))}}class so{constructor(e,t){this._controller=e,this._facade=t,this._groupModel=e.model().lineToolsGroupModel()}buildTree(){const e={};for(const t of this._controller.model().panes()){const n=t.sourcesByGroup().all().filter((e=>e.showInObjectTree()));e[t.id()]=lo(t.id(),0);for(const n of this._groupModel.groups()){const o=oo(n.id,n.instanceId()),r=(0,T.ensureNotNull)(this._facade.getObjectById(o));if(r.pane()===t){const o=[...n.lineTools()].sort(((e,t)=>e.zorder()>t.zorder()?-1:1)).map((e=>oo(e.id(),e.instanceId())));e[r.id()]=lo(r.id(),1,t.id(),o),e[t.id()].children.push(r.id());for(const t of o)e[t]=lo(t,2,r.id())}}for(const o of n){const n=oo(o.id(),o.instanceId());e[n]||(e[n]=lo(n,1,t.id()),e[t.id()].children.push(n))}e[t.id()].children.sort(((e,t)=>{const n=(0,T.ensureNotNull)(this._facade.getObjectById(e)),o=(0,T.ensureNotNull)(this._facade.getObjectById(t));return(0,T.ensureNotNull)(o.zOrder())-(0,T.ensureNotNull)(n.zOrder())}))}return this._facade.invalidateCache(new Set(Object.keys(e))),e}}function lo(e,t,n,o=[]){return{id:e,level:t,parentId:n,children:o}}var ao=n(36298),co=n(92249),uo=n(28853),ho=n(14292),go=n(76544),mo=n(73212),po=n(88348),fo=n(7295),vo=n(28824),bo=n(57674),yo=n(37591);const _o=new ao.TranslatedString("show {title}",v.t(null,void 0,n(87358))),So=new ao.TranslatedString("hide {title}",v.t(null,void 0,n(70301))),wo=new ao.TranslatedString("lock {title}",v.t(null,void 0,n(50193))),Co=new ao.TranslatedString("unlock {title}",v.t(null,void 0,n(92421))),To=new ao.TranslatedString("change {sourceTitle} title to {newSourceTitle}",v.t(null,void 0,n(40001))),Eo=new ao.TranslatedString("insert source(s) after",v.t(null,void 0,n(8343))),Io=!1;function Mo(e,t){
return t.every((t=>!(t.pane()!==e&&!t.allowsMovingbetweenPanes())))}function ko(e){return e instanceof ho.DataSource&&e.showInObjectTree()?oo(e.id(),e.instanceId()):null}function Do(e){return new ao.TranslatedString(e.name(),e.title(yo.TitleDisplayTarget.DataWindow))}const No=new to.Delegate;class Lo{constructor(e,t){this._syncStateChanged=new to.Delegate,this._updateSyncState=()=>{this._syncStateChanged.fire((0,T.ensureNotNull)(this.getDrawingSyncState()))},this._undoModel=e,this._dataSource=t,(0,co.isLineTool)(this._dataSource)&&(this._dataSource.linkKey().subscribe(this._updateSyncState),this._dataSource.sharingMode().subscribe(this._updateSyncState));const n=this._undoModel.lineBeingCreated();null!==n&&n===t&&n.isSynchronizable()&&po.isToolCreatingNow.subscribe(this._updateSyncState)}destroy(){(0,co.isLineTool)(this._dataSource)&&(this._dataSource.linkKey().unsubscribe(this._updateSyncState),this._dataSource.sharingMode().unsubscribe(this._updateSyncState)),po.isToolCreatingNow.unsubscribe(this._updateSyncState)}id(){return oo(this._dataSource.id(),this._dataSource.instanceId())}title(){const e=this._dataSource;return(0,co.isLineTool)(e)?e.properties().title.value()||e.translatedType():(0,go.isSeries)(e)&&this._undoModel.mainSeries()===e?e.symbolTitle(yo.TitleDisplayTarget.DataWindow,void 0,void 0,(0,_t.onWidget)()?"exchange":"listed_exchange"):e.title(yo.TitleDisplayTarget.DataWindow)}gaLabel(){return(0,uo.isStudy)(this._dataSource)?"Study":(0,co.isLineTool)(this._dataSource)?"Drawing":"Symbol"}canBeLocked(){return(0,co.isLineTool)(this._dataSource)&&this._dataSource.userEditEnabled()}canBeRemoved(){return this._undoModel.mainSeries()!==this._dataSource&&this._dataSource.isUserDeletable()}canBeHidden(){return this._dataSource.canBeHidden()}canBeRenamed(){return(0,co.isLineTool)(this._dataSource)}fullyConstructed(){return this._undoModel.lineBeingCreated()!==this._dataSource}isVisible(){return this._dataSource.properties().visible.value()}isActualInterval(){return!(0,co.isLineTool)(this._dataSource)&&!(0,uo.isStudy)(this._dataSource)||this._dataSource.isActualInterval()}onIsActualIntervalChange(){return(0,co.isLineTool)(this._dataSource)||(0,uo.isStudy)(this._dataSource)?this._dataSource.onIsActualIntervalChange():No}isLocked(){return!!(0,co.isLineTool)(this._dataSource)&&this._dataSource.properties().frozen.value()}onVisibilityChanged(){return this._dataSource.properties().visible.listeners()}onLockChanged(){return(0,co.isLineTool)(this._dataSource)?this._dataSource.properties().frozen.listeners():No}getIcon(){const e=c,t=this._dataSource.getSourceIcon(),n=(0,uo.isStudyStrategy)(this._dataSource);let o={type:kn.Svg,content:n?fo:vo};if(e&&t)if("loadSvg"===t.type){const[n,r]=t.svgId.split("."),i="linetool"===n?e.linetool[r]:e.series[Number(r)];o={type:kn.Svg,content:i||vo}}else"svgContent"===t.type&&(o={type:kn.Svg,content:t.content});return o}onIconChanged(){if(this._dataSource.onSourceIconChanged)return this._dataSource.onSourceIconChanged()}setVisible(e){const t=(e?_o:So).format({title:Do(this._dataSource)})
;this._undoModel.setProperty(this._dataSource.properties().visible,e,t)}setLocked(e){if((0,co.isLineTool)(this._dataSource)){const t=(e?wo:Co).format({title:Do(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().frozen,e,t)}}setName(e){if((0,co.isLineTool)(this._dataSource)){const t=To.format({sourceTitle:this._dataSource.properties().title.value()||Do(this._dataSource),newSourceTitle:e});this._undoModel.setProperty(this._dataSource.properties().title,e,t,Io)}}isCopiable(){return this._dataSource.copiable()}isClonable(){return this._dataSource.cloneable()}zOrder(){return this._dataSource.zorder()}remove(){this._undoModel.removeSource(this._dataSource,!1)}canBeAddedToSelection(){return this._undoModel.selection().canBeAddedToSelection(this._dataSource)}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),e.addSourceToSelection(this._dataSource)}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{e.addSourceToSelection(this._dataSource)}))}addSourcesToArray(e){return e.push(this._dataSource),e}insertSourcesBeforeThis(e){this._insertSources(e,(e=>this._undoModel.insertBefore(e,this._dataSource)))}insertSourcesAfterThis(e){this._insertSources(e,(e=>this._undoModel.insertAfter(e,this._dataSource)))}childrenIds(){return new Set}hasChildren(){return!1}pane(){return(0,T.ensureNotNull)(this._undoModel.model().paneForSource(this._dataSource))}allowsMovingbetweenPanes(){return!(0,co.isLineTool)(this._dataSource)}canBeAddedToGroup(){return(0,co.isLineTool)(this._dataSource)&&this._dataSource.boundToSymbol()}canInsertBeforeThis(e){return this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return this._canInsertBeforeOrAfter(e)}detachFromParent(){if((0,co.isLineTool)(this._dataSource)){const e=this._undoModel.model(),t=this._undoModel.lineToolsGroupController(),n=e.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==n&&t.excludeLineToolFromGroup(n,this._dataSource)}}onTitleChanged(){const e=this._dataSource.properties().title;return e?e.listeners():void 0}canBeSyncedInLayout(){return(0,co.isLineTool)(this._dataSource)&&this._dataSource.isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}setDrawingSyncState(e){if(!this.canBeSyncedInLayout()||!this.fullyConstructed())return;const t=this._dataSource;switch(e){case 0:if(null===t.linkKey().value())return;this._undoModel.unlinkLines([t]);break;case 1:if(null!==t.linkKey().value())return;this._undoModel.shareLineTools([t],1)}}getDrawingSyncState(){return this.canBeSyncedInLayout()?this.fullyConstructed()&&null!==this._dataSource.linkKey().value()?1:0:null}doNotAffectChartInvalidation(){return(0,co.isLineTool)(this._dataSource)&&Io}_canInsertBeforeOrAfter(e){const t=this._undoModel.model();if(!Mo(this.pane(),e))return!1;if((0,co.isLineTool)(this._dataSource)){if(null!==t.lineToolsGroupModel().groupForLineTool(this._dataSource)&&e.some((e=>!e.canBeAddedToGroup())))return!1}return!0}_insertSources(e,t){const n=this._undoModel.model(),o=this._undoModel.lineToolsGroupController()
;this._undoModel.beginUndoMacro(Eo);const r=()=>{e.forEach((e=>e.detachFromParent()))},i=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);if((0,co.isLineTool)(this._dataSource)){const t=n.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==t?((0,T.assert)(!e.some((e=>e.hasChildren()))),i.forEach((e=>{(0,co.isLineTool)(e)&&(t.containsLineTool(e)||o.addLineToolToGroup(t,e))}))):r()}else r();t(i),this._undoModel.endUndoMacro()}}class xo{constructor(e,t){this._onTitleChanged=new to.Delegate,this._onVisibilityChanged=new to.Delegate,this._onLockChanged=new to.Delegate,this._onIsActualIntervalChanged=new to.Delegate,this._syncStateChanged=new to.Delegate,this._linkKeyChangedBound=this._linkKeyChanged.bind(this),this._undoModel=e,this._group=t,this._lineTools=t.lineTools(),this._paneId=(0,T.ensureNotNull)(e.model().paneForSource(this._lineTools[0])).id();const n=()=>{this._lineTools.forEach((e=>{e.properties().visible.listeners().subscribe(this,(()=>this._onVisibilityChanged.fire())),e.properties().frozen.listeners().subscribe(this,(()=>this._onLockChanged.fire())),e.onIsActualIntervalChange().subscribe(this,(()=>this._onIsActualIntervalChanged.fire())),e.linkKey().subscribe(this._linkKeyChangedBound),e.sharingMode().subscribe(this._linkKeyChangedBound)}))};this._group.onChanged().subscribe(this,(e=>{this._unsubscribeFromAllLineTools(),this._lineTools=this._group.lineTools(),n(),e.lockedChanged&&this._onLockChanged.fire(),e.visibilityChanged&&this._onVisibilityChanged.fire(),e.titleChanged&&this._onTitleChanged.fire(),e.isActualIntervalChanged&&this._onIsActualIntervalChanged.fire();const t=this.getDrawingSyncState();null!==t&&this._syncStateChanged.fire(t)})),n(),this._lastActualZOrder=this.zOrder(),this._lastIsVisible=this.isVisible(),this._lastIsActualInterval=this.isActualInterval(),this._lastIsLocked=this.isLocked()}destroy(){this._unsubscribeFromAllLineTools(),this._group.onChanged().unsubscribeAll(this)}id(){return oo(this._group.id,this._group.instanceId())}title(){return this._group.name()}gaLabel(){return"Group"}getIcon(){return{type:kn.Svg,content:bo}}canBeRemoved(){return!0}canBeHidden(){return!0}canBeLocked(){return!0}canBeRenamed(){return!0}fullyConstructed(){return!0}isVisible(){return this._group.lineTools().length>0&&(this._lastIsVisible="Invisible"!==this._group.visibility()),this._lastIsVisible}isActualInterval(){return this._group.lineTools().length>0&&(this._lastIsActualInterval=this._group.lineTools().some((e=>e.isActualInterval()))),this._lastIsActualInterval}onIsActualIntervalChange(){return this._onIsActualIntervalChanged}isLocked(){return this._group.lineTools().length>0&&(this._lastIsLocked="Locked"===this._group.locked()),this._lastIsLocked}onTitleChanged(){return this._onTitleChanged}onVisibilityChanged(){return this._onVisibilityChanged}onLockChanged(){return this._onLockChanged}setVisible(e){this._undoModel.lineToolsGroupController().setGroupVisibility(this._group,e)}setLocked(e){this._undoModel.lineToolsGroupController().setGroupLock(this._group,e)}setName(e){
this._undoModel.lineToolsGroupController().setGroupName(this._group,e)}isCopiable(){return!1}isClonable(){return!1}zOrder(){return this._group.lineTools().length>0&&(this._lastActualZOrder=this._group.lineTools()[0].zorder()),this._lastActualZOrder}remove(){this._undoModel.lineToolsGroupController().removeGroup(this._group)}canBeAddedToSelection(){const e=this._undoModel.model();return this._lineTools.every((t=>e.selection().canBeAddedToSelection(t)))}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addSourcesToArray(e){return e.push(...this._lineTools),e}detachFromParent(){}insertSourcesBeforeThis(e){const t=this._insertBeforeTarget();this._insertSources(e,(e=>this._undoModel.insertBefore(e,t)))}insertSourcesAfterThis(e){const t=this._insertAfterTarget();this._insertSources(e,(e=>this._undoModel.insertAfter(e,t)))}childrenIds(){const e=[...this._lineTools];return e.sort(((e,t)=>t.zorder()-e.zorder())),new Set(e.map((e=>oo(e.id(),e.instanceId()))))}hasChildren(){return!0}pane(){return(0,T.ensureDefined)(this._undoModel.model().panes().find((e=>e.id()===this._paneId)))}allowsMovingbetweenPanes(){return!1}canBeAddedToGroup(){return!1}canInsertBeforeThis(e){return this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return this._canInsertBeforeOrAfter(e)}canBeSyncedInLayout(){return this._lineTools.length>0&&this._lineTools[0].isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}setDrawingSyncState(e){if(this.canBeSyncedInLayout())switch(e){case 0:const e=this._lineTools.filter((e=>null!==e.linkKey().value()));e.length>0&&this._undoModel.unlinkLines(e);break;case 1:const t=this._lineTools.filter((e=>null===e.linkKey().value()));t.length>0&&this._undoModel.shareLineTools(t,1)}}getDrawingSyncState(){return this.canBeSyncedInLayout()?this._lineTools.every((e=>null!==e.linkKey().value()))?1:0:null}doNotAffectChartInvalidation(){return Io}_linkKeyChanged(){this._syncStateChanged.fire((0,T.ensureNotNull)(this.getDrawingSyncState()))}_canInsertBeforeOrAfter(e){return Mo(this.pane(),e)}_insertSources(e,t){this._undoModel.beginUndoMacro(Eo);const n=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);e.forEach((e=>e.detachFromParent())),t(n),this._undoModel.endUndoMacro()}_insertBeforeTarget(){return(0,T.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()<t.zorder()?e:t),null))}_insertAfterTarget(){return(0,T.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()>t.zorder()?e:t),null))}_unsubscribeFromAllLineTools(){this._lineTools.forEach((e=>{e.properties().visible.listeners().unsubscribeAll(this),e.properties().frozen.listeners().unsubscribeAll(this),e.onIsActualIntervalChange().unsubscribeAll(this),e.linkKey().unsubscribe(this._linkKeyChangedBound),e.sharingMode().unsubscribe(this._linkKeyChangedBound)}))}}class zo{constructor(e){this._hoveredObjectChanged=new to.Delegate,
this._entitiesCache=new Map,this._undoModel=e,this._undoModel.model().hoveredSourceChanged().subscribe(this,this._onModelHoveredSourceChanged)}destroy(){for(const e of this._entitiesCache.values())null==e||e.destroy();this._undoModel.model().hoveredSourceChanged().unsubscribe(this,this._onModelHoveredSourceChanged)}getObjectById(e){if(this._entitiesCache.has(e))return(0,T.ensureDefined)(this._entitiesCache.get(e));const t=this._createObjectById(e);return this._entitiesCache.set(e,t),t}invalidateCache(e){Array.from(this._entitiesCache.keys()).forEach((t=>{var n;e.has(t)||(null===(n=this._entitiesCache.get(t))||void 0===n||n.destroy(),this._entitiesCache.delete(t))}))}canBeGroupped(e){if(0===e.length||1===e.length&&e[0].hasChildren())return!1;const t=[];if(e.forEach((e=>e.addSourcesToArray(t))),t.some((e=>!(0,co.isLineTool)(e)||!e.boundToSymbol())))return!1;const n=this._undoModel.model(),o=t.map((e=>n.paneForSource(e)));return!(new Set(o).size>1)}contextMenuActions(e,t,n){const o=new mo.ActionsProvider(e,n),r=[];return t.forEach((e=>e.addSourcesToArray(r))),o.contextMenuActionsForSources(r)}insertBefore(e,t){t.insertSourcesAfterThis(e)}insertAfter(e,t){t.insertSourcesBeforeThis(e)}setHoveredObject(e){const t=this._undoModel.model();if(null===e)return void t.setHoveredSource(null,null);const n=t.dataSourceForId(e);null!==n&&t.setHoveredSource(n,null)}hoveredObjectId(){return ko(this._undoModel.model().hoveredSource())}hoveredObjectChanged(){return this._hoveredObjectChanged}_onModelHoveredSourceChanged(e){this._hoveredObjectChanged.fire(ko(e))}_createObjectById(e){const t=ro(e).persistentId,n=this._undoModel.model(),o=n.dataSourceForId(t);if(null!==o)return new Lo(this._undoModel,o);const r=n.lineToolsGroupModel().groupForId(t);return null!==r?new xo(this._undoModel,r):null}}Error;var jo=n(80842),Ao=n(10643),Ro=n(39347),Oo=n(97145),Bo=n(97874),Fo=n(2872),Po=n(84959),Wo=n(91730),Ho=n(33055),Vo=n(35149);const Go=(0,l.getLogger)("Platform.GUI.ObjectTree");const Uo=new ao.TranslatedString("move objects",v.t(null,void 0,n(40566))),Zo=new ao.TranslatedString("lock objects",v.t(null,void 0,n(68163))),Ko=new ao.TranslatedString("unlock objects",v.t(null,void 0,n(66824))),Yo=new ao.TranslatedString("show objects",v.t(null,void 0,n(63549))),$o=new ao.TranslatedString("hide objects",v.t(null,void 0,n(28506))),qo=new ao.TranslatedString("remove objects",v.t(null,void 0,n(57428)));class Jo{constructor(e){this._nodes={},this._onChange=new to.Delegate,this._onGroupCreated=new to.Delegate,this._subscriptions=[],this._removeSourcesPromise=null,this._timeout=null,this._objects=[],this._options={general:!0,mainSeries:!0,mainSeriesTrade:!0,esdStudies:!0,fundamentals:!0,studies:!0,lineTools:!0,publishedCharts:!0,ordersAndPositions:!0,alerts:!1,chartEvents:!0,objectTree:!1,gotoLineTool:!0},this._isContextMenuOpened=new Oo.WatchedValue(!1),this._getObjectsToModify=e=>{const t=this.selection().selected();return t.find((t=>t===e))?t.map(this._ensuredEntity):[this._ensuredEntity(e)]},this._onActiveChartChanged=()=>{this._cleanup(),this._init()
},this._cleanup=()=>{null!==this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this._subscriptions.forEach((e=>{e.unsubscribeAll(this)})),this._selection.destroy(),this._chart.unsubscribe(this._onActiveChartChanged),null!==this._removeSourcesPromise&&this._removeSourcesPromise.cancel(),this._facade.destroy()},this._init=()=>{const e=this._chart.value();e.hasModel()&&(this._controller=e.model(),this._groupController=this._controller.lineToolsGroupController(),this._model=this._controller.model(),this._groupModel=this._model.lineToolsGroupModel(),this._facade=new zo(this._controller),this._subscriptions=[this._model.mainSeries().onStyleChanged(),this._model.mainSeries().dataEvents().symbolResolved(),this._model.mainSeries().onIntervalChanged(),this._model.panesCollectionChanged(),this._model.dataSourceCollectionChanged(),this._groupModel.onChanged()],this._subscriptions.forEach((e=>{e.subscribe(this,this._update)})),this._chart.subscribe(this._onActiveChartChanged),this._selection=new io(this._model),this._update())},this._update=()=>{null===this._timeout&&(this._timeout=setTimeout((()=>{this._recalculateTree(),this._onChange.fire(),this._timeout=null})))},this._ensuredEntity=e=>(0,T.ensureNotNull)(this._getEntityById(e)),this._chart=e,this._init()}destroy(){this._cleanup()}getState(){return{nodes:Object.values(this._nodes),selection:this._selection.selected()}}getChartId(){return this._chart.value().id()}insertSelection(e,t){const n=this._facade,o=this.selection().selected().map(this._ensuredEntity),[r,i]=this._normalizeTargetAndDropType(e,t);this._controller.withMacro(Uo,(()=>{switch(i){case"before":n.insertBefore(o,r);break;case"after":n.insertAfter(o,r)}})),this._update()}entity(e){return this._facade.getObjectById(e)}isMain(e){return ro(e.id()).persistentId===this._controller.mainSeries().id()}selection(){return this._selection}setIsLocked(e,t){const n=this._getObjectsToModify(e),o=n.every((e=>e.doNotAffectChartInvalidation())),r=t?Zo:Ko;this._controller.withMacro(r,(()=>{for(const e of n)e.setLocked(t)}),o),gn("Lock",mn(n))}setIsVisible(e,t){const n=this._getObjectsToModify(e),o=n.every((e=>e.doNotAffectChartInvalidation())),r=t?Yo:$o;this._controller.withMacro(r,(()=>{for(const e of n)e.setVisible(t)}),o),gn("Hide",mn(n))}remove(e){const t=()=>{const e=n.every((e=>e.doNotAffectChartInvalidation()));this._controller.withMacro(qo,(()=>{for(const e of n)e.remove()}),e),gn("Delete",mn(n)),this._update()},n=this._getObjectsToModify(e);t()}canSelectionBeGrouped(){const e=this._getSelectedEntities();return this._facade.canBeGroupped(e)}createGroupFromSelection(){const e=this._groupController.createGroupFromSelection();gn("Create Group");const t=oo(e.id,e.instanceId());this.selection().set([this._ensuredEntity(t)]),this._onGroupCreated.fire(t),this._update()}isSelectionDropable(e,t){const n=this.selection().selected().map(this._ensuredEntity),[o,r]=this._normalizeTargetAndDropType(e,t);switch(r){case"after":return o.canInsertAfterThis(n);case"before":return o.canInsertBeforeThis(n)}}onChange(){
return this._onChange}onGroupCreated(){return this._onGroupCreated}isSelectionCloneable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isClonable()))}isSelectionCopiable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isCopiable()))}openProperties(e,t){const n=this._model.dataSourceForId(ro(e.id()).persistentId);this.selection().selected().length>1&&this.selection().selected().includes(e.id())?this._chart.value().showSelectedSourcesProperties(t):(this.selection().set([e]),null!==n?this._controller.mainSeries()===n?this._chart.value().showGeneralChartProperties(void 0,{shouldReturnFocus:!0}):((0,co.isLineTool)(n)||(0,uo.isStudy)(n))&&this._chart.value().showChartPropertiesForSource(n,t,{shouldReturnFocus:!0}):this._chart.value().showChartPropertiesForSources({sources:this._chart.value().model().selection().lineDataSources(),title:e.title(),tabName:t,renamable:!0}))}canSelectionBeUnmerged(){const e=this._getSelectedEntities();return 1===e.length&&this.canNodeWithIdBeUnmerged(ro(e[0].id()).persistentId)}canNodeWithIdBeUnmerged(e){const t=this._model.dataSourceForId(e);return null!==t&&(0,jo.isPriceDataSource)(t)&&this._model.isUnmergeAvailableForSource(t)}unmergeSelectionUp(){this._unmergeSelection(0)}unmergeSelectionDown(){this._unmergeSelection(1)}copySelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,T.ensureNotNull)(this._model.dataSourceForId(ro(e.id()).persistentId))));this._chart.value().chartWidgetCollection().clipboard.uiRequestCopy(t),gn("Copy",mn(e))}cloneSelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,T.ensureNotNull)(this._model.dataSourceForId(ro(e.id()).persistentId))));t.every(co.isLineTool)&&(this._controller.cloneLineTools([...t],!1),gn("Clone",mn(e)))}rename(e,t){const n=this._getObjectsToModify(e.id());1===n.length&&n.some((e=>e.canBeRenamed()))&&(t(),gn("Rename",mn(n)))}async openContextMenu(e,t,n){var o;this._objects=this._getObjectsToModify(e.id());const r=this._facade.canBeGroupped(this._objects);let i;if(this._objects.some((e=>e.hasChildren())))i=this._getActionsForGroupItem(e,t,r);else{const e=await this._facade.contextMenuActions(this._chart.value(),this._objects,this._options);if(i=Array.from(e).filter(((e,t,n)=>"separator"!==e.type||!n[t+1]||"separator"!==n[t+1].type)),1===this._objects.length&&this._objects[0].canBeRenamed()){const e=i.findIndex((e=>"Copy"===e.id));i.splice(-1===e?i.length:e+1,0,this._getRenameAction(t))}if(r){const e=i.findIndex((e=>"Clone"===e.id));i.splice(-1===e?0:e,0,this._getGroupAction())}}if(i.length>0){this._chart.value().updateActions();const t=ro(e.id()).persistentId,r=this._model.dataSourceForId(t),s=r instanceof go.Series,l=0!==e.childrenIds().size;let a;a=s?{menuName:"ObjectTreeContextMenu",detail:{type:"series",id:r.instanceId()}}:(0,co.isLineTool)(r)?{menuName:"ObjectTreeContextMenu",detail:{type:"shape",id:null!==(o=null==r?void 0:r.id())&&void 0!==o?o:null}}:l?{menuName:"ObjectTreeContextMenu",detail:{type:"groupOfShapes",id:t||null}}:{menuName:"ObjectTreeContextMenu",detail:{
type:"study",id:(null==r?void 0:r.id())||null}},Ao.ContextMenuManager.showMenu(i,n,{takeFocus:!0,returnFocus:!0},a,(()=>{this._isContextMenuOpened.setValue(!1)})).then((()=>{this._isContextMenuOpened.setValue(!0)}))}}setHoveredObject(e){this._facade.setHoveredObject(e)}hoveredObjectChanged(){return this._facade.hoveredObjectChanged()}getNextNodeIdAfterRemove(e){var t;const{nodes:n}=this.getState(),o=ro(e).persistentId,r=n.find((t=>t.id===e)),i=this.entity(e);if(!(r&&r.parentId&&i&&i.canBeRemoved()))return null;if((null===(t=i.pane().mainDataSource())||void 0===t?void 0:t.id())===o&&!this.canNodeWithIdBeUnmerged(o)){const e=n.filter((e=>0===e.level)).map((e=>e.id)),t=this._takeNextOrPrevElement(e,r.parentId);return(0,T.ensureDefined)(n.find((e=>e.id===t))).children[0]}const s=(0,T.ensureDefined)(n.find((e=>e.id===r.parentId))).children;return 1===s.length?this.getNextNodeIdAfterRemove(r.parentId):this._takeNextOrPrevElement(s,e)}isContextMenuOpened(){return this._isContextMenuOpened.readonly()}getChartLayout(){return this._chart.value().chartWidgetCollection().layout}_takeNextOrPrevElement(e,t){const n=e.indexOf(t);return e[n===e.length-1?n-1:n+1]}_getGroupAction(){return new Ro.Action({actionId:"ObjectsTree.CreateGroup",label:jn,icon:O,onExecute:()=>{this.createGroupFromSelection()}})}_getRenameAction(e){return new Ro.Action({actionId:"ObjectsTree.RenameItem",label:An,icon:Ho,onExecute:()=>{e(),gn("Context menu rename",mn(this._objects))}})}_getActionsForGroupItem(e,t,n){const o=[];this._objects.forEach((e=>e.addSourcesToArray(o)));const r=[];1===this._objects.length&&r.unshift(this._getRenameAction(t),new Ro.Separator),n&&r.unshift(this._getGroupAction(),new Ro.Separator);const i=(0,mo.createSyncDrawingActions)(this._chart.value(),o.filter(co.isLineTool));i.length&&(i.shift(),i.push(new Ro.Separator),r.push(...i));const s=this._chart.value().actions().format.getState();return r.push(new Ro.Action({actionId:"ObjectsTree.ToggleItemLocked",label:e.isLocked()?Rn:On,icon:e.isLocked()?Bo:Fo,onExecute:()=>this.setIsLocked(e.id(),!e.isLocked())}),new Ro.Action({actionId:"ObjectsTree.ToggleItemVisibility",label:e.isVisible()?Bn:Fn,icon:e.isVisible()?Po:Wo,onExecute:()=>this.setIsVisible(e.id(),!e.isVisible())}),new Ro.Action({actionId:"ObjectsTree.RemoveItem",label:Pn,icon:Vo,onExecute:()=>this.remove(e.id()),hotkeyHash:C.isMacKeyboard?8:46}),new Ro.Separator,new Ro.Action({actionId:s.actionId,label:s.label,icon:s.icon,onExecute:()=>this.openProperties(e)})),r}_unmergeSelection(e){const t=this._getSelectedEntities();if(1!==t.length)throw new Error("Only one object can be unmerged");const n=t[0],o=(0,T.ensureNotNull)(this._model.dataSourceForId(ro(n.id()).persistentId));if(!(0,jo.isPriceDataSource)(o))throw new Error("Entity is not IPriceDataSource");(0===e?this._controller.unmergeSourceUp:this._controller.unmergeSourceDown).call(this._controller,o);gn(0===e?"New pane above":"New pane below",mn([n]))}_recalculateTree(){const e=new so(this._controller,this._facade);this._nodes=e.buildTree()}_normalizeTargetAndDropType(e,t){
let n=this._ensuredEntity(e);return"inside"===t&&(t="before",n=(0,T.ensureNotNull)(this.entity([...n.childrenIds()].shift()||""))),[n,t]}_getSelectedEntities(){const{selected:e,removed:t}=this._selection.selected().reduce(((e,t)=>{const n=this._getEntityById(t);return n?(e.selected.push(n),e):(e.removed.push(t),e)}),{selected:[],removed:[]});return t.length&&Go.logWarn(`Detected dangling sources in selection. They will be ignored: ${JSON.stringify(t)}`),e}_getEntityById(e){return this._facade.getObjectById(e)}}var Qo=n(85067);class Xo extends Qo.DialogRenderer{constructor(){super(),this._handleClose=()=>{r.unmountComponentAtNode(this._container),this._setVisibility(!1),null!==this._viewModel&&(this._viewModel.destroy(),this._viewModel=null)};const e=(0,Xn.service)(eo.CHART_WIDGET_COLLECTION_SERVICE);this._activeChartWidget=e.activeChartWidget.value(),this._viewModel=new Jo(e.activeChartWidget)}hide(){this._handleClose()}isVisible(){return this.visible().value()}show(){g().then((()=>{null!==this._viewModel&&(r.render(o.createElement(Jn,{onClose:this._handleClose,viewModel:this._viewModel,activeChartWidget:this._activeChartWidget}),this._container),this._setVisibility(!0))}))}}},37968:(e,t,n)=>{"use strict";n.d(t,{useForceUpdate:()=>r});var o=n(50959);const r=()=>{const[,e]=(0,o.useReducer)((e=>e+1),0);return e}},77975:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>r});var o=n(50959);const r=(e,t=!1)=>{const n="watchedValue"in e?e.watchedValue:void 0,r="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[i,s]=(0,o.useState)(n?n.value():r);return(t?o.useLayoutEffect:o.useEffect)((()=>{if(n){s(n.value());const e=e=>s(e);return n.subscribe(e),()=>n.unsubscribe(e)}return()=>{}}),[n]),i}},63932:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>s});var o=n(50959),r=n(97754),i=n(58096);n(83135);function s(e){const t=r(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${i.spinnerSizeMap[e.size||i.DEFAULT_SIZE]}`);return o.createElement("div",{className:t,style:e.style,role:"progressbar"})}},81261:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>c,handleAccessibleMenuFocus:()=>l,handleAccessibleMenuKeyDown:()=>a,queryMenuElements:()=>h});var o=n(16838),r=n(71468),i=n(68335);const s=[37,39,38,40];function l(e,t){e.target&&o.PLATFORM_ACCESSIBILITY_ENABLED&&e.relatedTarget===t.current&&c(e.target)}function a(e){if(!o.PLATFORM_ACCESSIBILITY_ENABLED)return;if(e.defaultPrevented)return;const t=(0,i.hashFromEvent)(e);if(!s.includes(t))return;const n=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const l=h(e.currentTarget).sort(o.navigationOrderComparator);if(0===l.length)return;const a=document.activeElement.closest('[data-role="menuitem"]');if(!(a instanceof HTMLElement))return;const c=l.indexOf(a);if(-1===c)return;const m=g(a),p=m.indexOf(document.activeElement),f=-1!==p,v=e=>{n&&(0,r.becomeSecondaryElement)(n),(0,r.becomeMainElement)(e),e.focus()};switch(t){case 37:if(!m.length)return;e.preventDefault(),v(0===p?l[c]:f?u(m,p,-1):m[m.length-1]);break;case 39:
if(!m.length)return;e.preventDefault(),p===m.length-1?v(l[c]):v(f?u(m,p,1):m[0]);break;case 38:{e.preventDefault();const t=u(l,c,-1);if(f){const e=d(t,p);v(e||t);break}v(t);break}case 40:{e.preventDefault();const t=u(l,c,1);if(f){const e=d(t,p);v(e||t);break}v(t)}}}function c(e){const[t]=h(e);t&&((0,r.becomeMainElement)(t),t.focus())}function u(e,t,n){return e[(t+e.length+n)%e.length]}function d(e,t){const n=g(e);return n.length?n[(t+n.length)%n.length]:null}function h(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled], [aria-disabled])')).filter((0,o.createScopedVisibleElementFilter)(e))}function g(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled], [aria-disabled])")).filter((0,o.createScopedVisibleElementFilter)(e))}},36898:(e,t,n)=>{"use strict";n.d(t,{useMouseClickAutoBlur:()=>s});var o=n(50959),r=n(76460),i=n(16838);function s(e,t=!0){(0,o.useEffect)((()=>{if(!i.PLATFORM_ACCESSIBILITY_ENABLED||!t)return;const n=t=>{const n=e.current;null!==n&&document.activeElement instanceof HTMLElement&&((0,r.isKeyboardClick)(t)||n.contains(document.activeElement)&&"INPUT"!==document.activeElement.tagName&&document.activeElement.blur())};return window.addEventListener("click",n,!0),()=>window.removeEventListener("click",n,!0)}),[t])}},48889:(e,t,n)=>{"use strict";n.d(t,{ToolbarIconButton:()=>l});var o=n(50959),r=n(50238),i=n(16838),s=n(50813);const l=(0,o.forwardRef)((function(e,t){const{tooltip:n,...l}=e,[a,c]=(0,r.useRovingTabindexElement)(t);return o.createElement(s.ToolWidgetIconButton,{"aria-label":i.PLATFORM_ACCESSIBILITY_ENABLED?n:void 0,...l,tag:i.PLATFORM_ACCESSIBILITY_ENABLED?"button":"div",ref:a,tabIndex:c,"data-tooltip":n})}))},50298:(e,t,n)=>{"use strict";n.d(t,{ToolbarMenuButton:()=>u});var o=n(50959),r=n(39416),i=n(8087),s=n(50238),l=n(16838),a=n(36898),c=n(81261);const u=(0,o.forwardRef)((function(e,t){const{tooltip:n,menuReference:u=null,...d}=e,[h,g]=(0,s.useRovingTabindexElement)(null),m=(0,r.useFunctionalRefObject)(u);return(0,a.useMouseClickAutoBlur)(m),o.createElement(i.ToolWidgetMenu,{"aria-label":l.PLATFORM_ACCESSIBILITY_ENABLED?n:void 0,...d,ref:t,tag:l.PLATFORM_ACCESSIBILITY_ENABLED?"button":"div",reference:h,tabIndex:g,"data-tooltip":n,menuReference:m,onMenuKeyDown:c.handleAccessibleMenuKeyDown,onMenuFocus:e=>(0,c.handleAccessibleMenuFocus)(e,h)})}))},54079:(e,t,n)=>{"use strict";n.d(t,{Toolbar:()=>d});var o=n(50959),r=n(50151),i=n(47201),s=n(3343),l=n(16838),a=n(71468),c=n(39416),u=n(36898);const d=(0,o.forwardRef)((function(e,t){const{onKeyDown:n,orientation:d,blurOnEscKeydown:h=!0,blurOnClick:g=!0,...m}=e,p=l.PLATFORM_ACCESSIBILITY_ENABLED?{role:"toolbar","aria-orientation":d}:{},f=(0,c.useFunctionalRefObject)(t);return(0,o.useLayoutEffect)((()=>{if(!l.PLATFORM_ACCESSIBILITY_ENABLED)return;const e=(0,r.ensureNotNull)(f.current),t=()=>{const t=(0,l.queryTabbableElements)(e).sort(l.navigationOrderComparator);if(0===t.length){const[t]=(0,l.queryFocusableElements)(e).sort(l.navigationOrderComparator);if(void 0===t)return;(0,a.becomeMainElement)(t)}
if(t.length>1){const[,...e]=t;for(const t of e)(0,a.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",t),()=>window.removeEventListener("keyboard-navigation-activation",t)}),[]),(0,u.useMouseClickAutoBlur)(f,g),o.createElement("div",{...m,...p,ref:f,onKeyDown:(0,i.createSafeMulticastEventHandler)((function(e){if(!l.PLATFORM_ACCESSIBILITY_ENABLED)return;if(e.defaultPrevented)return;if(!(document.activeElement instanceof HTMLElement))return;const t=(0,s.hashFromEvent)(e);if(27===t)return e.preventDefault(),void document.activeElement.blur();if("vertical"!==d&&37!==t&&39!==t)return;if("vertical"===d&&38!==t&&40!==t)return;const n=e.currentTarget,o=(0,l.queryFocusableElements)(n).sort(l.navigationOrderComparator);if(0===o.length)return;const r=o.indexOf(document.activeElement);if(-1===r)return;e.preventDefault();const i=()=>{const e=(r+o.length-1)%o.length;(0,a.becomeSecondaryElement)(o[r]),(0,a.becomeMainElement)(o[e]),o[e].focus()},c=()=>{const e=(r+o.length+1)%o.length;(0,a.becomeSecondaryElement)(o[r]),(0,a.becomeMainElement)(o[e]),o[e].focus()};switch(t){case 37:"vertical"!==d&&i();break;case 39:"vertical"!==d&&c();break;case 38:"vertical"===d&&i();break;case 40:"vertical"===d&&c()}}),n)})}))},29540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72"><path fill="currentColor" d="M15 24a21 21 0 1 1 42 0v7.41l8.97 5.01 1.08.6-.82.94-7.77 8.82 2.34 2.53-1.47 1.36L57 48.15V69H46v-7h-6v5h-9V56h-6v13H15V48.15l-2.33 2.52-1.47-1.36 2.35-2.53-7.78-8.82-.82-.93 1.08-.6L15 31.4V24Zm0 9.7-6.9 3.87L15 45.4V33.7Zm42 11.7 6.91-7.83-6.9-3.87v11.7ZM36 5a19 19 0 0 0-19 19v43h6V54h10v11h5v-5h10v7h7V24A19 19 0 0 0 36 5Zm-5 19.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM42.5 26a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/></svg>'},36296:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},33055:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M17.086 6.207a2 2 0 0 1 2.828 0l1.879 1.879a2 2 0 0 1 0 2.828l-.94.94-9 9-1 1-.146.146H6v-4.707l.146-.146 1-1 9-9 .94-.94zm2.121.707a1 1 0 0 0-1.414 0l-.586.586 1.647 1.646 1.646 1.647.586-.586a1 1 0 0 0 0-1.414l-1.879-1.879zm.586 4.586L18.5 10.207 10.207 18.5l1.293 1.293 8.293-8.293zm-9 9l-1.647-1.646L7.5 17.207l-.5.5V21h3.293l.5-.5zm-2.586-4L9.5 17.793 17.793 9.5 16.5 8.207 8.207 16.5z"/></svg>'},69533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},57674:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 11.5v8a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1v-8m-17 0v-4a1 1 0 0 1 1-1h4l2 2h9a1 1 0 0 1 1 1v2m-17 0h17"/></svg>'},80465:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M5.5 6C4.67 6 4 6.67 4 7.5V20.5c0 .83.67 1.5 1.5 1.5H16v-1H5.5a.5.5 0 0 1-.5-.5V12h16v1h1V9.5c0-.83-.67-1.5-1.5-1.5h-8.8L9.86 6.15 9.71 6H5.5zM21 11H5V7.5c0-.28.22-.5.5-.5h3.8l1.85 1.85.14.15h9.21c.28 0 .5.22.5.5V11zm1 11v-3h3v-1h-3v-3h-1v3h-3v1h3v3h1z"/></svg>'},94007:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M2.448 10.124a10.82 10.82 0 0 1-.336-.609L2.105 9.5l.007-.015a12.159 12.159 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373c2.297 0 4.047 1.292 5.25 2.646a12.166 12.166 0 0 1 1.687 2.466l.007.015-.007.015a12.163 12.163 0 0 1-1.686 2.466c-1.204 1.354-2.954 2.646-5.251 2.646-2.298 0-4.048-1.292-5.252-2.646a12.16 12.16 0 0 1-1.35-1.857zm14.558-.827l-.456.203.456.203v.002l-.003.005-.006.015-.025.052a11.813 11.813 0 0 1-.461.857 13.163 13.163 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982-2.703 0-4.703-1.522-6-2.982a13.162 13.162 0 0 1-1.83-2.677 7.883 7.883 0 0 1-.118-.243l-.007-.015-.002-.005v-.001l.456-.204-.456-.203v-.002l.002-.005.007-.015a4.66 4.66 0 0 1 .119-.243 13.158 13.158 0 0 1 1.83-2.677c1.296-1.46 3.296-2.982 5.999-2.982 2.702 0 4.702 1.522 5.998 2.981a13.158 13.158 0 0 1 1.83 2.678 8.097 8.097 0 0 1 .119.243l.006.015.003.005v.001zm-.456.203l.456-.203.09.203-.09.203-.456-.203zM1.092 9.297l.457.203-.457.203-.09-.203.09-.203zm9.958.203c0 1.164-.917 2.07-2 2.07-1.084 0-2-.906-2-2.07 0-1.164.916-2.07 2-2.07 1.083 0 2 .906 2 2.07zm1 0c0 1.695-1.344 3.07-3 3.07-1.657 0-3-1.375-3-3.07 0-1.695 1.343-3.07 3-3.07 1.656 0 3 1.375 3 3.07z"/></svg>'},52870:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7 5.5a2.5 2.5 0 0 1 5 0V7H7V5.5zM6 7V5.5a3.5 3.5 0 1 1 7 0V7a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2zm8 2a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9zm-3 2.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},74059:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M21.106 12.5H6.894a.5.5 0 0 1-.318-.886L14 5.5l7.424 6.114a.5.5 0 0 1-.318.886zM21.106 16.5H6.894a.5.5 0 0 0-.318.886L14 23.5l7.424-6.114a.5.5 0 0 0-.318-.886z"/></svg>'},91730:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4.605 14.089A10.052 10.052 0 0 1 4.56 14l.046-.089a17.18 17.18 0 0 1 2.329-3.327C8.58 8.758 10.954 7 14 7c3.046 0 5.421 1.757 7.066 3.585A17.18 17.18 0 0 1 23.44 14l-.046.089a17.18 17.18 0 0 1-2.329 3.327C19.42 19.242 17.046 21 14 21c-3.046 0-5.421-1.757-7.066-3.584a17.18 17.18 0 0 1-2.329-3.327zm19.848-.3L24 14l.453.212-.001.002-.003.005-.009.02a16.32 16.32 0 0 1-.662 1.195c-.44.72-1.1 1.684-1.969 2.65C20.08 20.008 17.454 22 14 22c-3.454 0-6.079-1.993-7.81-3.916a18.185 18.185 0 0 1-2.469-3.528 10.636 10.636 0 0 1-.161-.318l-.01-.019-.002-.005v-.002L4 14a55.06 55.06 0 0 1-.453-.212l.001-.002.003-.005.009-.02.033-.067a16.293 16.293 0 0 1 .629-1.126c.44-.723 1.1-1.686 1.969-2.652C7.92 7.993 10.546 6 14 6c3.454 0 6.079 1.993 7.81 3.916a18.183 18.183 0 0 1 2.469 3.528 10.588 10.588 0 0 1 .161.318l.01.019.002.005v.002zM24 14l.453-.211.099.211-.099.211L24 14zm-20.453-.211L4 14l-.453.211L3.448 14l.099-.211zM11 14a3 3 0 1 1 6 0 3 3 0 0 1-6 0zm3-4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm0 5a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/></svg>'},7295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 12.5l4.59-4.59a2 2 0 0 1 2.83 0l3.17 3.17a2 2 0 0 0 2.83 0L22.5 6.5m-8 9.5v5.5M12 19l2.5 2.5L17 19m4.5 3v-5.5M19 19l2.5-2.5L24 19"/></svg>'},28824:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},49756:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M11.5 4A2.5 2.5 0 0 0 7 5.5V7h6a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2V5.5a3.5 3.5 0 0 1 6.231-2.19c-.231.19-.73.69-.73.69zM13 8H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1zm-2 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},62766:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.692 3.012l-12 12.277.715.699 12-12.277-.715-.699zM9.05 15.627a7.042 7.042 0 0 1-3.144-.741l.742-.76c.72.311 1.52.5 2.402.5 2.297 0 4.047-1.29 5.25-2.645a12.168 12.168 0 0 0 1.687-2.466l.007-.015-.007-.015A12.166 12.166 0 0 0 14.3 7.019c-.11-.124-.225-.247-.344-.37l.699-.715c.137.14.268.28.392.42a13.16 13.16 0 0 1 1.83 2.678 8.117 8.117 0 0 1 .119.243l.006.015.003.005v.001l-.456.204.456.203v.002l-.003.005-.006.015-.025.052a11.762 11.762 0 0 1-.461.857 13.158 13.158 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982zm7.5-6.127l.456-.203.09.203-.09.203-.456-.203zm-7.5 3.07c-.27 0-.53-.037-.778-.105l.879-.899c.999-.052 1.833-.872 1.895-1.938l.902-.923c.**************.102.795 0 1.695-1.344 3.07-3 3.07zM6.15 10.294l.902-.923c.063-1.066.896-1.886 1.895-1.938l.879-.9a2.94 2.94 0 0 0-.777-.103c-1.657 0-3 1.374-3 3.069 0 .275.035.541.101.795zM9.05 4.373c.88 0 1.68.19 2.4.5l.743-.759a7.043 7.043 0 0 0-3.143-.74c-2.703 0-4.703 1.521-6 2.98a13.159 13.159 0 0 0-1.83 2.678 7.886 7.886 0 0 0-.118.243l-.007.015-.002.005v.001l.456.204-.457-.203-.09.203.09.203.457-.203-.456.203v.002l.002.005.007.015a4.5 4.5 0 0 0 .119.243 13.152 13.152 0 0 0 1.83 2.677c.124.14.255.28.392.42l.7-.715c-.12-.122-.235-.245-.345-.369a12.156 12.156 0 0 1-1.686-2.466L2.105 9.5l.007-.015a12.158 12.158 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373z"/></svg>'},98129:e=>{e.exports={ar:["استنساخ ، نسخ"],ca_ES:["Clona, Copia"],cs:"Clone, Copy",de:["Klonen, Kopieren"],el:"Clone, Copy",en:"Clone, Copy",es:["Clonar, Copiar"],fa:"Clone, Copy",fr:["Cloner, Copier"],he_IL:["שכפל, העתק"],hu_HU:"Clone, Copy",id_ID:["Duplikat, Salin"],it:["Clona, copia"],ja:["複製、コピー"],ko:["클론, 카피"],ms_MY:["Klon, Salin"],nl_NL:"Clone, Copy",pl:["Klonuj, Kopiuj"],pt:["Clonar, Copiar"],ro:"Clone, Copy",ru:["Клонировать, копировать"],sv:["Klon, kopiera"],th:["โคลน, ก๊อปปี้"],tr:["Klonla, Kopyala"],vi:["Nhân bản, Sao chép"],zh:["克隆，复制"],zh_TW:["克隆，複製"]}},91073:e=>{e.exports={ar:["أنشئ مجموعة من الرسومات"],ca_ES:["Creeu un grup de gràfics"],cs:"Create a group of drawings",de:["Erstellen Sie eine Gruppe von Zeichnungen"],el:"Create a group of drawings",en:"Create a group of drawings",es:["Cree un grupo de graficos"],fa:"Create a group of drawings",fr:["Créer un groupe de dessins"],he_IL:["צור קבוצת שרטוטים"],hu_HU:"Create a group of drawings",id_ID:["Buat kelompok untuk gambar"],it:["Crea un gruppo di disegni"],ja:["描画のグループを作成"],ko:["드로잉 그룹 만들기"],ms_MY:["Cipta kumpulan untuk lukisan"],nl_NL:"Create a group of drawings",pl:["Utwórz grupę obiektów rysowania"],pt:["Criar um grupo de desenhos"],ro:"Create a group of drawings",ru:["Создать группу объектов рисования"],sv:["Skapa en grup ritningar"],th:["สร้างกรุ๊ปของการวาด"],tr:["Bir grup çizim oluşturun"],vi:["Tạo nhóm các hình vẽ"],zh:["建立一组绘图"],zh_TW:["建立一組繪圖"]}},38207:e=>{e.exports={ar:["المجموعة مخفية في الفاصل الزمني الحالي"],ca_ES:["El grup està ocult a l'interval actual"],
cs:"Group is hidden on current interval",de:["Gruppe ist im aktuellen Intervall verborgen"],el:"Group is hidden on current interval",en:"Group is hidden on current interval",es:["El grupo está oculto en el intervalo actual"],fa:"Group is hidden on current interval",fr:["Le groupe est caché sur l'intervalle actuel"],he_IL:["הקבוצה מוסתרת באינטרוול הנוכחי"],hu_HU:"Group is hidden on current interval",id_ID:["Grup disembunyikan pada interval saat ini"],it:["Il gruppo è nascosto nel timeframe corrente"],ja:["グループは現在の時間足で非表示です"],ko:["현재 인터벌에 대해 그룹이 감춰져 있습니다"],ms_MY:["Kumpulan disembunyikan pada selang masa sekarang"],nl_NL:"Group is hidden on current interval",pl:["Grupa jest ukryta na bieżącym interwale"],pt:["O grupo está oculto no intervalo atual"],ro:"Group is hidden on current interval",ru:["Группа скрыта на текущем интервале"],sv:["Gruppen är dold i det aktuella intervallet"],th:["กรุ๊ปถูกซ่อนบนช่วงเวลาปัจจุบัน"],tr:["Grup şu anki aralıkta gizli"],vi:["Nhóm được ẩn trong chế độ hiện tại"],zh:["该组在当前时间间隔内隐藏"],zh_TW:["該組隱藏在當前的時間間隔內"]}},39781:e=>{e.exports={ar:["الرسم مخفي على الفاصل الزمني الحالي"],ca_ES:["El dibuix està ocult a l'interval actual"],cs:"Drawing is hidden on current interval",de:["Zeichnung ist für das aktuelle Intervall ausgeblendet"],el:"Drawing is hidden on current interval",en:"Drawing is hidden on current interval",es:["El dibujo está oculto en el intervalo actual"],fa:"Drawing is hidden on current interval",fr:["Le dessin est caché sur l'intervalle actuel"],he_IL:["הציור מוסתר באינטרוול הנוכחי"],hu_HU:"Drawing is hidden on current interval",id_ID:["Gambar disembunyikan pada interval saat ini"],it:["Il disegno è nascosto sul timeframe corrente"],ja:["描画は現在の時間足で非表示です"],ko:["커런트 인터벌에서는 드로잉이 숨겨져 있습니다"],ms_MY:["Lukisan disembunyikan pada selang masa terkini"],nl_NL:"Drawing is hidden on current interval",pl:["Rysunek jest ukryty na bieżącym interwale"],pt:["O desenho está oculto no intervalo atual"],ro:"Drawing is hidden on current interval",ru:["Объект рисования скрыт на этом интервале"],sv:["Ritning är dold på aktuellt intervall"],th:["การวาดถูกซ่อนไว้สำหรับช่วงเวลาปัจจุบัน"],tr:["Çizim, geçerli aralıkta gizlendi"],vi:["Bản vẽ bị ẩn trong khoảng thời gian hiện tại"],zh:["在当前时间周期内隐藏绘图"],zh_TW:["在當前時間周期內隱藏繪圖"]}},31095:e=>{e.exports={ar:["شجرة الكائنات"],ca_ES:["Arbre d'objectes"],cs:"Object tree",de:["Objektbaum"],el:"Object tree",en:"Object tree",es:["Árbol de objetos"],fa:"Object tree",fr:["Arborescence des objets"],he_IL:["אובייקט עץ"],hu_HU:"Object tree",id_ID:["Pohon objek"],it:["Albero oggetti"],ja:["オブジェクトツリー"],ko:["오브젝트 트리"],ms_MY:["Salasilah Objek"],nl_NL:"Object tree",pl:["Drzewo obiektów"],pt:["Árvore de objetos"],ro:"Object tree",ru:["Дерево объектов"],sv:["Objektträd"],th:["แผนผังวัตถุ"],tr:["Nesne ağacı"],vi:["Danh sách đối tượng"],zh:["对象树"],zh_TW:["物件樹"]}},74860:e=>{e.exports={ar:["رسومات محلية"],ca_ES:["Dibuixos locals"],cs:"Local drawings",de:["Lokale Zeichnungen"],el:"Local drawings",en:"Local drawings",es:["Dibujos locales"],fa:"Local drawings",fr:["Dessins locaux"],
he_IL:["שרטוטים מקומיים"],hu_HU:"Local drawings",id_ID:["Gambar lokal"],it:["Disegni in loco"],ja:["ローカルの描画"],ko:["로컬 드로잉"],ms_MY:["Lukisan tempatan"],nl_NL:"Local drawings",pl:["Rysunki lokalne"],pt:["Desenhos locais"],ro:"Local drawings",ru:["На выбранном графике"],sv:["Lokala ritningar"],th:["ภาพวาดท้องถิ่น"],tr:["Lokal çizimler"],vi:["Bản vẽ cục bộ"],zh:["本地绘图"],zh_TW:["當地繪圖"]}},72357:e=>{e.exports={ar:["إدارة رسومات التنسيق"],ca_ES:["Gestiona els dibuixos dels dissenys"],cs:"Manage layout drawings",de:["Zeichnungen des Layouts verwalten"],el:"Manage layout drawings",en:"Manage layout drawings",es:["Gestionar los dibujos de los diseños"],fa:"Manage layout drawings",fr:["Gérer les dessins de mise en page"],he_IL:["נהל שרטוטי פריסה"],hu_HU:"Manage layout drawings",id_ID:["Kelola layout gambar"],it:["Gestisci disegni del layout"],ja:["レイアウトの描画を管理"],ko:["레이아웃 드로잉 관리"],ms_MY:["Urus susun atur lukisan"],nl_NL:"Manage layout drawings",pl:["Zarządzaj rysunkami układu"],pt:["Administre seu layout de desenhos"],ro:"Manage layout drawings",ru:["Настройки объектов рисования графика"],sv:["Hantera layoutritningar"],th:["จัดการการวาดเลย์เอาท์"],tr:["Yerleşim çizimlerini yönet"],vi:["Quản lý bố cục hình vẽ"],zh:["管理布局绘图"],zh_TW:["管理版面繪圖"]}},18570:e=>{e.exports={ar:["لا يوجد رسوم حتى الآن"],ca_ES:["Encara no hi ha dibuixos"],cs:["Zatím Žádné Nákresy"],de:["Noch keine Zeichnungen"],el:["Δεν υπάρχουν ακομα σχέδια"],en:"No drawings yet",es:["No hay dibujos todavía"],fa:["شکلی رسم نشده است"],fr:["Pas de Dessins pour le moment"],he_IL:["אין שרטוט עדיין"],hu_HU:["Nincs még rajz"],id_ID:["Belum ada gambar saat ini"],it:["Nessun disegno disponibile"],ja:["未描画"],ko:["그림없음"],ms_MY:["Masih belum ada lukisan"],nl_NL:["Nog geen tekeningen"],pl:["Brak rysunków"],pt:["Ainda sem desenhos"],ro:"No drawings yet",ru:["Нет инструментов рисования"],sv:["Inga ritningar än"],th:["ยังไม่มีรูปวาด"],tr:["Henüz çizim yok"],vi:["Chưa có bản vẽ nào"],zh:["尚未绘图"],zh_TW:["尚無任何繪圖"]}},87871:e=>{e.exports={ar:["متزامن كليًا"],ca_ES:["Sincronitzat globalment"],cs:"Synced globally",de:["Global synchronisiert"],el:"Synced globally",en:"Synced globally",es:["Sincronizado globalmente"],fa:"Synced globally",fr:["Synchronisé globalement"],he_IL:["מסונכרן ברחבי העולם"],hu_HU:"Synced globally",id_ID:["Disinkronisasi secara global"],it:["Sincronizzazione globale"],ja:["グローバルに同期"],ko:["전체 싱크"],ms_MY:["Disegerakkan secara sejagat"],nl_NL:"Synced globally",pl:["Zsynchronizowane globalnie"],pt:["Sincronizado em tudo"],ro:"Synced globally",ru:["Синхр. везде"],sv:["Synkad globalt"],th:["ซิงค์ทั่วโลก"],tr:["Küresel senkronize"],vi:["Đã đồng bộ hóa trên toàn cầu"],zh:["全局同步"],zh_TW:["全球同步"]}},10538:e=>{e.exports={ar:["تمت المزامنة في التنسيق"],ca_ES:["Sincronitzat a la plantilla"],cs:"Synced in layout",de:["Im Layout synchronisiert"],el:"Synced in layout",en:"Synced in layout",es:["Sincronizado en la plantilla"],fa:"Synced in layout",fr:["Synchronisé dans la mise en page"],he_IL:["מסונכרן בפריסה"],hu_HU:"Synced in layout",id_ID:["Disinkronisasi dalam layout"],
it:["Sincronizzazione su layout"],ja:["レイアウト内で同期"],ko:["레이아웃 싱크"],ms_MY:["Disegerakkan di dalam susun atur"],nl_NL:"Synced in layout",pl:["Zsynchronizowane w układzie"],pt:["Sincronizado no layout"],ro:"Synced in layout",ru:["Синхр. на всех графиках"],sv:["Synkad i layout"],th:["ซิงค์ในเลย์เอาต์"],tr:["Düzende senkronize"],vi:["Đã đồng bộ hóa trong bố cục"],zh:["在布局内同步"],zh_TW:["版面同步"]}},85128:e=>{e.exports={ar:["إزالة كافة الرسومات لهذا الرمز"],ca_ES:["Elimina tots els dibuixos per a aquest símbol"],cs:"Remove all drawings for this symbol",de:["Alle Zeichnungen für dieses Symbol entfernen"],el:"Remove all drawings for this symbol",en:"Remove all drawings for this symbol",es:["Eliminar todos los dibujos para este símbolo"],fa:"Remove all drawings for this symbol",fr:["Supprimer tous les dessins pour ce symbole"],he_IL:["הסר את כל השרטוטים לסימול זה"],hu_HU:"Remove all drawings for this symbol",id_ID:["Hilangkan seluruh gambar pada simbol ini"],it:["Rimuove tutti i disegni su questo simbolo"],ja:["このシンボルのすべての描画を削除"],ko:["이 심볼에 대한 모든 드로잉 없애기"],ms_MY:["Buang semua lukisan untuk simbol ini"],nl_NL:"Remove all drawings for this symbol",pl:["Usuń wszystkie obiekty rysowania dla tego symbolu"],pt:["Remover todos os desenhos deste símbolo"],ro:"Remove all drawings for this symbol",ru:["Удалить все объекты рисования для этого символа"],sv:["Ta bort alla ritningar för denna symbol"],th:["ลบการวาดทั้งหมดของตัวย่อนี้"],tr:["Bu sembol için tüm çizimleri kaldır"],vi:["Loại bỏ tất cả nét vẽ cho mã này"],zh:["移除该商品代码的所有绘图"],zh_TW:["刪除此商品代碼的所有繪圖"]}},35038:e=>{e.exports={ar:["تغيير الأسم"],ca_ES:["Reanomenar"],cs:"Rename",de:["Umbenennen"],el:"Rename",en:"Rename",es:["Renombrar."],fa:"Rename",fr:["Renommer"],he_IL:["שנה שם"],hu_HU:["Átnevezés"],id_ID:["Mengganti Nama"],it:["Rinomina"],ja:["名前の変更"],ko:["이름 바꾸기"],ms_MY:["Namakan semula"],nl_NL:"Rename",pl:["Zmień nazwę"],pt:["Renomear"],ro:"Rename",ru:["Переименовать"],sv:["Döp om"],th:["เปลี่ยนชื่อ"],tr:["Yeni Ad Ver"],vi:["Đổi tên"],zh:["重命名"],zh_TW:["重新命名"]}},40001:e=>{e.exports={ar:["غيّر عنوان {sourceTitle} إلى {newSourceTitle}"],ca_ES:["canvia el títol {sourceTitle} per {newSourceTitle}"],cs:"change {sourceTitle} title to {newSourceTitle}",de:["{sourceTitle} zu {newSourceTitle} ändern"],el:"change {sourceTitle} title to {newSourceTitle}",en:"change {sourceTitle} title to {newSourceTitle}",es:["cambiar el título {sourceTitle} por {newSourceTitle}"],fa:"change {sourceTitle} title to {newSourceTitle}",fr:["Remplacer le titre {sourceTitle} par {newSourceTitle}"],he_IL:["שנה את {sourceTitle} כותרת ל- {newSourceTitle}"],hu_HU:"change {sourceTitle} title to {newSourceTitle}",id_ID:["Ubah judul {sourceTitle} menjadi {newSourceTitle}"],it:["Cambia titolo da {sourceTitle} a {newSourceTitle}"],ja:["{sourceTitle}のタイトルを{newSourceTitle}に変更"],ko:["{sourceTitle} 타이틀을 {newSourceTitle} 으로 바꾸기"],ms_MY:["Tukar tajuk {sourceTitle} kepada {newSourceTitle}"],nl_NL:"change {sourceTitle} title to {newSourceTitle}",pl:["Zmień tytuł {sourceTitle} na {newSourceTitle}."],
pt:["Mudar {sourceTitle} título para {newSourceTitle}"],ro:["Change {sourceTitle} title to {newSourceTitle}"],ru:["изменение названия {sourceTitle} на {newSourceTitle}"],sv:["Ändra {sourceTitle} titel till {newSourceTitle}"],th:["เปลี่ยนชื่อ {sourceTitle} ไปเป็น {newSourceTitle}"],tr:["{sourceTitle} başlığını {newSourceTitle} olarak değiştirin"],vi:["Thay đổi {sourceTitle} tiêu đề sang {newSourceTitle}"],zh:["将{sourceTitle}标题更改为{newSourceTitle}"],zh_TW:["將{sourceTitle}標題更改為{newSourceTitle}"]}},28506:e=>{e.exports={ar:["إخفاء العناصر"],ca_ES:["amaga objectes"],cs:"hide objects",de:["Objekte ausblenden"],el:"hide objects",en:"hide objects",es:["ocultar objetos"],fa:"hide objects",fr:["masquer les objets"],he_IL:["הסתר אובייקטים"],hu_HU:"hide objects",id_ID:["sembunyikan objek"],it:["nascondi oggetti"],ja:["オブジェクトの非表示"],ko:["오브젝트 숨기기"],ms_MY:["sembunyi objek"],nl_NL:"hide objects",pl:["ukryj obiekty"],pt:["ocultar objetos"],ro:"hide objects",ru:["скрытие объектов"],sv:["dölj objekt"],th:["ซ่อนออบเจ็กต์"],tr:["nesneleri gizle"],vi:["ẩn đối tượng"],zh:["隐藏对象"],zh_TW:["隱藏物件"]}},8343:e=>{e.exports={ar:["أدخل المصدر (المصادر) بعد ذلك"],ca_ES:["introdueix font(s) després"],cs:"insert source(s) after",de:["Quelle(n) einfügen nach"],el:"insert source(s) after",en:"insert source(s) after",es:["introducir fuente(s) después"],fa:"insert source(s) after",fr:["insérer la/les source(s) après"],he_IL:["הכנס מקור(ות) לאחר מכן"],hu_HU:"insert source(s) after",id_ID:["masukkan sumber setelah"],it:["inserimento fonti"],ja:["後にソースを挿入"],ko:["~뒤에 소스 넣기"],ms_MY:["masukkan sumber(s) selepas"],nl_NL:"insert source(s) after",pl:["wstaw źródła po"],pt:["inserir fonte(s) depois"],ro:"insert source(s) after",ru:["вставку объекта(ов) после"],sv:["infoga källa(källor) efter"],th:["แทรกแหล่งที่มาหลังจาก"],tr:["kaynağ(ı) ardına ekle"],vi:["chèn (các) nguồn sau đó"],zh:["插入源到后面"],zh_TW:["插入源到後面"]}},40566:e=>{e.exports={ar:["تحريك العناصر"],ca_ES:["mou objectes"],cs:"move objects",de:["Objekte Bewegen"],el:"move objects",en:"move objects",es:["mover objetos"],fa:"move objects",fr:["Déplacer les objets"],he_IL:["הזז אובייקטים"],hu_HU:"move objects",id_ID:["Pindahkan objek"],it:["Sposta oggetti"],ja:["オブジェクトを移動"],ko:["오브젝트 옮기기"],ms_MY:["Pindahkan objek"],nl_NL:"move objects",pl:["Przenieś obiekty"],pt:["Mover objetos"],ro:["Move objects"],ru:["перемещение объектов"],sv:["Flytta objekt"],th:["ย้ายวัตถุ"],tr:["nesneleri taşı"],vi:["Chuyển đối tượng"],zh:["移动对象"],zh_TW:["移動物件"]}},87358:e=>{e.exports={ar:["عرض ‎{title}‎"],ca_ES:["mostra {title}"],cs:"show {title}",de:["{title} anzeigen"],el:"show {title}",en:"show {title}",es:["mostrar {title}"],fa:"show {title}",fr:["afficher {title}"],he_IL:["הצג ‎{title}‎"],hu_HU:"show {title}",id_ID:["tampilkan {title}"],it:["mostra {title}"],ja:["{title}の表示"],ko:["{title} 보이기"],ms_MY:["tunjuk {title}"],nl_NL:"show {title}",pl:["pokaż {title}"],pt:["exibir {title}"],ro:"show {title}",ru:["отображение: {title}"],sv:["visa {title}"],th:["แสดง {title}"],tr:["{title} göster"],vi:["hiện {title}"],zh:["显示{title}"],
zh_TW:["顯示{title}"]}},63549:e=>{e.exports={ar:["إظهار العناصر"],ca_ES:["mostra objectes"],cs:"show objects",de:["Objekte einblenden"],el:"show objects",en:"show objects",es:["mostrar objetos"],fa:"show objects",fr:["afficher les objets"],he_IL:["הצג אובייקטים"],hu_HU:"show objects",id_ID:["tampilkan objek"],it:["mostra oggetti"],ja:["オブジェクトの表示"],ko:["오브젝트 보이기"],ms_MY:["tunjuk objek"],nl_NL:"show objects",pl:["pokaż obiekty"],pt:["exibir objetos"],ro:"show objects",ru:["отображение объектов"],sv:["visa objekt"],th:["แสดงออบเจ็กต์"],tr:["nesneleri göster"],vi:["hiển thị đối tượng"],zh:["显示对象"],zh_TW:["顯示物件"]}},57428:e=>{e.exports={ar:["إزالة العناصر"],ca_ES:["elimina objectes"],cs:"remove objects",de:["Objekte entfernen"],el:"remove objects",en:"remove objects",es:["eliminar objetos"],fa:"remove objects",fr:["supprimer les objets"],he_IL:["הסר אובייקטים"],hu_HU:"remove objects",id_ID:["Hilangkan objek"],it:["rimuovi oggetti"],ja:["オブジェクトの削除"],ko:["오브젝트 없애기"],ms_MY:["buang objek"],nl_NL:"remove objects",pl:["usuń obiekty"],pt:["remover objetos"],ro:"remove objects",ru:["удаление объектов"],sv:["Ta bort objekt"],th:["ลบออบเจ็กต์"],tr:["nesneleri kaldır"],vi:["di chuyển đối tượng"],zh:["移除对象"],zh_TW:["移除物件"]}},23481:e=>{e.exports={ar:["حذف كافة خطوط الأدوات لـ {symbol}"],ca_ES:["elimina totes les eines de línies per a {symbol}"],cs:"remove all line tools for {symbol}",de:["alle Linienwerkzeuge für {symbol} entfernen"],el:"remove all line tools for {symbol}",en:"remove all line tools for {symbol}",es:["eliminar todas las herramientas de líneas para {symbol}"],fa:"remove all line tools for {symbol}",fr:["supprimer tous les outils de ligne pour {symbol}."],he_IL:["הסרת כל קבוצת כלי קו ‎{symbol}‎"],hu_HU:"remove all line tools for {symbol}",id_ID:["Hilangkan semua peralatan garis untuk {symbol}"],it:["rimuovi tutte le linee da {symbol}"],ja:["{symbol}のすべてのラインツールの削除"],ko:["{symbol}의 모든 줄 도구 제거"],ms_MY:["buang semua alat garisan untuk {symbol}"],nl_NL:"remove all line tools for {symbol}",pl:["usuń wszystkie narzędzia linii dla {symbol}"],pt:["remover todas as ferramentas de linhas para {symbol}"],ro:"remove all line tools for {symbol}",ru:["удаление всех объектов рисования для {symbol}"],sv:["Ta bort alla linjeverktyg för {symbol}"],th:["ลบเครื่องมือเส้นทั้งหมดสำหรับ {symbol}"],tr:["{symbol} için tüm çizgi araçlarını kaldır"],vi:["loại bỏ tất cả đường công cụ cho {symbol}"],zh:["移除{symbol}的所有线条工具"],zh_TW:["移除{symbol}的所有線條工具"]}},42743:e=>{e.exports={ar:["مع ‎{drawingsCount}‎ رسم","مع ‎{drawingsCount}‎ رسم","مع ‎{drawingsCount}‎ رسم","مع ‎{drawingsCount}‎ رسوم","مع ‎{drawingsCount}‎ رسماً","مع ‎{drawingsCount}‎ رسماً"],ca_ES:["amb {drawingsCount} dibuix","amb {drawingsCount} dibuixos"],cs:"with {drawingsCount} drawing",de:["mit {drawingsCount} Zeichnung","mit {drawingsCount} Zeichnungen"],el:"with {drawingsCount} drawing",en:"with {drawingsCount} drawing",es:["con {drawingsCount} dibujo","con {drawingsCount} dibujos"],fa:["with {drawingsCount} drawings"],fr:["avec {drawingsCount} dessin","avec {drawingsCount} dessins"],
he_IL:["עם שרטוט ‎{drawingsCount}‎","עם ‎{drawingsCount}‎ שרטוטים","עם ‎{drawingsCount}‎ שרטוטים","עם ‎{drawingsCount}‎ שרטוטים"],hu_HU:["with {drawingsCount} drawings"],id_ID:["dengan {drawingsCount} gambar"],it:["con {drawingsCount} disegno","con {drawingsCount} disegni"],ja:["に{drawingsCount}個の描画"],ko:["{drawingsCount} 드로잉이 있는"],ms_MY:["dengan {drawingsCount} lukisan"],nl_NL:"with {drawingsCount} drawing",pl:["z {drawingsCount} rysunkiem","z {drawingsCount} rysunkami","z {drawingsCount} rysunkami","z {drawingsCount} rysunkami"],pt:["com {drawingsCount} desenho","com {drawingsCount} desenhos"],ro:"with {drawingsCount} drawing",ru:["с {drawingsCount} объектом рисования","с {drawingsCount} объектами рисования","с {drawingsCount} объектами рисования","с {drawingsCount} объектами рисования"],sv:["med {drawingsCount} ritverktyg","med {drawingsCount} ritverktyg"],th:["กับ {drawingsCount} การวาด"],tr:["{drawingsCount} çizim ile","{drawingsCount} çizim ile"],vi:["với {drawingsCount} nét vẽ"],zh:["含{drawingsCount}个绘图"],zh_TW:["含{drawingsCount}個繪圖"]}},88143:e=>{e.exports={ar:["‎{drawingsCount}‎ رسم","‎{drawingsCount}‎ رسم","‎{drawingsCount}‎ رسم","‎{drawingsCount}‎ رسوم","‎{drawingsCount}‎ رسماً","‎{drawingsCount}‎ رسماً"],ca_ES:["{drawingsCount} dibuix","{drawingsCount} dibuixos"],cs:"{drawingsCount} drawing",de:["{drawingsCount} Zeichnung","{drawingsCount} Zeichnungen"],el:"{drawingsCount} drawing",en:"{drawingsCount} drawing",es:["{drawingsCount} dibujo","{drawingsCount} dibujos"],fa:["{drawingsCount} drawings"],fr:["{drawingsCount} dessin","{drawingsCount} dessins"],he_IL:["שרטוט ‎{drawingsCount}‎","‎{drawingsCount}‎ שרטוטים","‎{drawingsCount}‎ שרטוטים","‎{drawingsCount}‎ שרטוטים"],hu_HU:["{drawingsCount} drawings"],id_ID:["{drawingsCount} gambar"],it:["{drawingsCount} disegno","{drawingsCount} disegni"],ja:["{drawingsCount}個の描画"],ko:["{drawingsCount} 드로잉"],ms_MY:["{drawingsCount} lukisan"],nl_NL:"{drawingsCount} drawing",pl:["{drawingsCount} rysunek","{drawingsCount} rysunki","{drawingsCount} rysunków","{drawingsCount} rysunków"],pt:["{drawingsCount} desenho","{drawingsCount} desenhos"],ro:"{drawingsCount} drawing",ru:["{drawingsCount} объект рисования","{drawingsCount} объекта рисования","{drawingsCount} объектов рисования","{drawingsCount} объектов рисования"],sv:["{drawingsCount} ritverktyg","{drawingsCount} ritverktyg"],th:["{drawingsCount} การวาด"],tr:["{drawingsCount} çizimi","{drawingsCount} çizimi"],vi:["{drawingsCount} nét vẽ"],zh:["{drawingsCount}个绘图"],zh_TW:["{drawingsCount}個繪圖"]}},52908:e=>{e.exports={ar:["‎{symbolsCount}‎ رمز","‎{symbolsCount}‎ رمز","‎{symbolsCount}‎ رمز","‎{symbolsCount}‎ رموز","‎{symbolsCount}‎ رمزاً","‎{symbolsCount}‎ رمزاً"],ca_ES:["{symbolsCount} símbol","{symbolsCount} símbols"],cs:"{symbolsCount} symbol",de:["{symbolsCount} Symbol","{symbolsCount} Symbole"],el:"{symbolsCount} symbol",en:"{symbolsCount} symbol",es:["{symbolsCount} símbolo","{symbolsCount} símbolos"],fa:["{symbolsCount} symbols"],fr:["{symbolsCount} symbole","{symbolsCount} symboles"],
he_IL:["סימול ‎{symbolsCount}‎","‎{symbolsCount}‎ סימולים","‎{symbolsCount}‎ סימולים","‎{symbolsCount}‎ סימולים"],hu_HU:["{symbolsCount} symbols"],id_ID:["{symbolsCount} simbol"],it:["{symbolsCount} simbolo","{symbolsCount} simboli"],ja:["{symbolsCount}シンボル"],ko:["{symbolsCount} 심볼"],ms_MY:["Simbol {symbolsCount}"],nl_NL:"{symbolsCount} symbol",pl:"{symbolsCount} symbol",pt:["{symbolsCount} símbolo","{symbolsCount} símbolos"],ro:"{symbolsCount} symbol",ru:["{symbolsCount} символ","{symbolsCount} символа","{symbolsCount} символов","{symbolsCount} символов"],sv:"{symbolsCount} symbol",th:["{symbolsCount} สัญลักษณ์"],tr:["{symbolsCount} sembol","{symbolsCount} sembol"],vi:["{symbolsCount} mã giao dịch"],zh:["{symbolsCount}个商品"],zh_TW:["{symbolsCount}個商品"]}}}]);