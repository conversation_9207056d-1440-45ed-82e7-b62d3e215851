"""
Consolidated Predictions Page - Combines all prediction functionality
"""

import streamlit as st
import pandas as pd
import numpy as np
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

# Import prediction components
from app.components.prediction import prediction_component
from app.components.advanced_prediction import advanced_prediction_component
from app.components.enhanced_prediction import enhanced_prediction_component
from app.components.ensemble_predictions import ensemble_predictions_component

# Import utilities
from app.utils.data_processing import is_model_trained
from app.utils.state_manager import get_available_stock_files
from app.utils.session_state import get_session_value, set_session_value

# Configure logging
logger = logging.getLogger(__name__)

# Helper functions for enhanced error handling and validation
def validate_prediction_requirements() -> List[str]:
    """Validate all requirements for making predictions"""
    errors = []

    # Check historical data
    if not hasattr(st.session_state, 'historical_data') or st.session_state.historical_data is None:
        errors.append("Historical data is required")
    elif st.session_state.historical_data.empty:
        errors.append("Historical data is empty")
    elif len(st.session_state.historical_data) < 60:
        errors.append("Insufficient historical data (minimum 60 records required)")

    # Check symbol
    if not hasattr(st.session_state, 'symbol') or not st.session_state.symbol:
        errors.append("Stock symbol is required")

    # Check data quality
    if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in st.session_state.historical_data.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {', '.join(missing_columns)}")

    return errors

def display_validation_errors(errors: List[str]):
    """Display validation errors with helpful guidance"""
    st.error("⚠️ **Cannot proceed with predictions due to the following issues:**")

    for i, error in enumerate(errors, 1):
        st.markdown(f"{i}. {error}")

    st.markdown("---")
    st.markdown("### 🔧 **Quick Solutions:**")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("📤 Upload Data", type="primary", use_container_width=True):
            st.session_state.page = "Upload Data"
            st.rerun()
    with col2:
        if st.button("📊 Select Stock", use_container_width=True):
            st.session_state.page = "Stock Management"
            st.rerun()

def display_stock_info(symbol: str):
    """Display enhanced stock information"""
    st.info(f"📈 **Current Stock:** {symbol}")

    # Add data quality indicators
    if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
        df = st.session_state.historical_data

        # Quick stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Records", len(df))
        with col2:
            latest_price = df['Close'].iloc[-1] if not df.empty else 0
            st.metric("Latest Price", f"${latest_price:.2f}")
        with col3:
            date_range = (df['Date'].max() - df['Date'].min()).days if not df.empty else 0
            st.metric("Data Range", f"{date_range} days")
        with col4:
            data_quality = "Good" if len(df) >= 100 else "Limited" if len(df) >= 60 else "Poor"
            st.metric("Data Quality", data_quality)

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_trained_models_cache(symbol: str) -> Dict[str, List[int]]:
    """Get trained models with caching for performance"""
    common_horizons = [4, 15, 30, 60, 1440, 10080]
    model_types = ['rf', 'lstm', 'gb', 'lr', 'ensemble']

    trained_models = {}
    for model_type in model_types:
        trained_horizons = []
        for horizon in common_horizons:
            if is_model_trained(symbol, horizon, model_type):
                trained_horizons.append(horizon)
        if trained_horizons:
            trained_models[model_type] = trained_horizons

    return trained_models

def safe_get_live_data(symbol: str) -> Optional[pd.DataFrame]:
    """Safely fetch live data with comprehensive error handling"""
    try:
        from scrapers.price_scraper import PriceScraper

        with st.spinner("Fetching latest price data..."):
            scraper = PriceScraper(source="tradingview")
            price_data = scraper.get_price(symbol)
            scraper.close_driver()

            if price_data and isinstance(price_data, dict):
                # Validate required fields
                required_fields = ['Close', 'Date']
                if all(field in price_data for field in required_fields):
                    df = pd.DataFrame([price_data])
                    # Ensure Date is datetime
                    if 'Date' in df.columns:
                        df['Date'] = pd.to_datetime(df['Date'])
                    st.success(f"✅ Live price fetched: ${price_data['Close']:.2f}")
                    return df
                else:
                    st.warning(f"⚠️ Incomplete live data: missing {[f for f in required_fields if f not in price_data]}")
                    return None
            else:
                st.warning("⚠️ Could not fetch live price data")
                return None

    except Exception as e:
        st.warning(f"⚠️ Error fetching live data: {str(e)}")
        logger.error(f"Live data fetch error: {str(e)}")
        return None

def validate_prediction_inputs(symbol: str, horizon: int, model_type: str) -> List[str]:
    """Validate prediction inputs comprehensively"""
    errors = []

    # Check if model is trained
    if not is_model_trained(symbol, horizon, model_type, 'saved_models', 'minutes'):
        errors.append(f"No trained {model_type.upper()} model found for {horizon} minute horizon")

    # Check data recency
    if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
        df = st.session_state.historical_data
        if not df.empty and 'Date' in df.columns:
            latest_date = pd.to_datetime(df['Date'].max())
            days_old = (datetime.now() - latest_date).days
            if days_old > 30:
                errors.append(f"Historical data is {days_old} days old - predictions may be less accurate")

    return errors

def calculate_prediction_confidence(historical_data: pd.DataFrame, symbol: str, model_type: str, horizon: int) -> float:
    """Calculate confidence score for predictions based on historical performance"""
    try:
        # Simple confidence calculation based on data quality and model type
        data_quality_score = min(len(historical_data) / 100, 1.0)  # More data = higher confidence

        # Model-specific confidence adjustments
        model_confidence = {
            'rf': 0.85,
            'lstm': 0.80,
            'gb': 0.82,
            'lr': 0.70,
            'ensemble': 0.90
        }

        base_confidence = model_confidence.get(model_type, 0.75)

        # Adjust for horizon (shorter horizons generally more accurate)
        horizon_factor = max(0.5, 1.0 - (horizon / 1440) * 0.3)  # Decrease confidence for longer horizons

        final_confidence = base_confidence * data_quality_score * horizon_factor
        return min(final_confidence, 0.95)  # Cap at 95%

    except Exception:
        return 0.75  # Default confidence

def generate_prediction_insights(current_price: float, predicted_price: float, horizon: int) -> Dict[str, str]:
    """Generate insights about the prediction"""
    price_change = predicted_price - current_price
    percent_change = (price_change / current_price) * 100

    insights = {}

    # Direction insight
    if abs(percent_change) < 0.5:
        insights['direction'] = "📊 **Sideways Movement** - Price expected to remain relatively stable"
    elif percent_change > 0:
        insights['direction'] = "📈 **Bullish Signal** - Price expected to increase"
    else:
        insights['direction'] = "📉 **Bearish Signal** - Price expected to decrease"

    # Magnitude insight
    if abs(percent_change) < 1:
        insights['magnitude'] = "🔹 **Low Volatility** - Small price movement expected"
    elif abs(percent_change) < 3:
        insights['magnitude'] = "🔸 **Moderate Movement** - Noticeable price change expected"
    else:
        insights['magnitude'] = "🔶 **High Volatility** - Significant price movement expected"

    # Time-based insight
    if horizon <= 15:
        insights['timeframe'] = "⚡ **Short-term** - Suitable for scalping strategies"
    elif horizon <= 240:
        insights['timeframe'] = "🕐 **Intraday** - Good for day trading decisions"
    else:
        insights['timeframe'] = "📅 **Longer-term** - Consider for swing trading"

    return insights

def unified_prediction_engine(symbol: str, horizons: List[int], model_type: str, use_live_data: bool = True) -> Dict[int, float]:
    """
    Unified prediction engine to ensure consistent results across all prediction tabs.
    This is the SINGLE SOURCE OF TRUTH for all predictions in the app.
    """
    try:
        # Get historical data
        historical_data = st.session_state.historical_data
        if historical_data is None or historical_data.empty:
            raise ValueError("No historical data available")

        # Validate data quality
        if len(historical_data) < 60:
            raise ValueError(f"Insufficient historical data: {len(historical_data)} rows (minimum 60 required)")

        # Get live data if requested
        current_live_data = None
        if use_live_data:
            current_live_data = safe_get_live_data(symbol)

        # Make prediction using the appropriate method
        if current_live_data is not None and not current_live_data.empty:
            # Use predict_from_live_data for consistency
            from models.predict import predict_from_live_data
            predictions = predict_from_live_data(
                live_data=current_live_data,
                historical_data=historical_data,
                symbol=symbol,
                horizons=horizons,
                model_type=model_type.lower(),
                models_path='saved_models'
            )
            st.info(f"🔴 **Live Data Used**: Current price ${current_live_data['Close'].iloc[-1]:.2f}")
        else:
            # Use historical data only
            from models.predict import predict_future_prices
            predictions = predict_future_prices(
                historical_data,
                symbol,
                horizons=horizons,
                model_type=model_type.lower(),
                models_path='saved_models'
            )
            st.info(f"📊 **Historical Data Used**: Latest price ${historical_data['Close'].iloc[-1]:.2f}")

        return predictions

    except Exception as e:
        logger.error(f"Unified prediction engine error: {str(e)}")
        raise

def explain_prediction_differences():
    """Explain why different prediction methods might give different results"""
    with st.expander("🤔 **Why do different prediction tabs give different results?**"):
        st.markdown("""
        **This is NORMAL and expected! Here's why:**

        ### **🔬 Different Algorithms**
        - **Quick Predictions**: Uses single models (RF, LSTM, etc.)
        - **Advanced Analysis**: May use different preprocessing or parameters
        - **Ensemble Models**: Combines multiple models (averaging results)
        - **Enhanced Predictions**: Uses adaptive model selection

        ### **📊 Different Data Processing**
        - **Feature Engineering**: Some tabs may use additional technical indicators
        - **Data Normalization**: Different scaling methods
        - **Sequence Length**: Different lookback periods

        ### **⚙️ Different Parameters**
        - **Model Configuration**: Different hyperparameters
        - **Training Data**: Some models may use different training sets
        - **Prediction Horizons**: Internal horizon conversions

        ### **💡 Which Result to Trust?**
        1. **Ensemble Models** - Generally most reliable (combines multiple models)
        2. **Enhanced Predictions** - Uses adaptive selection
        3. **Quick Predictions** - Good for fast estimates
        4. **Advanced Analysis** - Best for detailed analysis

        ### **🎯 Recommendation**
        Use **Ensemble Models** for final trading decisions as they provide the most robust predictions.
        """)

def show_predictions_consolidated():
    """Main function to display the consolidated Predictions page"""

    st.title("🔮 AI Predictions")
    st.markdown("### Advanced Stock Price Forecasting with Multiple AI Models")

    # Enhanced validation with detailed error handling
    validation_errors = validate_prediction_requirements()
    if validation_errors:
        display_validation_errors(validation_errors)
        return

    # Display current stock info with enhanced details
    symbol = get_session_value('symbol')
    if symbol:
        display_stock_info(symbol)

    # Check for trained models with caching
    if symbol:
        trained_models_dict = get_trained_models_cache(symbol)

        if not trained_models_dict:
            st.warning("⚠️ No trained models found for this stock.")
            st.info("You need to train models before making predictions.")

            if st.button("🤖 Train Models Now", type="primary"):
                st.session_state.page = "Train Model"
                st.rerun()
            return
        else:
            total_models = sum(len(horizons) for horizons in trained_models_dict.values())
            st.success(f"✅ Found {len(trained_models_dict)} model types with {total_models} trained horizons")

            # Show available models summary
            with st.expander("📋 Available Models Summary"):
                for model_type, horizons in trained_models_dict.items():
                    st.write(f"**{model_type.upper()}**: {len(horizons)} horizons ({', '.join(map(str, horizons))} min)")

    # Add explanation about prediction differences
    explain_prediction_differences()

    # Create tabs for different prediction types
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🚀 Quick Predictions",
        "🔬 Advanced Analysis",
        "🎯 Ensemble Models",
        "⚡ Enhanced Predictions",
        "📊 Model Comparison"
    ])

    with tab1:
        show_quick_predictions()

    with tab2:
        show_advanced_predictions()

    with tab3:
        show_ensemble_predictions()

    with tab4:
        show_enhanced_predictions()

    with tab5:
        show_model_comparison()

def show_quick_predictions():
    """Quick and simple predictions interface"""

    st.header("🚀 Quick Predictions")
    st.markdown("Get fast predictions with minimal configuration.")

    # Simple interface
    col1, col2 = st.columns(2)

    with col1:
        # Prediction horizon selection
        horizon_type = st.selectbox(
            "Prediction Timeframe",
            ["Short-term (minutes)", "Medium-term (hours)", "Long-term (days)"],
            key="quick_pred_timeframe"
        )

        if horizon_type == "Short-term (minutes)":
            horizons = [4, 15, 30, 60]
            unit = "minutes"
        elif horizon_type == "Medium-term (hours)":
            horizons = [120, 240, 480, 720]  # 2h, 4h, 8h, 12h
            unit = "minutes"
        else:  # Long-term
            horizons = [1440, 2880, 7200, 10080]  # 1d, 2d, 5d, 1w
            unit = "minutes"

        selected_horizon = st.selectbox(
            f"Select horizon ({unit})",
            horizons,
            key="quick_pred_horizon"
        )

    with col2:
        # Model selection
        model_type = st.selectbox(
            "Model Type",
            ["Auto (Best Available)", "Random Forest", "LSTM", "Ensemble"],
            key="quick_pred_model"
        )

        # Convert display names to internal names
        model_mapping = {
            "Auto (Best Available)": "auto",
            "Random Forest": "rf",
            "LSTM": "lstm",
            "Ensemble": "ensemble"
        }

        internal_model = model_mapping[model_type]

    # Check if we have the required data
    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    if st.session_state.historical_data is None:
        st.warning("Please load historical data first.")
        return

    # Prediction button
    if st.button("🔮 Generate Prediction", type="primary", use_container_width=True):
        with st.spinner("Generating prediction..."):
            try:
                # Import required modules
                import plotly.graph_objects as go

                # Enhanced validation
                validation_errors = validate_prediction_inputs(st.session_state.symbol, selected_horizon, internal_model)
                if validation_errors:
                    for error in validation_errors:
                        st.error(f"❌ {error}")
                    st.info("💡 Please address the issues above before generating predictions.")
                    return

                # Use the unified prediction engine for consistency
                predictions = unified_prediction_engine(
                    symbol=st.session_state.symbol,
                    horizons=[selected_horizon],
                    model_type=internal_model,
                    use_live_data=True
                )

                if selected_horizon in predictions:
                    predicted_price = predictions[selected_horizon]

                    # Calculate prediction time
                    from datetime import datetime, timedelta
                    current_time = datetime.now()
                    pred_time = current_time + timedelta(minutes=selected_horizon)

                    # Get current price (check if live data was used in prediction)
                    if hasattr(st.session_state, 'live_data') and st.session_state.live_data is not None:
                        current_price = st.session_state.live_data['Close'].iloc[-1]
                    else:
                        current_price = st.session_state.historical_data['Close'].iloc[-1]

                    # Calculate change
                    price_change = predicted_price - current_price
                    percent_change = (price_change / current_price) * 100

                    # Calculate confidence and insights
                    confidence = calculate_prediction_confidence(
                        st.session_state.historical_data,
                        st.session_state.symbol,
                        internal_model,
                        selected_horizon
                    )
                    insights = generate_prediction_insights(current_price, predicted_price, selected_horizon)

                    # Display results with enhanced information
                    st.success("🎯 Prediction generated successfully!")

                    # Create columns for results
                    result_col1, result_col2, result_col3, result_col4 = st.columns(4)

                    with result_col1:
                        st.metric(
                            "Current Price",
                            f"${current_price:.2f}",
                            delta=None
                        )

                    with result_col2:
                        st.metric(
                            f"Predicted Price ({selected_horizon} min)",
                            f"${predicted_price:.2f}",
                            delta=f"${price_change:.2f}"
                        )

                    with result_col3:
                        st.metric(
                            "Expected Change",
                            f"{percent_change:+.2f}%",
                            delta=None
                        )

                    with result_col4:
                        confidence_color = "🟢" if confidence > 0.8 else "🟡" if confidence > 0.6 else "🔴"
                        st.metric(
                            "Confidence",
                            f"{confidence_color} {confidence:.1%}",
                            delta=None
                        )

                    # Display insights
                    st.markdown("### 🧠 **Prediction Insights**")
                    insight_cols = st.columns(3)

                    with insight_cols[0]:
                        st.markdown(insights['direction'])
                    with insight_cols[1]:
                        st.markdown(insights['magnitude'])
                    with insight_cols[2]:
                        st.markdown(insights['timeframe'])

                    # Show prediction details
                    st.info(f"**Prediction Details:**\n"
                           f"- Stock: {st.session_state.symbol}\n"
                           f"- Model: {model_type}\n"
                           f"- Horizon: {selected_horizon} minutes\n"
                           f"- Prediction Time: {pred_time.strftime('%Y-%m-%d %H:%M:%S')}")

                    # Create a simple chart
                    import plotly.graph_objects as go

                    fig = go.Figure()

                    # Add historical data (last 30 points)
                    hist_data = st.session_state.historical_data.tail(30)
                    fig.add_trace(go.Scatter(
                        x=hist_data['Date'],
                        y=hist_data['Close'],
                        mode='lines',
                        name='Historical',
                        line=dict(color='blue')
                    ))

                    # Add current price point if live data exists
                    if hasattr(st.session_state, 'live_data') and st.session_state.live_data is not None:
                        fig.add_trace(go.Scatter(
                            x=st.session_state.live_data['Date'],
                            y=st.session_state.live_data['Close'],
                            mode='markers',
                            name='Current',
                            marker=dict(color='green', size=10)
                        ))

                    # Add prediction point
                    fig.add_trace(go.Scatter(
                        x=[pred_time],
                        y=[predicted_price],
                        mode='markers',
                        name='Prediction',
                        marker=dict(color='red', size=12, symbol='star')
                    ))

                    # Add trend line
                    if hasattr(st.session_state, 'live_data') and st.session_state.live_data is not None:
                        last_time = st.session_state.live_data['Date'].iloc[-1]
                        last_price = st.session_state.live_data['Close'].iloc[-1]
                    else:
                        last_time = st.session_state.historical_data['Date'].iloc[-1]
                        last_price = st.session_state.historical_data['Close'].iloc[-1]

                    fig.add_trace(go.Scatter(
                        x=[last_time, pred_time],
                        y=[last_price, predicted_price],
                        mode='lines',
                        name='Trend',
                        line=dict(color='red', dash='dot')
                    ))

                    fig.update_layout(
                        title=f'{st.session_state.symbol} Quick Prediction',
                        xaxis_title='Date',
                        yaxis_title='Price',
                        hovermode='x unified'
                    )

                    st.plotly_chart(fig, use_container_width=True)

                    # Store prediction in session state
                    st.session_state.last_quick_prediction = {
                        'symbol': st.session_state.symbol,
                        'model': model_type,
                        'horizon': selected_horizon,
                        'current_price': current_price,
                        'predicted_price': predicted_price,
                        'prediction_time': pred_time,
                        'generated_at': current_time
                    }

                else:
                    st.error("Failed to generate prediction. Please try again.")

            except Exception as e:
                st.error(f"Error generating prediction: {str(e)}")
                logger.error(f"Quick prediction error: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

                # Show more specific error information
                if "Ensemble" in str(e):
                    st.info("**Ensemble Model Issue:** This might be due to missing ensemble model files. Try using a different model type or train an ensemble model first.")
                elif "model" in str(e).lower():
                    st.info("**Model Loading Issue:** The selected model might not be trained for this horizon. Please train the model first.")
                else:
                    st.info("**General Error:** Please check the logs for more details or try with different settings.")

def show_advanced_predictions():
    """Advanced predictions with full configuration"""

    st.header("🔬 Advanced Analysis")
    st.markdown("Comprehensive predictions with detailed configuration and analysis.")

    # Use the advanced prediction component
    try:
        advanced_prediction_component(
            st.session_state.historical_data,
            st.session_state.live_data,
            st.session_state.symbol
        )
    except Exception as e:
        st.error(f"Error in advanced predictions: {str(e)}")
        logger.error(f"Advanced prediction error: {str(e)}")

def show_ensemble_predictions():
    """Ensemble model predictions"""

    st.header("🎯 Ensemble Models")
    st.markdown("Combine multiple models for improved accuracy and reliability.")

    # Add ensemble explanation
    with st.expander("ℹ️ About Ensemble Models"):
        st.markdown("""
        **Ensemble models combine predictions from multiple individual models to create more accurate and robust forecasts.**

        **Benefits:**
        - **Higher Accuracy**: Combines strengths of different models
        - **Reduced Risk**: Less likely to make extreme errors
        - **Better Generalization**: Works well across different market conditions

        **Available Ensemble Types:**
        - **Simple Average**: Equal weight to all models
        - **Weighted Average**: Performance-based weighting
        - **Stacking**: Advanced meta-learning approach
        """)

    # Use the ensemble predictions component
    try:
        ensemble_predictions_component(
            st.session_state.historical_data,
            st.session_state.live_data,
            st.session_state.symbol
        )
    except Exception as e:
        st.error(f"Error in ensemble predictions: {str(e)}")
        logger.error(f"Ensemble prediction error: {str(e)}")

def show_enhanced_predictions():
    """Enhanced predictions with adaptive features"""

    st.header("⚡ Enhanced Predictions")
    st.markdown("Next-generation predictions with adaptive model selection and confidence intervals.")

    # Add enhanced features explanation
    with st.expander("✨ Enhanced Features"):
        st.markdown("""
        **Enhanced predictions include advanced features:**

        - **Adaptive Model Selection**: Automatically chooses the best model for current conditions
        - **Confidence Intervals**: Provides uncertainty estimates with predictions
        - **Market Regime Detection**: Adjusts predictions based on market conditions
        - **Real-time Optimization**: Continuously improves based on recent performance
        """)

    # Use the enhanced prediction component
    try:
        enhanced_prediction_component(
            st.session_state.historical_data,
            st.session_state.live_data,
            st.session_state.symbol
        )
    except Exception as e:
        st.error(f"Error in enhanced predictions: {str(e)}")
        logger.error(f"Enhanced prediction error: {str(e)}")

def show_model_comparison():
    """Compare different models and their performance with live comparison"""

    st.header("📊 Model Comparison & Live Testing")
    st.markdown("**Compare predictions from different models side-by-side and understand their strengths.**")

    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    symbol = st.session_state.symbol

    # Model performance comparison
    st.subheader("🏆 Model Availability Matrix")

    # Check available models
    model_types = ['rf', 'lstm', 'gb', 'lr', 'ensemble']
    horizons = [4, 15, 30, 60, 1440]

    # Create performance matrix
    performance_data = []

    for model in model_types:
        model_row = {'Model': model.upper()}
        for horizon in horizons:
            if is_model_trained(symbol, horizon, model):
                model_row[f'{horizon}min'] = '✅ Trained'
            else:
                model_row[f'{horizon}min'] = '❌ Not Trained'
        performance_data.append(model_row)

    if performance_data:
        df_performance = pd.DataFrame(performance_data)
        st.dataframe(df_performance, use_container_width=True)

    # Live Model Comparison
    st.subheader("🔬 Live Model Comparison")
    st.markdown("**Test different models with the same parameters to see how they differ:**")

    # Selection for comparison
    col1, col2 = st.columns(2)

    with col1:
        comparison_horizon = st.selectbox(
            "Select horizon for comparison",
            [4, 15, 30, 60],
            index=1,
            key="comparison_horizon"
        )

    with col2:
        available_models = []
        for model in model_types:
            if is_model_trained(symbol, comparison_horizon, model):
                available_models.append(model)

        if len(available_models) < 2:
            st.warning(f"Need at least 2 trained models for {comparison_horizon}min horizon")
            return

        st.info(f"✅ {len(available_models)} models available for comparison")

    # Compare models button
    if st.button("🔍 Compare All Available Models", type="primary", use_container_width=True):
        with st.spinner("Generating predictions from all models..."):
            try:
                comparison_results = {}

                for model_type in available_models:
                    try:
                        predictions = unified_prediction_engine(
                            symbol=symbol,
                            horizons=[comparison_horizon],
                            model_type=model_type,
                            use_live_data=True
                        )
                        if comparison_horizon in predictions:
                            comparison_results[model_type] = predictions[comparison_horizon]
                    except Exception as e:
                        st.warning(f"Failed to get prediction from {model_type}: {str(e)}")

                if comparison_results:
                    # Display comparison results
                    st.subheader("📈 Prediction Comparison Results")

                    # Get current price for reference
                    current_price = st.session_state.historical_data['Close'].iloc[-1]

                    # Create comparison table
                    comparison_data = []
                    for model, predicted_price in comparison_results.items():
                        price_change = predicted_price - current_price
                        percent_change = (price_change / current_price) * 100

                        comparison_data.append({
                            'Model': model.upper(),
                            'Predicted Price': f"${predicted_price:.2f}",
                            'Price Change': f"${price_change:+.2f}",
                            'Percent Change': f"{percent_change:+.2f}%",
                            'Direction': "📈 Bullish" if price_change > 0 else "📉 Bearish" if price_change < 0 else "📊 Neutral"
                        })

                    df_comparison = pd.DataFrame(comparison_data)
                    st.dataframe(df_comparison, use_container_width=True)

                    # Statistical analysis
                    st.subheader("📊 Statistical Analysis")

                    prices = list(comparison_results.values())
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("Average Prediction", f"${np.mean(prices):.2f}")
                    with col2:
                        st.metric("Prediction Range", f"${np.max(prices) - np.min(prices):.2f}")
                    with col3:
                        st.metric("Standard Deviation", f"${np.std(prices):.2f}")
                    with col4:
                        agreement = "High" if np.std(prices) < current_price * 0.01 else "Medium" if np.std(prices) < current_price * 0.02 else "Low"
                        st.metric("Model Agreement", agreement)

                    # Interpretation
                    st.subheader("🧠 Interpretation")

                    if np.std(prices) < current_price * 0.01:
                        st.success("🎯 **High Agreement**: Models are in strong agreement. This prediction is likely reliable.")
                    elif np.std(prices) < current_price * 0.02:
                        st.warning("⚖️ **Medium Agreement**: Models show some variation. Consider the ensemble result.")
                    else:
                        st.error("⚠️ **Low Agreement**: Models disagree significantly. Use caution with this prediction.")

                    # Recommendation
                    ensemble_available = 'ensemble' in comparison_results
                    if ensemble_available:
                        st.info(f"💡 **Recommendation**: Use the Ensemble model result (${comparison_results['ensemble']:.2f}) as it combines all models.")
                    else:
                        avg_prediction = np.mean(prices)
                        st.info(f"💡 **Recommendation**: Consider the average prediction (${avg_prediction:.2f}) as a consensus estimate.")

                else:
                    st.error("No successful predictions generated for comparison.")

            except Exception as e:
                st.error(f"Error during model comparison: {str(e)}")
                logger.error(f"Model comparison error: {str(e)}")

    # Model recommendations
    st.subheader("💡 Model Recommendations")

    recommendations = {
        "Short-term (< 1 hour)": {
            "Best Models": ["LSTM", "Random Forest"],
            "Reason": "Neural networks excel at capturing short-term patterns"
        },
        "Medium-term (1-24 hours)": {
            "Best Models": ["Ensemble", "Gradient Boosting"],
            "Reason": "Ensemble methods provide stability for medium-term forecasts"
        },
        "Long-term (> 1 day)": {
            "Best Models": ["Random Forest", "Linear Regression"],
            "Reason": "Traditional ML models handle long-term trends well"
        }
    }

    for timeframe, info in recommendations.items():
        with st.expander(f"📈 {timeframe}"):
            st.write(f"**Recommended Models:** {', '.join(info['Best Models'])}")
            st.write(f"**Reason:** {info['Reason']}")

    # Quick training suggestions
    st.subheader("🚀 Quick Actions")

    action_cols = st.columns(3)

    with action_cols[0]:
        if st.button("🤖 Train More Models", use_container_width=True):
            st.session_state.page = "Train Model"
            st.rerun()

    with action_cols[1]:
        if st.button("📊 View Performance Metrics", use_container_width=True):
            st.session_state.page = "Performance Metrics"
            st.rerun()

    with action_cols[2]:
        if st.button("📈 Live Trading", use_container_width=True):
            st.session_state.page = "Live Trading"
            st.rerun()
