"""
Professional TradingView Charts Page
Standalone professional charting using TradingView library
"""

import streamlit as st
import os
from pathlib import Path

def show_professional_charts():
    """Display professional TradingView charts page"""
    
    st.title("🚀 Professional TradingView Charts")
    st.markdown("**Professional-grade charting for Egyptian Exchange stocks using TradingView library**")
    
    # Information section
    with st.expander("ℹ️ About Professional Charts", expanded=False):
        st.markdown("""
        **🎯 Features:**
        - **Real TradingView Library** - Full professional charting capabilities
        - **Interactive Controls** - Symbol selection, timeframes, themes
        - **Technical Indicators** - RSI, MACD, and more professional indicators
        - **Drawing Tools** - Professional drawing and analysis tools
        - **EGX Stock Support** - Optimized for Egyptian Exchange stocks
        
        **📊 How to Use:**
        1. Click "Open Professional Charts" below
        2. Select your stock symbol (COMI, ETEL, etc.)
        3. Choose timeframe and theme
        4. Click "Load Chart" to display professional chart
        5. Use "Add RSI" and "Add MACD" for technical analysis
        """)
    
    # Chart launch section
    st.markdown("---")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("### 📊 Launch Professional Charts")
        
        # Check if standalone file exists
        standalone_file = Path("tradingview_standalone.html")
        
        if standalone_file.exists():
            st.success("✅ Professional TradingView charts are ready!")
            
            # Create launch button
            if st.button("🚀 Open Professional Charts", type="primary", use_container_width=True):
                # Get absolute path to the HTML file
                html_path = standalone_file.absolute()
                
                # Display instructions
                st.balloons()
                st.success("🎉 Professional charts launched!")
                
                # Show the file path for manual opening
                st.info(f"""
                **📂 Chart Location:** `{html_path}`
                
                **🔧 How to Access:**
                1. **Copy the path above**
                2. **Paste it in your browser address bar**
                3. **Press Enter to open professional charts**
                
                Or simply **double-click** the `tradingview_standalone.html` file in your project folder.
                """)
                
                # Alternative: Show HTML content in iframe (if needed)
                with st.expander("🔧 Alternative: Embedded View (Experimental)", expanded=False):
                    st.warning("⚠️ This embedded view might have limitations. Use the standalone file for best experience.")
                    
                    # Read and display HTML content
                    try:
                        with open(standalone_file, 'r', encoding='utf-8') as f:
                            html_content = f.read()
                        
                        # Display in iframe-like component
                        st.components.v1.html(html_content, height=800, scrolling=True)
                        
                    except Exception as e:
                        st.error(f"Error loading embedded view: {str(e)}")
            
            # Additional options
            st.markdown("---")
            
            # Quick access section
            st.markdown("### ⚡ Quick Access")
            
            quick_col1, quick_col2 = st.columns(2)
            
            with quick_col1:
                if st.button("📁 Open Project Folder", use_container_width=True):
                    try:
                        import subprocess
                        import platform
                        
                        project_path = Path.cwd()
                        
                        if platform.system() == "Windows":
                            subprocess.run(f'explorer "{project_path}"', shell=True)
                        elif platform.system() == "Darwin":  # macOS
                            subprocess.run(f'open "{project_path}"', shell=True)
                        else:  # Linux
                            subprocess.run(f'xdg-open "{project_path}"', shell=True)
                            
                        st.success("📂 Project folder opened!")
                        
                    except Exception as e:
                        st.error(f"Could not open folder: {str(e)}")
                        st.info(f"Manual path: `{Path.cwd()}`")
            
            with quick_col2:
                if st.button("🔄 Refresh Charts", use_container_width=True):
                    st.rerun()
            
        else:
            st.error("❌ Professional charts file not found!")
            st.markdown("""
            **🔧 Setup Required:**
            
            The `tradingview_standalone.html` file is missing. This file contains the professional TradingView charts.
            
            **📋 To Fix:**
            1. Make sure the `tradingview_standalone.html` file exists in your project root
            2. Ensure the TradingView library files are properly placed
            3. Refresh this page after setup
            """)
            
            if st.button("🔄 Check Again", type="secondary"):
                st.rerun()
    
    # Technical information
    st.markdown("---")
    
    with st.expander("🔧 Technical Information", expanded=False):
        st.markdown("""
        **📁 File Structure:**
        ```
        AI Stocks Bot/
        ├── tradingview_standalone.html          # Professional charts page
        ├── trading-view-charting-library/       # TradingView library files
        │   └── tradingview-master_*/
        │       ├── charting_library/
        │       └── datafeeds/
        └── app/
            └── pages/
                └── professional_charts.py       # This page
        ```
        
        **🚀 Features Available:**
        - **Full TradingView Library** - Complete professional charting
        - **Real-time Data** - Sample data with realistic price movements
        - **Technical Analysis** - RSI, MACD, and other indicators
        - **Interactive Controls** - Zoom, pan, drawing tools
        - **Multiple Symbols** - COMI, ETEL, and demo symbols
        - **Theme Support** - Dark and light themes
        - **Responsive Design** - Works on different screen sizes
        
        **💡 Why Standalone:**
        The TradingView library works perfectly as a standalone HTML page but has integration
        challenges with Streamlit's component system. This approach gives you the full
        professional TradingView experience without compromises.
        """)

if __name__ == "__main__":
    show_professional_charts()
