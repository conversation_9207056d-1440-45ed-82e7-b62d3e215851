"""
Test the API fix for Advanced Technical Analysis and SMC Analysis
"""

import requests
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_api_connection():
    """Test if the API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running and healthy")
            return True
        else:
            print(f"❌ API server responded with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {str(e)}")
        return False

def test_advanced_technical_analysis_fix():
    """Test the Advanced Technical Analysis API call"""
    print("\n🧪 Testing Advanced Technical Analysis Fix")
    print("=" * 50)
    
    try:
        # Import the fixed function
        from app.pages.advanced_technical_analysis import fetch_technical_analysis
        
        # Test with COMI stock
        symbols = ["EGX-COMI"]
        intervals = ["1D"]
        
        print(f"📊 Testing with symbols: {symbols}")
        print(f"⏰ Testing with intervals: {intervals}")
        
        result = fetch_technical_analysis(symbols, intervals)
        
        if result:
            print("✅ Advanced Technical Analysis API call successful!")
            print(f"📈 Received data for: {list(result.keys())}")
            
            # Check data structure
            for symbol, data in result.items():
                if data:
                    print(f"   {symbol}: {len(data)} timeframe(s)")
                    if data[0].get('price'):
                        print(f"   Current price: {data[0]['price']:,.0f} EGP")
                else:
                    print(f"   {symbol}: No data")
            return True
        else:
            print("❌ Advanced Technical Analysis API call failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Advanced Technical Analysis: {str(e)}")
        return False

def test_smc_analysis_fix():
    """Test the SMC Analysis API call"""
    print("\n🧪 Testing SMC Analysis Fix")
    print("=" * 50)
    
    try:
        # Import the fixed function
        from app.pages.smc_analysis import fetch_price_data_for_smc
        
        # Test with COMI stock
        symbol = "EGX-COMI"
        interval = "1D"
        
        print(f"📊 Testing with symbol: {symbol}")
        print(f"⏰ Testing with interval: {interval}")
        
        result = fetch_price_data_for_smc(symbol, interval)
        
        if result is not None:
            print("✅ SMC Analysis API call successful!")
            print(f"📈 Generated OHLCV data with {len(result)} bars")
            print(f"   Current price: {result['close'].iloc[-1]:,.2f} EGP")
            print(f"   Date range: {result.index[0].date()} to {result.index[-1].date()}")
            return True
        else:
            print("❌ SMC Analysis API call failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing SMC Analysis: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 API Fix Verification Test")
    print("=" * 60)
    
    # Test API connection first
    if not test_api_connection():
        print("\n❌ API server is not running. Please start it first:")
        print("   cd scrapers && python api_server.py")
        return
    
    # Test both fixes
    advanced_ta_success = test_advanced_technical_analysis_fix()
    smc_success = test_smc_analysis_fix()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if advanced_ta_success:
        print("✅ Advanced Technical Analysis: FIXED")
    else:
        print("❌ Advanced Technical Analysis: STILL BROKEN")
    
    if smc_success:
        print("✅ SMC Analysis: FIXED")
    else:
        print("❌ SMC Analysis: STILL BROKEN")
    
    if advanced_ta_success and smc_success:
        print("\n🎉 ALL FIXES SUCCESSFUL!")
        print("✅ Your Streamlit app should now work correctly")
        print("\n💡 Next steps:")
        print("1. Refresh your Streamlit app")
        print("2. Go to Advanced Technical Analysis page")
        print("3. Select COMI and click 'Analyze EGX Stocks'")
        print("4. Go to SMC Analysis page")
        print("5. Select COMI and click 'Run SMC Analysis'")
        print("6. Both should now show data instead of 'Failed to fetch'")
    else:
        print("\n⚠️ Some fixes failed. Check the error messages above.")

if __name__ == "__main__":
    main()
