2025-05-31 12:04:01,831 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-31 12:04:07,503 - app - INFO - Memory management utilities loaded
2025-05-31 12:04:07,516 - app - INFO - Error handling utilities loaded
2025-05-31 12:04:07,523 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 12:04:07,525 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 12:04:07,527 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 12:04:07,529 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 12:04:07,544 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 12:04:07,558 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 12:04:07,560 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 12:04:07,564 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 12:04:07,565 - app - INFO - Applied NumPy fix
2025-05-31 12:04:07,570 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 12:04:07,575 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 12:04:07,576 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 12:04:07,577 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 12:04:07,578 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 12:04:07,578 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 12:04:07,579 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 12:04:07,579 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 12:04:26,024 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 12:04:26,024 - app - INFO - Applied TensorFlow fix
2025-05-31 12:04:26,039 - app.config - INFO - Configuration initialized
2025-05-31 12:04:26,057 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 12:04:26,356 - models.train - INFO - TensorFlow test successful
2025-05-31 12:04:30,823 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 12:04:30,823 - models.train - INFO - Transformer model is available
2025-05-31 12:04:30,824 - models.train - INFO - Using TensorFlow-based models
2025-05-31 12:04:30,828 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 12:04:30,829 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 12:04:30,870 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 12:04:32,519 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 12:04:32,520 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 12:04:32,894 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 12:04:32,917 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:33,697 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 12:04:38,120 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 12:04:38,194 - app.utils.session_state - INFO - Initializing session state
2025-05-31 12:04:38,195 - app.utils.session_state - INFO - Session state initialized
2025-05-31 12:04:39,386 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 12:04:39,395 - app.utils.memory_management - INFO - Memory before cleanup: 425.38 MB
2025-05-31 12:04:39,517 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 12:04:39,517 - app.utils.memory_management - INFO - Memory after cleanup: 425.77 MB (freed -0.39 MB)
2025-05-31 12:04:54,007 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:54,054 - app.utils.memory_management - INFO - Memory before cleanup: 429.62 MB
2025-05-31 12:04:54,204 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 12:04:54,206 - app.utils.memory_management - INFO - Memory after cleanup: 429.62 MB (freed -0.00 MB)
2025-05-31 12:04:55,205 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:55,369 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.15 seconds
2025-05-31 12:04:55,383 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 12:04:55,383 - app - INFO - Data shape: (581, 36)
2025-05-31 12:04:55,383 - app - INFO - File COMI contains 2025 data
2025-05-31 12:04:55,419 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-31 12:04:55,419 - app - INFO - Features shape: (581, 36)
2025-05-31 12:04:55,439 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 12:04:55,439 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 12:04:55,440 - app - INFO - Data shape: (581, 36)
2025-05-31 12:04:55,440 - app - INFO - File COMI contains 2025 data
2025-05-31 12:04:55,444 - app.utils.memory_management - INFO - Memory before cleanup: 433.95 MB
2025-05-31 12:04:55,570 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 12:04:55,570 - app.utils.memory_management - INFO - Memory after cleanup: 433.99 MB (freed -0.04 MB)
2025-05-31 12:04:55,717 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:55,877 - app.utils.memory_management - INFO - Memory before cleanup: 435.07 MB
2025-05-31 12:04:55,992 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 12:04:55,992 - app.utils.memory_management - INFO - Memory after cleanup: 435.07 MB (freed 0.00 MB)
2025-05-31 12:05:56,515 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:05:56,873 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-31 12:05:58,409 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-31 12:05:58,438 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.03 seconds
2025-05-31 12:05:58,439 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-31 12:05:58,439 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-31 12:05:58,439 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-31 12:05:59,085 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.68 seconds
2025-05-31 12:05:59,407 - app.utils.memory_management - INFO - Memory before cleanup: 445.65 MB
2025-05-31 12:05:59,547 - app.utils.memory_management - INFO - Garbage collection: collected 3399 objects
2025-05-31 12:05:59,550 - app.utils.memory_management - INFO - Memory after cleanup: 445.65 MB (freed 0.00 MB)
2025-05-31 12:08:12,730 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:08:13,070 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-31 12:08:13,395 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-31 12:08:13,405 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-31 12:08:13,405 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-31 12:08:13,407 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-31 12:08:13,409 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-31 12:08:13,709 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.31 seconds
2025-05-31 12:08:13,982 - app.utils.memory_management - INFO - Memory before cleanup: 447.39 MB
2025-05-31 12:08:14,175 - app.utils.memory_management - INFO - Garbage collection: collected 2097 objects
2025-05-31 12:08:14,178 - app.utils.memory_management - INFO - Memory after cleanup: 447.39 MB (freed 0.00 MB)
2025-05-31 12:08:51,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:08:51,374 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-31 12:08:52,376 - app.utils.memory_management - INFO - Memory before cleanup: 456.88 MB
2025-05-31 12:08:52,511 - app.utils.memory_management - INFO - Garbage collection: collected 593 objects
2025-05-31 12:08:52,512 - app.utils.memory_management - INFO - Memory after cleanup: 456.88 MB (freed 0.00 MB)
2025-05-31 12:09:28,779 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:09:29,168 - app.utils.memory_management - INFO - Memory before cleanup: 460.50 MB
2025-05-31 12:09:29,282 - app.utils.memory_management - INFO - Garbage collection: collected 385 objects
2025-05-31 12:09:29,282 - app.utils.memory_management - INFO - Memory after cleanup: 460.50 MB (freed 0.00 MB)
2025-05-31 12:09:48,930 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:09:48,942 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 12:09:48,954 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-31 12:09:50,276 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-31 12:09:50,546 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 12:09:50,546 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 12:09:50,547 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 12:09:50,547 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 12:09:50,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 12:09:50,554 - app.utils.error_handling - INFO - live_trading_component executed in 1.61 seconds
2025-05-31 12:09:50,557 - app.utils.memory_management - INFO - Memory before cleanup: 462.63 MB
2025-05-31 12:09:50,676 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-05-31 12:09:50,676 - app.utils.memory_management - INFO - Memory after cleanup: 462.63 MB (freed 0.00 MB)
2025-05-31 12:10:35,474 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:10:35,534 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 12:10:35,534 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 12:10:35,535 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 12:10:35,535 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 12:10:35,541 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 12:10:35,544 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 12:10:35,545 - app.utils.memory_management - INFO - Memory before cleanup: 463.67 MB
2025-05-31 12:10:35,708 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-31 12:10:35,709 - app.utils.memory_management - INFO - Memory after cleanup: 463.67 MB (freed 0.00 MB)
2025-05-31 12:10:51,421 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:10:51,504 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-31 12:10:51,515 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-31 12:10:51,525 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-31 12:10:51,535 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 10.2 hours of target)
2025-05-31 12:10:51,546 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 10.0 hours of target)
2025-05-31 12:10:51,556 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.7 hours of target)
2025-05-31 12:10:51,566 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.2 hours of target)
2025-05-31 12:10:51,577 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.7 hours of target)
2025-05-31 12:10:51,586 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.2 hours of target)
2025-05-31 12:10:51,593 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 8.5 hours of target)
2025-05-31 12:10:51,593 - app.components.performance_metrics - INFO - Target time 2025-05-29 12:41:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,612 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:11:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,612 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:31:47.823527 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,634 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:33:26.243941 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,643 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:46:49.828351 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,693 - app.components.performance_metrics - INFO - Updated performance metrics for 7 model-symbol combinations
2025-05-31 12:10:51,841 - app.utils.memory_management - INFO - Memory before cleanup: 466.03 MB
2025-05-31 12:10:52,007 - app.utils.memory_management - INFO - Garbage collection: collected 1609 objects
2025-05-31 12:10:52,007 - app.utils.memory_management - INFO - Memory after cleanup: 466.03 MB (freed 0.00 MB)
2025-05-31 12:11:20,123 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:20,150 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-31 12:11:20,162 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-31 12:11:20,173 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-31 12:11:20,181 - app.components.performance_metrics - INFO - Target time 2025-05-29 12:41:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,191 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:11:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,199 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:31:47.823527 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,213 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:33:26.243941 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,223 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:46:49.828351 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,388 - app.utils.memory_management - INFO - Memory before cleanup: 467.45 MB
2025-05-31 12:11:20,512 - app.utils.memory_management - INFO - Garbage collection: collected 1605 objects
2025-05-31 12:11:20,512 - app.utils.memory_management - INFO - Memory after cleanup: 467.45 MB (freed 0.00 MB)
2025-05-31 12:11:21,499 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:21,511 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:21,512 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:21,512 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:21,513 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:21,524 - app.utils.memory_management - INFO - Memory before cleanup: 467.44 MB
2025-05-31 12:11:21,644 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-05-31 12:11:21,645 - app.utils.memory_management - INFO - Memory after cleanup: 467.44 MB (freed 0.00 MB)
2025-05-31 12:11:31,623 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:31,633 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:31,634 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:31,634 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:31,634 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:31,650 - app.utils.memory_management - INFO - Memory before cleanup: 467.44 MB
2025-05-31 12:11:31,765 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-31 12:11:31,765 - app.utils.memory_management - INFO - Memory after cleanup: 467.44 MB (freed 0.00 MB)
2025-05-31 12:11:45,082 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:45,091 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:45,091 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:45,092 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:45,093 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:45,107 - app.utils.memory_management - INFO - Memory before cleanup: 467.44 MB
2025-05-31 12:11:45,233 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-31 12:11:45,233 - app.utils.memory_management - INFO - Memory after cleanup: 467.44 MB (freed 0.00 MB)
2025-05-31 12:11:57,419 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:57,432 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:57,433 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:57,433 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:57,434 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:57,445 - app.utils.memory_management - INFO - Memory before cleanup: 467.42 MB
2025-05-31 12:11:57,557 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-31 12:11:57,557 - app.utils.memory_management - INFO - Memory after cleanup: 467.42 MB (freed 0.00 MB)
2025-05-31 12:11:58,510 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:58,527 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:58,527 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:58,527 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:58,528 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:58,544 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 12:11:58,545 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 12:11:58,547 - app.utils.backtesting - INFO - Created model instance of type ensemble
2025-05-31 12:11:58,548 - app.utils.backtesting - INFO - Loading model for COMI with horizon 1
2025-05-31 12:11:58,548 - app.utils.backtesting - INFO - Looking in path: saved_models
2025-05-31 12:11:58,551 - app.utils.backtesting - INFO - Found matching model files: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-31 12:11:58,552 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_1min.joblib
2025-05-31 12:11:58,552 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_1min.joblib
2025-05-31 12:11:58,620 - app.utils.backtesting - INFO - Model loaded successfully: RandomForestRegressor
2025-05-31 12:11:58,620 - app.utils.backtesting - INFO - Starting backtest with test period: 30 days
2025-05-31 12:11:58,620 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-05-31 12:11:58,633 - app.utils.backtesting - INFO - Added technical indicators. New shape: (581, 36)
2025-05-31 12:11:58,641 - app.utils.backtesting - INFO - Prepared features. Final shape: (581, 36)
2025-05-31 12:11:58,641 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 5 features
2025-05-31 12:11:58,641 - app.utils.backtesting - INFO - Model type detection: sklearn=True, hybrid=False
2025-05-31 12:11:58,659 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,659 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,674 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,676 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,679 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,679 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,681 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,682 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,683 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,683 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,683 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,683 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,683 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,683 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,692 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,692 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,692 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,692 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,698 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,699 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,700 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,701 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,702 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,703 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,704 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,704 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,707 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,708 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,710 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,710 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,712 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,713 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,714 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,715 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,716 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,717 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,719 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,719 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,721 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,721 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,723 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,723 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,726 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,727 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,729 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,730 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,732 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,733 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,734 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,734 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,735 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-05-31 12:11:58,736 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-05-31 12:11:58,737 - app.utils.backtesting - INFO - Backtest completed with 5 results
2025-05-31 12:11:58,777 - app.utils.memory_management - INFO - Memory before cleanup: 472.20 MB
2025-05-31 12:11:58,892 - app.utils.memory_management - INFO - Garbage collection: collected 1103 objects
2025-05-31 12:11:58,892 - app.utils.memory_management - INFO - Memory after cleanup: 472.20 MB (freed 0.00 MB)
2025-05-31 12:12:08,867 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:12:08,877 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:12:08,877 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:12:08,877 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:12:08,878 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:12:08,909 - app.utils.memory_management - INFO - Memory before cleanup: 472.30 MB
2025-05-31 12:12:09,068 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-05-31 12:12:09,068 - app.utils.memory_management - INFO - Memory after cleanup: 472.30 MB (freed 0.00 MB)
2025-05-31 12:12:18,432 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:12:18,454 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-31 12:12:18,454 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 12:12:18,454 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-31 12:12:18,454 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-31 12:12:19,297 - app.utils.memory_management - INFO - Memory before cleanup: 478.71 MB
2025-05-31 12:12:19,412 - app.utils.memory_management - INFO - Garbage collection: collected 2606 objects
2025-05-31 12:12:19,412 - app.utils.memory_management - INFO - Memory after cleanup: 478.71 MB (freed 0.00 MB)
2025-05-31 22:45:55,208 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:45:56,618 - app - INFO - Memory management utilities loaded
2025-05-31 22:45:56,620 - app - INFO - Error handling utilities loaded
2025-05-31 22:45:56,621 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:45:56,623 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:45:56,623 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:45:56,624 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 22:45:56,625 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 22:45:56,627 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 22:45:56,629 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 22:45:56,630 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 22:45:56,631 - app - INFO - Applied NumPy fix
2025-05-31 22:45:56,635 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:45:56,635 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:45:56,641 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:45:56,641 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 22:45:56,642 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:45:56,642 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:45:56,642 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:45:56,642 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 22:46:00,963 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 22:46:00,963 - app - INFO - Applied TensorFlow fix
2025-05-31 22:46:00,966 - app.config - INFO - Configuration initialized
2025-05-31 22:46:00,971 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 22:46:01,420 - models.train - INFO - TensorFlow test successful
2025-05-31 22:46:04,231 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 22:46:04,232 - models.train - INFO - Transformer model is available
2025-05-31 22:46:04,232 - models.train - INFO - Using TensorFlow-based models
2025-05-31 22:46:04,233 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 22:46:04,234 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 22:46:04,236 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 22:46:04,756 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:46:04,757 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:46:04,757 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:46:04,757 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:46:04,757 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:46:04,760 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:46:04,862 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 22:46:04,864 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:05,195 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 22:46:06,358 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 22:46:06,388 - app.utils.session_state - INFO - Initializing session state
2025-05-31 22:46:06,390 - app.utils.session_state - INFO - Session state initialized
2025-05-31 22:46:07,897 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 22:46:07,928 - app.utils.memory_management - INFO - Memory before cleanup: 435.18 MB
2025-05-31 22:46:08,206 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-05-31 22:46:08,208 - app.utils.memory_management - INFO - Memory after cleanup: 435.25 MB (freed -0.07 MB)
2025-05-31 22:46:16,766 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:16,799 - app.utils.memory_management - INFO - Memory before cleanup: 439.02 MB
2025-05-31 22:46:16,978 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:46:16,978 - app.utils.memory_management - INFO - Memory after cleanup: 439.02 MB (freed 0.00 MB)
2025-05-31 22:46:18,377 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:18,539 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-05-31 22:46:18,547 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:46:18,547 - app - INFO - Data shape: (581, 36)
2025-05-31 22:46:18,547 - app - INFO - File COMI contains 2025 data
2025-05-31 22:46:18,602 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-31 22:46:18,602 - app - INFO - Features shape: (581, 36)
2025-05-31 22:46:18,624 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 22:46:18,625 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:46:18,626 - app - INFO - Data shape: (581, 36)
2025-05-31 22:46:18,626 - app - INFO - File COMI contains 2025 data
2025-05-31 22:46:18,630 - app.utils.memory_management - INFO - Memory before cleanup: 443.34 MB
2025-05-31 22:46:18,788 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 22:46:18,788 - app.utils.memory_management - INFO - Memory after cleanup: 443.38 MB (freed -0.04 MB)
2025-05-31 22:46:18,978 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:19,183 - app.utils.memory_management - INFO - Memory before cleanup: 444.46 MB
2025-05-31 22:46:19,364 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 22:46:19,366 - app.utils.memory_management - INFO - Memory after cleanup: 444.46 MB (freed 0.00 MB)
2025-05-31 22:46:24,013 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:24,048 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:46:24,314 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:46:24,315 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:46:24,316 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:46:24,316 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:46:24,321 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:46:24,324 - app.utils.error_handling - INFO - live_trading_component executed in 0.28 seconds
2025-05-31 22:46:24,329 - app.utils.memory_management - INFO - Memory before cleanup: 446.62 MB
2025-05-31 22:46:24,521 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 22:46:24,521 - app.utils.memory_management - INFO - Memory after cleanup: 446.62 MB (freed 0.00 MB)
2025-05-31 22:46:40,673 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:40,718 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:46:40,723 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:46:40,730 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-413' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:46:40,755 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:46:40,826 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:46:40,828 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:46:40,829 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:46:40,829 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:46:40,836 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:46:40,840 - app.utils.error_handling - INFO - live_trading_component executed in 0.14 seconds
2025-05-31 22:46:40,844 - app.utils.memory_management - INFO - Memory before cleanup: 447.98 MB
2025-05-31 22:46:41,038 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 22:46:41,039 - app.utils.memory_management - INFO - Memory after cleanup: 447.98 MB (freed 0.00 MB)
2025-05-31 22:46:52,721 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:52,759 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:46:52,760 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-31 22:46:52,760 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:46:52,782 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:46:52,783 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:46:52,784 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:46:52,785 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:46:52,795 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:46:52,799 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-05-31 22:46:52,800 - app.utils.memory_management - INFO - Memory before cleanup: 447.86 MB
2025-05-31 22:46:53,020 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-31 22:46:53,022 - app.utils.memory_management - INFO - Memory after cleanup: 447.86 MB (freed 0.00 MB)
2025-05-31 22:47:01,154 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:01,201 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:01,202 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:01,205 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-617' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:47:01,206 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:47:01,252 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:01,252 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:47:01,253 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:01,254 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:47:01,260 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:01,266 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-31 22:47:01,266 - app.utils.memory_management - INFO - Memory before cleanup: 447.95 MB
2025-05-31 22:47:01,488 - app.utils.memory_management - INFO - Garbage collection: collected 221 objects
2025-05-31 22:47:01,489 - app.utils.memory_management - INFO - Memory after cleanup: 447.95 MB (freed 0.00 MB)
2025-05-31 22:47:13,667 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:13,700 - app.utils.memory_management - INFO - Memory before cleanup: 447.88 MB
2025-05-31 22:47:13,925 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-05-31 22:47:13,925 - app.utils.memory_management - INFO - Memory after cleanup: 447.88 MB (freed 0.00 MB)
2025-05-31 22:47:15,441 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:15,475 - app.utils.memory_management - INFO - Memory before cleanup: 447.89 MB
2025-05-31 22:47:15,675 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-31 22:47:15,676 - app.utils.memory_management - INFO - Memory after cleanup: 447.89 MB (freed -0.00 MB)
2025-05-31 22:47:16,751 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:16,783 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:16,784 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:16,786 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:16,793 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,795 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,796 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,796 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:16,803 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:16,804 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:16,811 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,819 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:47:16,829 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 22:47:16,831 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:16,833 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:16,837 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:47:16,845 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,846 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,847 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,848 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:16,851 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:16,853 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:47:16,860 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,863 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,865 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,867 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:16,869 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:16,870 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:16,881 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,882 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,884 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,923 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:47:16,934 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:16,936 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:16,940 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,954 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:47:16,955 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:47:16,956 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:47:16,957 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:47:16,957 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:47:16,958 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:47:16,964 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:47:16,964 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:47:16,965 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:47:16,965 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:47:16,965 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:47:16,966 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:47:16,966 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:47:16,966 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:47:16,967 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:47:16,968 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:47:16,969 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:47:16,969 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:47:16,970 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:47:17,171 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:47:17,220 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:17,221 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,222 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:17,229 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,230 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,231 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:17,238 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:17,241 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:17,251 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,258 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:47:17,258 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:17,259 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:17,259 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:47:17,267 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,268 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,268 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:17,269 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:17,269 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:47:17,275 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,277 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,280 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:17,281 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,281 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:17,288 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,289 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,300 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,307 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:17,307 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:17,307 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:17,309 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,325 - app.utils.memory_management - INFO - Memory before cleanup: 447.95 MB
2025-05-31 22:47:17,503 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 22:47:17,505 - app.utils.memory_management - INFO - Memory after cleanup: 447.95 MB (freed 0.00 MB)
2025-05-31 22:47:29,107 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:29,157 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,157 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:29,159 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,160 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:29,161 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:29,161 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 22:47:29,163 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,164 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:29,164 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,164 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:29,165 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:29,165 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 22:47:29,166 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 22:47:29,170 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:47:29,173 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:29,174 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:29,175 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1051' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:47:29,176 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:47:29,177 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:47:29,213 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 22:47:29,418 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 22:47:29,643 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-31 22:47:29,647 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 22:47:29,647 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 22:47:29,649 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 22:47:29,649 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 22:47:29,649 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 22:47:29,649 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 22:47:29,762 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 22:47:29,763 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 22:47:29,763 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 22:47:29,769 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 22:47:29,770 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7538120603561401
2025-05-31 22:47:29,771 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00889905853015
2025-05-31 22:47:29,781 - app.pages.predictions_consolidated - ERROR - Quick prediction error: 'Close'
2025-05-31 22:47:29,888 - app.pages.predictions_consolidated - ERROR - Traceback: Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Close'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\app\pages\predictions_consolidated.py", line 486, in show_quick_predictions
    current_price = st.session_state.live_data['Close'].iloc[-1]
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'Close'

2025-05-31 22:47:29,916 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:47:29,931 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:29,931 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:29,979 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:29,988 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:47:29,989 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:47:29,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:47:29,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:47:29,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:47:29,993 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:47:29,995 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:47:29,995 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:47:29,995 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:47:29,997 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:47:30,186 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:47:30,229 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:30,230 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,232 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:30,239 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,240 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:30,248 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:30,248 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:30,260 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,267 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:47:30,268 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:30,272 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:30,272 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:47:30,281 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,281 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,282 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:30,282 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:30,283 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:47:30,290 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,291 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,293 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:30,293 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,294 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:30,299 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,300 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,308 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,314 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:30,314 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:30,316 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:30,316 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,333 - app.utils.memory_management - INFO - Memory before cleanup: 456.18 MB
2025-05-31 22:47:30,508 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:47:30,508 - app.utils.memory_management - INFO - Memory after cleanup: 456.18 MB (freed 0.00 MB)
2025-05-31 22:48:23,549 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:23,617 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:23,626 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:23,628 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:23,636 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,648 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:48:23,650 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:23,652 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:23,654 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1346' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:48:23,658 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:48:23,660 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:48:23,665 - app.components.advanced_prediction - ERROR - Error generating predictions: 'Close'
2025-05-31 22:48:23,669 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:23,671 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:23,673 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:23,677 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:23,679 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:23,681 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:23,684 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:23,689 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:23,689 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:23,695 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:23,891 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:23,933 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:23,934 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:23,935 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:23,945 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,946 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:23,947 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:23,954 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:23,957 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:23,968 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,975 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:23,977 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:23,979 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:23,982 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:23,992 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,994 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:23,994 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:23,994 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:23,996 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:24,002 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:24,004 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:24,004 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:24,006 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:24,006 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:24,016 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:24,017 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:24,025 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:24,032 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:24,033 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:24,034 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:24,035 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:24,049 - app.utils.memory_management - INFO - Memory before cleanup: 457.19 MB
2025-05-31 22:48:24,265 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:24,266 - app.utils.memory_management - INFO - Memory after cleanup: 457.15 MB (freed 0.04 MB)
2025-05-31 22:48:38,492 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:38,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:38,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:38,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:38,568 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,577 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:38,577 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:38,578 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:38,578 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:38,578 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:38,579 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:38,581 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:38,583 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:38,583 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:38,584 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:38,781 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:38,821 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:38,823 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,824 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:38,834 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,835 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,836 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:38,841 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:38,842 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:38,848 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,857 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:38,859 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:38,859 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:38,860 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:38,870 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,870 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,871 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:38,871 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:38,872 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:38,885 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,885 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,896 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,903 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:38,904 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:38,904 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:38,905 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,918 - app.utils.memory_management - INFO - Memory before cleanup: 457.12 MB
2025-05-31 22:48:39,089 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:39,089 - app.utils.memory_management - INFO - Memory after cleanup: 457.12 MB (freed 0.00 MB)
2025-05-31 22:48:42,247 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:42,295 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:42,308 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:42,309 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:42,320 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,329 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:42,329 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:42,331 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:42,331 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:42,331 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:42,332 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:42,333 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:42,334 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:42,334 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:42,334 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:42,512 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:42,549 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:42,550 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,551 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:42,558 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,558 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,559 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:42,565 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:42,567 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:42,578 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,584 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:42,585 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:42,585 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:42,586 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:42,591 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,592 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,593 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:42,593 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:42,593 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:42,602 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,606 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,613 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:42,619 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,626 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:42,639 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,645 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,662 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,668 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:42,669 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:42,669 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:42,669 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,680 - app.utils.memory_management - INFO - Memory before cleanup: 457.12 MB
2025-05-31 22:48:42,848 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:42,848 - app.utils.memory_management - INFO - Memory after cleanup: 457.12 MB (freed 0.00 MB)
2025-05-31 22:48:50,981 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:51,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:51,046 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:51,047 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:51,053 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,060 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:51,061 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:51,061 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:51,062 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:51,064 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:51,064 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:51,064 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:51,065 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:51,284 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:51,303 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:48:51,305 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:51,305 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:51,306 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1958' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:48:51,307 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:48:51,406 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:51,407 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,408 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:51,416 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,417 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,417 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:51,424 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:51,425 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:51,431 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,437 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:51,439 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:51,439 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:51,439 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:51,445 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,445 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,447 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:51,447 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:51,447 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:51,452 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,453 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,453 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:51,455 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,456 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:51,461 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,462 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,469 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,476 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:51,477 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:51,477 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:51,478 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,490 - app.utils.memory_management - INFO - Memory before cleanup: 457.19 MB
2025-05-31 22:48:51,661 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:51,661 - app.utils.memory_management - INFO - Memory after cleanup: 457.19 MB (freed 0.00 MB)
2025-05-31 22:48:59,344 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:59,370 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:48:59,371 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-31 22:48:59,372 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:48:59,387 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:59,388 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:48:59,389 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:59,390 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:48:59,397 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:59,400 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 22:48:59,401 - app.utils.memory_management - INFO - Memory before cleanup: 457.12 MB
2025-05-31 22:48:59,597 - app.utils.memory_management - INFO - Garbage collection: collected 328 objects
2025-05-31 22:48:59,598 - app.utils.memory_management - INFO - Memory after cleanup: 457.12 MB (freed 0.00 MB)
2025-05-31 22:49:08,205 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:49:08,235 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:49:08,236 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:49:08,236 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-2193' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:49:08,238 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:49:08,261 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:49:08,261 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:49:08,263 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:49:08,264 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:49:08,270 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:49:08,273 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-05-31 22:49:08,274 - app.utils.memory_management - INFO - Memory before cleanup: 457.18 MB
2025-05-31 22:49:08,522 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-31 22:49:08,522 - app.utils.memory_management - INFO - Memory after cleanup: 457.18 MB (freed 0.00 MB)
2025-05-31 22:50:51,104 - app - INFO - Cleaning up resources...
2025-05-31 22:50:51,105 - app.utils.memory_management - INFO - Memory before cleanup: 456.83 MB
2025-05-31 22:50:51,254 - app.utils.memory_management - INFO - Garbage collection: collected 291 objects
2025-05-31 22:50:51,254 - app.utils.memory_management - INFO - Memory after cleanup: 456.83 MB (freed 0.00 MB)
2025-05-31 22:50:51,255 - app - INFO - Application shutdown complete
2025-05-31 22:51:17,697 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:51:19,003 - app - INFO - Memory management utilities loaded
2025-05-31 22:51:19,005 - app - INFO - Error handling utilities loaded
2025-05-31 22:51:19,005 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:51:19,007 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:51:19,007 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:51:19,007 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 22:51:19,009 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 22:51:19,011 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 22:51:19,014 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 22:51:19,014 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 22:51:19,015 - app - INFO - Applied NumPy fix
2025-05-31 22:51:19,016 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:51:19,016 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:51:19,018 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 22:51:23,256 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 22:51:23,256 - app - INFO - Applied TensorFlow fix
2025-05-31 22:51:23,259 - app.config - INFO - Configuration initialized
2025-05-31 22:51:23,263 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 22:51:23,273 - models.train - INFO - TensorFlow test successful
2025-05-31 22:51:23,756 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 22:51:23,757 - models.train - INFO - Transformer model is available
2025-05-31 22:51:23,757 - models.train - INFO - Using TensorFlow-based models
2025-05-31 22:51:23,758 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 22:51:23,759 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 22:51:23,761 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 22:51:24,081 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:51:24,082 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:51:24,083 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:51:24,083 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:51:24,083 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:51:24,176 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 22:51:24,179 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:24,496 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 22:51:25,135 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 22:51:25,159 - app.utils.session_state - INFO - Initializing session state
2025-05-31 22:51:25,161 - app.utils.session_state - INFO - Session state initialized
2025-05-31 22:51:26,506 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 22:51:26,526 - app.utils.memory_management - INFO - Memory before cleanup: 434.01 MB
2025-05-31 22:51:26,708 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-05-31 22:51:26,709 - app.utils.memory_management - INFO - Memory after cleanup: 434.02 MB (freed -0.01 MB)
2025-05-31 22:51:41,704 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:41,744 - app.utils.memory_management - INFO - Memory before cleanup: 437.91 MB
2025-05-31 22:51:41,966 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:51:41,968 - app.utils.memory_management - INFO - Memory after cleanup: 437.91 MB (freed 0.00 MB)
2025-05-31 22:51:42,941 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:42,994 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-31 22:51:42,995 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:51:42,996 - app - INFO - Data shape: (581, 36)
2025-05-31 22:51:42,996 - app - INFO - File COMI contains 2025 data
2025-05-31 22:51:43,038 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-05-31 22:51:43,039 - app - INFO - Features shape: (581, 36)
2025-05-31 22:51:43,073 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 22:51:43,076 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:51:43,078 - app - INFO - Data shape: (581, 36)
2025-05-31 22:51:43,080 - app - INFO - File COMI contains 2025 data
2025-05-31 22:51:43,091 - app.utils.memory_management - INFO - Memory before cleanup: 442.40 MB
2025-05-31 22:51:43,295 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 22:51:43,295 - app.utils.memory_management - INFO - Memory after cleanup: 442.44 MB (freed -0.04 MB)
2025-05-31 22:51:43,486 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:43,561 - app.utils.memory_management - INFO - Memory before cleanup: 443.46 MB
2025-05-31 22:51:43,735 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 22:51:43,736 - app.utils.memory_management - INFO - Memory after cleanup: 443.46 MB (freed 0.00 MB)
2025-05-31 22:51:46,265 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:46,299 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:51:46,364 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:51:46,368 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:51:46,371 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:51:46,373 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:51:46,379 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:51:46,383 - app.utils.error_handling - INFO - live_trading_component executed in 0.10 seconds
2025-05-31 22:51:46,385 - app.utils.memory_management - INFO - Memory before cleanup: 445.96 MB
2025-05-31 22:51:46,601 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 22:51:46,607 - app.utils.memory_management - INFO - Memory after cleanup: 445.96 MB (freed 0.00 MB)
2025-05-31 22:51:52,367 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:52,399 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:51:52,400 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:51:52,401 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-367' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:51:52,404 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:51:52,437 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:51:52,438 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:51:52,439 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:51:52,439 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:51:52,446 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:51:52,450 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-05-31 22:51:52,455 - app.utils.memory_management - INFO - Memory before cleanup: 447.23 MB
2025-05-31 22:51:52,648 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 22:51:52,649 - app.utils.memory_management - INFO - Memory after cleanup: 447.23 MB (freed 0.00 MB)
2025-05-31 22:52:50,999 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:52:51,057 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:51,060 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:51,063 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-537' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:52:51,065 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:52:51,097 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:52:51,108 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:52:51,110 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:52:51,114 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:52:51,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:52:51,128 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-31 22:52:51,130 - app.utils.memory_management - INFO - Memory before cleanup: 447.21 MB
2025-05-31 22:52:51,420 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-31 22:52:51,421 - app.utils.memory_management - INFO - Memory after cleanup: 447.21 MB (freed 0.00 MB)
2025-05-31 22:52:53,427 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:52:53,464 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:53,464 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:53,465 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-622' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:52:53,466 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:52:53,493 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:52:53,493 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:52:53,494 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:52:53,495 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:52:53,506 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:52:53,512 - app.utils.error_handling - INFO - live_trading_component executed in 0.07 seconds
2025-05-31 22:52:53,512 - app.utils.memory_management - INFO - Memory before cleanup: 447.21 MB
2025-05-31 22:52:53,707 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-31 22:52:53,708 - app.utils.memory_management - INFO - Memory after cleanup: 447.21 MB (freed 0.00 MB)
2025-05-31 22:52:55,795 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:52:55,833 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:55,833 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:55,834 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-711' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:52:55,835 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:52:55,862 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:52:55,863 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:52:55,863 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:52:55,864 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:52:55,871 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:52:55,875 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-05-31 22:52:55,876 - app.utils.memory_management - INFO - Memory before cleanup: 447.22 MB
2025-05-31 22:52:56,108 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-31 22:52:56,112 - app.utils.memory_management - INFO - Memory after cleanup: 447.22 MB (freed 0.00 MB)
2025-05-31 22:58:15,081 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:58:15,087 - app - INFO - Memory management utilities loaded
2025-05-31 22:58:15,088 - app - INFO - Error handling utilities loaded
2025-05-31 22:58:15,090 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:58:15,091 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:58:15,091 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:58:15,091 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
