2025-05-31 12:04:01,831 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-31 12:04:07,503 - app - INFO - Memory management utilities loaded
2025-05-31 12:04:07,516 - app - INFO - Error handling utilities loaded
2025-05-31 12:04:07,523 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 12:04:07,525 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 12:04:07,527 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 12:04:07,529 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 12:04:07,544 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 12:04:07,558 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 12:04:07,560 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 12:04:07,564 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 12:04:07,565 - app - INFO - Applied NumPy fix
2025-05-31 12:04:07,570 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 12:04:07,575 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 12:04:07,576 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 12:04:07,577 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 12:04:07,578 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 12:04:07,578 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 12:04:07,579 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 12:04:07,579 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 12:04:26,024 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 12:04:26,024 - app - INFO - Applied TensorFlow fix
2025-05-31 12:04:26,039 - app.config - INFO - Configuration initialized
2025-05-31 12:04:26,057 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 12:04:26,356 - models.train - INFO - TensorFlow test successful
2025-05-31 12:04:30,823 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 12:04:30,823 - models.train - INFO - Transformer model is available
2025-05-31 12:04:30,824 - models.train - INFO - Using TensorFlow-based models
2025-05-31 12:04:30,828 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 12:04:30,829 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 12:04:30,870 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 12:04:32,519 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 12:04:32,520 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 12:04:32,520 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 12:04:32,521 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 12:04:32,894 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 12:04:32,917 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:33,697 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 12:04:38,120 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 12:04:38,194 - app.utils.session_state - INFO - Initializing session state
2025-05-31 12:04:38,195 - app.utils.session_state - INFO - Session state initialized
2025-05-31 12:04:39,386 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 12:04:39,395 - app.utils.memory_management - INFO - Memory before cleanup: 425.38 MB
2025-05-31 12:04:39,517 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 12:04:39,517 - app.utils.memory_management - INFO - Memory after cleanup: 425.77 MB (freed -0.39 MB)
2025-05-31 12:04:54,007 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:54,054 - app.utils.memory_management - INFO - Memory before cleanup: 429.62 MB
2025-05-31 12:04:54,204 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 12:04:54,206 - app.utils.memory_management - INFO - Memory after cleanup: 429.62 MB (freed -0.00 MB)
2025-05-31 12:04:55,205 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:55,369 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.15 seconds
2025-05-31 12:04:55,383 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 12:04:55,383 - app - INFO - Data shape: (581, 36)
2025-05-31 12:04:55,383 - app - INFO - File COMI contains 2025 data
2025-05-31 12:04:55,419 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-31 12:04:55,419 - app - INFO - Features shape: (581, 36)
2025-05-31 12:04:55,439 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 12:04:55,439 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 12:04:55,440 - app - INFO - Data shape: (581, 36)
2025-05-31 12:04:55,440 - app - INFO - File COMI contains 2025 data
2025-05-31 12:04:55,444 - app.utils.memory_management - INFO - Memory before cleanup: 433.95 MB
2025-05-31 12:04:55,570 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 12:04:55,570 - app.utils.memory_management - INFO - Memory after cleanup: 433.99 MB (freed -0.04 MB)
2025-05-31 12:04:55,717 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:04:55,877 - app.utils.memory_management - INFO - Memory before cleanup: 435.07 MB
2025-05-31 12:04:55,992 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 12:04:55,992 - app.utils.memory_management - INFO - Memory after cleanup: 435.07 MB (freed 0.00 MB)
2025-05-31 12:05:56,515 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:05:56,873 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-31 12:05:58,409 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-31 12:05:58,438 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.03 seconds
2025-05-31 12:05:58,439 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-31 12:05:58,439 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-31 12:05:58,439 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-31 12:05:59,085 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.68 seconds
2025-05-31 12:05:59,407 - app.utils.memory_management - INFO - Memory before cleanup: 445.65 MB
2025-05-31 12:05:59,547 - app.utils.memory_management - INFO - Garbage collection: collected 3399 objects
2025-05-31 12:05:59,550 - app.utils.memory_management - INFO - Memory after cleanup: 445.65 MB (freed 0.00 MB)
2025-05-31 12:08:12,730 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:08:13,070 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-31 12:08:13,395 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-31 12:08:13,405 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-31 12:08:13,405 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-31 12:08:13,407 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-31 12:08:13,409 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-31 12:08:13,709 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.31 seconds
2025-05-31 12:08:13,982 - app.utils.memory_management - INFO - Memory before cleanup: 447.39 MB
2025-05-31 12:08:14,175 - app.utils.memory_management - INFO - Garbage collection: collected 2097 objects
2025-05-31 12:08:14,178 - app.utils.memory_management - INFO - Memory after cleanup: 447.39 MB (freed 0.00 MB)
2025-05-31 12:08:51,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:08:51,374 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-31 12:08:52,376 - app.utils.memory_management - INFO - Memory before cleanup: 456.88 MB
2025-05-31 12:08:52,511 - app.utils.memory_management - INFO - Garbage collection: collected 593 objects
2025-05-31 12:08:52,512 - app.utils.memory_management - INFO - Memory after cleanup: 456.88 MB (freed 0.00 MB)
2025-05-31 12:09:28,779 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:09:29,168 - app.utils.memory_management - INFO - Memory before cleanup: 460.50 MB
2025-05-31 12:09:29,282 - app.utils.memory_management - INFO - Garbage collection: collected 385 objects
2025-05-31 12:09:29,282 - app.utils.memory_management - INFO - Memory after cleanup: 460.50 MB (freed 0.00 MB)
2025-05-31 12:09:48,930 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:09:48,942 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 12:09:48,954 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-31 12:09:50,276 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-31 12:09:50,546 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 12:09:50,546 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 12:09:50,547 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 12:09:50,547 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 12:09:50,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 12:09:50,554 - app.utils.error_handling - INFO - live_trading_component executed in 1.61 seconds
2025-05-31 12:09:50,557 - app.utils.memory_management - INFO - Memory before cleanup: 462.63 MB
2025-05-31 12:09:50,676 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-05-31 12:09:50,676 - app.utils.memory_management - INFO - Memory after cleanup: 462.63 MB (freed 0.00 MB)
2025-05-31 12:10:35,474 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:10:35,534 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 12:10:35,534 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 12:10:35,535 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 12:10:35,535 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 12:10:35,541 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 12:10:35,544 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 12:10:35,545 - app.utils.memory_management - INFO - Memory before cleanup: 463.67 MB
2025-05-31 12:10:35,708 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-31 12:10:35,709 - app.utils.memory_management - INFO - Memory after cleanup: 463.67 MB (freed 0.00 MB)
2025-05-31 12:10:51,421 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:10:51,504 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-31 12:10:51,515 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-31 12:10:51,525 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-31 12:10:51,535 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 10.2 hours of target)
2025-05-31 12:10:51,546 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 10.0 hours of target)
2025-05-31 12:10:51,556 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.7 hours of target)
2025-05-31 12:10:51,566 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.2 hours of target)
2025-05-31 12:10:51,577 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.7 hours of target)
2025-05-31 12:10:51,586 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 9.2 hours of target)
2025-05-31 12:10:51,593 - app.components.performance_metrics - INFO - Found price 81.4 for COMI at 2025-05-28 00:00:00 (within 8.5 hours of target)
2025-05-31 12:10:51,593 - app.components.performance_metrics - INFO - Target time 2025-05-29 12:41:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,612 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:11:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,612 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:31:47.823527 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,634 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:33:26.243941 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,643 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:46:49.828351 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:10:51,693 - app.components.performance_metrics - INFO - Updated performance metrics for 7 model-symbol combinations
2025-05-31 12:10:51,841 - app.utils.memory_management - INFO - Memory before cleanup: 466.03 MB
2025-05-31 12:10:52,007 - app.utils.memory_management - INFO - Garbage collection: collected 1609 objects
2025-05-31 12:10:52,007 - app.utils.memory_management - INFO - Memory after cleanup: 466.03 MB (freed 0.00 MB)
2025-05-31 12:11:20,123 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:20,150 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-31 12:11:20,162 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-31 12:11:20,173 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-31 12:11:20,181 - app.components.performance_metrics - INFO - Target time 2025-05-29 12:41:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,191 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:11:45.387724 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,199 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:31:47.823527 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,213 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:33:26.243941 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,223 - app.components.performance_metrics - INFO - Target time 2025-05-29 13:46:49.828351 is in the future compared to latest data 2025-05-29 00:00:00
2025-05-31 12:11:20,388 - app.utils.memory_management - INFO - Memory before cleanup: 467.45 MB
2025-05-31 12:11:20,512 - app.utils.memory_management - INFO - Garbage collection: collected 1605 objects
2025-05-31 12:11:20,512 - app.utils.memory_management - INFO - Memory after cleanup: 467.45 MB (freed 0.00 MB)
2025-05-31 12:11:21,499 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:21,511 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:21,512 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:21,512 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:21,513 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:21,524 - app.utils.memory_management - INFO - Memory before cleanup: 467.44 MB
2025-05-31 12:11:21,644 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-05-31 12:11:21,645 - app.utils.memory_management - INFO - Memory after cleanup: 467.44 MB (freed 0.00 MB)
2025-05-31 12:11:31,623 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:31,633 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:31,634 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:31,634 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:31,634 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:31,650 - app.utils.memory_management - INFO - Memory before cleanup: 467.44 MB
2025-05-31 12:11:31,765 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-31 12:11:31,765 - app.utils.memory_management - INFO - Memory after cleanup: 467.44 MB (freed 0.00 MB)
2025-05-31 12:11:45,082 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:45,091 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:45,091 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:45,092 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:45,093 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:45,107 - app.utils.memory_management - INFO - Memory before cleanup: 467.44 MB
2025-05-31 12:11:45,233 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-31 12:11:45,233 - app.utils.memory_management - INFO - Memory after cleanup: 467.44 MB (freed 0.00 MB)
2025-05-31 12:11:57,419 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:57,432 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:57,433 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:57,433 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:57,434 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:57,445 - app.utils.memory_management - INFO - Memory before cleanup: 467.42 MB
2025-05-31 12:11:57,557 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-31 12:11:57,557 - app.utils.memory_management - INFO - Memory after cleanup: 467.42 MB (freed 0.00 MB)
2025-05-31 12:11:58,510 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:11:58,527 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:11:58,527 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:11:58,527 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:11:58,528 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:11:58,544 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 12:11:58,545 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 12:11:58,547 - app.utils.backtesting - INFO - Created model instance of type ensemble
2025-05-31 12:11:58,548 - app.utils.backtesting - INFO - Loading model for COMI with horizon 1
2025-05-31 12:11:58,548 - app.utils.backtesting - INFO - Looking in path: saved_models
2025-05-31 12:11:58,551 - app.utils.backtesting - INFO - Found matching model files: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-31 12:11:58,552 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_1min.joblib
2025-05-31 12:11:58,552 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_1min.joblib
2025-05-31 12:11:58,620 - app.utils.backtesting - INFO - Model loaded successfully: RandomForestRegressor
2025-05-31 12:11:58,620 - app.utils.backtesting - INFO - Starting backtest with test period: 30 days
2025-05-31 12:11:58,620 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-05-31 12:11:58,633 - app.utils.backtesting - INFO - Added technical indicators. New shape: (581, 36)
2025-05-31 12:11:58,641 - app.utils.backtesting - INFO - Prepared features. Final shape: (581, 36)
2025-05-31 12:11:58,641 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 5 features
2025-05-31 12:11:58,641 - app.utils.backtesting - INFO - Model type detection: sklearn=True, hybrid=False
2025-05-31 12:11:58,659 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,659 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,674 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,676 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,679 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,679 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,681 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,682 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,683 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,683 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,683 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,683 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,683 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,683 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,692 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,692 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,692 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,692 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,698 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,699 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,700 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,701 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,702 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,703 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,704 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,704 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,707 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,708 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,710 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,710 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,712 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,713 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,714 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,715 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,716 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,717 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,719 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,719 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,721 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,721 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,723 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,723 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,726 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,727 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,729 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,730 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,732 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,733 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,734 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: Found array with dim 3. RandomForestRegressor expected <= 2.
2025-05-31 12:11:58,734 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: X has 300 features, but RandomForestRegressor is expecting 420 features as input.
2025-05-31 12:11:58,735 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-05-31 12:11:58,736 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-05-31 12:11:58,737 - app.utils.backtesting - INFO - Backtest completed with 5 results
2025-05-31 12:11:58,777 - app.utils.memory_management - INFO - Memory before cleanup: 472.20 MB
2025-05-31 12:11:58,892 - app.utils.memory_management - INFO - Garbage collection: collected 1103 objects
2025-05-31 12:11:58,892 - app.utils.memory_management - INFO - Memory after cleanup: 472.20 MB (freed 0.00 MB)
2025-05-31 12:12:08,867 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:12:08,877 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-31 12:12:08,877 - app.utils.backtesting - INFO - Historical data shape: (581, 36)
2025-05-31 12:12:08,877 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-31 12:12:08,878 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-29 00:00:00
2025-05-31 12:12:08,909 - app.utils.memory_management - INFO - Memory before cleanup: 472.30 MB
2025-05-31 12:12:09,068 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-05-31 12:12:09,068 - app.utils.memory_management - INFO - Memory after cleanup: 472.30 MB (freed 0.00 MB)
2025-05-31 12:12:18,432 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 12:12:18,454 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-31 12:12:18,454 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 12:12:18,454 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-31 12:12:18,454 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-31 12:12:19,297 - app.utils.memory_management - INFO - Memory before cleanup: 478.71 MB
2025-05-31 12:12:19,412 - app.utils.memory_management - INFO - Garbage collection: collected 2606 objects
2025-05-31 12:12:19,412 - app.utils.memory_management - INFO - Memory after cleanup: 478.71 MB (freed 0.00 MB)
2025-05-31 22:45:55,208 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:45:56,618 - app - INFO - Memory management utilities loaded
2025-05-31 22:45:56,620 - app - INFO - Error handling utilities loaded
2025-05-31 22:45:56,621 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:45:56,623 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:45:56,623 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:45:56,624 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 22:45:56,625 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 22:45:56,627 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 22:45:56,629 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 22:45:56,630 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 22:45:56,631 - app - INFO - Applied NumPy fix
2025-05-31 22:45:56,635 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:45:56,635 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:45:56,641 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:45:56,641 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 22:45:56,642 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:45:56,642 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:45:56,642 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:45:56,642 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 22:46:00,963 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 22:46:00,963 - app - INFO - Applied TensorFlow fix
2025-05-31 22:46:00,966 - app.config - INFO - Configuration initialized
2025-05-31 22:46:00,971 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 22:46:01,420 - models.train - INFO - TensorFlow test successful
2025-05-31 22:46:04,231 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 22:46:04,232 - models.train - INFO - Transformer model is available
2025-05-31 22:46:04,232 - models.train - INFO - Using TensorFlow-based models
2025-05-31 22:46:04,233 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 22:46:04,234 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 22:46:04,236 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 22:46:04,756 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:46:04,757 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:46:04,757 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:46:04,757 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:46:04,757 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:46:04,759 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:46:04,760 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:46:04,862 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 22:46:04,864 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:05,195 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 22:46:06,358 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 22:46:06,388 - app.utils.session_state - INFO - Initializing session state
2025-05-31 22:46:06,390 - app.utils.session_state - INFO - Session state initialized
2025-05-31 22:46:07,897 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 22:46:07,928 - app.utils.memory_management - INFO - Memory before cleanup: 435.18 MB
2025-05-31 22:46:08,206 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-05-31 22:46:08,208 - app.utils.memory_management - INFO - Memory after cleanup: 435.25 MB (freed -0.07 MB)
2025-05-31 22:46:16,766 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:16,799 - app.utils.memory_management - INFO - Memory before cleanup: 439.02 MB
2025-05-31 22:46:16,978 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:46:16,978 - app.utils.memory_management - INFO - Memory after cleanup: 439.02 MB (freed 0.00 MB)
2025-05-31 22:46:18,377 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:18,539 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-05-31 22:46:18,547 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:46:18,547 - app - INFO - Data shape: (581, 36)
2025-05-31 22:46:18,547 - app - INFO - File COMI contains 2025 data
2025-05-31 22:46:18,602 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-31 22:46:18,602 - app - INFO - Features shape: (581, 36)
2025-05-31 22:46:18,624 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 22:46:18,625 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:46:18,626 - app - INFO - Data shape: (581, 36)
2025-05-31 22:46:18,626 - app - INFO - File COMI contains 2025 data
2025-05-31 22:46:18,630 - app.utils.memory_management - INFO - Memory before cleanup: 443.34 MB
2025-05-31 22:46:18,788 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 22:46:18,788 - app.utils.memory_management - INFO - Memory after cleanup: 443.38 MB (freed -0.04 MB)
2025-05-31 22:46:18,978 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:19,183 - app.utils.memory_management - INFO - Memory before cleanup: 444.46 MB
2025-05-31 22:46:19,364 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 22:46:19,366 - app.utils.memory_management - INFO - Memory after cleanup: 444.46 MB (freed 0.00 MB)
2025-05-31 22:46:24,013 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:24,048 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:46:24,314 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:46:24,315 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:46:24,316 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:46:24,316 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:46:24,321 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:46:24,324 - app.utils.error_handling - INFO - live_trading_component executed in 0.28 seconds
2025-05-31 22:46:24,329 - app.utils.memory_management - INFO - Memory before cleanup: 446.62 MB
2025-05-31 22:46:24,521 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 22:46:24,521 - app.utils.memory_management - INFO - Memory after cleanup: 446.62 MB (freed 0.00 MB)
2025-05-31 22:46:40,673 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:40,718 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:46:40,723 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:46:40,730 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-413' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:46:40,755 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:46:40,826 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:46:40,828 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:46:40,829 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:46:40,829 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:46:40,836 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:46:40,840 - app.utils.error_handling - INFO - live_trading_component executed in 0.14 seconds
2025-05-31 22:46:40,844 - app.utils.memory_management - INFO - Memory before cleanup: 447.98 MB
2025-05-31 22:46:41,038 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 22:46:41,039 - app.utils.memory_management - INFO - Memory after cleanup: 447.98 MB (freed 0.00 MB)
2025-05-31 22:46:52,721 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:46:52,759 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:46:52,760 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-31 22:46:52,760 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:46:52,782 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:46:52,783 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:46:52,784 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:46:52,785 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:46:52,795 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:46:52,799 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-05-31 22:46:52,800 - app.utils.memory_management - INFO - Memory before cleanup: 447.86 MB
2025-05-31 22:46:53,020 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-31 22:46:53,022 - app.utils.memory_management - INFO - Memory after cleanup: 447.86 MB (freed 0.00 MB)
2025-05-31 22:47:01,154 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:01,201 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:01,202 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:01,205 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-617' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:47:01,206 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:47:01,252 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:01,252 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:47:01,253 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:01,254 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:47:01,260 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:01,266 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-31 22:47:01,266 - app.utils.memory_management - INFO - Memory before cleanup: 447.95 MB
2025-05-31 22:47:01,488 - app.utils.memory_management - INFO - Garbage collection: collected 221 objects
2025-05-31 22:47:01,489 - app.utils.memory_management - INFO - Memory after cleanup: 447.95 MB (freed 0.00 MB)
2025-05-31 22:47:13,667 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:13,700 - app.utils.memory_management - INFO - Memory before cleanup: 447.88 MB
2025-05-31 22:47:13,925 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-05-31 22:47:13,925 - app.utils.memory_management - INFO - Memory after cleanup: 447.88 MB (freed 0.00 MB)
2025-05-31 22:47:15,441 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:15,475 - app.utils.memory_management - INFO - Memory before cleanup: 447.89 MB
2025-05-31 22:47:15,675 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-05-31 22:47:15,676 - app.utils.memory_management - INFO - Memory after cleanup: 447.89 MB (freed -0.00 MB)
2025-05-31 22:47:16,751 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:16,783 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:16,784 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:16,786 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:16,793 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,795 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,796 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,796 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:16,803 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:16,804 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:16,811 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,819 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:47:16,829 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 22:47:16,831 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:16,833 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:16,837 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:47:16,845 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,846 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,847 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,848 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:16,851 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:16,853 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:47:16,860 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,863 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,865 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,867 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:16,869 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:16,870 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:16,881 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,882 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:16,884 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 22:47:16,923 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:47:16,934 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:16,936 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:16,940 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:16,954 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:47:16,955 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:47:16,956 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:47:16,957 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:47:16,957 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:47:16,958 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:47:16,964 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:47:16,964 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:47:16,965 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:47:16,965 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:47:16,965 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:47:16,966 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:47:16,966 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:47:16,966 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:47:16,967 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:47:16,968 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:47:16,969 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:47:16,969 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:47:16,970 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:47:17,171 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:47:17,220 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:17,221 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,222 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:17,229 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,230 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,231 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:17,238 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:17,241 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:17,251 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,258 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:47:17,258 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:17,259 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:17,259 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:47:17,267 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,268 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,268 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:17,269 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:17,269 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:47:17,275 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,277 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,280 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:17,281 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,281 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:17,288 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:17,289 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:17,300 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,307 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:17,307 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:17,307 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:17,309 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:17,325 - app.utils.memory_management - INFO - Memory before cleanup: 447.95 MB
2025-05-31 22:47:17,503 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 22:47:17,505 - app.utils.memory_management - INFO - Memory after cleanup: 447.95 MB (freed 0.00 MB)
2025-05-31 22:47:29,107 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:47:29,157 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,157 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:29,159 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,160 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:29,161 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:29,161 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 22:47:29,163 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,164 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:29,164 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:29,164 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:29,165 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:29,165 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 22:47:29,166 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 22:47:29,170 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:47:29,173 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:29,174 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:47:29,175 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1051' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:47:29,176 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:47:29,177 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:47:29,213 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 22:47:29,418 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 22:47:29,643 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-31 22:47:29,647 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 22:47:29,647 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 22:47:29,649 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 22:47:29,649 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 22:47:29,649 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 22:47:29,649 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 22:47:29,762 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 22:47:29,763 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 22:47:29,763 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 22:47:29,769 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 22:47:29,770 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7538120603561401
2025-05-31 22:47:29,771 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00889905853015
2025-05-31 22:47:29,781 - app.pages.predictions_consolidated - ERROR - Quick prediction error: 'Close'
2025-05-31 22:47:29,888 - app.pages.predictions_consolidated - ERROR - Traceback: Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Close'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\app\pages\predictions_consolidated.py", line 486, in show_quick_predictions
    current_price = st.session_state.live_data['Close'].iloc[-1]
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'Close'

2025-05-31 22:47:29,916 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:47:29,931 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:29,931 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:29,979 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:29,988 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:47:29,989 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:47:29,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:47:29,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:47:29,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:47:29,993 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:47:29,995 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:47:29,995 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:47:29,995 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:47:29,997 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:47:30,186 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:47:30,229 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:30,230 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,232 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:30,239 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,240 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:47:30,248 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:30,248 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:47:30,260 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,267 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:47:30,268 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:47:30,272 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:30,272 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:47:30,281 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,281 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,282 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:47:30,282 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:30,283 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:47:30,290 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,291 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,293 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:47:30,293 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,294 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:47:30,299 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:47:30,300 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:47:30,308 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,314 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:47:30,314 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:47:30,316 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:47:30,316 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:47:30,333 - app.utils.memory_management - INFO - Memory before cleanup: 456.18 MB
2025-05-31 22:47:30,508 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:47:30,508 - app.utils.memory_management - INFO - Memory after cleanup: 456.18 MB (freed 0.00 MB)
2025-05-31 22:48:23,549 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:23,617 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:23,626 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:23,628 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:23,636 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,648 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:48:23,650 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:23,652 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:23,654 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1346' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:48:23,658 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:48:23,660 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:48:23,665 - app.components.advanced_prediction - ERROR - Error generating predictions: 'Close'
2025-05-31 22:48:23,669 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:23,671 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:23,673 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:23,677 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:23,679 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:23,681 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:23,684 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:23,689 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:23,689 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:23,695 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:23,891 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:23,933 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:23,934 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:23,935 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:23,945 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,946 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:23,947 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:23,954 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:23,957 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:23,968 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,975 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:23,977 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:23,979 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:23,982 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:23,992 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:23,994 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:23,994 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:23,994 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:23,996 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:24,002 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:24,004 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:24,004 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:24,006 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:24,006 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:24,016 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:24,017 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:24,025 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:24,032 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:24,033 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:24,034 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:24,035 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:24,049 - app.utils.memory_management - INFO - Memory before cleanup: 457.19 MB
2025-05-31 22:48:24,265 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:24,266 - app.utils.memory_management - INFO - Memory after cleanup: 457.15 MB (freed 0.04 MB)
2025-05-31 22:48:38,492 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:38,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:38,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:38,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:38,568 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,577 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:38,577 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:38,578 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:38,578 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:38,578 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:38,579 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:38,581 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:38,583 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:38,583 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:38,584 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:38,781 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:38,821 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:38,823 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,824 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:38,834 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,835 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,836 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:38,841 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:38,842 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:38,848 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,857 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:38,859 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:38,859 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:38,860 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:38,870 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,870 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,871 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:38,871 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:38,872 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,876 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:38,885 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:38,885 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:38,896 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,903 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:38,904 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:38,904 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:38,905 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:38,918 - app.utils.memory_management - INFO - Memory before cleanup: 457.12 MB
2025-05-31 22:48:39,089 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:39,089 - app.utils.memory_management - INFO - Memory after cleanup: 457.12 MB (freed 0.00 MB)
2025-05-31 22:48:42,247 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:42,295 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:42,308 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:42,309 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:42,320 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,329 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:42,329 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:42,331 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:42,331 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:42,331 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:42,332 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:42,333 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:42,334 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:42,334 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:42,334 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:42,512 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:42,549 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:42,550 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,551 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:42,558 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,558 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,559 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:42,565 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:42,567 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:42,578 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,584 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:42,585 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:42,585 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:42,586 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:42,591 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,592 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,593 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:42,593 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:42,593 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:42,602 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,606 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,613 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:42,619 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,626 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:42,639 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:42,645 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:42,662 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,668 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:42,669 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:42,669 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:42,669 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:42,680 - app.utils.memory_management - INFO - Memory before cleanup: 457.12 MB
2025-05-31 22:48:42,848 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:42,848 - app.utils.memory_management - INFO - Memory after cleanup: 457.12 MB (freed 0.00 MB)
2025-05-31 22:48:50,981 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:51,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 22:48:51,046 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:51,047 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:51,053 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,060 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 22:48:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 22:48:51,060 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 22:48:51,061 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 22:48:51,061 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 22:48:51,062 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 22:48:51,064 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 22:48:51,064 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 22:48:51,064 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 22:48:51,065 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 22:48:51,284 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 22:48:51,303 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:48:51,305 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:51,305 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:48:51,306 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1958' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:48:51,307 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:48:51,406 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:51,407 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,408 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:51,416 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,417 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,417 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:51,424 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:51,425 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 22:48:51,431 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,437 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 22:48:51,439 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 22:48:51,439 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:51,439 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 22:48:51,445 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,445 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,447 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 22:48:51,447 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:51,447 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 22:48:51,452 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,453 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,453 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:51,455 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,456 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:51,461 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 22:48:51,462 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 22:48:51,469 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,476 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 22:48:51,477 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 22:48:51,477 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 22:48:51,478 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 22:48:51,490 - app.utils.memory_management - INFO - Memory before cleanup: 457.19 MB
2025-05-31 22:48:51,661 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:48:51,661 - app.utils.memory_management - INFO - Memory after cleanup: 457.19 MB (freed 0.00 MB)
2025-05-31 22:48:59,344 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:48:59,370 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 22:48:59,371 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-31 22:48:59,372 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:48:59,387 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:48:59,388 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:48:59,389 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:48:59,390 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:48:59,397 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:48:59,400 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 22:48:59,401 - app.utils.memory_management - INFO - Memory before cleanup: 457.12 MB
2025-05-31 22:48:59,597 - app.utils.memory_management - INFO - Garbage collection: collected 328 objects
2025-05-31 22:48:59,598 - app.utils.memory_management - INFO - Memory after cleanup: 457.12 MB (freed 0.00 MB)
2025-05-31 22:49:08,205 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:49:08,235 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:49:08,236 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:49:08,236 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-2193' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:49:08,238 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:49:08,261 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:49:08,261 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:49:08,263 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:49:08,264 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:49:08,270 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:49:08,273 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-05-31 22:49:08,274 - app.utils.memory_management - INFO - Memory before cleanup: 457.18 MB
2025-05-31 22:49:08,522 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-05-31 22:49:08,522 - app.utils.memory_management - INFO - Memory after cleanup: 457.18 MB (freed 0.00 MB)
2025-05-31 22:50:51,104 - app - INFO - Cleaning up resources...
2025-05-31 22:50:51,105 - app.utils.memory_management - INFO - Memory before cleanup: 456.83 MB
2025-05-31 22:50:51,254 - app.utils.memory_management - INFO - Garbage collection: collected 291 objects
2025-05-31 22:50:51,254 - app.utils.memory_management - INFO - Memory after cleanup: 456.83 MB (freed 0.00 MB)
2025-05-31 22:50:51,255 - app - INFO - Application shutdown complete
2025-05-31 22:51:17,697 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:51:19,003 - app - INFO - Memory management utilities loaded
2025-05-31 22:51:19,005 - app - INFO - Error handling utilities loaded
2025-05-31 22:51:19,005 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:51:19,007 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:51:19,007 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:51:19,007 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 22:51:19,009 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 22:51:19,011 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 22:51:19,014 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 22:51:19,014 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 22:51:19,015 - app - INFO - Applied NumPy fix
2025-05-31 22:51:19,016 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:51:19,016 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:51:19,017 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:51:19,018 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 22:51:23,256 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 22:51:23,256 - app - INFO - Applied TensorFlow fix
2025-05-31 22:51:23,259 - app.config - INFO - Configuration initialized
2025-05-31 22:51:23,263 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 22:51:23,273 - models.train - INFO - TensorFlow test successful
2025-05-31 22:51:23,756 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 22:51:23,757 - models.train - INFO - Transformer model is available
2025-05-31 22:51:23,757 - models.train - INFO - Using TensorFlow-based models
2025-05-31 22:51:23,758 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 22:51:23,759 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 22:51:23,761 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 22:51:24,081 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:51:24,082 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:51:24,083 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:51:24,083 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:51:24,083 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:51:24,084 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:51:24,176 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 22:51:24,179 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:24,496 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 22:51:25,135 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 22:51:25,159 - app.utils.session_state - INFO - Initializing session state
2025-05-31 22:51:25,161 - app.utils.session_state - INFO - Session state initialized
2025-05-31 22:51:26,506 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 22:51:26,526 - app.utils.memory_management - INFO - Memory before cleanup: 434.01 MB
2025-05-31 22:51:26,708 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-05-31 22:51:26,709 - app.utils.memory_management - INFO - Memory after cleanup: 434.02 MB (freed -0.01 MB)
2025-05-31 22:51:41,704 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:41,744 - app.utils.memory_management - INFO - Memory before cleanup: 437.91 MB
2025-05-31 22:51:41,966 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:51:41,968 - app.utils.memory_management - INFO - Memory after cleanup: 437.91 MB (freed 0.00 MB)
2025-05-31 22:51:42,941 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:42,994 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-31 22:51:42,995 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:51:42,996 - app - INFO - Data shape: (581, 36)
2025-05-31 22:51:42,996 - app - INFO - File COMI contains 2025 data
2025-05-31 22:51:43,038 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-05-31 22:51:43,039 - app - INFO - Features shape: (581, 36)
2025-05-31 22:51:43,073 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 22:51:43,076 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:51:43,078 - app - INFO - Data shape: (581, 36)
2025-05-31 22:51:43,080 - app - INFO - File COMI contains 2025 data
2025-05-31 22:51:43,091 - app.utils.memory_management - INFO - Memory before cleanup: 442.40 MB
2025-05-31 22:51:43,295 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 22:51:43,295 - app.utils.memory_management - INFO - Memory after cleanup: 442.44 MB (freed -0.04 MB)
2025-05-31 22:51:43,486 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:43,561 - app.utils.memory_management - INFO - Memory before cleanup: 443.46 MB
2025-05-31 22:51:43,735 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 22:51:43,736 - app.utils.memory_management - INFO - Memory after cleanup: 443.46 MB (freed 0.00 MB)
2025-05-31 22:51:46,265 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:46,299 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:51:46,364 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:51:46,368 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:51:46,371 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:51:46,373 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:51:46,379 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:51:46,383 - app.utils.error_handling - INFO - live_trading_component executed in 0.10 seconds
2025-05-31 22:51:46,385 - app.utils.memory_management - INFO - Memory before cleanup: 445.96 MB
2025-05-31 22:51:46,601 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 22:51:46,607 - app.utils.memory_management - INFO - Memory after cleanup: 445.96 MB (freed 0.00 MB)
2025-05-31 22:51:52,367 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:51:52,399 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:51:52,400 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:51:52,401 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-367' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:51:52,404 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:51:52,437 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:51:52,438 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:51:52,439 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:51:52,439 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:51:52,446 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:51:52,450 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-05-31 22:51:52,455 - app.utils.memory_management - INFO - Memory before cleanup: 447.23 MB
2025-05-31 22:51:52,648 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 22:51:52,649 - app.utils.memory_management - INFO - Memory after cleanup: 447.23 MB (freed 0.00 MB)
2025-05-31 22:52:50,999 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:52:51,057 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:51,060 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:51,063 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-537' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:52:51,065 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:52:51,097 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:52:51,108 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:52:51,110 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:52:51,114 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:52:51,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:52:51,128 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-31 22:52:51,130 - app.utils.memory_management - INFO - Memory before cleanup: 447.21 MB
2025-05-31 22:52:51,420 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-31 22:52:51,421 - app.utils.memory_management - INFO - Memory after cleanup: 447.21 MB (freed 0.00 MB)
2025-05-31 22:52:53,427 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:52:53,464 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:53,464 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:53,465 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-622' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:52:53,466 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:52:53,493 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:52:53,493 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:52:53,494 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:52:53,495 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:52:53,506 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:52:53,512 - app.utils.error_handling - INFO - live_trading_component executed in 0.07 seconds
2025-05-31 22:52:53,512 - app.utils.memory_management - INFO - Memory before cleanup: 447.21 MB
2025-05-31 22:52:53,707 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-31 22:52:53,708 - app.utils.memory_management - INFO - Memory after cleanup: 447.21 MB (freed 0.00 MB)
2025-05-31 22:52:55,795 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:52:55,833 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:55,833 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:52:55,834 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-711' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:52:55,835 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:52:55,862 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:52:55,863 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:52:55,863 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:52:55,864 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:52:55,871 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:52:55,875 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-05-31 22:52:55,876 - app.utils.memory_management - INFO - Memory before cleanup: 447.22 MB
2025-05-31 22:52:56,108 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-31 22:52:56,112 - app.utils.memory_management - INFO - Memory after cleanup: 447.22 MB (freed 0.00 MB)
2025-05-31 22:58:15,081 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:58:15,087 - app - INFO - Memory management utilities loaded
2025-05-31 22:58:15,088 - app - INFO - Error handling utilities loaded
2025-05-31 22:58:15,090 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:58:15,091 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:58:15,091 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:58:15,091 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 22:58:45,248 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 22:58:46,734 - app - INFO - Memory management utilities loaded
2025-05-31 22:58:46,736 - app - INFO - Error handling utilities loaded
2025-05-31 22:58:46,738 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 22:58:46,740 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 22:58:46,744 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 22:58:46,746 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 22:58:46,750 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 22:58:46,752 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 22:58:46,754 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 22:58:46,754 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 22:58:46,756 - app - INFO - Applied NumPy fix
2025-05-31 22:58:46,756 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:58:46,758 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:58:46,762 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:58:46,764 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 22:58:46,766 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:58:46,766 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:58:46,768 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:58:46,770 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 22:58:50,577 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 22:58:50,577 - app - INFO - Applied TensorFlow fix
2025-05-31 22:58:50,579 - app.config - INFO - Configuration initialized
2025-05-31 22:58:50,582 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 22:58:50,593 - models.train - INFO - TensorFlow test successful
2025-05-31 22:58:51,105 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 22:58:51,105 - models.train - INFO - Transformer model is available
2025-05-31 22:58:51,105 - models.train - INFO - Using TensorFlow-based models
2025-05-31 22:58:51,107 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 22:58:51,107 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 22:58:51,109 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 22:58:51,428 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:58:51,429 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 22:58:51,429 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 22:58:51,429 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 22:58:51,429 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 22:58:51,430 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 22:58:51,430 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 22:58:51,430 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 22:58:51,431 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 22:58:51,431 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 22:58:51,514 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 22:58:51,517 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:58:51,850 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 22:58:52,492 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 22:58:52,513 - app.utils.session_state - INFO - Initializing session state
2025-05-31 22:58:52,516 - app.utils.session_state - INFO - Session state initialized
2025-05-31 22:58:53,803 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 22:58:53,821 - app.utils.memory_management - INFO - Memory before cleanup: 434.20 MB
2025-05-31 22:58:54,032 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-05-31 22:58:54,033 - app.utils.memory_management - INFO - Memory after cleanup: 434.58 MB (freed -0.39 MB)
2025-05-31 22:59:09,866 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:59:09,907 - app.utils.memory_management - INFO - Memory before cleanup: 438.32 MB
2025-05-31 22:59:10,169 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 22:59:10,169 - app.utils.memory_management - INFO - Memory after cleanup: 438.32 MB (freed -0.00 MB)
2025-05-31 22:59:10,892 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:59:10,953 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-31 22:59:10,955 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:59:10,955 - app - INFO - Data shape: (581, 36)
2025-05-31 22:59:10,956 - app - INFO - File COMI contains 2025 data
2025-05-31 22:59:10,994 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-05-31 22:59:10,995 - app - INFO - Features shape: (581, 36)
2025-05-31 22:59:11,021 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 22:59:11,023 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 22:59:11,024 - app - INFO - Data shape: (581, 36)
2025-05-31 22:59:11,025 - app - INFO - File COMI contains 2025 data
2025-05-31 22:59:11,028 - app.utils.memory_management - INFO - Memory before cleanup: 442.86 MB
2025-05-31 22:59:11,233 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 22:59:11,234 - app.utils.memory_management - INFO - Memory after cleanup: 442.89 MB (freed -0.04 MB)
2025-05-31 22:59:11,414 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:59:11,498 - app.utils.memory_management - INFO - Memory before cleanup: 443.95 MB
2025-05-31 22:59:11,694 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 22:59:11,694 - app.utils.memory_management - INFO - Memory after cleanup: 443.93 MB (freed 0.02 MB)
2025-05-31 22:59:14,184 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:59:14,212 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 22:59:14,275 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:59:14,278 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:59:14,282 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:59:14,284 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:59:14,292 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:59:14,295 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-05-31 22:59:14,296 - app.utils.memory_management - INFO - Memory before cleanup: 446.29 MB
2025-05-31 22:59:14,498 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 22:59:14,499 - app.utils.memory_management - INFO - Memory after cleanup: 446.29 MB (freed 0.00 MB)
2025-05-31 22:59:19,199 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 22:59:19,241 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:59:19,243 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 22:59:19,246 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-383' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 22:59:19,250 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 22:59:19,286 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 22:59:19,289 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 22:59:19,290 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 22:59:19,294 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 22:59:19,311 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 22:59:19,317 - app.utils.error_handling - INFO - live_trading_component executed in 0.10 seconds
2025-05-31 22:59:19,318 - app.utils.memory_management - INFO - Memory before cleanup: 447.49 MB
2025-05-31 22:59:19,525 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 22:59:19,526 - app.utils.memory_management - INFO - Memory after cleanup: 447.49 MB (freed 0.00 MB)
2025-05-31 23:00:17,050 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:17,059 - app.utils.session_state - INFO - Initializing session state
2025-05-31 23:00:17,064 - app.utils.session_state - INFO - Session state initialized
2025-05-31 23:00:17,093 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 23:00:17,115 - app.utils.memory_management - INFO - Memory before cleanup: 447.22 MB
2025-05-31 23:00:17,407 - app.utils.memory_management - INFO - Garbage collection: collected 254 objects
2025-05-31 23:00:17,409 - app.utils.memory_management - INFO - Memory after cleanup: 447.22 MB (freed 0.00 MB)
2025-05-31 23:00:22,788 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:22,815 - app.utils.memory_management - INFO - Memory before cleanup: 447.73 MB
2025-05-31 23:00:23,022 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-31 23:00:23,022 - app.utils.memory_management - INFO - Memory after cleanup: 447.73 MB (freed 0.00 MB)
2025-05-31 23:00:23,980 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:24,044 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 23:00:24,045 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:00:24,046 - app - INFO - Data shape: (581, 36)
2025-05-31 23:00:24,046 - app - INFO - File COMI contains 2025 data
2025-05-31 23:00:24,089 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-05-31 23:00:24,089 - app - INFO - Features shape: (581, 36)
2025-05-31 23:00:24,114 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 23:00:24,115 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:00:24,115 - app - INFO - Data shape: (581, 36)
2025-05-31 23:00:24,116 - app - INFO - File COMI contains 2025 data
2025-05-31 23:00:24,120 - app.utils.memory_management - INFO - Memory before cleanup: 449.59 MB
2025-05-31 23:00:24,295 - app.utils.memory_management - INFO - Garbage collection: collected 315 objects
2025-05-31 23:00:24,296 - app.utils.memory_management - INFO - Memory after cleanup: 449.59 MB (freed 0.00 MB)
2025-05-31 23:00:24,473 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:24,524 - app.utils.memory_management - INFO - Memory before cleanup: 449.61 MB
2025-05-31 23:00:24,703 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 23:00:24,704 - app.utils.memory_management - INFO - Memory after cleanup: 449.59 MB (freed 0.02 MB)
2025-05-31 23:00:26,658 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:26,683 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:00:26,697 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:26,699 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:00:26,703 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:26,703 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:00:26,710 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:00:26,713 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 23:00:26,714 - app.utils.memory_management - INFO - Memory before cleanup: 449.54 MB
2025-05-31 23:00:26,900 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:00:26,901 - app.utils.memory_management - INFO - Memory after cleanup: 449.54 MB (freed 0.00 MB)
2025-05-31 23:00:37,494 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:37,525 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:00:37,526 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:00:37,527 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-849' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 23:00:37,529 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 23:00:37,572 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:37,572 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:00:37,574 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:37,576 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:00:37,584 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:00:37,588 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-05-31 23:00:37,589 - app.utils.memory_management - INFO - Memory before cleanup: 449.59 MB
2025-05-31 23:00:37,858 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 23:00:37,858 - app.utils.memory_management - INFO - Memory after cleanup: 449.59 MB (freed 0.00 MB)
2025-05-31 23:00:42,037 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:42,068 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 23:00:42,068 - app.components.live_trading - INFO - Successfully closed previous WebDriver
2025-05-31 23:00:42,069 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:00:42,091 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:42,091 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:00:42,092 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:42,093 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:00:42,099 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:00:42,102 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-05-31 23:00:42,102 - app.utils.memory_management - INFO - Memory before cleanup: 449.54 MB
2025-05-31 23:00:42,289 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-31 23:00:42,290 - app.utils.memory_management - INFO - Memory after cleanup: 449.54 MB (freed 0.00 MB)
2025-05-31 23:00:47,086 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:47,125 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:00:47,126 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:00:47,127 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1040' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 23:00:47,128 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 23:00:47,151 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:47,151 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:00:47,153 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:47,153 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:00:47,159 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:00:47,162 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-05-31 23:00:47,164 - app.utils.memory_management - INFO - Memory before cleanup: 449.60 MB
2025-05-31 23:00:47,365 - app.utils.memory_management - INFO - Garbage collection: collected 221 objects
2025-05-31 23:00:47,365 - app.utils.memory_management - INFO - Memory after cleanup: 449.60 MB (freed 0.00 MB)
2025-05-31 23:00:57,513 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:00:57,539 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:57,540 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:00:57,540 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:57,548 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,549 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:00:57,550 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:00:57,550 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:00:57,559 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:00:57,559 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:00:57,564 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,572 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:00:57,578 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 23:00:57,580 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:00:57,580 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:00:57,581 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:00:57,586 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,587 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:00:57,589 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 23:00:57,590 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:00:57,590 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:00:57,590 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:00:57,597 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,597 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:00:57,598 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 23:00:57,598 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:57,599 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:00:57,599 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:57,607 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,608 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:00:57,610 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:00:57,664 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:00:57,671 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:00:57,672 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:00:57,679 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,689 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:00:57,693 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:00:57,693 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:00:57,695 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:00:57,697 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:00:57,699 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:00:57,701 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:00:57,704 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:00:57,704 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:00:57,705 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:00:57,706 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:00:57,707 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:00:57,707 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:00:57,709 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:00:57,710 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:00:57,711 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:00:57,712 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:00:57,714 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:00:57,714 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:00:57,897 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:00:57,945 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:57,946 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:00:57,947 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:57,956 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,957 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:00:57,958 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:00:57,968 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:00:57,968 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:00:57,977 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,985 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:00:57,988 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:00:57,988 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:00:57,989 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:00:57,995 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:57,996 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:00:57,996 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:00:57,997 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:00:57,997 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:00:58,003 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:58,005 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:00:58,007 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:00:58,007 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:00:58,007 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:00:58,013 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:00:58,015 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:00:58,027 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:00:58,031 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:00:58,031 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:00:58,038 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:00:58,038 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:00:58,049 - app.utils.memory_management - INFO - Memory before cleanup: 449.59 MB
2025-05-31 23:00:58,220 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 23:00:58,221 - app.utils.memory_management - INFO - Memory after cleanup: 449.57 MB (freed 0.02 MB)
2025-05-31 23:01:02,632 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:02,676 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:02,677 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:01:02,677 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:02,678 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:01:02,679 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:01:02,679 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:01:02,680 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:02,681 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:01:02,682 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:02,684 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:01:02,685 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:01:02,685 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:01:02,686 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 23:01:02,688 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:01:02,690 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:01:02,692 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:01:02,692 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-1399' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 23:01:02,694 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 23:01:02,695 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 23:01:02,732 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 23:01:02,914 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 23:01:03,103 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:01:03,106 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 23:01:03,108 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 23:01:03,108 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 23:01:03,112 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 23:01:03,112 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:01:03,112 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:01:03,156 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 23:01:03,157 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:01:03,157 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:01:03,165 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:01:03,165 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7538120603561401
2025-05-31 23:01:03,165 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00889905853015
2025-05-31 23:01:03,167 - app.pages.predictions_consolidated - ERROR - Quick prediction error: 'Close'
2025-05-31 23:01:03,174 - app.pages.predictions_consolidated - ERROR - Traceback: Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Close'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\app\pages\predictions_consolidated.py", line 486, in show_quick_predictions
    current_price = st.session_state.live_data['Close'].iloc[-1]
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'Close'

2025-05-31 23:01:03,189 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:01:03,200 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:01:03,201 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:01:03,208 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:03,215 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:01:03,216 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:01:03,217 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:01:03,217 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:01:03,217 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:01:03,219 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:01:03,220 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:01:03,221 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:01:03,221 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:01:03,221 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:01:03,412 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:01:03,456 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:03,456 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:03,458 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:01:03,467 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:03,467 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:01:03,468 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:01:03,479 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:01:03,481 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:01:03,490 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:03,497 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:01:03,497 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:01:03,498 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:01:03,500 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:01:03,506 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:03,507 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:01:03,507 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:01:03,509 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:01:03,509 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:01:03,515 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:03,517 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:01:03,517 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:03,517 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:03,519 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:01:03,526 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:03,529 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:01:03,536 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:03,546 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:01:03,548 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:01:03,550 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:01:03,553 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:03,567 - app.utils.memory_management - INFO - Memory before cleanup: 454.95 MB
2025-05-31 23:01:03,812 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:01:03,812 - app.utils.memory_management - INFO - Memory after cleanup: 454.95 MB (freed 0.00 MB)
2025-05-31 23:01:18,304 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:18,660 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-31 23:01:19,384 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-31 23:01:19,419 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.03 seconds
2025-05-31 23:01:19,421 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-31 23:01:19,421 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-31 23:01:19,424 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-31 23:01:19,988 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.60 seconds
2025-05-31 23:01:20,262 - app.utils.memory_management - INFO - Memory before cleanup: 461.84 MB
2025-05-31 23:01:20,412 - app.utils.memory_management - INFO - Garbage collection: collected 4220 objects
2025-05-31 23:01:20,413 - app.utils.memory_management - INFO - Memory after cleanup: 461.84 MB (freed 0.00 MB)
2025-05-31 23:01:30,735 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:31,087 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-31 23:01:31,359 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-31 23:01:31,374 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-05-31 23:01:31,375 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-31 23:01:31,376 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-31 23:01:31,376 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-31 23:01:31,764 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.41 seconds
2025-05-31 23:01:32,073 - app.utils.memory_management - INFO - Memory before cleanup: 462.55 MB
2025-05-31 23:01:32,294 - app.utils.memory_management - INFO - Garbage collection: collected 2700 objects
2025-05-31 23:01:32,295 - app.utils.memory_management - INFO - Memory after cleanup: 462.53 MB (freed 0.02 MB)
2025-05-31 23:01:45,292 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:45,536 - app.utils.memory_management - INFO - Memory before cleanup: 462.54 MB
2025-05-31 23:01:45,781 - app.utils.memory_management - INFO - Garbage collection: collected 355 objects
2025-05-31 23:01:45,784 - app.utils.memory_management - INFO - Memory after cleanup: 462.54 MB (freed 0.00 MB)
2025-05-31 23:01:45,978 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:46,029 - app.utils.memory_management - INFO - Memory before cleanup: 462.54 MB
2025-05-31 23:01:46,298 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 23:01:46,299 - app.utils.memory_management - INFO - Memory after cleanup: 462.54 MB (freed 0.00 MB)
2025-05-31 23:01:57,535 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:57,572 - app.utils.memory_management - INFO - Memory before cleanup: 462.54 MB
2025-05-31 23:01:57,772 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-31 23:01:57,772 - app.utils.memory_management - INFO - Memory after cleanup: 462.54 MB (freed 0.00 MB)
2025-05-31 23:01:57,944 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:01:57,990 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:01:58,006 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:01:58,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:01:58,017 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:58,024 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:01:58,025 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:01:58,025 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:01:58,026 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:01:58,026 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:01:58,028 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:01:58,030 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:01:58,030 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:01:58,031 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:01:58,031 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:01:58,213 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:01:58,253 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:58,254 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:58,254 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:01:58,267 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:58,267 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:01:58,268 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:01:58,273 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:01:58,273 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:01:58,282 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:58,292 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:01:58,294 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:01:58,295 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:01:58,296 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:01:58,306 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:58,307 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:01:58,307 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:01:58,308 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:01:58,308 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:01:58,314 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:58,315 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:01:58,316 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:01:58,316 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:58,317 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:01:58,324 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:01:58,325 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:01:58,336 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:58,344 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:01:58,344 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:01:58,348 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:01:58,348 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:01:58,363 - app.utils.memory_management - INFO - Memory before cleanup: 462.54 MB
2025-05-31 23:01:58,576 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:01:58,576 - app.utils.memory_management - INFO - Memory after cleanup: 462.54 MB (freed 0.00 MB)
2025-05-31 23:02:03,857 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:02:03,998 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:02:04,003 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:02:04,007 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:02:04,012 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:02:04,022 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:02:04,027 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:02:04,030 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:02:04,036 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:02:04,039 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:02:04,044 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:02:04,048 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:02:04,051 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:02:04,057 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 23:02:04,074 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:02:04,084 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:02:04,088 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:02:04,092 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-3025' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 23:02:04,100 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 23:02:04,103 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 23:02:04,166 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 23:02:04,406 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 23:02:04,557 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:02:04,560 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 23:02:04,560 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 23:02:04,561 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:02:04,561 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:02:04,604 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 23:02:04,605 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:02:04,605 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:02:04,612 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:02:04,614 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7538120603561401
2025-05-31 23:02:04,616 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00889905853015
2025-05-31 23:02:04,619 - app.pages.predictions_consolidated - ERROR - Quick prediction error: 'Close'
2025-05-31 23:02:04,621 - app.pages.predictions_consolidated - ERROR - Traceback: Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Close'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\app\pages\predictions_consolidated.py", line 486, in show_quick_predictions
    current_price = st.session_state.live_data['Close'].iloc[-1]
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'Close'

2025-05-31 23:02:04,636 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:02:04,643 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:02:04,644 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:02:04,651 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:02:04,659 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:02:04,660 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:02:04,661 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:02:04,662 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:02:04,662 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:02:04,663 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:02:04,665 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:02:04,665 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:02:04,665 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:02:04,666 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:02:04,835 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:02:04,886 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:02:04,887 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:02:04,887 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:02:04,894 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:02:04,894 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:02:04,895 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:02:04,902 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:02:04,902 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:02:04,908 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:02:04,916 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:02:04,918 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:02:04,919 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:02:04,920 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:02:04,931 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:02:04,933 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:02:04,938 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:02:04,939 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:02:04,942 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:02:04,956 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:02:04,958 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:02:04,960 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:02:04,962 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:02:04,966 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:02:04,980 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:02:04,985 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:02:05,001 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:02:05,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:02:05,009 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:02:05,009 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:02:05,010 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:02:05,025 - app.utils.memory_management - INFO - Memory before cleanup: 467.69 MB
2025-05-31 23:02:05,192 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:02:05,192 - app.utils.memory_management - INFO - Memory after cleanup: 467.69 MB (freed 0.00 MB)
2025-05-31 23:14:00,169 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 23:14:01,656 - app - INFO - Memory management utilities loaded
2025-05-31 23:14:01,659 - app - INFO - Error handling utilities loaded
2025-05-31 23:14:01,665 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 23:14:01,669 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 23:14:01,673 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 23:14:01,675 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 23:14:01,677 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 23:14:01,680 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 23:14:01,683 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 23:14:01,687 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 23:14:01,688 - app - INFO - Applied NumPy fix
2025-05-31 23:14:01,696 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:14:01,699 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:14:01,703 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:14:01,707 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 23:14:01,711 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:14:01,716 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:14:01,720 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:14:01,721 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 23:14:05,663 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 23:14:05,663 - app - INFO - Applied TensorFlow fix
2025-05-31 23:14:05,667 - app.config - INFO - Configuration initialized
2025-05-31 23:14:05,671 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 23:14:05,684 - models.train - INFO - TensorFlow test successful
2025-05-31 23:14:06,248 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 23:14:06,249 - models.train - INFO - Transformer model is available
2025-05-31 23:14:06,249 - models.train - INFO - Using TensorFlow-based models
2025-05-31 23:14:06,251 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 23:14:06,251 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 23:14:06,254 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 23:14:06,604 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:14:06,604 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:14:06,604 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:14:06,604 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:14:06,605 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:14:06,605 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 23:14:06,605 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 23:14:06,605 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:14:06,605 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:14:06,605 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:14:06,694 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 23:14:06,699 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:07,020 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 23:14:07,668 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 23:14:07,688 - app.utils.session_state - INFO - Initializing session state
2025-05-31 23:14:07,689 - app.utils.session_state - INFO - Session state initialized
2025-05-31 23:14:08,959 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 23:14:08,978 - app.utils.memory_management - INFO - Memory before cleanup: 434.83 MB
2025-05-31 23:14:09,158 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-05-31 23:14:09,159 - app.utils.memory_management - INFO - Memory after cleanup: 434.84 MB (freed -0.00 MB)
2025-05-31 23:14:13,545 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:13,582 - app.utils.memory_management - INFO - Memory before cleanup: 438.24 MB
2025-05-31 23:14:13,770 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:14:13,772 - app.utils.memory_management - INFO - Memory after cleanup: 438.24 MB (freed 0.00 MB)
2025-05-31 23:14:14,983 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:15,041 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-31 23:14:15,045 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:14:15,047 - app - INFO - Data shape: (581, 36)
2025-05-31 23:14:15,059 - app - INFO - File COMI contains 2025 data
2025-05-31 23:14:15,119 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-05-31 23:14:15,122 - app - INFO - Features shape: (581, 36)
2025-05-31 23:14:15,152 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 23:14:15,155 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:14:15,159 - app - INFO - Data shape: (581, 36)
2025-05-31 23:14:15,160 - app - INFO - File COMI contains 2025 data
2025-05-31 23:14:15,165 - app.utils.memory_management - INFO - Memory before cleanup: 442.77 MB
2025-05-31 23:14:15,345 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 23:14:15,345 - app.utils.memory_management - INFO - Memory after cleanup: 442.80 MB (freed -0.04 MB)
2025-05-31 23:14:15,515 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:15,593 - app.utils.memory_management - INFO - Memory before cleanup: 444.00 MB
2025-05-31 23:14:15,774 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 23:14:15,774 - app.utils.memory_management - INFO - Memory after cleanup: 443.98 MB (freed 0.02 MB)
2025-05-31 23:14:18,134 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:18,162 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:14:18,220 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:18,221 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:14:18,221 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:18,222 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:14:18,230 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:18,232 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-05-31 23:14:18,234 - app.utils.memory_management - INFO - Memory before cleanup: 446.31 MB
2025-05-31 23:14:18,466 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:14:18,466 - app.utils.memory_management - INFO - Memory after cleanup: 446.31 MB (freed 0.00 MB)
2025-05-31 23:14:21,019 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:23,098 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:14:23,098 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:14:23,098 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-391' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 23:14:23,101 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 23:14:23,119 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:23,119 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:14:23,131 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:23,133 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:14:23,140 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:23,143 - app.utils.error_handling - INFO - live_trading_component executed in 2.10 seconds
2025-05-31 23:14:23,144 - app.utils.memory_management - INFO - Memory before cleanup: 447.73 MB
2025-05-31 23:14:23,329 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 23:14:23,330 - app.utils.memory_management - INFO - Memory after cleanup: 447.73 MB (freed 0.00 MB)
2025-05-31 23:14:43,934 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:43,974 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:43,976 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:43,977 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:43,990 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:43,993 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:14:43,996 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:14:43,998 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:44,009 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:44,010 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:14:44,018 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,028 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:14:44,038 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 23:14:44,044 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:14:44,048 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:14:44,050 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:14:44,064 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,090 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,094 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 23:14:44,098 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:14:44,109 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:14:44,116 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:14:44,130 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,132 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,132 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 23:14:44,133 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:44,133 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:44,134 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:44,144 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,144 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,144 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:14:44,180 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:14:44,189 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:44,189 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:14:44,196 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,211 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:14:44,214 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:14:44,216 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:14:44,216 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:14:44,217 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:14:44,219 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:14:44,222 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:14:44,229 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:14:44,229 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:14:44,229 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:14:44,455 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:14:44,502 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:44,506 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:44,507 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:44,519 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,521 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,521 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:44,527 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:44,527 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:14:44,535 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,543 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:14:44,543 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:14:44,544 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:14:44,545 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:14:44,551 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,555 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,555 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:14:44,556 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:14:44,557 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:14:44,565 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,565 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,566 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:44,567 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:44,569 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:44,576 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:44,577 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:14:44,588 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:44,595 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:44,596 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:14:44,597 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:14:44,598 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:44,615 - app.utils.memory_management - INFO - Memory before cleanup: 448.36 MB
2025-05-31 23:14:44,795 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 23:14:44,795 - app.utils.memory_management - INFO - Memory after cleanup: 448.36 MB (freed 0.00 MB)
2025-05-31 23:14:49,674 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:14:49,719 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:49,719 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:49,719 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:49,720 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:14:49,720 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:14:49,720 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:14:49,721 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:49,723 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:49,723 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:49,724 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:14:49,725 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:14:49,725 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:14:49,725 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 23:14:49,728 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:14:51,759 - scrapers.price_scraper - INFO - Getting advanced data for symbols: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:14:51,760 - scrapers.advanced_scraper - INFO - Starting scrape for pairs: ['EGX-COMI'] with intervals: ['1D']
2025-05-31 23:14:51,760 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-738' coro=<Connection.run() done, defined at D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py:272> exception=NotImplementedError()>
Traceback (most recent call last):
  File "D:\AI Stocks Bot\scrapers\price_scraper.py", line 179, in get_advanced_data_sync
    current_loop = asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_connection.py", line 279, in run
    await self._transport.connect()
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\subprocess.py", line 218, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 1675, in subprocess_exec
    transport = await self._make_subprocess_transport(
  File "C:\Program Files\Python310\lib\asyncio\base_events.py", line 498, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-05-31 23:14:51,760 - scrapers.price_scraper - ERROR - Error getting advanced data: 
2025-05-31 23:14:51,760 - scrapers.price_scraper - INFO - Driver close called but not needed for advanced scraper
2025-05-31 23:14:51,797 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 23:14:51,977 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 23:14:52,134 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:14:52,134 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 23:14:52,138 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 23:14:52,140 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 23:14:52,140 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 23:14:52,140 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:14:52,142 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:14:52,194 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 23:14:52,196 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:14:52,196 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:14:52,202 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:14:52,202 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7538120603561401
2025-05-31 23:14:52,202 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00889905853015
2025-05-31 23:14:52,204 - app.pages.predictions_consolidated - ERROR - Quick prediction error: 'Close'
2025-05-31 23:14:52,212 - app.pages.predictions_consolidated - ERROR - Traceback: Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Close'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\app\pages\predictions_consolidated.py", line 486, in show_quick_predictions
    current_price = st.session_state.live_data['Close'].iloc[-1]
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'Close'

2025-05-31 23:14:52,228 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:14:52,235 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:52,235 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:14:52,242 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:52,254 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:14:52,255 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:14:52,255 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:14:52,255 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:14:52,256 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:14:52,257 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:14:52,260 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:14:52,261 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:14:52,261 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:14:52,262 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:14:52,451 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:14:52,515 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:52,517 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:52,518 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:52,529 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:52,530 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:14:52,530 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:14:52,536 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:52,536 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:14:52,543 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:52,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:14:52,553 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:14:52,554 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:14:52,554 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:14:52,562 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:52,562 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:14:52,564 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:14:52,564 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:14:52,564 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:14:52,570 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:52,570 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:14:52,572 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:14:52,572 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:52,572 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:14:52,580 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:14:52,580 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:14:52,595 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:52,606 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:14:52,610 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:14:52,614 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:14:52,614 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:14:52,630 - app.utils.memory_management - INFO - Memory before cleanup: 455.33 MB
2025-05-31 23:14:52,839 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:14:52,840 - app.utils.memory_management - INFO - Memory after cleanup: 455.33 MB (freed 0.00 MB)
2025-05-31 23:31:42,411 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 23:31:43,832 - app - INFO - Memory management utilities loaded
2025-05-31 23:31:43,836 - app - INFO - Error handling utilities loaded
2025-05-31 23:31:43,842 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 23:31:43,842 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 23:31:43,844 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 23:31:43,844 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 23:31:43,844 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 23:31:43,846 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 23:31:43,846 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 23:31:43,846 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 23:31:43,848 - app - INFO - Applied NumPy fix
2025-05-31 23:31:43,848 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:31:43,850 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:31:43,850 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:31:43,852 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 23:31:43,852 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:31:43,852 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:31:43,854 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:31:43,854 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 23:31:48,098 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 23:31:48,098 - app - INFO - Applied TensorFlow fix
2025-05-31 23:31:48,102 - app.config - INFO - Configuration initialized
2025-05-31 23:31:48,108 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 23:31:48,120 - models.train - INFO - TensorFlow test successful
2025-05-31 23:31:48,735 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 23:31:48,736 - models.train - INFO - Transformer model is available
2025-05-31 23:31:48,736 - models.train - INFO - Using TensorFlow-based models
2025-05-31 23:31:48,738 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 23:31:48,738 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 23:31:48,742 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 23:31:49,105 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:31:49,106 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:31:49,106 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:31:49,106 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:31:49,107 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:31:49,107 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 23:31:49,108 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 23:31:49,108 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:31:49,108 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:31:49,108 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:31:49,219 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 23:31:49,223 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:31:49,575 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 23:31:50,289 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 23:31:50,315 - app.utils.session_state - INFO - Initializing session state
2025-05-31 23:31:50,319 - app.utils.session_state - INFO - Session state initialized
2025-05-31 23:31:51,724 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 23:31:51,739 - app.utils.memory_management - INFO - Memory before cleanup: 429.14 MB
2025-05-31 23:31:51,917 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:31:51,918 - app.utils.memory_management - INFO - Memory after cleanup: 429.16 MB (freed -0.01 MB)
2025-05-31 23:32:12,742 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:32:12,785 - app.utils.memory_management - INFO - Memory before cleanup: 433.06 MB
2025-05-31 23:32:12,979 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:32:12,981 - app.utils.memory_management - INFO - Memory after cleanup: 433.06 MB (freed 0.00 MB)
2025-05-31 23:32:13,843 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:32:13,903 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 23:32:13,904 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:32:13,905 - app - INFO - Data shape: (581, 36)
2025-05-31 23:32:13,905 - app - INFO - File COMI contains 2025 data
2025-05-31 23:32:13,952 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-31 23:32:13,954 - app - INFO - Features shape: (581, 36)
2025-05-31 23:32:13,986 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 23:32:13,987 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:32:13,988 - app - INFO - Data shape: (581, 36)
2025-05-31 23:32:13,988 - app - INFO - File COMI contains 2025 data
2025-05-31 23:32:13,996 - app.utils.memory_management - INFO - Memory before cleanup: 437.44 MB
2025-05-31 23:32:14,224 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-31 23:32:14,224 - app.utils.memory_management - INFO - Memory after cleanup: 437.48 MB (freed -0.04 MB)
2025-05-31 23:32:14,388 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:32:14,461 - app.utils.memory_management - INFO - Memory before cleanup: 438.55 MB
2025-05-31 23:32:14,632 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 23:32:14,633 - app.utils.memory_management - INFO - Memory after cleanup: 438.54 MB (freed 0.02 MB)
2025-05-31 23:32:16,353 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:32:16,379 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:32:16,455 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:32:16,457 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:32:16,458 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:32:16,459 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:32:16,474 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:32:16,480 - app.utils.error_handling - INFO - live_trading_component executed in 0.11 seconds
2025-05-31 23:32:16,482 - app.utils.memory_management - INFO - Memory before cleanup: 440.75 MB
2025-05-31 23:32:16,691 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:32:16,691 - app.utils.memory_management - INFO - Memory after cleanup: 440.75 MB (freed 0.00 MB)
2025-05-31 23:32:19,694 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:32:20,884 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:32:20,885 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:32:56,987 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:32:56,988 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:32:56,988 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:32:56,990 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:32:56,995 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:32:56,998 - app.utils.error_handling - INFO - live_trading_component executed in 37.28 seconds
2025-05-31 23:32:56,999 - app.utils.memory_management - INFO - Memory before cleanup: 442.43 MB
2025-05-31 23:32:57,216 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 23:32:57,217 - app.utils.memory_management - INFO - Memory after cleanup: 442.43 MB (freed 0.00 MB)
2025-05-31 23:33:11,063 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:33:11,124 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:11,126 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:11,127 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:11,136 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,142 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,143 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:33:11,144 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:33:11,158 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:11,160 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:11,176 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,193 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:33:11,209 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 23:33:11,213 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:33:11,215 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:11,217 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:33:11,233 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,236 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,239 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 23:33:11,241 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:33:11,243 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:11,248 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:33:11,263 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,267 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,271 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 23:33:11,272 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:11,277 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:11,282 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:11,309 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,316 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,320 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:33:11,367 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:33:11,381 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:11,384 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:11,396 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,423 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:33:11,424 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:33:11,428 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:33:11,430 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:33:11,433 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:33:11,436 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:33:11,441 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:33:11,450 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:33:11,460 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:33:11,466 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:33:11,469 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:33:11,472 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:33:11,475 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:33:11,478 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:33:11,483 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:33:11,488 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:33:11,494 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:33:11,496 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:33:11,498 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:33:11,705 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:33:11,757 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:11,760 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:11,762 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:11,772 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,772 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,775 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:33:11,785 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:11,789 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:11,801 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,816 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:33:11,819 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:33:11,819 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:11,822 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:33:11,831 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,832 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,833 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:33:11,835 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:11,838 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:33:11,847 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,856 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,857 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:11,858 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:11,858 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:11,867 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:11,871 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:11,885 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:11,895 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:11,896 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:11,898 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:11,900 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:11,922 - app.utils.memory_management - INFO - Memory before cleanup: 442.44 MB
2025-05-31 23:33:12,134 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 23:33:12,135 - app.utils.memory_management - INFO - Memory after cleanup: 442.44 MB (freed 0.00 MB)
2025-05-31 23:33:17,468 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:33:17,531 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:33:17,542 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:17,544 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:17,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:17,558 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:33:17,559 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:33:17,560 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:33:17,560 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:33:17,561 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:33:17,562 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:33:17,564 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:33:17,566 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:33:17,568 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:33:17,570 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:33:17,791 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:33:17,837 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:17,838 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:17,839 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:17,850 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:17,851 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:17,852 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:33:17,858 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:17,859 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:17,868 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:17,878 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:33:17,878 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:33:17,879 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:17,879 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:33:17,886 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:17,887 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:33:17,887 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:33:17,888 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:17,888 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:33:17,895 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:17,896 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:33:17,898 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:17,903 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:17,904 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:17,913 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:17,915 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:17,929 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:17,939 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:17,939 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:17,940 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:17,941 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:17,959 - app.utils.memory_management - INFO - Memory before cleanup: 443.16 MB
2025-05-31 23:33:18,150 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:33:18,150 - app.utils.memory_management - INFO - Memory after cleanup: 443.16 MB (freed 0.00 MB)
2025-05-31 23:33:20,039 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:33:20,106 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,115 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,122 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,128 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,136 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,143 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,153 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,160 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,168 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,183 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,196 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,205 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:20,206 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 23:33:20,210 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:33:21,393 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:33:21,393 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:33:51,993 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:33:52,026 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:33:52,263 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:33:52,415 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:33:52,418 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-31 23:33:52,420 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 23:33:52,421 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 23:33:52,423 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-31 23:33:52,424 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-31 23:33:52,424 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-31 23:33:52,426 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:33:52,428 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-31 23:33:52,429 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:33:52,538 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:33:52,576 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:33:52,576 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:33:52,578 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:33:52,584 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:33:52,584 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7633576881885529
2025-05-31 23:33:52,585 - models.predict - INFO - Prediction for 60 minutes horizon: 83.53200131273269
2025-05-31 23:33:52,587 - app.pages.predictions_consolidated - ERROR - Quick prediction error: 'Close'
2025-05-31 23:33:52,595 - app.pages.predictions_consolidated - ERROR - Traceback: Traceback (most recent call last):
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'Close'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\app\pages\predictions_consolidated.py", line 486, in show_quick_predictions
    current_price = st.session_state.live_data['Close'].iloc[-1]
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
  File "D:\AI Stocks Bot\python310_venv\lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'Close'

2025-05-31 23:33:52,624 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:33:52,634 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:52,635 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:52,644 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:52,655 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:33:52,655 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:33:52,656 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:33:52,656 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:33:52,656 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:33:52,657 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:33:52,660 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:33:52,661 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:33:52,661 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:33:52,662 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:33:52,849 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:33:52,896 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:52,897 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:52,898 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:52,912 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:52,914 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:52,915 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:33:52,921 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:52,922 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:33:52,930 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:52,939 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:33:52,939 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:33:52,940 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:52,940 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:33:52,951 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:52,952 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:33:52,953 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:33:52,953 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:52,954 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:33:52,959 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:52,962 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:33:52,963 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:33:52,964 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:52,964 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:33:52,972 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:33:52,972 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:33:52,986 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:52,994 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:33:52,996 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:33:52,997 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:33:52,997 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:33:53,013 - app.utils.memory_management - INFO - Memory before cleanup: 451.77 MB
2025-05-31 23:33:53,189 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:33:53,191 - app.utils.memory_management - INFO - Memory after cleanup: 451.73 MB (freed 0.04 MB)
2025-05-31 23:36:50,455 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 23:36:51,078 - app - INFO - Memory management utilities loaded
2025-05-31 23:36:51,080 - app - INFO - Error handling utilities loaded
2025-05-31 23:36:51,082 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 23:36:51,084 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 23:36:51,085 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 23:36:51,085 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 23:36:51,085 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 23:36:51,087 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 23:36:51,092 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 23:36:51,108 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 23:36:51,110 - app - INFO - Applied NumPy fix
2025-05-31 23:36:51,124 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:36:51,125 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:36:51,127 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:36:51,129 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 23:36:51,130 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:36:51,131 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:36:51,137 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:36:51,140 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 23:36:54,927 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 23:36:54,927 - app - INFO - Applied TensorFlow fix
2025-05-31 23:36:54,928 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:36:56,076 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:36:56,077 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:37:24,519 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:37:26,121 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:37:26,123 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:37:53,933 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:37:56,122 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:37:56,123 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:37:57,343 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:37:57,343 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:INVALID_SYMBOL
2025-05-31 23:38:27,686 - scrapers.price_scraper - WARNING - Could not find price for INVALID_SYMBOL on TradingView
2025-05-31 23:38:29,944 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:38:29,947 - app - INFO - Cleaning up resources...
2025-05-31 23:38:29,950 - app.utils.memory_management - INFO - Memory before cleanup: 314.30 MB
2025-05-31 23:38:30,055 - app.utils.memory_management - INFO - Garbage collection: collected 76 objects
2025-05-31 23:38:30,057 - app.utils.memory_management - INFO - Memory after cleanup: 314.74 MB (freed -0.43 MB)
2025-05-31 23:38:30,057 - app - INFO - Application shutdown complete
2025-05-31 23:38:56,534 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 23:38:56,562 - app - INFO - Memory management utilities loaded
2025-05-31 23:38:56,567 - app - INFO - Error handling utilities loaded
2025-05-31 23:38:56,580 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 23:38:56,590 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 23:38:56,593 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 23:38:56,595 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 23:38:56,600 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'CompatibleMT19937' object has no attribute 'capsule'
2025-05-31 23:38:56,601 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-31 23:38:56,610 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-31 23:38:56,616 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 23:38:56,618 - app - INFO - Applied NumPy fix
2025-05-31 23:38:56,626 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:38:56,630 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:38:56,633 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:38:56,636 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 23:38:56,642 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:38:56,646 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:38:56,647 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:38:56,650 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 23:38:56,659 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 23:38:56,664 - app - INFO - Applied TensorFlow fix
2025-05-31 23:38:56,677 - app.config - INFO - Configuration initialized
2025-05-31 23:38:56,694 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 23:38:56,699 - models.train - INFO - TensorFlow test successful
2025-05-31 23:38:56,712 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 23:38:56,717 - models.train - INFO - Transformer model is available
2025-05-31 23:38:56,725 - models.train - INFO - Using TensorFlow-based models
2025-05-31 23:38:56,780 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 23:38:56,791 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 23:38:56,848 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 23:38:56,926 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:38:56,930 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:38:56,933 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:38:56,941 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:38:56,946 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:38:56,949 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 23:38:56,965 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'CompatibleBitGenerator' has no attribute 'register'
2025-05-31 23:38:56,968 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:38:56,969 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:38:56,970 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:38:56,984 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 23:38:56,994 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:38:57,329 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 23:38:57,383 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 23:38:57,418 - app.utils.session_state - INFO - Initializing session state
2025-05-31 23:38:57,420 - app.utils.session_state - INFO - Session state initialized
2025-05-31 23:38:57,444 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 23:38:57,478 - app.utils.memory_management - INFO - Memory before cleanup: 452.32 MB
2025-05-31 23:38:57,746 - app.utils.memory_management - INFO - Garbage collection: collected 402 objects
2025-05-31 23:38:57,750 - app.utils.memory_management - INFO - Memory after cleanup: 452.32 MB (freed 0.00 MB)
2025-05-31 23:39:10,608 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:39:10,633 - app.utils.memory_management - INFO - Memory before cleanup: 452.94 MB
2025-05-31 23:39:10,806 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:39:10,807 - app.utils.memory_management - INFO - Memory after cleanup: 452.94 MB (freed 0.00 MB)
2025-05-31 23:39:12,209 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:39:12,234 - app.utils.memory_management - INFO - Memory before cleanup: 452.97 MB
2025-05-31 23:39:12,448 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-31 23:39:12,448 - app.utils.memory_management - INFO - Memory after cleanup: 452.97 MB (freed 0.00 MB)
2025-05-31 23:39:12,630 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:39:12,704 - app.utils.memory_management - INFO - Memory before cleanup: 452.98 MB
2025-05-31 23:39:12,907 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 23:39:12,907 - app.utils.memory_management - INFO - Memory after cleanup: 452.97 MB (freed 0.02 MB)
2025-05-31 23:39:16,540 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:39:16,568 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:39:16,588 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:39:16,590 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:39:16,591 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:39:16,591 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:39:16,597 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:39:16,600 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 23:39:16,601 - app.utils.memory_management - INFO - Memory before cleanup: 452.97 MB
2025-05-31 23:39:16,794 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:39:16,795 - app.utils.memory_management - INFO - Memory after cleanup: 452.97 MB (freed 0.00 MB)
2025-05-31 23:39:18,782 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:39:20,015 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:39:20,015 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:39:50,636 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:39:50,637 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:39:50,638 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:39:50,638 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:39:50,645 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:39:50,648 - app.utils.error_handling - INFO - live_trading_component executed in 31.85 seconds
2025-05-31 23:39:50,649 - app.utils.memory_management - INFO - Memory before cleanup: 453.07 MB
2025-05-31 23:39:50,834 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 23:39:50,835 - app.utils.memory_management - INFO - Memory after cleanup: 453.07 MB (freed 0.00 MB)
2025-05-31 23:40:20,551 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:40:20,590 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:40:20,591 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:20,592 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:40:20,600 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:20,600 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:40:20,602 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:40:20,603 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:40:20,610 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:20,611 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:40:20,621 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:20,628 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:40:20,641 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 23:40:20,665 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:40:20,668 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:40:20,670 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:40:20,676 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:20,683 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:40:20,685 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 23:40:20,688 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:40:20,689 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:40:20,690 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:40:20,700 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:20,701 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:40:20,704 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 23:40:20,706 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:40:20,708 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:20,710 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:40:20,720 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:20,723 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:40:20,725 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:40:20,770 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:40:20,782 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:20,784 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:40:20,793 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:20,804 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:40:20,806 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:40:20,808 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:40:20,809 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:40:20,814 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:40:20,820 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:40:20,823 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:40:20,825 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:40:20,826 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:40:20,828 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:40:20,829 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:40:20,832 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:40:20,833 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:40:20,835 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:40:20,839 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:40:20,840 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:40:20,842 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:40:20,843 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:40:20,846 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:40:21,133 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:40:21,197 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:40:21,199 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:21,201 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:40:21,211 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:21,212 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:40:21,214 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:40:21,222 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:21,224 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:40:21,230 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:21,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:40:21,242 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:40:21,243 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:40:21,245 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:40:21,257 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:21,259 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:40:21,261 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:40:21,262 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:40:21,263 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:40:21,274 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:21,276 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:40:21,278 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:40:21,280 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:21,282 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:40:21,292 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:21,293 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:40:21,303 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:21,312 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:21,313 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:40:21,314 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:40:21,316 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:21,333 - app.utils.memory_management - INFO - Memory before cleanup: 453.04 MB
2025-05-31 23:40:21,535 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 23:40:21,536 - app.utils.memory_management - INFO - Memory after cleanup: 453.04 MB (freed 0.00 MB)
2025-05-31 23:40:25,273 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:40:25,333 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:40:25,342 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:25,342 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:40:25,351 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:25,364 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:40:25,366 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:40:25,367 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:40:25,370 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:40:25,371 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:40:25,375 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:40:25,376 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:40:25,377 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:40:25,378 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:40:25,381 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:40:25,591 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:40:25,637 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:40:25,640 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:25,640 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:40:25,652 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:25,653 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:40:25,655 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:40:25,663 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:25,664 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:40:25,671 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:25,679 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:40:25,679 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:40:25,680 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:40:25,681 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:40:25,688 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:25,689 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:40:25,690 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:40:25,690 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:40:25,691 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:40:25,698 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:25,699 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:40:25,700 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:40:25,701 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:25,701 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:40:25,709 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:25,709 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:40:25,717 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:25,726 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:40:25,727 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:40:25,728 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:40:25,729 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:40:25,747 - app.utils.memory_management - INFO - Memory before cleanup: 453.61 MB
2025-05-31 23:40:25,951 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:40:25,975 - app.utils.memory_management - INFO - Memory after cleanup: 453.61 MB (freed 0.00 MB)
2025-05-31 23:40:27,220 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:40:27,272 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,283 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,289 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,295 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,301 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,308 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,315 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,321 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,328 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,335 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,342 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,348 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon using glob
2025-05-31 23:40:27,349 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 23:40:27,354 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:40:28,608 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:40:28,610 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:41:05,703 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:41:05,736 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:41:05,925 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:41:06,074 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:41:06,076 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-31 23:41:06,078 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 23:41:06,079 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 23:41:06,081 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-31 23:41:06,082 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-31 23:41:06,082 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-31 23:41:06,083 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:41:06,084 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-31 23:41:06,084 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:41:06,121 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:41:06,157 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:41:06,158 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:41:06,158 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:41:06,165 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:41:06,165 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7633576881885529
2025-05-31 23:41:06,167 - models.predict - INFO - Prediction for 60 minutes horizon: 83.53200131273269
2025-05-31 23:41:06,299 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:41:06,307 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:06,308 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:41:06,317 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:06,324 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:41:06,325 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:41:06,325 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:41:06,325 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:41:06,326 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:41:06,328 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:41:06,330 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:41:06,331 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:41:06,331 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:41:06,331 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:41:06,583 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:41:06,642 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:41:06,643 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:06,644 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:41:06,652 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:06,654 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:41:06,655 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:41:06,665 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:06,669 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:41:06,679 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:06,685 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:41:06,686 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:41:06,686 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:41:06,687 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:41:06,693 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:06,694 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:41:06,694 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:41:06,695 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:41:06,696 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:41:06,702 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:06,703 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:41:06,704 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:41:06,704 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:06,705 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:41:06,711 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:06,711 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:41:06,719 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:06,726 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:06,726 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:41:06,727 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:41:06,728 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:06,739 - app.utils.memory_management - INFO - Memory before cleanup: 456.56 MB
2025-05-31 23:41:06,901 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:41:06,902 - app.utils.memory_management - INFO - Memory after cleanup: 456.56 MB (freed 0.00 MB)
2025-05-31 23:41:35,633 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:41:35,746 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:41:35,777 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:35,780 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:41:35,793 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:35,801 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:41:35,802 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:41:35,803 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:41:35,805 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:41:35,807 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:41:35,812 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:41:35,815 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:41:35,819 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:41:35,824 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:41:35,827 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:41:36,052 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:41:36,105 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:41:36,107 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:36,109 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:41:36,117 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,118 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:41:36,121 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:41:36,130 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:36,137 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:41:36,146 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,154 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:41:36,156 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:41:36,158 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:41:36,160 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:41:36,169 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,170 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:41:36,172 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:41:36,175 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:41:36,177 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:41:36,185 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,186 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:41:36,187 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:41:36,188 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:36,188 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:41:36,196 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,198 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:41:36,216 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,223 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,232 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,240 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,248 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:36,263 - app.utils.memory_management - INFO - Memory before cleanup: 457.46 MB
2025-05-31 23:41:36,518 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:41:36,519 - app.utils.memory_management - INFO - Memory after cleanup: 457.46 MB (freed 0.00 MB)
2025-05-31 23:41:40,074 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:41:40,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:41:40,153 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:40,153 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:41:40,159 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,167 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:41:40,167 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:41:40,169 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:41:40,169 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:41:40,169 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:41:40,171 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:41:40,172 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:41:40,173 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:41:40,173 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:41:40,174 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:41:40,357 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:41:40,396 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:41:40,397 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:40,398 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:41:40,410 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,412 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:41:40,413 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:41:40,421 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:41:40,422 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:41:40,429 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,436 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:41:40,436 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:41:40,437 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:41:40,437 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:41:40,444 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,446 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:41:40,447 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:41:40,447 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:41:40,448 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:41:40,454 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,455 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:41:40,455 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:41:40,456 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:41:40,456 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:41:40,463 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,463 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:41:40,477 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,484 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,491 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,500 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,506 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:41:40,511 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:41:41,676 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:41:41,677 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:42:21,802 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:42:21,832 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:42:22,014 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:42:22,164 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:42:22,166 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-31 23:42:22,167 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-31 23:42:22,167 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-31 23:42:22,167 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-31 23:42:22,169 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:42:22,170 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-31 23:42:22,170 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:42:22,206 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:42:22,243 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:42:22,244 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:42:22,244 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:42:22,251 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:42:22,252 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7633576881885529
2025-05-31 23:42:22,254 - models.predict - INFO - Prediction for 60 minutes horizon: 83.53200131273269
2025-05-31 23:42:22,257 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:42:23,377 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:42:23,378 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:42:57,087 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:42:57,117 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:42:57,297 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:42:57,467 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:42:57,469 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-31 23:42:57,471 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-31 23:42:57,472 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-31 23:42:57,474 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-31 23:42:57,482 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:42:58,692 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:42:58,692 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:43:31,162 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:43:31,191 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:43:31,390 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:43:31,544 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:43:31,546 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-31 23:43:31,547 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-31 23:43:31,547 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-31 23:43:31,547 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-31 23:43:31,549 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:43:31,549 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-31 23:43:31,549 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-31 23:43:31,592 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-31 23:43:31,599 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:43:31,617 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:43:31,618 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:43:31,620 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:43:31,620 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7475613242929999
2025-05-31 23:43:31,621 - models.predict - INFO - Prediction for 60 minutes horizon: 82.6663605712564
2025-05-31 23:43:31,624 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:43:32,861 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:43:32,861 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:44:09,229 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:44:09,263 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:44:09,472 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:44:09,624 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:44:09,625 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-31 23:44:09,626 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-31 23:44:09,626 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-31 23:44:09,626 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-31 23:44:09,627 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:44:09,628 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-31 23:44:09,628 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-31 23:44:09,661 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-31 23:44:09,662 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:44:09,662 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-31 23:44:09,663 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:44:09,674 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:44:09,675 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7262234971697867
2025-05-31 23:44:09,675 - models.predict - INFO - Prediction for 60 minutes horizon: 81.49704764490431
2025-05-31 23:44:09,678 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:44:10,821 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:44:10,822 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:44:44,851 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:44:44,884 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:44:45,137 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:44:45,292 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:44:45,295 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-31 23:44:45,297 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-31 23:44:45,298 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-31 23:44:45,298 - models.predict - INFO - Created fallback ensemble model with base price: 82.85
2025-05-31 23:44:45,299 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-31 23:44:45,299 - models.predict - INFO - Ensemble model already loaded
2025-05-31 23:44:45,320 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-31 23:44:45,320 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 82.87786373071036
2025-05-31 23:44:45,321 - models.predict - WARNING - Prediction 4583.406932442927 is too far from current price 82.85, using fallback
2025-05-31 23:44:45,321 - models.predict - INFO - Prediction for 60 minutes horizon: 83.52866915793868
2025-05-31 23:44:45,345 - app.utils.memory_management - INFO - Memory before cleanup: 458.27 MB
2025-05-31 23:44:45,556 - app.utils.memory_management - INFO - Garbage collection: collected 9 objects
2025-05-31 23:44:45,556 - app.utils.memory_management - INFO - Memory after cleanup: 458.27 MB (freed 0.00 MB)
2025-05-31 23:46:00,511 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:46:00,539 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 23:46:00,568 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:46:00,569 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:46:00,571 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:46:00,573 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:46:00,579 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:46:00,582 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 23:46:00,584 - app.utils.memory_management - INFO - Memory before cleanup: 458.43 MB
2025-05-31 23:46:00,774 - app.utils.memory_management - INFO - Garbage collection: collected 355 objects
2025-05-31 23:46:00,775 - app.utils.memory_management - INFO - Memory after cleanup: 458.43 MB (freed 0.00 MB)
2025-05-31 23:46:13,463 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:46:13,499 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:46:34,902 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:46:34,903 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:46:34,904 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:46:34,905 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:46:34,909 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:46:34,914 - app.utils.error_handling - INFO - live_trading_component executed in 21.43 seconds
2025-05-31 23:46:34,915 - app.utils.memory_management - INFO - Memory before cleanup: 458.46 MB
2025-05-31 23:46:35,107 - app.utils.memory_management - INFO - Garbage collection: collected 216 objects
2025-05-31 23:46:35,108 - app.utils.memory_management - INFO - Memory after cleanup: 458.46 MB (freed 0.00 MB)
2025-05-31 23:53:36,555 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 23:53:37,230 - app - INFO - Memory management utilities loaded
2025-05-31 23:53:37,232 - app - INFO - Error handling utilities loaded
2025-05-31 23:53:37,232 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 23:53:37,233 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 23:53:37,233 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 23:53:37,234 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 23:53:37,234 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-31 23:53:37,235 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-31 23:53:37,235 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-31 23:53:37,235 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 23:53:37,235 - app - INFO - Applied NumPy fix
2025-05-31 23:53:37,236 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:53:37,237 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:53:37,237 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:53:37,239 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 23:53:37,239 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:53:37,239 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:53:37,239 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:53:37,240 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 23:53:41,336 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 23:53:41,339 - app - INFO - Applied TensorFlow fix
2025-05-31 23:53:42,630 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 23:53:42,630 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 23:53:42,630 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 23:53:42,634 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 23:53:42,842 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:53:42,842 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:53:42,842 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:53:42,843 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:53:42,844 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:53:42,844 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 23:53:42,845 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-31 23:53:42,845 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:53:42,845 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:53:42,845 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:53:42,934 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 23:53:42,943 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 23:53:45,248 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:53:46,433 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:53:46,433 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:54:13,827 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:54:13,850 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-31 23:54:13,851 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-31 23:54:13,851 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-31 23:54:13,852 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-31 23:54:13,854 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:54:15,000 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:54:15,001 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:54:40,223 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:54:40,264 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 23:54:40,435 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 23:54:40,675 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-31 23:54:40,707 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 23:54:40,760 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 23:54:40,766 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 23:54:40,768 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 23:54:40,769 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:54:40,770 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:54:40,825 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 23:54:40,827 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:54:40,828 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:54:40,838 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:54:40,839 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7537354242801666
2025-05-31 23:54:40,840 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00469940180149
2025-05-31 23:54:40,843 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:54:40,849 - app - INFO - Cleaning up resources...
2025-05-31 23:54:40,853 - app.utils.memory_management - INFO - Memory before cleanup: 420.71 MB
2025-05-31 23:54:41,013 - app.utils.memory_management - INFO - Garbage collection: collected 31 objects
2025-05-31 23:54:41,016 - app.utils.memory_management - INFO - Memory after cleanup: 420.71 MB (freed 0.00 MB)
2025-05-31 23:54:41,017 - app - INFO - Application shutdown complete
2025-05-31 23:55:06,822 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-31 23:55:06,829 - app - INFO - Memory management utilities loaded
2025-05-31 23:55:06,831 - app - INFO - Error handling utilities loaded
2025-05-31 23:55:06,834 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-31 23:55:06,835 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-31 23:55:06,836 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-31 23:55:06,837 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-31 23:55:06,838 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'CompatibleMT19937' object has no attribute 'capsule'
2025-05-31 23:55:06,839 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-31 23:55:06,841 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-31 23:55:06,845 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-31 23:55:06,845 - app - INFO - Applied NumPy fix
2025-05-31 23:55:06,847 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:55:06,847 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:55:06,849 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:55:06,849 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-31 23:55:06,851 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:55:06,854 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:55:06,854 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:55:06,857 - app - INFO - Applied NumPy BitGenerator fix
2025-05-31 23:55:06,861 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-31 23:55:06,862 - app - INFO - Applied TensorFlow fix
2025-05-31 23:55:06,865 - app.config - INFO - Configuration initialized
2025-05-31 23:55:06,871 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-31 23:55:06,875 - models.train - INFO - TensorFlow test successful
2025-05-31 23:55:06,879 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-31 23:55:06,880 - models.train - INFO - Transformer model is available
2025-05-31 23:55:06,880 - models.train - INFO - Using TensorFlow-based models
2025-05-31 23:55:06,883 - models.predict - INFO - Transformer model is available for predictions
2025-05-31 23:55:06,885 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-31 23:55:06,888 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-31 23:55:06,897 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:55:06,899 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-31 23:55:06,900 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-31 23:55:06,902 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-31 23:55:06,905 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-31 23:55:06,907 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-31 23:55:06,911 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'CompatibleBitGenerator' has no attribute 'register'
2025-05-31 23:55:06,915 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-31 23:55:06,916 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-31 23:55:06,919 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-31 23:55:06,924 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-31 23:55:06,928 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:06,963 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-31 23:55:06,998 - app.services.llm_service - INFO - llama_cpp is available
2025-05-31 23:55:07,026 - app.utils.session_state - INFO - Initializing session state
2025-05-31 23:55:07,030 - app.utils.session_state - INFO - Session state initialized
2025-05-31 23:55:07,047 - app - INFO - Found 8 stock files in data/stocks
2025-05-31 23:55:07,062 - app.utils.memory_management - INFO - Memory before cleanup: 453.18 MB
2025-05-31 23:55:07,264 - app.utils.memory_management - INFO - Garbage collection: collected 335 objects
2025-05-31 23:55:07,267 - app.utils.memory_management - INFO - Memory after cleanup: 453.18 MB (freed 0.00 MB)
2025-05-31 23:55:16,631 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:16,665 - app.utils.memory_management - INFO - Memory before cleanup: 454.39 MB
2025-05-31 23:55:16,857 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:55:16,859 - app.utils.memory_management - INFO - Memory after cleanup: 454.39 MB (freed 0.00 MB)
2025-05-31 23:55:17,975 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:18,002 - app.utils.memory_management - INFO - Memory before cleanup: 454.78 MB
2025-05-31 23:55:18,222 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-31 23:55:18,223 - app.utils.memory_management - INFO - Memory after cleanup: 454.78 MB (freed 0.00 MB)
2025-05-31 23:55:18,409 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:18,471 - app.utils.memory_management - INFO - Memory before cleanup: 454.79 MB
2025-05-31 23:55:18,684 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-31 23:55:18,684 - app.utils.memory_management - INFO - Memory after cleanup: 454.77 MB (freed 0.02 MB)
2025-05-31 23:55:22,998 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:23,023 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:55:23,040 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:55:23,042 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:55:23,043 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:55:23,044 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:55:23,050 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:55:23,054 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-05-31 23:55:23,054 - app.utils.memory_management - INFO - Memory before cleanup: 454.83 MB
2025-05-31 23:55:23,246 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-31 23:55:23,247 - app.utils.memory_management - INFO - Memory after cleanup: 454.83 MB (freed 0.00 MB)
2025-05-31 23:55:25,306 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:26,547 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:55:26,548 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:55:49,712 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:55:49,713 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-31 23:55:49,716 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:55:49,720 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-31 23:55:49,757 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:55:49,818 - app.utils.error_handling - INFO - live_trading_component executed in 24.49 seconds
2025-05-31 23:55:49,896 - app.utils.memory_management - INFO - Memory before cleanup: 454.91 MB
2025-05-31 23:55:50,211 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-31 23:55:50,212 - app.utils.memory_management - INFO - Memory after cleanup: 454.91 MB (freed 0.00 MB)
2025-05-31 23:55:59,498 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:55:59,544 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:55:59,547 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:55:59,552 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:55:59,567 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:55:59,568 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:55:59,570 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:55:59,572 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:55:59,581 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:55:59,584 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:55:59,599 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:55:59,613 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:55:59,627 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-31 23:55:59,630 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:55:59,635 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:55:59,639 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:55:59,649 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:55:59,652 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:55:59,657 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-31 23:55:59,663 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:55:59,665 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:55:59,670 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:55:59,683 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:55:59,685 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:55:59,686 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-31 23:55:59,691 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:55:59,694 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:55:59,696 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:55:59,709 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:55:59,713 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:55:59,718 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-31 23:55:59,764 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:55:59,776 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:55:59,779 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:55:59,789 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:55:59,803 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:55:59,803 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:55:59,804 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:55:59,804 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:55:59,804 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:55:59,806 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:55:59,807 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:55:59,808 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:55:59,809 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:55:59,810 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:55:59,810 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:55:59,811 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:55:59,812 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:55:59,812 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:55:59,816 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:55:59,817 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:55:59,818 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:55:59,818 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:55:59,819 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:56:00,066 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:56:00,117 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:00,118 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:00,119 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:00,129 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:00,131 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:00,132 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:56:00,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:00,144 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:00,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:00,158 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:56:00,159 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:56:00,161 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:56:00,162 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:56:00,168 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:00,169 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:56:00,169 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:56:00,170 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:56:00,170 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:56:00,177 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:00,178 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:56:00,179 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:00,180 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:00,180 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:00,187 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:00,188 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:00,197 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:00,206 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:00,210 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:56:00,211 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:56:00,212 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:00,226 - app.utils.memory_management - INFO - Memory before cleanup: 454.87 MB
2025-05-31 23:56:00,429 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-31 23:56:00,431 - app.utils.memory_management - INFO - Memory after cleanup: 454.87 MB (freed 0.00 MB)
2025-05-31 23:56:03,244 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:56:03,295 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:03,296 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:56:03,297 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:03,297 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:56:03,298 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:56:03,298 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:56:03,300 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:03,300 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:56:03,300 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:03,301 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:56:03,301 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:56:03,302 - app.utils.data_processing - INFO - Found hybrid model for COMI with 4 minutes horizon
2025-05-31 23:56:03,302 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-05-31 23:56:03,305 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:56:04,460 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:56:04,460 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:56:26,754 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:56:26,794 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-31 23:56:26,981 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-31 23:56:27,161 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:56:27,164 - models.predict - INFO - Using scikit-learn rf model for 4 minutes horizon
2025-05-31 23:56:27,166 - models.hybrid_model - INFO - XGBoost is available
2025-05-31 23:56:27,167 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-31 23:56:27,170 - models.predict - INFO - Loading rf model for COMI with horizon 4
2025-05-31 23:56:27,170 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:56:27,171 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4min.joblib
2025-05-31 23:56:27,222 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-31 23:56:27,223 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:56:27,223 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:56:27,229 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:56:27,229 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7537354242801666
2025-05-31 23:56:27,230 - models.predict - INFO - Prediction for 4 minutes horizon: 83.00469940180149
2025-05-31 23:56:27,272 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:56:27,279 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:27,280 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:27,288 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:27,295 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:56:27,296 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:56:27,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:56:27,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:56:27,298 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:56:27,299 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:56:27,300 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:56:27,302 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:56:27,303 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:56:27,303 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:56:27,531 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:56:27,586 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:27,588 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:27,588 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:27,594 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:27,595 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:27,596 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:56:27,613 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:27,614 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:27,622 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:27,628 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:56:27,629 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:56:27,640 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:56:27,648 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:56:27,666 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:27,675 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:56:27,686 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:56:27,694 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:56:27,695 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:56:27,706 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:27,707 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:56:27,709 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:27,709 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:27,710 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:27,723 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:27,725 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:27,732 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:27,741 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:27,741 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:56:27,742 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:56:27,743 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:27,757 - app.utils.memory_management - INFO - Memory before cleanup: 459.70 MB
2025-05-31 23:56:27,954 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:56:27,955 - app.utils.memory_management - INFO - Memory after cleanup: 459.70 MB (freed 0.00 MB)
2025-05-31 23:56:52,066 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:56:52,140 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:56:52,150 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:52,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:52,159 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,173 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:56:52,174 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:56:52,176 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:56:52,177 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:56:52,179 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:56:52,182 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:56:52,183 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:56:52,187 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:56:52,191 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:56:52,195 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:56:52,415 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:56:52,468 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:52,470 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:52,471 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:52,482 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,483 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:52,485 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:56:52,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:52,494 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:52,501 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,520 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:56:52,521 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:56:52,525 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:56:52,527 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:56:52,535 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,536 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:56:52,539 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:56:52,539 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:56:52,540 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:56:52,550 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,591 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:56:52,619 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:52,621 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:52,623 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:52,634 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,636 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:52,664 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,672 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,681 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,688 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,699 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:52,716 - app.utils.memory_management - INFO - Memory before cleanup: 459.66 MB
2025-05-31 23:56:52,917 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-31 23:56:52,918 - app.utils.memory_management - INFO - Memory after cleanup: 459.66 MB (freed 0.00 MB)
2025-05-31 23:56:54,987 - app - INFO - Using TensorFlow-based LSTM model
2025-05-31 23:56:55,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-31 23:56:55,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:55,053 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:55,060 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,068 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-31 23:56:55,068 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-31 23:56:55,069 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-31 23:56:55,069 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-31 23:56:55,070 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-31 23:56:55,073 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-31 23:56:55,074 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-31 23:56:55,074 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-31 23:56:55,075 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-31 23:56:55,075 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-31 23:56:55,259 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-31 23:56:55,298 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:55,299 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:55,299 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:55,308 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,309 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:55,310 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-31 23:56:55,320 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-31 23:56:55,326 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-31 23:56:55,343 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-31 23:56:55,359 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-31 23:56:55,361 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-31 23:56:55,361 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-31 23:56:55,376 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,376 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-31 23:56:55,377 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-31 23:56:55,377 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-31 23:56:55,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-31 23:56:55,386 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,389 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-31 23:56:55,392 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-31 23:56:55,394 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-31 23:56:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-31 23:56:55,403 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,404 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-31 23:56:55,424 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,434 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,441 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,450 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,456 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-31 23:56:55,462 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:56:56,593 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:56:56,593 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:57:23,552 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:57:23,592 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:57:23,769 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:57:23,946 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:57:23,948 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-31 23:57:23,948 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-31 23:57:23,949 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-31 23:57:23,949 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-31 23:57:23,950 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:57:23,951 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-31 23:57:23,951 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:57:23,989 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-31 23:57:24,025 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:57:24,025 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:57:24,025 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:57:24,031 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:57:24,032 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7757719242572785
2025-05-31 23:57:24,032 - models.predict - INFO - Prediction for 60 minutes horizon: 84.21230144929885
2025-05-31 23:57:24,036 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:57:25,184 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:57:25,185 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:57:55,776 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:57:55,812 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:57:55,987 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:57:56,132 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:57:56,134 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-31 23:57:56,134 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-31 23:57:56,134 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-31 23:57:56,135 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-31 23:57:56,138 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:57:57,257 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:57:57,258 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:58:25,384 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:58:25,417 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:58:25,578 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:58:25,728 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:58:25,730 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-31 23:58:25,730 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-31 23:58:25,730 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-31 23:58:25,731 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-31 23:58:25,732 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:58:25,733 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-31 23:58:25,733 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-31 23:58:25,740 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-31 23:58:25,747 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:58:25,766 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-31 23:58:25,766 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:58:25,767 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:58:25,768 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.771070069753655
2025-05-31 23:58:25,768 - models.predict - INFO - Prediction for 60 minutes horizon: 83.9546398225003
2025-05-31 23:58:25,771 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:58:26,973 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:58:26,974 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:58:56,360 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:58:56,398 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:58:56,584 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:58:56,749 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:58:56,751 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-31 23:58:56,751 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-31 23:58:56,752 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-31 23:58:56,752 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-31 23:58:56,753 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-31 23:58:56,754 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-31 23:58:56,754 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-31 23:58:56,755 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-31 23:58:56,756 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-31 23:58:56,757 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-31 23:58:56,757 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-31 23:58:56,758 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-31 23:58:56,758 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 0.7464911410725774
2025-05-31 23:58:56,759 - models.predict - INFO - Prediction for 60 minutes horizon: 82.60771453077723
2025-05-31 23:58:56,762 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-05-31 23:58:57,975 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-05-31 23:58:57,976 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-05-31 23:59:31,950 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-05-31 23:59:31,982 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-31 23:59:32,149 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-31 23:59:32,322 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-31 23:59:32,324 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-31 23:59:32,326 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-31 23:59:32,326 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-31 23:59:32,327 - models.predict - INFO - Created fallback ensemble model with base price: 82.85
2025-05-31 23:59:32,327 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-31 23:59:32,327 - models.predict - INFO - Ensemble model already loaded
2025-05-31 23:59:32,347 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-31 23:59:32,347 - models.predict - INFO - Current price: 82.85, Predicted scaled value: 84.5855559748202
2025-05-31 23:59:32,348 - models.predict - WARNING - Prediction 4676.988467420146 is too far from current price 82.85, using fallback
2025-05-31 23:59:32,348 - models.predict - INFO - Prediction for 60 minutes horizon: 82.46641678284305
2025-05-31 23:59:32,370 - app.utils.memory_management - INFO - Memory before cleanup: 462.85 MB
2025-05-31 23:59:32,537 - app.utils.memory_management - INFO - Garbage collection: collected 9 objects
2025-05-31 23:59:32,538 - app.utils.memory_management - INFO - Memory after cleanup: 462.85 MB (freed 0.00 MB)
2025-06-01 00:17:07,432 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:17:07,459 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 00:17:07,491 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-01 00:17:07,493 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 00:17:07,493 - app.utils.common - INFO - Data shape: (581, 36)
2025-06-01 00:17:07,494 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-01 00:17:09,391 - app.utils.memory_management - INFO - Memory before cleanup: 435.77 MB
2025-06-01 00:17:09,592 - app.utils.memory_management - INFO - Garbage collection: collected 2811 objects
2025-06-01 00:17:09,593 - app.utils.memory_management - INFO - Memory after cleanup: 435.75 MB (freed 0.02 MB)
2025-06-01 00:32:36,955 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:32:37,128 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-01 00:32:37,136 - app - INFO - Memory management utilities loaded
2025-06-01 00:32:37,138 - app - INFO - Error handling utilities loaded
2025-06-01 00:32:37,139 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-01 00:32:37,140 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-01 00:32:37,140 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-01 00:32:37,141 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-01 00:32:37,141 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'CompatibleMT19937' object has no attribute 'capsule'
2025-06-01 00:32:37,142 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-06-01 00:32:37,142 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-06-01 00:32:37,143 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-01 00:32:37,143 - app - INFO - Applied NumPy fix
2025-06-01 00:32:37,145 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:32:37,145 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:32:37,146 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:32:37,146 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-01 00:32:37,147 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:32:37,147 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:32:37,148 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:32:37,148 - app - INFO - Applied NumPy BitGenerator fix
2025-06-01 00:32:37,149 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-01 00:32:37,150 - app - INFO - Applied TensorFlow fix
2025-06-01 00:32:37,153 - app.config - INFO - Configuration initialized
2025-06-01 00:32:37,159 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-01 00:32:37,162 - models.train - INFO - TensorFlow test successful
2025-06-01 00:32:37,165 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-01 00:32:37,166 - models.train - INFO - Transformer model is available
2025-06-01 00:32:37,167 - models.train - INFO - Using TensorFlow-based models
2025-06-01 00:32:37,168 - models.predict - INFO - Transformer model is available for predictions
2025-06-01 00:32:37,169 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-01 00:32:37,172 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-01 00:32:37,183 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-01 00:32:37,183 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-01 00:32:37,184 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-01 00:32:37,184 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-01 00:32:37,184 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-01 00:32:37,185 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-01 00:32:37,185 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'CompatibleBitGenerator' has no attribute 'register'
2025-06-01 00:32:37,186 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-01 00:32:37,186 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-01 00:32:37,186 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-01 00:32:37,188 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-01 00:32:37,190 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:32:37,220 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-01 00:32:37,240 - app.services.llm_service - INFO - llama_cpp is available
2025-06-01 00:32:37,257 - app.utils.session_state - INFO - Initializing session state
2025-06-01 00:32:37,259 - app.utils.session_state - INFO - Session state initialized
2025-06-01 00:32:37,268 - app - INFO - Found 8 stock files in data/stocks
2025-06-01 00:32:37,280 - app.utils.memory_management - INFO - Memory before cleanup: 428.59 MB
2025-06-01 00:32:37,477 - app.utils.memory_management - INFO - Garbage collection: collected 366 objects
2025-06-01 00:32:37,478 - app.utils.memory_management - INFO - Memory after cleanup: 428.61 MB (freed -0.02 MB)
2025-06-01 00:32:42,252 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:32:42,279 - app.utils.memory_management - INFO - Memory before cleanup: 429.46 MB
2025-06-01 00:32:42,517 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-01 00:32:42,517 - app.utils.memory_management - INFO - Memory after cleanup: 429.46 MB (freed 0.00 MB)
2025-06-01 00:32:43,629 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:32:43,673 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-01 00:32:43,674 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 00:32:43,675 - app - INFO - Data shape: (581, 36)
2025-06-01 00:32:43,675 - app - INFO - File COMI contains 2025 data
2025-06-01 00:32:43,710 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-01 00:32:43,711 - app - INFO - Features shape: (581, 36)
2025-06-01 00:32:43,734 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-01 00:32:43,735 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-01 00:32:43,735 - app - INFO - Data shape: (581, 36)
2025-06-01 00:32:43,735 - app - INFO - File COMI contains 2025 data
2025-06-01 00:32:43,740 - app.utils.memory_management - INFO - Memory before cleanup: 431.80 MB
2025-06-01 00:32:43,939 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-01 00:32:43,940 - app.utils.memory_management - INFO - Memory after cleanup: 431.80 MB (freed 0.00 MB)
2025-06-01 00:32:44,126 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:32:44,181 - app.utils.memory_management - INFO - Memory before cleanup: 431.89 MB
2025-06-01 00:32:44,384 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-01 00:32:44,385 - app.utils.memory_management - INFO - Memory after cleanup: 431.87 MB (freed 0.02 MB)
2025-06-01 00:32:46,318 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:32:46,370 - app.utils.memory_management - INFO - Memory before cleanup: 431.92 MB
2025-06-01 00:32:46,588 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-01 00:32:46,588 - app.utils.memory_management - INFO - Memory after cleanup: 431.92 MB (freed 0.00 MB)
2025-06-01 00:33:01,408 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:33:01,454 - app.utils.memory_management - INFO - Memory before cleanup: 432.27 MB
2025-06-01 00:33:01,659 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-01 00:33:01,660 - app.utils.memory_management - INFO - Memory after cleanup: 432.27 MB (freed 0.00 MB)
2025-06-01 00:33:02,897 - app - INFO - Using TensorFlow-based LSTM model
2025-06-01 00:33:09,047 - app.utils.memory_management - INFO - Memory before cleanup: 432.30 MB
2025-06-01 00:33:09,237 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-01 00:33:09,238 - app.utils.memory_management - INFO - Memory after cleanup: 432.30 MB (freed 0.00 MB)
2025-06-01 00:38:20,042 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020412C65E10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/32a4a8c54d7fca7ac489b31ed9af3965
2025-06-01 00:38:24,113 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020412C974F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/32a4a8c54d7fca7ac489b31ed9af3965
2025-06-01 00:38:28,144 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020412C97130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/32a4a8c54d7fca7ac489b31ed9af3965
2025-06-01 00:38:32,180 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-01 00:38:32,196 - app - INFO - Cleaning up resources...
2025-06-01 00:38:32,197 - app.utils.memory_management - INFO - Memory before cleanup: 431.16 MB
2025-06-01 00:38:32,383 - app.utils.memory_management - INFO - Garbage collection: collected 971 objects
2025-06-01 00:38:32,383 - app.utils.memory_management - INFO - Memory after cleanup: 431.18 MB (freed -0.02 MB)
2025-06-01 00:38:32,385 - app - INFO - Application shutdown complete
2025-06-01 00:38:32,385 - app - INFO - Cleaning up resources...
2025-06-01 00:38:32,385 - app.utils.memory_management - INFO - Memory before cleanup: 431.18 MB
2025-06-01 00:38:32,538 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 00:38:32,538 - app.utils.memory_management - INFO - Memory after cleanup: 431.18 MB (freed 0.00 MB)
2025-06-01 00:38:32,538 - app - INFO - Application shutdown complete
2025-06-01 00:38:32,538 - app - INFO - Cleaning up resources...
2025-06-01 00:38:32,538 - app.utils.memory_management - INFO - Memory before cleanup: 431.18 MB
2025-06-01 00:38:32,694 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-01 00:38:32,694 - app.utils.memory_management - INFO - Memory after cleanup: 431.18 MB (freed 0.00 MB)
2025-06-01 00:38:32,694 - app - INFO - Application shutdown complete
2025-06-01 00:38:32,694 - app - INFO - Cleaning up resources...
2025-06-01 00:38:32,694 - app.utils.memory_management - INFO - Memory before cleanup: 431.18 MB
2025-06-01 00:38:32,855 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-06-01 00:38:32,859 - app.utils.memory_management - INFO - Memory after cleanup: 431.18 MB (freed 0.00 MB)
2025-06-01 00:38:32,861 - app - INFO - Application shutdown complete
