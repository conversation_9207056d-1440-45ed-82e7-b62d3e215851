<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <meta name="robots" content="NONE,NOARCHIVE">
  <title>{% if exception_type %}{{ exception_type }}{% else %}Report{% endif %}
         {% if request %} at {{ request.path_info }}{% endif %}</title>
  <style>
    html * { padding:0; margin:0; }
    body * { padding:10px 20px; }
    body * * { padding:0; }
    body { font-family: sans-serif; background-color:#fff; color:#000; }
    body > :where(header, main, footer) { border-bottom:1px solid #ddd; }
    h1 { font-weight:normal; }
    h2 { margin-bottom:.8em; }
    h3 { margin:1em 0 .5em 0; }
    h4 { margin:0 0 .5em 0; font-weight: normal; }
    code, pre { font-size: 100%; white-space: pre-wrap; word-break: break-word; }
    summary { cursor: pointer; }
    table { border:1px solid #ccc; border-collapse: collapse; width:100%; background:white; }
    tbody td, tbody th { vertical-align:top; padding:2px 3px; }
    thead th {
      padding:1px 6px 1px 3px; background:#fefefe; text-align:left;
      font-weight:normal; font-size: 0.6875rem; border:1px solid #ddd;
    }
    tbody th { width:12em; text-align:right; color:#666; padding-right:.5em; }
    table.vars { margin:5px 10px 2px 40px; width: auto; }
    table.vars td, table.req td { font-family:monospace; }
    table td.code { width:100%; }
    table td.code pre { overflow:hidden; }
    table.source th { color:#666; }
    table.source td { font-family:monospace; white-space:pre; border-bottom:1px solid #eee; }
    ul.traceback { list-style-type:none; color: #222; }
    ul.traceback li.cause { word-break: break-word; }
    ul.traceback li.frame { padding-bottom:1em; color:#4f4f4f; }
    ul.traceback li.user { background-color:#e0e0e0; color:#000 }
    div.context { padding:10px 0; overflow:hidden; }
    div.context ol { padding-left:30px; margin:0 10px; list-style-position: inside; }
    div.context ol li { font-family:monospace; white-space:pre; color:#777; cursor:pointer; padding-left: 2px; }
    div.context ol li pre { display:inline; }
    div.context ol.context-line li { color:#464646; background-color:#dfdfdf; padding: 3px 2px; }
    div.context ol.context-line li span { position:absolute; right:32px; }
    .user div.context ol.context-line li { background-color:#bbb; color:#000; }
    .user div.context ol li { color:#666; }
    div.commands, summary.commands { margin-left: 40px; }
    div.commands a, summary.commands { color:#555; text-decoration:none; }
    .user div.commands a { color: black; }
    #summary { background: #ffc; }
    #summary h2 { font-weight: normal; color: #666; }
    #info { padding: 0; }
    #info > * { padding:10px 20px; }
    #explanation { background:#eee; }
    #template, #template-not-exist { background:#f6f6f6; }
    #template-not-exist ul { margin: 0 0 10px 20px; }
    #template-not-exist .postmortem-section { margin-bottom: 3px; }
    #unicode-hint { background:#eee; }
    #traceback { background:#eee; }
    #requestinfo { background:#f6f6f6; padding-left:120px; }
    #summary table { border:none; background:transparent; }
    #requestinfo h2, #requestinfo h3 { position:relative; margin-left:-100px; }
    #requestinfo h3 { margin-bottom:-1em; }
    .error { background: #ffc; }
    .specific { color:#cc3300; font-weight:bold; }
    h2 span.commands { font-size: 0.7rem; font-weight:normal; }
    span.commands a:link {color:#5E5694;}
    pre.exception_value { font-family: sans-serif; color: #575757; font-size: 1.5rem; margin: 10px 0 10px 0; }
    .append-bottom { margin-bottom: 10px; }
    .fname { user-select: all; }
  </style>
  {% if not is_email %}
  <script>
    function hideAll(elems) {
      for (var e = 0; e < elems.length; e++) {
        elems[e].style.display = 'none';
      }
    }
    window.onload = function() {
      hideAll(document.querySelectorAll('ol.pre-context'));
      hideAll(document.querySelectorAll('ol.post-context'));
      hideAll(document.querySelectorAll('div.pastebin'));
    }
    function toggle() {
      for (var i = 0; i < arguments.length; i++) {
        var e = document.getElementById(arguments[i]);
        if (e) {
          e.style.display = e.style.display == 'none' ? 'block': 'none';
        }
      }
      return false;
    }
    function switchPastebinFriendly(link) {
      s1 = "Switch to copy-and-paste view";
      s2 = "Switch back to interactive view";
      link.textContent = link.textContent.trim() == s1 ? s2: s1;
      toggle('browserTraceback', 'pastebinTraceback');
      return false;
    }
  </script>
  {% endif %}
</head>
<body>
<header id="summary">
  <h1>{% if exception_type %}{{ exception_type }}{% else %}Report{% endif %}
      {% if request %} at {{ request.path_info }}{% endif %}</h1>
  <pre class="exception_value">{% if exception_value %}{{ exception_value|force_escape }}{% if exception_notes %}{{ exception_notes }}{% endif %}{% else %}No exception message supplied{% endif %}</pre>
  <table class="meta">
{% if request %}
    <tr>
      <th scope="row">Request Method:</th>
      <td>{{ request.META.REQUEST_METHOD }}</td>
    </tr>
    <tr>
      <th scope="row">Request URL:</th>
      <td>{{ request_insecure_uri }}</td>
    </tr>
{% endif %}
    <tr>
      <th scope="row">Django Version:</th>
      <td>{{ django_version_info }}</td>
    </tr>
{% if exception_type %}
    <tr>
      <th scope="row">Exception Type:</th>
      <td>{{ exception_type }}</td>
    </tr>
{% endif %}
{% if exception_type and exception_value %}
    <tr>
      <th scope="row">Exception Value:</th>
      <td><pre>{{ exception_value|force_escape }}</pre></td>
    </tr>
{% endif %}
{% if lastframe %}
    <tr>
      <th scope="row">Exception Location:</th>
      <td><span class="fname">{{ lastframe.filename }}</span>, line {{ lastframe.lineno }}, in {{ lastframe.function }}</td>
    </tr>
{% endif %}
{% if raising_view_name %}
    <tr>
      <th scope="row">Raised during:</th>
      <td>{{ raising_view_name }}</td>
    </tr>
{% endif %}
    <tr>
      <th scope="row">Python Executable:</th>
      <td>{{ sys_executable }}</td>
    </tr>
    <tr>
      <th scope="row">Python Version:</th>
      <td>{{ sys_version_info }}</td>
    </tr>
    <tr>
      <th scope="row">Python Path:</th>
      <td><pre><code>{{ sys_path|pprint }}</code></pre></td>
    </tr>
    <tr>
      <th scope="row">Server time:</th>
      <td>{{server_time|date:"r"}}</td>
    </tr>
  </table>
</header>

<main id="info">
{% if unicode_hint %}
<div id="unicode-hint">
    <h2>Unicode error hint</h2>
    <p>The string that could not be encoded/decoded was: <strong>{{ unicode_hint }}</strong></p>
</div>
{% endif %}
{% if template_does_not_exist %}
<div id="template-not-exist">
    <h2>Template-loader postmortem</h2>
    {% if postmortem %}
        <p class="append-bottom">Django tried loading these templates, in this order:</p>
        {% for entry in postmortem %}
            <p class="postmortem-section">Using engine <code>{{ entry.backend.name }}</code>:</p>
            <ul>
                {% if entry.tried %}
                    {% for attempt in entry.tried %}
                        <li><code>{{ attempt.0.loader_name }}</code>: {{ attempt.0.name }} ({{ attempt.1 }})</li>
                    {% endfor %}
                {% else %}
                    <li>This engine did not provide a list of tried templates.</li>
                {% endif %}
            </ul>
        {% endfor %}
    {% else %}
        <p>No templates were found because your 'TEMPLATES' setting is not configured.</p>
    {% endif %}
</div>
{% endif %}
{% if template_info %}
<div id="template">
   <h2>Error during template rendering</h2>
   <p>In template <code>{{ template_info.name }}</code>, error at line <strong>{{ template_info.line }}</strong></p>
   <h3>{{ template_info.message|force_escape }}</h3>
   <table class="source{% if template_info.top %} cut-top{% endif %}
      {% if template_info.bottom != template_info.total %} cut-bottom{% endif %}">
   {% for source_line in template_info.source_lines %}
   {% if source_line.0 == template_info.line %}
   <tr class="error"><th scope="row">{{ source_line.0 }}</th>
     <td>{{ template_info.before }}<span class="specific">{{ template_info.during }}</span>{{ template_info.after }}</td>
   </tr>
   {% else %}
      <tr><th scope="row">{{ source_line.0 }}</th>
      <td>{{ source_line.1 }}</td></tr>
   {% endif %}
   {% endfor %}
   </table>
</div>
{% endif %}
{% if frames %}
<div id="traceback">
  <h2>Traceback{% if not is_email %} <span class="commands"><a href="#" role="button" onclick="return switchPastebinFriendly(this);">
    Switch to copy-and-paste view</a></span>{% endif %}
  </h2>
  <div id="browserTraceback">
    <ul class="traceback">
      {% for frame in frames %}
        {% ifchanged frame.exc_cause %}{% if frame.exc_cause %}
          <li class="cause"><h3>
          {% if frame.exc_cause_explicit %}
            The above exception ({{ frame.exc_cause|force_escape }}) was the direct cause of the following exception:
          {% else %}
            During handling of the above exception ({{ frame.exc_cause|force_escape }}), another exception occurred:
          {% endif %}
        </h3></li>
        {% endif %}{% endifchanged %}
        <li class="frame {{ frame.type }}">
          {% if frame.tb %}
            <code class="fname">{{ frame.filename }}</code>, line {{ frame.lineno }}, in {{ frame.function }}
          {% elif forloop.first %}
            None
          {% else %}
            Traceback: None
          {% endif %}

          {% if frame.context_line %}
            <div class="context" id="c{{ frame.id }}">
              {% if frame.pre_context and not is_email %}
                <ol start="{{ frame.pre_context_lineno }}" class="pre-context" id="pre{{ frame.id }}">
                {% for line in frame.pre_context %}
                  <li onclick="toggle('pre{{ frame.id }}', 'post{{ frame.id }}')"><pre>{{ line }}</pre></li>
                {% endfor %}
                </ol>
              {% endif %}
              <ol start="{{ frame.lineno }}" class="context-line">
                <li onclick="toggle('pre{{ frame.id }}', 'post{{ frame.id }}')"><pre>{{ frame.context_line }}{{ frame.colno }}</pre>{% if not is_email %} <span>…</span>{% endif %}</li>
              </ol>
              {% if frame.post_context and not is_email  %}
                <ol start='{{ frame.lineno|add:"1" }}' class="post-context" id="post{{ frame.id }}">
                  {% for line in frame.post_context %}
                  <li onclick="toggle('pre{{ frame.id }}', 'post{{ frame.id }}')"><pre>{{ line }}</pre></li>
                  {% endfor %}
              </ol>
              {% endif %}
            </div>
          {% endif %}

          {% if frame.vars %}
            {% if is_email %}
              <div class="commands">
                <h2>Local Vars</h2>
              </div>
            {% else %}
              <details>
                <summary class="commands">Local vars</summary>
            {% endif %}
            <table class="vars" id="v{{ frame.id }}">
              <thead>
                <tr>
                  <th scope="col">Variable</th>
                  <th scope="col">Value</th>
                </tr>
              </thead>
              <tbody>
                {% for var in frame.vars|dictsort:0 %}
                  <tr>
                    <td>{{ var.0 }}</td>
                    <td class="code"><pre>{{ var.1 }}</pre></td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
            {% if not is_email %}</details>{% endif %}
          {% endif %}
        </li>
      {% endfor %}
    </ul>
  </div>
{% if not is_email %}
  <form action="https://dpaste.com/" name="pasteform" id="pasteform" method="post">
  <div id="pastebinTraceback" class="pastebin">
    <input type="hidden" name="language" value="PythonConsole">
    <input type="hidden" name="title"
      value="{{ exception_type }}{% if request %} at {{ request.path_info }}{% endif %}">
    <input type="hidden" name="source" value="Django Dpaste Agent">
    <input type="hidden" name="poster" value="Django">
    <textarea name="content" id="traceback_area" cols="140" rows="25">
Environment:

{% if request %}
Request Method: {{ request.META.REQUEST_METHOD }}
Request URL: {{ request_insecure_uri }}
{% endif %}
Django Version: {{ django_version_info }}
Python Version: {{ sys_version_info }}
Installed Applications:
{{ settings.INSTALLED_APPS|pprint }}
Installed Middleware:
{{ settings.MIDDLEWARE|pprint }}

{% if template_does_not_exist %}Template loader postmortem
{% if postmortem %}Django tried loading these templates, in this order:
{% for entry in postmortem %}
Using engine {{ entry.backend.name }}:
{% if entry.tried %}{% for attempt in entry.tried %}    * {{ attempt.0.loader_name }}: {{ attempt.0.name }} ({{ attempt.1 }})
{% endfor %}{% else %}    This engine did not provide a list of tried templates.
{% endif %}{% endfor %}
{% else %}No templates were found because your 'TEMPLATES' setting is not configured.
{% endif %}{% endif %}{% if template_info %}
Template error:
In template {{ template_info.name }}, error at line {{ template_info.line }}
   {{ template_info.message|force_escape }}
{% for source_line in template_info.source_lines %}{% if source_line.0 == template_info.line %}   {{ source_line.0 }} : {{ template_info.before }} {{ template_info.during }} {{ template_info.after }}{% else %}   {{ source_line.0 }} : {{ source_line.1 }}{% endif %}{% endfor %}{% endif %}

Traceback (most recent call last):{% for frame in frames %}
{% ifchanged frame.exc_cause %}{% if frame.exc_cause %}{% if frame.exc_cause_explicit %}
The above exception ({{ frame.exc_cause|force_escape }}) was the direct cause of the following exception:
{% else %}
During handling of the above exception ({{ frame.exc_cause|force_escape }}), another exception occurred:
{% endif %}{% endif %}{% endifchanged %}  {% if frame.tb %}File "{{ frame.filename }}"{% if frame.context_line %}, line {{ frame.lineno }}{% endif %}, in {{ frame.function }}
{% if frame.context_line %}    {% spaceless %}{{ frame.context_line }}{% endspaceless %}{{ frame.tb_area_colno }}{% endif %}{% elif forloop.first %}None{% else %}Traceback: None{% endif %}{% endfor %}

Exception Type: {{ exception_type }}{% if request %} at {{ request.path_info }}{% endif %}
Exception Value: {{ exception_value|force_escape }}{% if exception_notes %}{{ exception_notes }}{% endif %}
</textarea>
  <br><br>
  <input type="submit" value="Share this traceback on a public website">
  </div>
</form>
{% endif %}
</div>
{% endif %}

<div id="requestinfo">
  <h2>Request information</h2>

{% if request %}
  {% if user_str %}
    <h3 id="user-info">USER</h3>
    <p>{{ user_str }}</p>
  {% endif %}

  <h3 id="get-info">GET</h3>
  {% if request.GET %}
    <table class="req">
      <thead>
        <tr>
          <th scope="col">Variable</th>
          <th scope="col">Value</th>
        </tr>
      </thead>
      <tbody>
        {% for k, v in request_GET_items %}
          <tr>
            <td>{{ k }}</td>
            <td class="code"><pre>{{ v|pprint }}</pre></td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <p>No GET data</p>
  {% endif %}

  <h3 id="post-info">POST</h3>
  {% if filtered_POST_items %}
    <table class="req">
      <thead>
        <tr>
          <th scope="col">Variable</th>
          <th scope="col">Value</th>
        </tr>
      </thead>
      <tbody>
        {% for k, v in filtered_POST_items %}
          <tr>
            <td>{{ k }}</td>
            <td class="code"><pre>{{ v|pprint }}</pre></td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <p>No POST data</p>
  {% endif %}

  <h3 id="files-info">FILES</h3>
  {% if request.FILES %}
    <table class="req">
      <thead>
        <tr>
          <th scope="col">Variable</th>
          <th scope="col">Value</th>
        </tr>
      </thead>
      <tbody>
        {% for k, v in request_FILES_items %}
          <tr>
            <td>{{ k }}</td>
            <td class="code"><pre>{{ v|pprint }}</pre></td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <p>No FILES data</p>
  {% endif %}

  <h3 id="cookie-info">COOKIES</h3>
  {% if request.COOKIES %}
    <table class="req">
      <thead>
        <tr>
          <th scope="col">Variable</th>
          <th scope="col">Value</th>
        </tr>
      </thead>
      <tbody>
        {% for k, v in request_COOKIES_items %}
          <tr>
            <td>{{ k }}</td>
            <td class="code"><pre>{{ v|pprint }}</pre></td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <p>No cookie data</p>
  {% endif %}

  <h3 id="meta-info">META</h3>
  <table class="req">
    <thead>
      <tr>
        <th scope="col">Variable</th>
        <th scope="col">Value</th>
      </tr>
    </thead>
    <tbody>
      {% for k, v in request_meta.items|dictsort:0 %}
        <tr>
          <td>{{ k }}</td>
          <td class="code"><pre>{{ v|pprint }}</pre></td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% else %}
  <p>Request data not supplied</p>
{% endif %}

  <h3 id="settings-info">Settings</h3>
  <h4>Using settings module <code>{{ settings.SETTINGS_MODULE }}</code></h4>
  <table class="req">
    <thead>
      <tr>
        <th scope="col">Setting</th>
        <th scope="col">Value</th>
      </tr>
    </thead>
    <tbody>
      {% for k, v in settings.items|dictsort:0 %}
        <tr>
          <td>{{ k }}</td>
          <td class="code"><pre>{{ v|pprint }}</pre></td>
        </tr>
      {% endfor %}
    </tbody>
  </table>

</div>
</main>

{% if not is_email %}
  <footer id="explanation">
    <p>
      You’re seeing this error because you have <code>DEBUG = True</code> in your
      Django settings file. Change that to <code>False</code>, and Django will
      display a standard page generated by the handler for this status code.
    </p>
  </footer>
{% endif %}
</body>
</html>
